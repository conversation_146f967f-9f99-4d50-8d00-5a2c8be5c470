/**
 * GraphQL Operations for Account Management
 * 
 * Queries and mutations for account operations
 */

import { gql } from '@apollo/client'

// ==================== FRAGMENTS ====================

export const ACCOUNT_TYPE_FRAGMENT = gql`
  fragment AccountTypeFragment on AccountType {
    id
    name
    description
    category
    color
    icon
    isActive
    createdAt
    updatedAt
  }
`

export const ACCOUNT_FRAGMENT = gql`
  fragment AccountFragment on Account {
    id
    name
    description
    balance
    currency
    color
    icon
    isActive
    userId
    accountTypeId
    createdAt
    updatedAt
  }
`

export const ACCOUNT_WITH_TYPE_FRAGMENT = gql`
  fragment AccountWithTypeFragment on Account {
    ...AccountFragment
    accountType {
      ...AccountTypeFragment
    }
  }
  ${ACCOUNT_FRAGMENT}
  ${ACCOUNT_TYPE_FRAGMENT}
`

// ==================== QUERIES ====================

export const GET_ACCOUNT_TYPES = gql`
  query GetAccountTypes {
    accountTypes {
      ...AccountTypeFragment
    }
  }
  ${ACCOUNT_TYPE_FRAGMENT}
`

export const GET_ACCOUNTS = gql`
  query GetAccounts(
    $first: Int
    $after: String
    $search: String
    $accountTypeId: UUID
    $isActive: Boolean
  ) {
    accounts(
      first: $first
      after: $after
      search: $search
      accountTypeId: $accountTypeId
      isActive: $isActive
    ) {
      edges {
        node {
          ...AccountWithTypeFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${ACCOUNT_WITH_TYPE_FRAGMENT}
`

export const GET_ACCOUNT_BY_ID = gql`
  query GetAccountById($id: UUID!) {
    account(id: $id) {
      ...AccountWithTypeFragment
      user {
        id
        name
        email
      }
      transactions(first: 20) {
        edges {
          node {
            id
            kind
            amount
            date
            description
            category {
              id
              name
              type
            }
          }
        }
      }
    }
  }
  ${ACCOUNT_WITH_TYPE_FRAGMENT}
`

export const SEARCH_ACCOUNTS = gql`
  query SearchAccounts($search: String!, $first: Int = 10) {
    accounts(search: $search, first: $first) {
      edges {
        node {
          ...AccountWithTypeFragment
        }
      }
      totalCount
    }
  }
  ${ACCOUNT_WITH_TYPE_FRAGMENT}
`

export const GET_ACCOUNT_SUMMARY = gql`
  query GetAccountSummary {
    accounts(first: 100, isActive: true) {
      edges {
        node {
          id
          name
          balance
          currency
          accountType {
            name
            category
            color
          }
        }
      }
      totalCount
    }
  }
`

// ==================== MUTATIONS ====================

export const CREATE_ACCOUNT = gql`
  mutation CreateAccount($input: CreateAccountInput!) {
    createAccount(input: $input) {
      ...AccountWithTypeFragment
    }
  }
  ${ACCOUNT_WITH_TYPE_FRAGMENT}
`

export const UPDATE_ACCOUNT = gql`
  mutation UpdateAccount($id: UUID!, $input: UpdateAccountInput!) {
    updateAccount(id: $id, input: $input) {
      ...AccountWithTypeFragment
    }
  }
  ${ACCOUNT_WITH_TYPE_FRAGMENT}
`

export const DELETE_ACCOUNT = gql`
  mutation DeleteAccount($id: UUID!) {
    deleteAccount(id: $id)
  }
`
