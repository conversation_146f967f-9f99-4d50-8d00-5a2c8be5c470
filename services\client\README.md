# API Client Implementation

## 📋 **Tổng quan**

API Client hoàn chỉnh cho Money Management App với TypeScript support, error handling, và specialized services cho từng entity.

## 🏗️ **Cấu trúc**

```
services/api/
├── apiClient.ts          # Core API client với HTTP methods
├── userApi.ts           # User-specific API operations
├── accountApi.ts        # Account-specific API operations  
├── transactionApi.ts    # Transaction-specific API operations
├── index.ts             # Barrel exports và utilities
└── README.md            # Documentation
```

## 🔧 **Core Features**

### **1. Base API Client (`apiClient.ts`)**
- **HTTP Methods**: GET, POST, PUT, PATCH, DELETE
- **Error Handling**: Custom error classes với detailed information
- **Timeout Support**: Configurable request timeout
- **Authentication**: Bearer token support
- **Request/Response Interceptors**: Automatic JSON parsing
- **Query String Building**: Automatic parameter serialization

### **2. Specialized API Services**
- **UserApiService**: User management và preferences
- **AccountApiService**: Account và account type management
- **TransactionApiService**: Transaction CRUD với advanced features
- **ContactApiService**: Contact và contact type management
- **CategoryApiService**: Category management với hierarchy support
- **BudgetApiService**: Budget management với analysis features
- **RecurringTransactionApiService**: Recurring transaction automation
- **DashboardApiService**: Dashboard statistics và analytics

### **3. Error Classes**
- **ApiClientError**: Base API error với status code
- **NetworkError**: Network-related errors
- **ValidationError**: Validation errors với field details

## 🚀 **Usage Examples**

### **Basic Usage**
```typescript
import { ApiClient, UserApiService, AccountApiService, TransactionApiService } from '@/services/api'

// Using core client
const response = await ApiClient.getUsers({ page: 1, limit: 10 })

// Using specialized services
const users = await UserApiService.getActiveUsers()
const accounts = await AccountApiService.getUserAccounts('user-id')
const transactions = await TransactionApiService.getCurrentMonthTransactions()

// Or using convenience exports
import { getUsers, getActiveUsers, getUserAccounts, getCurrentMonthTransactions } from '@/services/api'

const response = await getUsers({ page: 1, limit: 10 })
const users = await getActiveUsers()
const accounts = await getUserAccounts('user-id')
const transactions = await getCurrentMonthTransactions()
```

### **Authentication**
```typescript
import { ApiClient, setAuthToken, removeAuthToken, isAuthenticated } from '@/services/api'

// Set authentication token
ApiClient.setAuthToken('your-jwt-token')
// or
setAuthToken('your-jwt-token')

// Check authentication
const isAuth = ApiClient.isAuthenticated()
// or
const isAuth = isAuthenticated()

// Remove authentication
ApiClient.removeAuthToken()
// or
removeAuthToken()
```

### **Error Handling**
```typescript
import { 
  isApiError, 
  isNetworkError, 
  getApiErrorMessage 
} from '@/services/api'

try {
  const result = await userApi.createUser(userData)
} catch (error) {
  if (isApiError(error)) {
    console.error('API Error:', error.status, error.message)
  } else if (isNetworkError(error)) {
    console.error('Network Error:', error.message)
  } else {
    console.error('Unknown Error:', getApiErrorMessage(error))
  }
}
```

### **Configuration**
```typescript
import { ApiClient, configureApi, initializeApi } from '@/services/api'

// Direct configuration
ApiClient.setBaseURL('https://api.example.com')
ApiClient.setTimeout(30000)
ApiClient.setAuthToken('your-token')

// Or use convenience function
configureApi({
  baseURL: 'https://api.example.com',
  timeout: 30000,
  token: 'your-token'
})

// Or initialize with token only
initializeApi('your-token')
```

## 📊 **API Services Details**

### **UserApiService**
```typescript
// Static class approach
await UserApiService.getUsers({ role: 'admin' })
await UserApiService.createUser({ name: 'John', email: '<EMAIL>' })
await UserApiService.updateUser({ id: 'user-id', name: 'John Doe' })

// Convenience methods
await UserApiService.getCurrentUser()
await UserApiService.searchUsers('john')
await UserApiService.activateUser('user-id')
await UserApiService.updateUserTheme('user-id', 'dark')

// Preferences
await UserApiService.getCurrentUserPreferences()
await UserApiService.updateCurrentUserPreferences({ currency: 'USD' })

// Or using convenience exports
import { getUsers, createUser, getCurrentUser, searchUsers } from '@/services/api'

await getUsers({ role: 'admin' })
await createUser({ name: 'John', email: '<EMAIL>' })
await getCurrentUser()
await searchUsers('john')
```

### **AccountApiService**
```typescript
// Static class approach
await AccountApiService.getAccounts({ currency: 'VND' })
await AccountApiService.createAccount({ name: 'Cash', type: 'cash' })
await AccountApiService.updateAccountBalance('account-id', 1000000)

// Account types
await AccountApiService.getAccountTypes()
await AccountApiService.getBankAccountTypes()
await AccountApiService.createAccountType({ name: 'Crypto', isBankAccount: false })

// Convenience methods
await AccountApiService.getUserAccounts('user-id')
await AccountApiService.getBankAccounts()
await AccountApiService.getAccountsWithPositiveBalance()

// Or using convenience exports
import { getAccounts, createAccount, getUserAccounts, getBankAccounts } from '@/services/api'

await getAccounts({ currency: 'VND' })
await createAccount({ name: 'Cash', type: 'cash' })
await getUserAccounts('user-id')
await getBankAccounts()
```

### **TransactionApiService**
```typescript
// Static class approach
await TransactionApiService.getTransactions({ kind: 'expense' })
await TransactionApiService.createTransaction({
  name: 'Grocery',
  amount: 150000,
  kind: 'expense'
})

// Filtered queries
await TransactionApiService.getIncomeTransactions()
await TransactionApiService.getCurrentMonthTransactions()
await TransactionApiService.getTransactionsByAccount('account-id')

// Bulk operations
await TransactionApiService.bulkDeleteTransactions(['id1', 'id2'])
await TransactionApiService.bulkClearTransactions(['id1', 'id2'])

// Import/Export
await TransactionApiService.importTransactions({ file, format: 'csv' })
await TransactionApiService.exportTransactions({ format: 'excel' })

// Statistics
await TransactionApiService.getMonthlyStats(2024, 1)
await TransactionApiService.getAccountTransactionStats('account-id')

// Or using convenience exports
import {
  getTransactions,
  createTransaction,
  getIncomeTransactions,
  getCurrentMonthTransactions,
  bulkDeleteTransactions
} from '@/services/api'

await getTransactions({ kind: 'expense' })
await createTransaction({ name: 'Grocery', amount: 150000, kind: 'expense' })
await getIncomeTransactions()
await getCurrentMonthTransactions()
await bulkDeleteTransactions(['id1', 'id2'])
```

## 🛠️ **Advanced Features**

### **Bulk Operations**
```typescript
// Bulk delete
await transactionApi.bulkDeleteTransactions(['id1', 'id2', 'id3'])

// Bulk update
await transactionApi.bulkUpdateTransactions(['id1', 'id2'], {
  isCleared: true,
  categoryId: 'new-category-id'
})

// Bulk create
await transactionApi.bulkCreateTransactions([
  { name: 'Transaction 1', amount: 100 },
  { name: 'Transaction 2', amount: 200 }
])
```

### **Import/Export**
```typescript
// Import from CSV
const importResult = await transactionApi.importTransactions({
  file: csvFile,
  format: 'csv',
  options: {
    skipFirstRow: true,
    dateFormat: 'DD/MM/YYYY'
  }
})

// Export to Excel
const exportResult = await transactionApi.exportTransactions({
  format: 'excel',
  filters: { startDate: '2024-01-01', endDate: '2024-12-31' },
  options: { includeHeaders: true }
})
```

### **Caching**
```typescript
import { 
  cacheApiResponse, 
  getCachedApiResponse, 
  clearApiCache 
} from '@/services/api'

// Cache response for 5 minutes
const data = await userApi.getUsers()
cacheApiResponse('users-list', data, 300000)

// Get cached response
const cached = getCachedApiResponse('users-list')

// Clear cache
clearApiCache()
```

### **Retry Logic**
```typescript
import { retryApiCall } from '@/services/api'

// Retry API call with exponential backoff
const result = await retryApiCall(
  () => transactionApi.createTransaction(data),
  3, // max retries
  1000 // base delay in ms
)
```

## 🔒 **Security Features**

### **Authentication**
- Bearer token support
- Automatic token attachment to requests
- Token management utilities

### **Error Handling**
- Detailed error information
- Status code preservation
- Custom error types for different scenarios

### **Request Validation**
- TypeScript type checking
- Runtime validation endpoints
- Input sanitization

## 📈 **Performance Features**

### **Request Optimization**
- Automatic query string building
- Request deduplication
- Timeout management

### **Caching**
- In-memory response caching
- TTL-based cache expiration
- Cache management utilities

### **Bulk Operations**
- Batch processing for multiple operations
- Reduced API calls
- Error handling for partial failures

## 🧪 **Testing Support**

### **Mock-friendly Design**
- Dependency injection support
- Configurable base URLs
- Separate service instances

### **Error Simulation**
- Custom error classes
- Detailed error information
- Network error simulation

## 📝 **Type Safety**

### **Full TypeScript Support**
- Complete type definitions
- Request/response type checking
- Generic type support

### **API Type Integration**
- Uses types from `@/types/api`
- Consistent with database schema
- Enhanced response types with computed fields

## 🔄 **Migration Guide**

### **From Old API Client**
1. Update imports: `import { userApi } from '@/services/api'`
2. Use specialized services instead of generic client
3. Update error handling to use new error classes
4. Leverage new convenience methods

### **Breaking Changes**
- Entity IDs are now UUIDs instead of numbers
- Response structure includes computed fields
- Error handling uses custom error classes
- Authentication uses Bearer tokens

## 🚀 **Next Steps**

1. **Add More Services**: Contact, Category, Budget APIs
2. **WebSocket Support**: Real-time updates
3. **Offline Support**: Request queuing and sync
4. **Advanced Caching**: Redis integration
5. **Metrics**: Request timing and success rates
