/**
 * Minimal GraphQL Schema for Testing
 */

import { gql } from 'graphql-tag'

export const typeDefs = gql`
  # Scalars
  scalar DateTime
  scalar UUID
  scalar JSON

  # Enums
  enum UserRole {
    admin
    user
  }

  enum CategoryType {
    INCOME
    EXPENSE
  }

  # Category Types
  type Category {
    id: UUID!
    name: String!
    description: String
    type: CategoryType!
    color: String!
    icon: String
    parentId: UUID
    userId: UUID!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    parent: Category
    children: [Category!]!
    user: User!
  }

  type CategoryConnection {
    edges: [CategoryEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type CategoryEdge {
    node: Category!
    cursor: String!
  }

  # User Types
  type User {
    id: UUID!
    email: String!
    name: String!
    role: UserRole!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
  }

  type UserConnection {
    edges: [UserEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type UserEdge {
    node: User!
    cursor: String!
  }

  type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
  }

  # Queries
  type Query {
    hello: String
    health: String
    currentUser: User
    users(
      first: Int
      after: String
      search: String
      role: UserRole
      isActive: Boolean
    ): UserConnection!
    
    categories(
      first: Int
      after: String
      search: String
      type: CategoryType
      parentId: UUID
      isActive: Boolean
    ): CategoryConnection!
  }

  # Mutations
  type Mutation {
    createUser(input: CreateUserInput!): User!
    updateUser(id: UUID!, input: UpdateUserInput!): User!
    deleteUser(id: UUID!): Boolean!
  }

  # Input Types
  input CreateUserInput {
    email: String!
    name: String!
    password: String!
    role: UserRole = user
  }

  input UpdateUserInput {
    email: String
    name: String
    role: UserRole
    isActive: Boolean
  }
`
