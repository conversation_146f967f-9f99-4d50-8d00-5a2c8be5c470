import { BaseTableActiveable } from '../base';
import { UUID, CurrencyCode } from '../common';

export type {
  Account,
  AccountBase,
  AccountWithRelations,
  CreateAccountInput,
  UpdateAccountInput,
  AccountFilterInput,
  AccountPaymentAppLink,
  AccountDebitCardLink
};

/**
 * Base interface for Account
 */
interface AccountBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  accountTypeId: UUID;
  balance: number;
  currency: CurrencyCode;
  color: string;
  userId: UUID;
  contactId: UUID | null;
}

/**
 * Account with all possible relations
 */
interface AccountWithRelations extends AccountBase {
  user: UserBase;
  accountType: AccountTypeBase;
  contact: ContactBase | null;
  bankInfo: BankInfoBase | null;
  debitCards: DebitCardBase[];
  accountDebitCardLinks: AccountDebitCardLink[];
  accountPaymentAppLinks: AccountPaymentAppLink[];
  categoryGoals: CategoryGoalBase[];
  
  // Transaction relations
  incomeExpenseTransactions: TransactionBase[];
  transferFromTransactions: TransactionBase[];
  transferToTransactions: TransactionBase[];
  connectedTransactions: TransactionBase[];
}

/**
 * Union type for Account
 */
type Account = AccountBase | AccountWithRelations;

/**
 * Input type for creating a new account
 */
interface CreateAccountInput {
  name: string;
  description?: string | null;
  accountTypeId: UUID;
  balance?: number;
  currency?: CurrencyCode;
  color?: string;
  contactId?: UUID | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input type for updating an existing account
 */
interface UpdateAccountInput {
  id: UUID;
  name?: string;
  description?: string | null;
  accountTypeId?: UUID;
  balance?: number;
  currency?: CurrencyCode;
  color?: string;
  contactId?: UUID | null;
  isActive?: boolean;
}

/**
 * Input type for filtering accounts
 */
interface AccountFilterInput {
  search?: string;
  accountTypeId?: UUID;
  contactId?: UUID | null;
  currency?: CurrencyCode;
  isActive?: boolean;
  minBalance?: number;
  maxBalance?: number;
  hasBankInfo?: boolean;
  hasContact?: boolean;
}

/**
 * Represents a link between an account and a payment app
 */
interface AccountPaymentAppLink {
  id: UUID;
  accountId: UUID;
  paymentAppId: UUID;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

/**
 * Represents a link between an account and a debit card
 */
interface AccountDebitCardLink {
  id: UUID;
  accountId: UUID;
  debitCardId: UUID;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// Import related types
import { AccountTypeBase } from './account-type';
import { BankInfoBase } from './bank-info';
import { DebitCardBase } from './debit-card';
import { UserBase } from '../user';
import { ContactBase } from '../contact/contact';
import { CategoryGoalBase } from '../category/category-goal';
import { TransactionBase } from '../transaction/transaction';
