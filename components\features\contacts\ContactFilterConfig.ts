// Filter Configuration for Contacts

import { FilterConfig, FilterDefinition, SortOption } from '@/types/ui'
import { ContactWithAccounts } from '@/types/entities'

// Define filter options
const contactTypeOptions = [
  { value: 'all', label: 'Tất cả loại', count: 0 },
  { value: 'family', label: '<PERSON>ia đình', count: 0 },
  { value: 'friend', label: 'Bạn bè', count: 0 },
  { value: 'business', label: '<PERSON>anh nghiệp', count: 0 },
  { value: 'service', label: 'Dịch vụ', count: 0 }
]

const statusOptions = [
  { value: 'all', label: 'Tất cả trạng thái', count: 0 },
  { value: 'active', label: 'Đang hoạt động', count: 0 },
  { value: 'inactive', label: 'Đã ẩn', count: 0 }
]

const accountOptions = [
  { value: 'all', label: 'Tất cả', count: 0 },
  { value: 'with_accounts', label: '<PERSON><PERSON> tài khoản', count: 0 },
  { value: 'without_accounts', label: 'Không có tài khoản', count: 0 }
]

// Define filter definitions
const contactFilters: FilterDefinition<ContactWithAccounts>[] = [
  {
    key: 'contactType',
    label: 'Loại liên hệ',
    type: 'select',
    options: contactTypeOptions,
    predicate: (contact, value) => {
      if (value === 'all') return true
      return contact.contactType?.id === value
    },
    defaultValue: 'all'
  },
  {
    key: 'status',
    label: 'Trạng thái',
    type: 'select',
    options: statusOptions,
    predicate: (contact, value) => {
      if (value === 'all') return true
      if (value === 'active') return contact.isActive
      if (value === 'inactive') return !contact.isActive
      return true
    },
    defaultValue: 'all'
  },
  {
    key: 'accounts',
    label: 'Tài khoản',
    type: 'select',
    options: accountOptions,
    predicate: (contact, value): boolean => {
      if (value === 'all') return true
      const hasAccounts = contact.accounts && contact.accounts.length > 0
      if (value === 'with_accounts') return !!hasAccounts
      if (value === 'without_accounts') return !hasAccounts
      return true
    },
    defaultValue: 'all'
  },
  {
    key: 'hasPhone',
    label: 'Có số điện thoại',
    type: 'select',
    options: [
      { value: 'all', label: 'Tất cả', count: 0 },
      { value: 'yes', label: 'Có', count: 0 },
      { value: 'no', label: 'Không', count: 0 }
    ],
    predicate: (contact, value) => {
      if (value === 'all') return true
      const hasPhone = !!(contact.phone && contact.phone.trim())
      if (value === 'yes') return hasPhone
      if (value === 'no') return !hasPhone
      return true
    },
    defaultValue: 'all'
  },
  {
    key: 'hasEmail',
    label: 'Có email',
    type: 'select',
    options: [
      { value: 'all', label: 'Tất cả', count: 0 },
      { value: 'yes', label: 'Có', count: 0 },
      { value: 'no', label: 'Không', count: 0 }
    ],
    predicate: (contact, value) => {
      if (value === 'all') return true
      const hasEmail = !!(contact.email && contact.email.trim())
      if (value === 'yes') return hasEmail
      if (value === 'no') return !hasEmail
      return true
    },
    defaultValue: 'all'
  }
]

// Define sort options
const contactSortOptions: SortOption<ContactWithAccounts>[] = [
  {
    key: 'name_asc',
    label: 'Tên (A-Z)',
    compareFn: (a, b) => a.name.localeCompare(b.name, 'vi')
  },
  {
    key: 'name_desc',
    label: 'Tên (Z-A)',
    compareFn: (a, b) => b.name.localeCompare(a.name, 'vi')
  },
  {
    key: 'type_asc',
    label: 'Loại (A-Z)',
    compareFn: (a, b) => {
      const aTypeName = a.contactType?.name || ''
      const bTypeName = b.contactType?.name || ''
      if (aTypeName === bTypeName) {
        return a.name.localeCompare(b.name, 'vi')
      }
      return aTypeName.localeCompare(bTypeName, 'vi')
    }
  },
  {
    key: 'created_desc',
    label: 'Mới nhất',
    compareFn: (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  },
  {
    key: 'created_asc',
    label: 'Cũ nhất',
    compareFn: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  },
  {
    key: 'updated_desc',
    label: 'Cập nhật gần nhất',
    compareFn: (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  },
  {
    key: 'accounts_desc',
    label: 'Nhiều tài khoản nhất',
    compareFn: (a, b) => {
      const aCount = a.accounts?.length || 0
      const bCount = b.accounts?.length || 0
      if (aCount === bCount) {
        return a.name.localeCompare(b.name, 'vi')
      }
      return bCount - aCount
    }
  },
  {
    key: 'balance_desc',
    label: 'Số dư cao nhất',
    compareFn: (a, b) => {
      const aBalance = a.accounts?.reduce((sum, acc) => sum + acc.balance, 0) || 0
      const bBalance = b.accounts?.reduce((sum, acc) => sum + acc.balance, 0) || 0
      if (aBalance === bBalance) {
        return a.name.localeCompare(b.name, 'vi')
      }
      return bBalance - aBalance
    }
  }
]

// Create filter configuration
export const contactFilterConfig: FilterConfig<ContactWithAccounts> = {
  searchFields: ['name', 'phone', 'email', 'address'],
  filters: contactFilters,
  sortOptions: contactSortOptions,
  defaultSort: contactSortOptions[0] // Name A-Z
}

// Helper function to update filter options with counts
export const updateContactFilterCounts = (
  config: FilterConfig<ContactWithAccounts>,
  contacts: ContactWithAccounts[]
): FilterConfig<ContactWithAccounts> => {
  const updatedFilters = config.filters.map(filter => {
    if (!filter.options) return filter

    const updatedOptions = filter.options.map(option => {
      let count = 0
      
      if (filter.key === 'contactType') {
        if (option.value === 'all') count = contacts.length
        else count = contacts.filter(contact => contact.contactType?.id === option.value).length
      } else if (filter.key === 'status') {
        if (option.value === 'all') count = contacts.length
        else if (option.value === 'active') count = contacts.filter(contact => contact.isActive).length
        else if (option.value === 'inactive') count = contacts.filter(contact => !contact.isActive).length
      } else if (filter.key === 'accounts') {
        if (option.value === 'all') count = contacts.length
        else if (option.value === 'with_accounts') {
          count = contacts.filter(contact => contact.accounts && contact.accounts.length > 0).length
        } else if (option.value === 'without_accounts') {
          count = contacts.filter(contact => !contact.accounts || contact.accounts.length === 0).length
        }
      } else if (filter.key === 'hasPhone') {
        if (option.value === 'all') count = contacts.length
        else if (option.value === 'yes') {
          count = contacts.filter(contact => !!(contact.phone && contact.phone.trim())).length
        } else if (option.value === 'no') {
          count = contacts.filter(contact => !(contact.phone && contact.phone.trim())).length
        }
      } else if (filter.key === 'hasEmail') {
        if (option.value === 'all') count = contacts.length
        else if (option.value === 'yes') {
          count = contacts.filter(contact => !!(contact.email && contact.email.trim())).length
        } else if (option.value === 'no') {
          count = contacts.filter(contact => !(contact.email && contact.email.trim())).length
        }
      }

      return { ...option, count }
    })

    return { ...filter, options: updatedOptions }
  })

  return { ...config, filters: updatedFilters }
}

// Export individual pieces for flexibility
export { contactFilters, contactSortOptions, contactTypeOptions, statusOptions, accountOptions }
export default contactFilterConfig
