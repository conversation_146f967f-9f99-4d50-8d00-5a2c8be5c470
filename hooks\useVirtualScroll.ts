/**
 * Virtual Scrolling Hook
 *
 * Optimizes rendering of large lists by only rendering visible items
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react'

interface UseVirtualScrollOptions {
  itemHeight: number
  containerHeight: number
  items: any[]
  overscan?: number // Number of items to render outside visible area
}

interface VirtualScrollResult {
  visibleItems: any[]
  startIndex: number
  endIndex: number
  totalHeight: number
  offsetY: number
  scrollToIndex: (index: number) => void
}

export function useVirtualScroll({
  itemHeight,
  containerHeight,
  items,
  overscan = 5
}: UseVirtualScrollOptions): VirtualScrollResult {
  const [scrollTop, setScrollTop] = useState(0)

  // Calculate visible range
  const { startIndex, endIndex, visibleItems } = useMemo(() => {
    const visibleStart = Math.floor(scrollTop / itemHeight)
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    )

    // Add overscan
    const start = Math.max(0, visibleStart - overscan)
    const end = Math.min(items.length - 1, visibleEnd + overscan)

    return {
      startIndex: start,
      endIndex: end,
      visibleItems: items.slice(start, end + 1)
    }
  }, [scrollTop, itemHeight, containerHeight, items, overscan])

  // Total height of all items
  const totalHeight = items.length * itemHeight

  // Offset for positioning visible items
  const offsetY = startIndex * itemHeight

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number) => {
    const targetScrollTop = index * itemHeight
    setScrollTop(targetScrollTop)
  }, [itemHeight])

  // Handle scroll events
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight,
    offsetY,
    scrollToIndex,
    handleScroll
  } as VirtualScrollResult & { handleScroll: (event: React.UIEvent<HTMLDivElement>) => void }
}

// Virtual List Component
interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  height: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  overscan?: number
}

export function VirtualList<T>({
  items,
  itemHeight,
  height,
  renderItem,
  className = '',
  overscan = 5
}: VirtualListProps<T>): JSX.Element {
  const {
    visibleItems,
    startIndex,
    totalHeight,
    offsetY
  } = useVirtualScroll({
    itemHeight,
    containerHeight: height,
    items,
    overscan
  })

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    // Handle scroll logic here if needed
  }

  return React.createElement('div', {
    className: `overflow-auto ${className}`,
    style: { height },
    onScroll: handleScroll
  }, React.createElement('div', {
    style: { height: totalHeight, position: 'relative' }
  }, React.createElement('div', {
    style: {
      transform: `translateY(${offsetY}px)`,
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0
    }
  }, visibleItems.map((item, index) =>
    React.createElement('div', {
      key: startIndex + index,
      style: { height: itemHeight }
    }, renderItem(item, startIndex + index))
  ))))
}

// Grid Virtual Scrolling
interface UseVirtualGridOptions {
  itemWidth: number
  itemHeight: number
  containerWidth: number
  containerHeight: number
  items: any[]
  columns: number
  overscan?: number
}

export function useVirtualGrid({
  itemWidth,
  itemHeight,
  containerWidth,
  containerHeight,
  items,
  columns,
  overscan = 2
}: UseVirtualGridOptions) {
  const [scrollTop, setScrollTop] = useState(0)

  const { visibleItems, startRow, endRow } = useMemo(() => {
    const rows = Math.ceil(items.length / columns)
    const visibleStartRow = Math.floor(scrollTop / itemHeight)
    const visibleEndRow = Math.min(
      visibleStartRow + Math.ceil(containerHeight / itemHeight),
      rows - 1
    )

    const start = Math.max(0, visibleStartRow - overscan)
    const end = Math.min(rows - 1, visibleEndRow + overscan)

    const startIndex = start * columns
    const endIndex = Math.min((end + 1) * columns - 1, items.length - 1)

    return {
      startRow: start,
      endRow: end,
      visibleItems: items.slice(startIndex, endIndex + 1)
    }
  }, [scrollTop, itemHeight, containerHeight, items, columns, overscan])

  const totalHeight = Math.ceil(items.length / columns) * itemHeight
  const offsetY = startRow * itemHeight

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    startRow,
    endRow,
    totalHeight,
    offsetY,
    handleScroll
  }
}

export default useVirtualScroll
