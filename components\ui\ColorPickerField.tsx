"use client"

import React, { useState } from "react"
import { ColorPicker } from "./ColorPicker"

interface ColorPickerFieldProps {
  value: string
  onChange: (value: string) => void
}

export function ColorPickerField({ value, onChange }: ColorPickerFieldProps) {
  const [showPicker, setShowPicker] = useState(false)

  const handleColorChange = (color: string) => {
    onChange(color)
    setShowPicker(false) // Close picker after selection
  }

  const handleClose = () => {
    setShowPicker(false)
  }

  return (
    <>
      <button
        type="button"
        onClick={() => setShowPicker(true)}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors text-left flex items-center gap-2"
      >
        <div 
          className="w-6 h-6 rounded border border-gray-300 dark:border-gray-600"
          style={{ backgroundColor: value }}
        />
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {value || "Click to select color"}
        </span>
      </button>

      {showPicker && (
        <ColorPicker
          selectedColor={value}
          onColorChange={handleColorChange}
          onClose={handleClose}
        />
      )}
    </>
  )
}
