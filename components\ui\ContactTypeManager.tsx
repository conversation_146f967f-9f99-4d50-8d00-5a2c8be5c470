"use client"

import React, { useState } from "react"
import { Plus, Trash2, Edit, Palette, Users } from "lucide-react"
import { ContactType } from "@/lib/generated/prisma"

// Default contact types for the system
const defaultContactTypes: ContactType[] = [
  {
    id: "person",
    name: "<PERSON><PERSON> nhân",
    description: "<PERSON><PERSON><PERSON> hệ cá nhân",
    color: "#10b981",
    icon: "user",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "business",
    name: "<PERSON><PERSON><PERSON> nghiệ<PERSON>",
    description: "<PERSON><PERSON><PERSON> <PERSON>ệ doanh nghiệp",
    color: "#3b82f6",
    icon: "building2",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "family",
    name: "<PERSON><PERSON> đình",
    description: "Thành viên gia đình",
    color: "#f59e0b",
    icon: "heart",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

interface ContactTypeManagerProps {
  contactTypes: ContactType[]
  selectedType: ContactType | null
  onTypeSelect: (type: ContactType) => void
  onTypeCreate: (type: Omit<ContactType, 'id'>) => void
  onTypeUpdate: (id: string, type: Partial<ContactType>) => void
  onTypeDelete: (id: string) => void
  disabled?: boolean
}

const PRESET_COLORS = [
  "#10b981", "#3b82f6", "#8b5cf6", "#f59e0b", "#ef4444",
  "#06b6d4", "#84cc16", "#ec4899", "#6366f1", "#f97316"
]

export function ContactTypeManager({
  contactTypes,
  selectedType,
  onTypeSelect,
  onTypeCreate,
  onTypeUpdate,
  onTypeDelete,
  disabled = false
}: ContactTypeManagerProps) {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingType, setEditingType] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    color: PRESET_COLORS[0],
    description: ""
  })

  const handleCreateType = () => {
    if (formData.name.trim()) {
      onTypeCreate({
        name: formData.name.trim(),
        color: formData.color,
        description: formData.description.trim() || undefined
      })
      setFormData({ name: "", color: PRESET_COLORS[0], description: "" })
      setShowCreateForm(false)
    }
  }

  const handleUpdateType = (id: string) => {
    if (formData.name.trim()) {
      onTypeUpdate(id, {
        name: formData.name.trim(),
        color: formData.color,
        description: formData.description.trim() || undefined
      })
      setEditingType(null)
      setFormData({ name: "", color: PRESET_COLORS[0], description: "" })
    }
  }

  const startEdit = (type: ContactType) => {
    setFormData({
      name: type.name,
      color: type.color,
      description: type.description || ""
    })
    setEditingType(type.id)
    setShowCreateForm(false)
  }

  const cancelEdit = () => {
    setEditingType(null)
    setShowCreateForm(false)
    setFormData({ name: "", color: PRESET_COLORS[0], description: "" })
  }

  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex items-center justify-between">
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
          <Users size={14} className="inline mr-1" />
          Loại liên hệ
        </label>
        <button
          type="button"
          onClick={() => setShowCreateForm(!showCreateForm)}
          disabled={disabled}
          className="flex items-center gap-1 px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 disabled:opacity-50"
        >
          <Plus size={12} />
          Tạo loại mới
        </button>
      </div>

      {/* Contact Types Grid */}
      <div className="grid grid-cols-2 gap-2">
        {contactTypes.map((type) => (
          <div key={type.id} className="group">
            {editingType === type.id ? (
              // Edit Form
              <div className="border-2 border-blue-500 rounded-lg p-2 space-y-2">
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="Tên loại liên hệ"
                />
                <div className="flex items-center gap-1">
                  {PRESET_COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, color }))}
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.color === color ? 'border-gray-900 dark:border-gray-100' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                <div className="flex gap-1">
                  <button
                    type="button"
                    onClick={() => handleUpdateType(type.id)}
                    className="flex-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Lưu
                  </button>
                  <button
                    type="button"
                    onClick={cancelEdit}
                    className="flex-1 px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    Hủy
                  </button>
                </div>
              </div>
            ) : (
              // Type Button
              <button
                type="button"
                onClick={() => onTypeSelect(type)}
                disabled={disabled}
                className={`w-full p-2 rounded-lg border-2 text-xs font-medium transition-all text-left ${
                  selectedType?.id === type.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-500/20'
                    : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: type.color }}
                    />
                    <span className="text-gray-900 dark:text-gray-100">{type.name}</span>
                  </div>
                  
                  {!type.isDefault && (
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          startEdit(type)
                        }}
                        className="p-1 text-gray-400 hover:text-blue-600"
                      >
                        <Edit size={10} />
                      </button>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          onTypeDelete(type.id)
                        }}
                        className="p-1 text-gray-400 hover:text-red-600"
                      >
                        <Trash2 size={10} />
                      </button>
                    </div>
                  )}
                </div>
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Create Form */}
      {showCreateForm && (
        <div className="border-2 border-green-500 rounded-lg p-3 space-y-2">
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            placeholder="Tên loại liên hệ mới"
          />
          <input
            type="text"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            placeholder="Mô tả (tùy chọn)"
          />
          <div className="flex items-center gap-1">
            <Palette size={12} className="text-gray-500" />
            {PRESET_COLORS.map((color) => (
              <button
                key={color}
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, color }))}
                className={`w-4 h-4 rounded-full border-2 ${
                  formData.color === color ? 'border-gray-900 dark:border-gray-100' : 'border-gray-300 dark:border-gray-600'
                }`}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleCreateType}
              className="flex-1 px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
            >
              Tạo loại
            </button>
            <button
              type="button"
              onClick={() => setShowCreateForm(false)}
              className="flex-1 px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Hủy
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
