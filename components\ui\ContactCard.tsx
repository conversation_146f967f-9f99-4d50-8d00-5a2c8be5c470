"use client"

import React from "react"
import { Edit, Trash2, Phone, Mail, CreditCard, Building2, User, Star } from "lucide-react"
import { Account, Contact } from "@/lib/generated/prisma"

// Extended Contact type with accounts relation
type ContactWithAccounts = Contact & {
  accounts?: Account[]
}

interface ContactCardProps {
  contact: ContactWithAccounts
  onEdit?: (contact: ContactWithAccounts) => void
}

export function ContactCard({ contact, onEdit }: ContactCardProps) {
  return (
    <div className="group bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/50 dark:hover:border-gray-600/50 transition-all duration-200">
      {/* Mobile Layout */}
      <div className="block md:hidden p-4 space-y-3">
        {/* Header Row */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <User size={16} className="text-blue-600 dark:text-blue-400 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                {contact.name}
              </h3>
              {contact.description && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 line-clamp-2" title={contact.description}>
                  {contact.description}
                </p>
              )}
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {onEdit && (
              <button
                onClick={() => onEdit(contact)}
                className="p-1.5 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                title="Edit contact"
              >
                <Edit size={14} />
              </button>
            )}

          </div>
        </div>

        {/* Contact Info */}
        <div className="grid grid-cols-1 gap-2 text-xs">
          {contact.phone && (
            <div className="flex items-center gap-1.5">
              <Phone size={12} className="text-gray-400" />
              <span className="text-gray-600 dark:text-gray-400">
                {contact.phone}
              </span>
            </div>
          )}
          
          {contact.email && (
            <div className="flex items-center gap-1.5">
              <Mail size={12} className="text-gray-400" />
              <span className="text-gray-600 dark:text-gray-400">
                {contact.email}
              </span>
            </div>
          )}
          
          {contact.accounts && contact.accounts.length > 0 && (
            <div className="space-y-1">
              {contact.accounts.slice(0, 2).map((account) => (
                <div key={account.id} className="flex items-center gap-1.5">
                  <Building2 size={12} className="text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400 text-xs">
                    {account.name}
                  </span>
                </div>
              ))}
              {contact.accounts.length > 2 && (
                <div className="text-xs text-gray-500 dark:text-gray-400 ml-4">
                  +{contact.accounts.length - 2} more accounts
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:block p-4">
        <div className="grid grid-cols-12 gap-3 items-center">
          {/* Name Column */}
          <div className="col-span-3">
            <div className="flex items-center gap-2">
              <User size={16} className="text-blue-600 dark:text-blue-400 flex-shrink-0" />
              <div>
                <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                  {contact.name}
                </span>
                {contact.description && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 line-clamp-1" title={contact.description}>
                    {contact.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Phone Column */}
          <div className="col-span-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {contact.phone || "-"}
            </span>
          </div>

          {/* Email Column */}
          <div className="col-span-3">
            <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
              {contact.email || "-"}
            </span>
          </div>

          {/* Bank Info Column */}
          <div className="col-span-3">
            {contact.accounts && contact.accounts.length > 0 ? (
              <div className="space-y-1">
                {contact.accounts.slice(0, 1).map((account) => (
                  <div key={account.id}>
                    <div className="flex items-center gap-1">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {account.name}
                      </span>
                    </div>
                  </div>
                ))}
                {contact.accounts.length > 1 && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    +{contact.accounts.length - 1} more
                  </p>
                )}
              </div>
            ) : (
              <span className="text-sm text-gray-600 dark:text-gray-400">-</span>
            )}
          </div>

          {/* Actions Column */}
          <div className="col-span-1 flex items-center justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {onEdit && (
              <button
                onClick={() => onEdit(contact)}
                className="p-1.5 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                title="Edit contact"
              >
                <Edit size={14} />
              </button>
            )}

          </div>
        </div>
      </div>
    </div>
  )
}
