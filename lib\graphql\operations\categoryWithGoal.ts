/**
 * GraphQL Operations for Category Management with Goals
 * 
 * Queries and mutations for combined category and goal operations
 */

import { gql } from '@apollo/client'
import { CATEGORY_WITH_PARENT_FRAGMENT } from './category'
import { CATEGORY_GOAL_FRAGMENT } from './categoryGoal'

// ==================== FRAGMENTS ====================

export const CATEGORY_WITH_GOAL_FRAGMENT = gql`
  fragment CategoryWithGoalFragment on Category {
    ...CategoryWithParentFragment
    categoryGoal {
      ...CategoryGoalFragment
    }
  }
  ${CATEGORY_WITH_PARENT_FRAGMENT}
  ${CATEGORY_GOAL_FRAGMENT}
`

// ==================== QUERIES ====================

export const GET_CATEGORY_WITH_GOAL = gql`
  query GetCategoryWithGoal($id: UUID!) {
    category(id: $id) {
      ...CategoryWithGoalFragment
    }
  }
  ${CATEGORY_WITH_GOAL_FRAGMENT}
`

export const GET_CATEGORIES_WITH_GOALS = gql`
  query GetCategoriesWithGoals(
    $first: Int
    $after: String
    $search: String
    $type: CategoryType
    $parentId: UUID
    $isActive: Boolean
  ) {
    categories(
      first: $first
      after: $after
      search: $search
      type: $type
      parentId: $parentId
      isActive: $isActive
    ) {
      edges {
        node {
          ...CategoryWithGoalFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${CATEGORY_WITH_GOAL_FRAGMENT}
`

// ==================== MUTATIONS ====================

export const CREATE_CATEGORY_WITH_GOAL = gql`
  mutation CreateCategoryWithGoal($input: CreateCategoryWithGoalInput!) {
    createCategoryWithGoal(input: $input) {
      ...CategoryWithGoalFragment
    }
  }
  ${CATEGORY_WITH_GOAL_FRAGMENT}
`

export const UPDATE_CATEGORY_WITH_GOAL = gql`
  mutation UpdateCategoryWithGoal($id: UUID!, $input: UpdateCategoryWithGoalInput!) {
    updateCategoryWithGoal(id: $id, input: $input) {
      ...CategoryWithGoalFragment
    }
  }
  ${CATEGORY_WITH_GOAL_FRAGMENT}
`
