"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { GenericCard, EntityCard } from "@/components/shared/cards/GenericCard"
import { Transaction } from "@/lib/generated/prisma"
import { CardAction } from "@/types/ui"
import {
  Edit, Trash2, ExternalLink, TrendingUp, TrendingDown, ArrowUpDown,
  Calendar, DollarSign, Building2, Tag, FileText, Clock, MapPin
} from "lucide-react"
import { formatVND } from "@/lib/utils/currency"

interface TransactionCardProps {
  transaction: Transaction
  onEdit?: (transaction: Transaction) => void
  onView?: (transaction: Transaction) => void
  showAccountInfo?: boolean
  showCategoryInfo?: boolean
  variant?: 'default' | 'compact' | 'detailed'
  accountName?: string
  categoryName?: string
  categoryColor?: string
  categoryIcon?: string
}

export function TransactionCard({
  transaction,
  onEdit,
  onView,
  showAccountInfo = true,
  showCategoryInfo = true,
  variant = 'default',
  accountName,
  categoryName,
  categoryColor,
  categoryIcon
}: TransactionCardProps) {
  const router = useRouter()

  // Format currency helper using shared utility
  const formatCurrency = formatVND

  // Format date helper
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(new Date(date))
  }

  // Get transaction type info
  const getTypeInfo = () => {
    switch (transaction.kind) {
      case 'income':
        return {
          icon: TrendingUp,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          label: 'Thu nhập'
        }
      case 'expense':
        return {
          icon: TrendingDown,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          label: 'Chi tiêu'
        }
      case 'transfer':
        return {
          icon: ArrowUpDown,
          color: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          label: 'Chuyển khoản'
        }
      default:
        return {
          icon: FileText,
          color: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          label: 'Khác'
        }
    }
  }

  // Handle view transaction
  const handleViewTransaction = () => {
    if (onView) {
      onView(transaction)
    } else {
      try {
        router.push(`/transactions/${transaction.id}`)
      } catch (error) {
        console.error('TransactionCard: Router navigation failed:', error)
        window.location.href = `/transactions/${transaction.id}`
      }
    }
  }

  // Define card actions
  const actions: CardAction<Transaction>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: handleViewTransaction,
      variant: "outline"
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    }
  ]

  const typeInfo = getTypeInfo()

  // Render transaction content
  const renderTransactionContent = () => (
    <div className="space-y-3">
      {/* Header with amount and type */}
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {/* Type icon */}
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${typeInfo.bgColor}`}>
            <typeInfo.icon size={20} className={typeInfo.color} />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight truncate">
              {transaction.description}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {typeInfo.label}
            </p>
          </div>
        </div>

        {/* Amount */}
        <div className="text-right">
          <div className={`font-bold text-lg ${typeInfo.color}`}>
            {transaction.kind === 'expense' ? '-' : '+'}
            {formatCurrency(transaction.amount)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {formatDate(transaction.date)}
          </div>
        </div>
      </div>

      {/* Transaction details */}
      <div className="space-y-2">
        {/* Account info */}
        {showAccountInfo && accountName && (
          <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <Building2 size={12} />
            <span className="truncate">{accountName}</span>
          </div>
        )}

        {/* Category info */}
        {showCategoryInfo && categoryName && (
          <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <div
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: categoryColor || '#6B7280' }}
            />
            <span className="truncate">{categoryName}</span>
          </div>
        )}

        {/* Transfer details */}
        {transaction.kind === 'transfer' && (
          <div className="space-y-1">
            {transaction.toAccountId && (
              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                <ArrowUpDown size={12} />
                <span className="truncate">
                  Đến: {transaction.note || 'Không rõ'}
                </span>
              </div>
            )}
            {false && (
              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                <ArrowUpDown size={12} />
                <span className="truncate">
                  Từ: {transaction.fromAccount.name || 'Không rõ'}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Tags */}
        {false && (
          <div className="flex items-center gap-1 flex-wrap">
            <Tag size={12} className="text-gray-400" />
            {[] /* TODO: Add tags support */.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-xs"
              >
                {tag}
              </span>
            ))}
            {[] /* TODO: Add tags support */.length > 3 && (
              <span className="text-xs text-gray-500 dark:text-gray-400">
                +{[] /* TODO: Add tags support */.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Location */}
        {transaction.location && (
          <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <MapPin size={12} />
            <span className="truncate">{transaction.location}</span>
          </div>
        )}

        {/* Recurring indicator */}
        {false && (
          <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
            <Clock size={12} />
            <span>Định kỳ</span>
          </div>
        )}

        {/* Verification status */}
        {false && (
          <div className="flex items-center gap-2 text-xs">
            <div className={`w-2 h-2 rounded-full ${
              false /* TODO: Add verification support */ 
                ? 'bg-green-500' 
                : 'bg-yellow-500'
            }`} />
            <span className={false /* TODO: Add verification support */ 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-yellow-600 dark:text-yellow-400'
            }>
              {false /* TODO: Add verification support */ ? 'Đã xác minh' : 'Chưa xác minh'}
            </span>
          </div>
        )}
      </div>

      {/* Notes */}
      {transaction.note && (
        <div className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 pt-2 border-t border-gray-100 dark:border-gray-700">
          {transaction.note}
        </div>
      )}
    </div>
  )

  return (
    <GenericCard
      item={transaction}
      renderContent={renderTransactionContent}
      actions={actions}
      onClick={handleViewTransaction}
      variant={variant}
      hoverable={true}
      className="h-full"
    />
  )
}

// Alternative implementation using EntityCard
export function SimpleTransactionCard({
  transaction,
  onEdit,
  onView,
  accountName,
  categoryName
}: TransactionCardProps) {
  const formatCurrency = formatVND

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric"
    }).format(new Date(date))
  }

  const typeInfo = {
    income: { label: 'Thu nhập', color: 'green', icon: '💰' },
    expense: { label: 'Chi tiêu', color: 'red', icon: '💸' },
    transfer: { label: 'Chuyển khoản', color: 'blue', icon: '🔄' }
  }[transaction.kind] || { label: 'Khác', color: 'gray', icon: '📄' }

  const actions: CardAction<Transaction>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: onView || (() => {}),
      variant: "outline",
      hidden: !onView
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    }
  ]

  const metadata = [
    {
      label: "Ngày",
      value: formatDate(transaction.date),
      icon: Calendar
    },
    {
      label: "Số tiền",
      value: formatCurrency(transaction.amount),
      icon: DollarSign
    },
    ...(accountName ? [{
      label: "Tài khoản",
      value: accountName,
      icon: Building2
    }] : []),
    ...(categoryName ? [{
      label: "Danh mục",
      value: categoryName,
      icon: Tag
    }] : [])
  ]

  return (
    <EntityCard
      item={transaction}
      title={transaction.description || 'Giao dịch'}
      subtitle={typeInfo.label}
      description={transaction.note || undefined}
      status={{
        label: false /* TODO: Add verification support */ ? "Đã xác minh" : "Chưa xác minh",
        color: false /* TODO: Add verification support */ ? "green" : "yellow"
      }}
      metadata={metadata}
      avatar={{
        fallback: typeInfo.icon,
        color: typeInfo.color === 'green' ? '#10b981' : 
               typeInfo.color === 'red' ? '#ef4444' : 
               typeInfo.color === 'blue' ? '#3b82f6' : '#6b7280'
      }}
      actions={actions}
      onClick={onView}
    />
  )
}

export default TransactionCard
