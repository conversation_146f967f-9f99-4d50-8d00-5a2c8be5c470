/**
 * GraphQL Scalar Resolvers
 * 
 * Custom scalar type resolvers for DateTime, UUID, and JSON
 */

import { GraphQLScalarType, Kind } from 'graphql'
import { GraphQLError } from 'graphql'

// DateTime scalar
const DateTimeScalar = new GraphQLScalarType({
  name: 'DateTime',
  description: 'Date custom scalar type',
  serialize(value: any) {
    if (value instanceof Date) {
      return value.toISOString()
    }
    if (typeof value === 'string') {
      return new Date(value).toISOString()
    }
    throw new GraphQLError(`Value is not a valid DateTime: ${value}`)
  },
  parseValue(value: any) {
    if (typeof value === 'string') {
      const date = new Date(value)
      if (isNaN(date.getTime())) {
        throw new GraphQLError(`Value is not a valid DateTime: ${value}`)
      }
      return date
    }
    throw new GraphQLError(`Value is not a valid DateTime: ${value}`)
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      const date = new Date(ast.value)
      if (isNaN(date.getTime())) {
        throw new GraphQLError(`Value is not a valid DateTime: ${ast.value}`)
      }
      return date
    }
    throw new GraphQLError(`Can only parse strings to DateTime but got a: ${ast.kind}`)
  }
})

// UUID scalar
const UUIDScalar = new GraphQLScalarType({
  name: 'UUID',
  description: 'UUID custom scalar type',
  serialize(value: any) {
    if (typeof value === 'string') {
      // Basic UUID validation
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      if (uuidRegex.test(value)) {
        return value
      }
    }
    throw new GraphQLError(`Value is not a valid UUID: ${value}`)
  },
  parseValue(value: any) {
    if (typeof value === 'string') {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      if (uuidRegex.test(value)) {
        return value
      }
    }
    throw new GraphQLError(`Value is not a valid UUID: ${value}`)
  },
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      if (uuidRegex.test(ast.value)) {
        return ast.value
      }
    }
    throw new GraphQLError(`Can only parse strings to UUID but got a: ${ast.kind}`)
  }
})

// JSON scalar
const JSONScalar = new GraphQLScalarType({
  name: 'JSON',
  description: 'JSON custom scalar type',
  serialize(value: any) {
    return value
  },
  parseValue(value: any) {
    return value
  },
  parseLiteral(ast) {
    switch (ast.kind) {
      case Kind.STRING:
      case Kind.BOOLEAN:
        return ast.value
      case Kind.INT:
      case Kind.FLOAT:
        return parseFloat(ast.value)
      case Kind.OBJECT: {
        const value = Object.create(null)
        ast.fields.forEach((field) => {
          value[field.name.value] = JSONScalar.parseLiteral(field.value)
        })
        return value
      }
      case Kind.LIST:
        return ast.values.map(JSONScalar.parseLiteral)
      default:
        return null
    }
  }
})

export const scalarResolvers = {
  DateTime: DateTimeScalar,
  UUID: UUIDScalar,
  JSON: JSONScalar
}
