// components/DataCard.tsx
import type React from "react"

type Props = {
  title: string
  value: string
  change?: string
  positive?: boolean
  negative?: boolean
  icon?: React.ReactNode
  gradient?: string
}

export function DataCard({ title, value, change, positive, negative, icon, gradient }: Props) {
  return (
    <div className="group relative overflow-hidden bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 hover:shadow-md transition-all duration-200">
      {/* Background gradient */}
      {gradient && <div className={`absolute inset-0 opacity-5 ${gradient}`}></div>}

      <div className="relative flex items-start justify-between">
        <div className="flex-1">
          <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">{title}</p>
          <p className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-1">{value}</p>

          {change && (
            <div className="flex items-center gap-1">
              <span
                className={`text-xs font-medium ${
                  positive
                    ? "text-emerald-600 dark:text-emerald-400"
                    : negative
                      ? "text-red-500 dark:text-red-400"
                      : "text-gray-500 dark:text-gray-400"
                }`}
              >
                {positive && "+"}
                {change}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">vs last</span>
            </div>
          )}
        </div>

        {icon && (
          <div className="flex-shrink-0 p-1.5 bg-gray-50 dark:bg-gray-700 rounded">
            <div className="text-gray-600 dark:text-gray-400 [&>svg]:w-4 [&>svg]:h-4">{icon}</div>
          </div>
        )}
      </div>
    </div>
  )
}
