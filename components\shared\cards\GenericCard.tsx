"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { GenericCardProps, CardAction } from '@/types/ui'

/**
 * GenericCard - Reusable card component for all entity types
 * Supports different variants, actions, and responsive design
 */
export function GenericCard<T>({
  item,
  renderContent,
  renderActions,
  actions = [],
  onClick,
  className = '',
  variant = 'default',
  hoverable = true,
  selectable = false,
  selected = false,
  children
}: GenericCardProps<T>) {
  const cardClasses = cn(
    // Base styles
    'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700',
    'transition-all duration-200 relative overflow-hidden',
    
    // Hover effects
    {
      'hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 hover:-translate-y-0.5': hoverable,
      'cursor-pointer': onClick,
      'ring-2 ring-blue-500 ring-offset-2': selected,
    },
    
    // Variant-based padding
    {
      'p-4': variant === 'default',
      'p-3': variant === 'compact',
      'p-6': variant === 'detailed'
    },
    
    className
  )

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger card click if clicking on action buttons
    if ((e.target as HTMLElement).closest('[data-card-action]')) {
      return
    }
    onClick?.(item)
  }

  const renderActionButtons = () => {
    if (renderActions) {
      return renderActions(item)
    }

    if (actions.length === 0) {
      return null
    }

    return (
      <div className="flex items-center gap-2 flex-wrap">
        {actions
          .filter(action => !action.hidden)
          .map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'outline'}
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                action.onClick(item)
              }}
              disabled={action.disabled}
              data-card-action
              className="h-8 px-3 text-xs"
            >
              {action.icon && <action.icon size={14} className="mr-1" />}
              {action.label}
            </Button>
          ))}
      </div>
    )
  }

  return (
    <div 
      className={cardClasses}
      onClick={handleCardClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={(e) => {
        if (onClick && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault()
          onClick(item)
        }
      }}
    >
      {/* Selection indicator */}
      {selected && (
        <div className="absolute top-2 right-2 w-3 h-3 bg-blue-500 rounded-full" />
      )}

      {/* Main content */}
      <div className="space-y-3">
        {renderContent(item)}
        
        {/* Actions section */}
        {(renderActions || actions.length > 0) && (
          <div className="pt-3 border-t border-gray-100 dark:border-gray-700">
            {renderActionButtons()}
          </div>
        )}
      </div>

      {children}
    </div>
  )
}

// Specialized card variants for common use cases

interface EntityCardProps<T> extends Omit<GenericCardProps<T>, 'renderContent'> {
  title: string
  subtitle?: string
  description?: string
  status?: {
    label: string
    color: 'green' | 'red' | 'yellow' | 'blue' | 'gray'
  }
  metadata?: Array<{
    label: string
    value: string | number
    icon?: React.ComponentType<{ size?: number; className?: string }>
  }>
  avatar?: {
    src?: string
    fallback: string
    color?: string
  }
}

export function EntityCard<T>({
  item,
  title,
  subtitle,
  description,
  status,
  metadata = [],
  avatar,
  ...props
}: EntityCardProps<T>) {
  const statusColors = {
    green: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    red: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    gray: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }

  const renderContent = () => (
    <>
      {/* Header with avatar and title */}
      <div className="flex items-start gap-3">
        {avatar && (
          <div 
            className={cn(
              'w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium text-white',
              avatar.color || 'bg-gray-500'
            )}
          >
            {avatar.src ? (
              <img 
                src={avatar.src} 
                alt={title}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              avatar.fallback
            )}
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {title}
              </h3>
              {subtitle && (
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {subtitle}
                </p>
              )}
            </div>
            
            {status && (
              <span className={cn(
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                statusColors[status.color]
              )}>
                {status.label}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Description */}
      {description && (
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
          {description}
        </p>
      )}

      {/* Metadata */}
      {metadata.length > 0 && (
        <div className="grid grid-cols-2 gap-3 text-sm">
          {metadata.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              {item.icon && <item.icon size={14} className="text-gray-400" />}
              <span className="text-gray-600 dark:text-gray-400">{item.label}:</span>
              <span className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {item.value}
              </span>
            </div>
          ))}
        </div>
      )}
    </>
  )

  return (
    <GenericCard
      item={item}
      renderContent={renderContent}
      {...props}
    />
  )
}

// Export both components
export default GenericCard
