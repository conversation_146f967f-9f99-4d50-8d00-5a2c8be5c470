"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { EntityCard } from "@/components/shared/cards/GenericCard"
import { AccountWithRelations } from "@/types/entities"
import { CardAction } from "@/types/ui"
import {
  Edit, Eye, EyeOff, ExternalLink, Building2, CreditCard,
  DollarSign, TrendingUp, TrendingDown, Calendar, FileText
} from "lucide-react"
import { formatVND } from "@/lib/utils/currency"
import { AccountType } from "@/lib/generated/prisma"

interface AccountCardProps {
  account: AccountWithRelations
  onEdit?: (account: AccountWithRelations) => void
  onToggleActive?: (account: AccountWithRelations) => void
  onView?: (account: AccountWithRelations) => void
  showStats?: boolean
  transactionCount?: number
  monthlyInflow?: number
  monthlyOutflow?: number
  variant?: 'default' | 'compact' | 'detailed'
}

const AccountCardComponent = React.memo(function AccountCard({
  account,
  onEdit,
  onToggleActive,
  onView,
  showStats = false,
  transactionCount = 0,
  monthlyInflow = 0,
  monthlyOutflow = 0,
  variant = 'default'
}: AccountCardProps) {
  const router = useRouter()

  // Format currency helper using shared utility
  const formatCurrency = formatVND

  // Get balance color based on account type and amount
  const getBalanceColor = () => {
    if (account.accountTypeId === 'credit' || account.accountTypeId === 'type-3') {
      // For credit cards, negative balance is good (less debt)
      return account.balance <= 0
        ? 'text-red-600 dark:text-red-400'
        : 'text-green-600 dark:text-green-400'
    } else {
      // For other accounts, positive balance is good
      return account.balance >= 0 
        ? 'text-green-600 dark:text-green-400' 
        : 'text-red-600 dark:text-red-400'
    }
  }



  // Handle view account
  const handleViewAccount = () => {
    if (onView) {
      onView(account)
    } else {
      try {
        router.push(`/accounts/${account.id}`)
      } catch (error) {
        console.error('AccountCard: Router navigation failed:', error)
        window.location.href = `/accounts/${account.id}`
      }
    }
  }

  // Define card actions
  const actions: CardAction<AccountWithRelations>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: handleViewAccount,
      variant: "outline"
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    },
    {
      label: account.isActive ? "Ẩn" : "Hiện",
      icon: account.isActive ? EyeOff : Eye,
      onClick: onToggleActive || (() => {}),
      variant: "outline",
      hidden: !onToggleActive
    }
  ]

  const typeInfo = account.accountType

  // Render account content
  const renderAccountContent = () => (
    <div className="space-y-2">
      {/* Header with name, type, and balance */}
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {/* Account type color indicator */}
          <div
            className="w-3 h-3 rounded-full flex-shrink-0"
            style={{ backgroundColor: typeInfo.color }}
          />
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 flex-1">
            {account.name}
          </h3>
        </div>

        {/* Status Badge */}
        <span className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
          account.isActive
            ? "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
            : "bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400"
        }`}>
          {account.isActive ? "Hoạt động" : "Đã ẩn"}
        </span>
      </div>

      {/* Account Type and Currency */}
      <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
        <Building2 size={12} className="text-blue-600" />
        <span>{typeInfo.name}</span>
        {account.currency && account.currency !== 'VND' && (
          <>
            <span>•</span>
            <span>{account.currency}</span>
          </>
        )}
      </div>

      {/* Balance */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
          <DollarSign size={12} />
          <span>Số dư hiện tại</span>
        </div>
        <div className={`font-semibold ${getBalanceColor()}`}>
          {formatCurrency(account.balance)}
        </div>
      </div>

      {/* Bank Info */}
      {account.bankInfo && (account.bankInfo.bankName || account.bankInfo.accountNumber) && (
        <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
          <CreditCard size={12} />
          <span>
            {account.bankInfo.bankName && account.bankInfo.accountNumber
              ? `${account.bankInfo.bankName} • ${account.bankInfo.accountNumber}`
              : account.bankInfo.bankName || account.bankInfo.accountNumber
            }
          </span>
        </div>
      )}

      {/* Description */}
      {account.description && (
        <div className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
          {account.description}
        </div>
      )}

      {/* Last Updated */}
      <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
        <Calendar size={12} />
        <span>Cập nhật: {new Date(account.updatedAt).toLocaleDateString('vi-VN')}</span>
      </div>

      {/* Monthly stats */}
      {showStats && (
        <div className="space-y-2 pt-2 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
              <FileText size={12} />
              <span className="font-semibold text-gray-900 dark:text-gray-100">
                {transactionCount}
              </span>
              <span>giao dịch</span>
            </div>
          </div>

          {(monthlyInflow > 0 || monthlyOutflow > 0) && (
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <TrendingUp size={12} className="text-green-500" />
                <div>
                  <div className="text-gray-600 dark:text-gray-400">Thu</div>
                  <div className="font-medium text-green-600 dark:text-green-400">
                    {formatCurrency(monthlyInflow)}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <TrendingDown size={12} className="text-red-500" />
                <div>
                  <div className="text-gray-600 dark:text-gray-400">Chi</div>
                  <div className="font-medium text-red-600 dark:text-red-400">
                    {formatCurrency(monthlyOutflow)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )

  return (
    <div className="group bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/50 dark:hover:border-gray-600/50 transition-all duration-200 h-full flex flex-col">

      {/* Account Info */}
      <div className="flex-1 space-y-2">
        {renderAccountContent()}
      </div>

      {/* Footer - Action Buttons */}
      <div className="flex items-center justify-between gap-2 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        {/* Status Indicator */}
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${
            account.isActive ? "bg-green-400" : "bg-gray-400"
          }`} />
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {account.isActive ? "Đang hoạt động" : "Đã ẩn"}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="flex items-center gap-1">
            <button
              onClick={handleViewAccount}
              className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
              title="Xem chi tiết"
            >
              <ExternalLink size={14} />
            </button>

            {onEdit && (
              <button
                onClick={() => onEdit(account)}
                className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                title="Chỉnh sửa"
              >
                <Edit size={14} />
              </button>
            )}

            {onToggleActive && (
              <button
                onClick={() => onToggleActive(account)}
                className={`p-1.5 rounded-lg transition-colors ${
                  account.isActive
                    ? "text-gray-400 hover:text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20"
                    : "text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20"
                }`}
                title={account.isActive ? "Ẩn tài khoản" : "Hiện tài khoản"}
              >
                {account.isActive ? <EyeOff size={14} /> : <Eye size={14} />}
              </button>
            )}


          </div>
        </div>
      </div>
    </div>
  )
})

export const AccountCard = AccountCardComponent

// Alternative implementation using EntityCard
export function SimpleAccountCard({
  account,
  onEdit,
  onToggleActive,
  onView,
  showStats = false,
  transactionCount = 0,
  monthlyInflow = 0,
  monthlyOutflow = 0
}: AccountCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: account.currency || "VND",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  // Map accountTypeId to display info
  const getTypeInfo = (typeId: string) => {
    switch (typeId) {
      case 'cash':
      case 'type-1':
        return { name: 'Tiền mặt', color: '#f59e0b', icon: '💵' }
      case 'bank':
      case 'type-2':
        return { name: 'Ngân hàng', color: '#3b82f6', icon: '🏦' }
      case 'credit':
      case 'type-3':
        return { name: 'Thẻ tín dụng', color: '#ef4444', icon: '💳' }
      case 'investment':
        return { name: 'Đầu tư', color: '#8b5cf6', icon: '📈' }
      case 'savings':
        return { name: 'Tiết kiệm', color: '#10b981', icon: '🏛️' }
      case 'digital':
        return { name: 'Ví điện tử', color: '#9333ea', icon: '📱' }
      default:
        return { name: typeId, color: '#6B7280', icon: '💳' }
    }
  }

  const typeInfo = getTypeInfo(account.accountTypeId)

  const actions: CardAction<AccountWithRelations>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: onView || (() => {}),
      variant: "outline",
      hidden: !onView
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    }
  ]

  const metadata = [
    {
      label: "Số dư",
      value: formatCurrency(account.balance),
      icon: DollarSign
    },
    {
      label: "Loại",
      value: typeInfo.name,
      icon: CreditCard
    },
    ...(account.bankInfo?.bankName ? [{
      label: "Ngân hàng",
      value: account.bankInfo.bankName,
      icon: Building2
    }] : []),
    ...(showStats ? [{
      label: "Giao dịch",
      value: transactionCount,
      icon: FileText
    }] : [])
  ]

  return (
    <EntityCard
      item={account}
      title={account.name}
      subtitle={typeInfo.name}
      description={account.description || undefined}
      status={{
        label: account.isActive ? "Hoạt động" : "Đã ẩn",
        color: account.isActive ? "green" : "gray"
      }}
      metadata={metadata}
      avatar={{
        fallback: typeInfo.icon || "💳",
        color: typeInfo.color
      }}
      actions={actions}
      onClick={onView}
      className={!account.isActive ? "opacity-60" : ""}
    />
  )
}

export default AccountCard
