"use client"

import React, { useMemo } from "react"
import { MultiSelectDropdown, MultiSelectOption } from "./MultiSelectDropdown"
import { Contact } from "@/lib/generated/prisma"
import { Account } from "@/lib/generated/prisma"
import { User, Building2 } from "lucide-react"

interface ContactAccountSelectorProps {
  contacts: Contact[]
  selectedContactIds: string[]
  selectedAccountIds: string[]
  onContactChange: (contactIds: string[]) => void
  onAccountChange: (accountIds: string[]) => void
  className?: string
  disabled?: boolean
  showUserContact?: boolean
  userContactLabel?: string
}

export function ContactAccountSelector({
  contacts,
  selectedContactIds,
  selectedAccountIds,
  onContactChange,
  onAccountChange,
  className = "",
  disabled = false,
  showUserContact = true,
  userContactLabel = "Tôi"
}: ContactAccountSelectorProps) {
  // Prepare contact options
  const contactOptions: MultiSelectOption[] = useMemo(() => {
    const options: MultiSelectOption[] = []
    
    // Add user contact if enabled
    if (showUserContact) {
      const userContact = contacts.find(c => c.isUserContact)
      if (userContact) {
        options.push({
          value: userContact.id.toString(),
          label: userContactLabel,
          disabled: false
        })
      }
    }
    
    // Add other contacts
    const otherContacts = contacts
      .filter(c => !c.isUserContact && c.isActive)
      .sort((a, b) => a.name.localeCompare(b.name))
    
    otherContacts.forEach(contact => {
      options.push({
        value: contact.id.toString(),
        label: contact.name,
        disabled: false
      })
    })
    
    return options
  }, [contacts, showUserContact, userContactLabel])

  // Prepare account options based on selected contacts
  const accountOptions: MultiSelectOption[] = useMemo(() => {
    if (selectedContactIds.length === 0) return []

    const options: MultiSelectOption[] = []

    selectedContactIds.forEach(contactId => {
      const contact = contacts.find(c => c.id.toString() === contactId)
      if (contact && contact.accounts && contact.accounts.length > 0) {
        contact.accounts.forEach(account => {
          const isUserContact = contact.isUserContact
          const contactLabel = isUserContact ? userContactLabel : contact.name
          const accountName = account.bankName || account.name
          const accountNumber = account.accountNumber || ''

          options.push({
            value: account.id.toString(),
            label: `${contactLabel} - ${accountName} ${accountNumber ? `(${accountNumber})` : ''}`,
            disabled: false
          })
        })
      }
    })

    return options.sort((a, b) => a.label.localeCompare(b.label))
  }, [contacts, selectedContactIds, userContactLabel])

  // Handle contact selection change
  const handleContactChange = (contactIds: string[]) => {
    onContactChange(contactIds)

    // Filter accounts to only include those from selected contacts
    const validAccountIds = selectedAccountIds.filter((accountId: string) => {
      return contactIds.some(contactId => {
        const contact = contacts.find(c => c.id.toString() === contactId)
        return contact?.accounts?.some(account => account.id.toString() === accountId)
      })
    })

    if (validAccountIds.length !== selectedAccountIds.length) {
      onAccountChange(validAccountIds)
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Contact Selection */}
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          <User size={14} className="inline mr-1" />
          Liên hệ
        </label>
        <MultiSelectDropdown
          options={contactOptions}
          selectedValues={selectedContactIds}
          onChange={handleContactChange}
          placeholder="Chọn liên hệ..."
          searchPlaceholder="Tìm liên hệ..."
          disabled={disabled}
          allowSelectAll={true}
          selectAllLabel="Chọn tất cả liên hệ"
        />
      </div>

      {/* Account Selection */}
      {selectedContactIds.length > 0 && (
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            <Building2 size={14} className="inline mr-1" />
            Tài khoản
          </label>
          <MultiSelectDropdown
            options={accountOptions}
            selectedValues={selectedAccountIds}
            onChange={onAccountChange}
            placeholder="Chọn tài khoản..."
            searchPlaceholder="Tìm tài khoản..."
            disabled={disabled || accountOptions.length === 0}
            allowSelectAll={true}
            selectAllLabel="Chọn tất cả tài khoản"
          />
          {accountOptions.length === 0 && selectedContactIds.length > 0 && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Các liên hệ đã chọn không có tài khoản
            </p>
          )}
        </div>
      )}
    </div>
  )
}

// Helper function to get contact and account info from IDs
export function getContactAccountInfo(
  contacts: Contact[],
  contactIds: string[],
  accountIds: string[]
) {
  const selectedContacts = contacts.filter(c =>
    contactIds.includes(c.id.toString())
  )

  const selectedAccounts: Array<{
    contact: Contact
    account: Account
  }> = []

  accountIds.forEach(accountId => {
    for (const contact of selectedContacts) {
      if (contact.accounts) {
        const account = contact.accounts.find(acc => acc.id.toString() === accountId)
        if (account) {
          selectedAccounts.push({ contact, account })
          break
        }
      }
    }
  })

  return {
    contacts: selectedContacts,
    accounts: selectedAccounts
  }
}

// Helper function to format display text for selected items
export function formatContactAccountDisplay(
  contacts: Contact[],
  contactIds: string[],
  accountIds: string[],
  userContactLabel: string = "Tôi"
): string {
  if (contactIds.length === 0) return ""

  if (accountIds.length === 0) {
    if (contactIds.length === 1) {
      const contact = contacts.find(c => c.id.toString() === contactIds[0])
      if (contact) {
        return contact.isUserContact ? userContactLabel : contact.name
      }
    }
    return `${contactIds.length} liên hệ`
  } else {
    if (accountIds.length === 1) {
      for (const contact of contacts) {
        if (contact.accounts) {
          const account = contact.accounts.find(acc => acc.id.toString() === accountIds[0])
          if (account) {
            const contactName = contact.isUserContact ? userContactLabel : contact.name
            const accountName = account.bankName || account.name
            const accountNumber = account.accountNumber ? ` (${account.accountNumber})` : ''
            return `${contactName} - ${accountName}${accountNumber}`
          }
        }
      }
    }
    return `${accountIds.length} tài khoản`
  }
}
