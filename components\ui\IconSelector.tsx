"use client"

import React, { useState } from "react"
import { Search, X } from "lucide-react"

// Predefined icons organized by category
const ICON_CATEGORIES = {
  "Ngân hàng & Tài chính": [
    "🏦", "💳", "💰", "💵", "💴", "💶", "💷", "🏧", "💸", "💲",
    "🪙", "💎", "📊", "📈", "📉", "🧾", "💼", "🏛️", "🔒", "🗝️"
  ],
  "Đầu tư & Tiết kiệm": [
    "📈", "📊", "💹", "🎯", "💎", "🏆", "⭐", "🌟", "💫", "✨",
    "🔥", "⚡", "🚀", "🌙", "☀️", "🌈", "🍀", "🎲", "🎰", "🎪"
  ],
  "Công nghệ & Digital": [
    "📱", "💻", "⌚", "🖥️", "📟", "📺", "📷", "📹", "🎮", "🕹️",
    "💿", "💾", "💽", "🔌", "🔋", "📡", "🛰️", "📶", "📳", "📴"
  ],
  "Giao thông & Di chuyển": [
    "🚗", "🚕", "🚙", "🚌", "🚎", "🏎️", "🚓", "🚑", "🚒", "🚐",
    "🛻", "🚚", "🚛", "🚜", "🏍️", "🛵", "🚲", "🛴", "🚁", "✈️"
  ],
  "Ăn uống & Giải trí": [
    "🍕", "🍔", "🍟", "🌭", "🥪", "🌮", "🌯", "🥙", "🍳", "🥘",
    "🍲", "🥗", "🍿", "🧂", "🥤", "☕", "🍺", "🍷", "🎬", "🎭"
  ],
  "Mua sắm & Dịch vụ": [
    "🛒", "🛍️", "🏪", "🏬", "🏢", "🏭", "🏗️", "🏘️", "🏠", "🏡",
    "🏰", "🏯", "🏟️", "🎡", "🎢", "🎠", "⛲", "🌁", "🌃", "🌆"
  ],
  "Sức khỏe & Y tế": [
    "🏥", "⚕️", "💊", "💉", "🩺", "🩹", "🦷", "🧬", "🔬", "🧪",
    "🧫", "🩸", "🫀", "🫁", "🧠", "👁️", "👂", "👃", "👄", "🦴"
  ],
  "Khác": [
    "📝", "📚", "📖", "📰", "🗞️", "📄", "📃", "📑", "📊", "📈",
    "📉", "🗂️", "📂", "📁", "🗃️", "🗄️", "📋", "📌", "📍", "📎"
  ]
}

interface IconSelectorProps {
  selectedIcon?: string
  onSelect: (icon: string) => void
  className?: string
}

export function IconSelector({ selectedIcon, onSelect, className = "" }: IconSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>(Object.keys(ICON_CATEGORIES)[0])

  // Filter icons based on search term
  const getFilteredIcons = () => {
    if (!searchTerm) {
      return ICON_CATEGORIES[selectedCategory as keyof typeof ICON_CATEGORIES] || []
    }

    // Search across all categories
    const allIcons = Object.values(ICON_CATEGORIES).flat()
    return allIcons.filter(icon => {
      // Simple search - could be enhanced with icon descriptions
      return true // For now, return all icons when searching
    })
  }

  const filteredIcons = getFilteredIcons()

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          type="text"
          placeholder="Tìm kiếm icon..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-10 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
        />
        {searchTerm && (
          <button
            onClick={() => setSearchTerm("")}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Category Tabs */}
      {!searchTerm && (
        <div className="flex flex-wrap gap-1">
          {Object.keys(ICON_CATEGORIES).map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1.5 text-xs rounded-lg transition-all ${
                selectedCategory === category
                  ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 border border-transparent"
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      )}

      {/* Icon Grid */}
      <div className="max-h-64 overflow-y-auto">
        <div className="grid grid-cols-8 gap-2">
          {filteredIcons.map((icon, index) => (
            <button
              key={`${icon}-${index}`}
              onClick={() => onSelect(icon)}
              className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-xl transition-all hover:bg-gray-50 dark:hover:bg-gray-800 ${
                selectedIcon === icon
                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-500/20"
                  : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
              }`}
              title={icon}
            >
              {icon}
            </button>
          ))}
        </div>
      </div>

      {/* Selected Icon Display */}
      {selectedIcon && (
        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <span className="text-2xl">{selectedIcon}</span>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Icon đã chọn
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {selectedIcon}
            </p>
          </div>
          <button
            onClick={() => onSelect("")}
            className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
            title="Xóa icon"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  )
}
