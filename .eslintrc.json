{
  "extends": [
    "next/core-web-vitals",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "plugin:prettier/recommended"
  ],
  "plugins": [
    "@typescript-eslint",
    "react",
    "react-hooks",
    "jsx-a11y",
    "import",
    "unused-imports"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    },
    "project": "./tsconfig.json"
  },
  "settings": {
    "react": {
      "version": "detect"
    },
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true,
        "project": "./tsconfig.json"
      },
      "alias": {
        "map": [
          ["@", "."],
          ["@/components", "./components"],
          ["@/types", "./types"],
          ["@/services", "./services"],
          ["@/hooks", "./hooks"],
          ["@/utils", "./utils"],
          ["@/lib", "./lib"],
          ["@/ui", "./components/ui"],
          ["@/shared", "./components/shared"],
          ["@/features", "./components/features"],
          ["@/layout", "./components/layout"],
          ["@/api", "./services/api"],
          ["@/mocks", "./services/mocks"]
        ],
        "extensions": [".js", ".jsx", ".ts", ".tsx"]
      },
      "node": {
        "extensions": [".js", ".jsx", ".ts", ".tsx"]
      }
    }
  },
  "rules": {
    // TypeScript specific rules
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-empty-function": "warn",
    "@typescript-eslint/no-non-null-assertion": "warn",
    "@typescript-eslint/prefer-const": "error",
    "@typescript-eslint/no-var-requires": "error",
    "@typescript-eslint/ban-ts-comment": "warn",
    "@typescript-eslint/consistent-type-imports": [
      "error",
      {
        "prefer": "type-imports",
        "disallowTypeAnnotations": false
      }
    ],

    // React specific rules
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react/display-name": "warn",
    "react/no-unescaped-entities": "warn",
    "react/jsx-key": "error",
    "react/jsx-no-duplicate-props": "error",
    "react/jsx-no-undef": "error",
    "react/jsx-uses-react": "off",
    "react/jsx-uses-vars": "error",
    "react/no-deprecated": "warn",
    "react/no-direct-mutation-state": "error",
    "react/no-unused-state": "warn",
    "react/prefer-stateless-function": "warn",
    "react/self-closing-comp": "error",

    // React Hooks rules
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",

    // Import/Export rules
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "never",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    "import/no-unresolved": "error",
    "import/no-duplicates": "error",
    "import/no-unused-modules": "warn",
    "import/no-cycle": "error",
    "import/no-self-import": "error",

    // Unused imports
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "warn",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "after-used",
        "argsIgnorePattern": "^_"
      }
    ],

    // Accessibility rules
    "jsx-a11y/alt-text": "error",
    "jsx-a11y/anchor-has-content": "error",
    "jsx-a11y/anchor-is-valid": "error",
    "jsx-a11y/aria-props": "error",
    "jsx-a11y/aria-proptypes": "error",
    "jsx-a11y/aria-unsupported-elements": "error",
    "jsx-a11y/click-events-have-key-events": "warn",
    "jsx-a11y/heading-has-content": "error",
    "jsx-a11y/img-redundant-alt": "error",
    "jsx-a11y/no-access-key": "error",

    // General JavaScript rules
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-debugger": "error",
    "no-alert": "warn",
    "no-unused-vars": "off", // Handled by @typescript-eslint/no-unused-vars
    "no-var": "error",
    "prefer-const": "error",
    "no-duplicate-imports": "error",
    "no-multiple-empty-lines": ["error", { "max": 2, "maxEOF": 1 }],
    "no-trailing-spaces": "error",
    "eol-last": "error",
    "comma-dangle": ["error", "never"],
    "semi": ["error", "never"],
    "quotes": ["error", "single", { "avoidEscape": true }],

    // Performance rules
    "no-await-in-loop": "warn",
    "no-inner-declarations": "error",
    "no-unreachable": "error",
    "no-unreachable-loop": "error",

    // Code quality rules
    "complexity": ["warn", 15],
    "max-depth": ["warn", 4],
    "max-lines": ["warn", 500],
    "max-lines-per-function": ["warn", 100],
    "max-params": ["warn", 5],

    // Next.js specific rules
    "@next/next/no-img-element": "error",
    "@next/next/no-html-link-for-pages": "error",
    "@next/next/no-sync-scripts": "error",
    "@next/next/no-title-in-document-head": "error"
  },
  "overrides": [
    {
      "files": ["*.js", "*.jsx"],
      "rules": {
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/explicit-function-return-type": "off"
      }
    },
    {
      "files": ["*.test.ts", "*.test.tsx", "*.spec.ts", "*.spec.tsx"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "no-console": "off"
      }
    },
    {
      "files": ["next.config.*", "tailwind.config.*", "postcss.config.*"],
      "rules": {
        "@typescript-eslint/no-var-requires": "off",
        "import/no-anonymous-default-export": "off"
      }
    }
  ],
  "ignorePatterns": [
    "node_modules/",
    ".next/",
    "out/",
    "dist/",
    "build/",
    "*.config.js",
    "*.config.mjs",
    "public/mockServiceWorker.js"
  ]
}
