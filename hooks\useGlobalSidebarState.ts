"use client"

import { useState, useEffect, useCallback } from "react"

interface SidebarState {
  isOpen: boolean
  width: number
  type: 'form' | 'menu' | 'ai'
}

interface GlobalSidebarState {
  // Menu sidebar
  menuSidebar: {
    isOpen: boolean
    isMinimal: boolean
  }
  
  // Form sidebars (right side)
  formSidebar: {
    isOpen: boolean
    width: number
    type?: string
    isResizing: boolean
  }
  
  // AI Assistant sidebar
  aiSidebar: {
    isOpen: boolean
    width: number
  }
}

export interface UseGlobalSidebarStateReturn {
  // State
  globalState: GlobalSidebarState
  
  // Menu sidebar actions
  setMenuSidebarOpen: (isOpen: boolean) => void
  setMenuSidebarMinimal: (isMinimal: boolean) => void
  
  // Form sidebar actions
  setFormSidebarOpen: (isOpen: boolean, width?: number, type?: string) => void
  setFormSidebarWidth: (width: number) => void
  setFormSidebarResizing: (isResizing: boolean) => void
  
  // AI sidebar actions
  setAISidebarOpen: (isOpen: boolean, width?: number) => void
  
  // Content margins
  getContentMargins: () => {
    marginLeft: string
    marginRight: string
  }
  
  // Responsive helpers
  isMobile: boolean
}

// Custom hook for mobile detection
function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false)
  
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }
    
    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])
  
  return isMobile
}

export function useGlobalSidebarState(): UseGlobalSidebarStateReturn {
  const isMobile = useIsMobile()
  
  const [globalState, setGlobalState] = useState<GlobalSidebarState>({
    menuSidebar: {
      isOpen: false,
      isMinimal: false
    },
    formSidebar: {
      isOpen: false,
      width: 400,
      type: undefined,
      isResizing: false
    },
    aiSidebar: {
      isOpen: false,
      width: 400
    }
  })

  // Menu sidebar actions
  const setMenuSidebarOpen = useCallback((isOpen: boolean) => {
    setGlobalState(prev => ({
      ...prev,
      menuSidebar: {
        ...prev.menuSidebar,
        isOpen
      }
    }))
  }, [])

  const setMenuSidebarMinimal = useCallback((isMinimal: boolean) => {
    setGlobalState(prev => ({
      ...prev,
      menuSidebar: {
        ...prev.menuSidebar,
        isMinimal
      }
    }))
  }, [])

  // Form sidebar actions
  const setFormSidebarOpen = useCallback((isOpen: boolean, width = 400, type?: string) => {
    setGlobalState(prev => ({
      ...prev,
      formSidebar: {
        isOpen,
        width: isOpen ? width : prev.formSidebar.width,
        type: isOpen ? type : undefined,
        isResizing: prev.formSidebar.isResizing
      }
    }))
  }, [])

  const setFormSidebarWidth = useCallback((width: number) => {
    // Use requestAnimationFrame for smooth updates
    requestAnimationFrame(() => {
      setGlobalState(prev => ({
        ...prev,
        formSidebar: {
          ...prev.formSidebar,
          width
        }
      }))
    })
  }, [])

  const setFormSidebarResizing = useCallback((isResizing: boolean) => {
    setGlobalState(prev => ({
      ...prev,
      formSidebar: {
        ...prev.formSidebar,
        isResizing
      }
    }))
  }, [])

  // AI sidebar actions
  const setAISidebarOpen = useCallback((isOpen: boolean, width = 400) => {
    setGlobalState(prev => ({
      ...prev,
      aiSidebar: {
        isOpen,
        width: isOpen ? width : prev.aiSidebar.width
      }
    }))
  }, [])

  // Calculate content margins
  const getContentMargins = useCallback(() => {
    if (isMobile) {
      return {
        marginLeft: '0',
        marginRight: '0'
      }
    }

    // Left margin for menu sidebar
    let marginLeft = '0'
    if (globalState.menuSidebar.isOpen || !isMobile) {
      marginLeft = globalState.menuSidebar.isMinimal ? '56px' : '224px'
    }

    // Right margin for form sidebar or AI sidebar
    let marginRight = '0'
    if (globalState.formSidebar.isOpen) {
      marginRight = `${globalState.formSidebar.width}px`
    } else if (globalState.aiSidebar.isOpen) {
      marginRight = `${globalState.aiSidebar.width}px`
    }

    return {
      marginLeft,
      marginRight
    }
  }, [globalState, isMobile])

  return {
    globalState,
    setMenuSidebarOpen,
    setMenuSidebarMinimal,
    setFormSidebarOpen,
    setFormSidebarWidth,
    setFormSidebarResizing,
    setAISidebarOpen,
    getContentMargins,
    isMobile
  }
}

// Global state instance for sharing across components
let globalSidebarStateInstance: ReturnType<typeof useGlobalSidebarState> | null = null

export function getGlobalSidebarState() {
  return globalSidebarStateInstance
}

export function setGlobalSidebarStateInstance(instance: ReturnType<typeof useGlobalSidebarState>) {
  globalSidebarStateInstance = instance
}
