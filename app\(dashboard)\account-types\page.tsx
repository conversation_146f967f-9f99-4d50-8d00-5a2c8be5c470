"use client"

import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AccountTypeCard } from "@/components/ui/AccountTypeCard"
import { ListPageLayout } from "@/components/layout/ResponsiveLayout"
import { GenericList } from "@/components/ui/GenericList"
import { useAuth } from "@/contexts/AuthContext"
import { useEffect, useRef, useState } from "react"

import { useGlobalTaskbarRestore } from "@/components/layout/GlobalLayout"
import { AccountTypeSidebar } from "@/components/ui/AccountTypeSidebar"
import { Button } from "@/components/ui/button"
import { PortalDropdown } from "@/components/ui/PortalDropdown"
import { useSidebarManager } from "@/hooks/useSidebarManager"
import { AccountType } from "@/lib/generated/prisma"
import { AccountTypeApiService } from "@/services/client/accountTypeApi"
import { extractNodes } from "@/lib/utils/connection-helpers"
import { Building2, Plus, Search, SortAsc, SortDesc, ChevronDown, ChevronUp, CreditCard, Smartphone } from "lucide-react"
import { toast } from "sonner"

function AccountTypesPageContent() {
  const { user } = useAuth()
  const {
    openSidebar,
    closeSidebar,
    isSidebarOpen
  } = useSidebarManager()

  const [selectedAccountType, setSelectedAccountType] = useState<AccountType | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [showSortOptions, setShowSortOptions] = useState(false)
  const sortButtonRef = useRef<HTMLButtonElement>(null)

  // Global taskbar restore
  const { restoredItem, restoreType, clearRestored } = useGlobalTaskbarRestore()

  // Fetch account types
  useEffect(() => {
    const fetchAccountTypes = async () => {
      if (!user?.id) {
        setError('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const response = await AccountTypeApiService.getAccountTypes()
        if (response.success && response.data) {
          setAccountTypes(extractNodes(response.data))
        } else {
          setError(response.error || 'Không thể tải danh sách loại tài khoản')
        }
      } catch (error: any) {
        console.error('Error fetching account types:', error)
        setError(error?.message || 'Không thể tải danh sách loại tài khoản')
      } finally {
        setLoading(false)
      }
    }

    fetchAccountTypes()
  }, [user?.id])

  // Statistics
  const totalTypes = accountTypes.length
  const activeTypes = accountTypes.filter(type => type.isActive).length
  const inactiveTypes = totalTypes - activeTypes
  const defaultTypes = accountTypes.filter(type => type.isDefault).length
  const bankAccountTypes = accountTypes.filter(type => type.isBankAccount).length
  const paymentAppTypes = accountTypes.filter(type => type.isPaymentApp).length

  // Filter tabs
  const [activeFilter, setActiveFilter] = useState('all')
  const filteredAccountTypes = accountTypes.filter(type => {
    switch (activeFilter) {
      case 'active':
        return type.isActive
      case 'inactive':
        return !type.isActive
      case 'default':
        return type.isDefault
      case 'custom':
        return !type.isDefault
      case 'bank':
        return type.isBankAccount
      case 'payment':
        return type.isPaymentApp
      default:
        return true
    }
  })

  const filterTabs = [
    { value: 'all', label: 'Tất cả', count: totalTypes },
    { value: 'active', label: 'Hoạt động', count: activeTypes },
    { value: 'inactive', label: 'Không hoạt động', count: inactiveTypes },
    { value: 'default', label: 'Mặc định', count: defaultTypes },
    { value: 'custom', label: 'Tùy chỉnh', count: totalTypes - defaultTypes },
    { value: 'bank', label: 'Ngân hàng', count: bankAccountTypes },
    { value: 'payment', label: 'Ví điện tử', count: paymentAppTypes }
  ]

  // Sort options
  const sortOptions = [
    { value: 'name', label: 'Tên loại tài khoản' },
    { value: 'createdAt', label: 'Ngày tạo' },
    { value: 'isDefault', label: 'Loại mặc định' }
  ]

  // Account type handlers
  const handleAddAccountType = () => {
    if (!user?.id) {
      setError('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.')
      return
    }

    setSelectedAccountType(null)
    setIsEditing(false)
    openSidebar('accountType')
  }

  const handleEditAccountType = (accountType: AccountType) => {
    setSelectedAccountType(accountType)
    setIsEditing(true)
    openSidebar('accountType')
  }

  const handleCloseAccountTypeSidebar = () => {
    closeSidebar('accountType')
    setSelectedAccountType(null)
    setIsEditing(false)
  }

  const handleSaveAccountType = async (data: any) => {
    try {
      setLoading(true)
      let response

      if (isEditing && selectedAccountType) {
        response = await AccountTypeApiService.updateAccountType(selectedAccountType.id, data)
      } else {
        response = await AccountTypeApiService.createAccountType(data)
      }

      if (response.success) {
        // Refresh account types list
        const accountTypesResponse = await AccountTypeApiService.getAccountTypes()
        if (accountTypesResponse.success && accountTypesResponse.data) {
          setAccountTypes(extractNodes(accountTypesResponse.data))
        }

        handleCloseAccountTypeSidebar()
        toast.success(`Loại tài khoản đã được ${isEditing ? 'cập nhật' : 'tạo'} thành công`)
      } else {
        setError(typeof response.error === 'string' ? response.error : 'Không thể lưu loại tài khoản. Vui lòng thử lại.')
      }
    } catch (error: any) {
      console.error('Error saving account type:', error)
      setError(error?.message || 'Không thể lưu loại tài khoản. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }

  // Handle global taskbar restore
  useEffect(() => {
    if (restoredItem && restoreType === 'accountType') {
      if (restoredItem.data) {
        setSelectedAccountType(restoredItem.data)
        setIsEditing(restoredItem.isEditing || false)
      }
      openSidebar('accountType')
      clearRestored()
    }
  }, [restoredItem, restoreType, clearRestored, openSidebar])

  return (
    <>
      <ListPageLayout
        title="Loại tài khoản"
        subtitle="Quản lý các loại tài khoản trong hệ thống"
        stats={[
          {
            label: "Tổng loại",
            value: totalTypes.toString(),
            icon: <Building2 size={14} className="text-blue-500" />
          },
          {
            label: "Đang hoạt động",
            value: activeTypes.toString(),
            icon: <CreditCard size={14} className="text-green-500" />
          },
          {
            label: "Ví điện tử",
            value: paymentAppTypes.toString(),
            icon: <Smartphone size={14} className="text-purple-500" />
          }
        ]}
        actions={
          <Button onClick={handleAddAccountType} className="flex items-center gap-2">
            <Plus size={16} />
            Thêm mới
          </Button>
        }
      >
        {/* Search, Filter and Sort Row */}
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3 mb-4">
          <div className="flex flex-wrap items-center gap-2">
            {/* Type Filter Tabs */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-0.5 h-8 overflow-x-auto">
              {filterTabs.map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => setActiveFilter(filter.value)}
                  className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 h-full flex items-center whitespace-nowrap ${
                    activeFilter === filter.value
                      ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>

            {/* Sort Dropdown */}
            <div className="relative ml-auto">
              <PortalDropdown
                trigger={
                  <Button
                    ref={sortButtonRef}
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSortOptions(!showSortOptions)}
                    className="h-8 px-3 text-xs"
                  >
                    <SortAsc size={12} className="mr-1" />
                    Sắp xếp
                    {showSortOptions ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
                  </Button>
                }
                isOpen={showSortOptions}
                onClose={() => setShowSortOptions(false)}
                align="right"
              >
                <div className="py-1">
                  {sortOptions.map((option) => (
                    <button
                      key={option.value}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                      onClick={() => {
                        // Handle sort logic here
                        setShowSortOptions(false)
                      }}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </PortalDropdown>
            </div>
          </div>
        </div>

        {/* Account Types List */}
        <GenericList
          data={filteredAccountTypes}
          renderCard={(accountType: AccountType) => (
            <AccountTypeCard
              key={accountType.id}
              accountType={accountType}
              onEdit={handleEditAccountType}
              onDelete={async (type) => {
                if (type.isDefault) {
                  toast.error('Không thể xóa loại tài khoản mặc định')
                  return
                }
                if (!confirm(`Bạn có chắc chắn muốn xóa loại tài khoản "${type.name}"?`)) {
                  return
                }
                try {
                  const deleteResponse = await AccountTypeApiService.deleteAccountType(type.id)
                  if (deleteResponse.success) {
                    // Refresh list
                    const response = await AccountTypeApiService.getAccountTypes()
                    if (response.success && response.data) {
                      setAccountTypes(extractNodes(response.data))
                    }
                    toast.success('Đã xóa loại tài khoản')
                  } else {
                    toast.error(deleteResponse.error || 'Không thể xóa loại tài khoản')
                  }
                } catch (error) {
                  toast.error('Không thể xóa loại tài khoản')
                }
              }}
              onToggleActive={async (type) => {
                try {
                  const updateResponse = await AccountTypeApiService.updateAccountType(type.id, {
                    isActive: !type.isActive
                  })
                  if (updateResponse.success) {
                    // Refresh list
                    const response = await AccountTypeApiService.getAccountTypes()
                    if (response.success && response.data) {
                      setAccountTypes(extractNodes(response.data))
                    }
                    toast.success(type.isActive ? 'Đã ẩn loại tài khoản' : 'Đã hiển thị loại tài khoản')
                  } else {
                    toast.error(updateResponse.error || 'Không thể cập nhật trạng thái')
                  }
                } catch (error) {
                  toast.error('Không thể cập nhật trạng thái')
                }
              }}
            />
          )}
          gridCols={{ default: 1, sm: 2, lg: 3, xl: 4 }}
          emptyMessage={
            filteredAccountTypes.length === 0 && accountTypes.length > 0
              ? `Không tìm thấy loại tài khoản nào phù hợp với bộ lọc hiện tại`
              : "Chưa có loại tài khoản nào. Hãy tạo loại tài khoản đầu tiên!"
          }
          loading={loading}
          searchable={false}
          sortable={false}
          paginated={true}
          defaultItemsPerPage={25}
          itemsPerPageOptions={[10, 25, 50, 100]}
          sidebarOpen={isSidebarOpen('accountType')}
          sidebarWidth={400}
        />
      </ListPageLayout>

      {/* Account Type Sidebar */}
      <AccountTypeSidebar
        isOpen={isSidebarOpen('accountType')}
        onClose={handleCloseAccountTypeSidebar}
        onSave={handleSaveAccountType}
        accountType={selectedAccountType}
        isEditing={isEditing}
      />

      {/* Toast Notifications */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 animate-fade-in">
          <div className="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="flex-1">{error}</span>
            <button
              onClick={() => setError(null)}
              className="text-white hover:text-gray-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  )
}

export default function AccountTypesPage() {
  return (
    <ProtectedRoute>
      <AccountTypesPageContent />
    </ProtectedRoute>
  )
}
