# GraphQL API

GraphQL API endpoint cho Money Management application, thay thế cho REST API truyền thống.

## 🚀 Endpoint

```
POST /api/graphql
GET /api/graphql (GraphQL Playground - chỉ trong development)
```

## 📋 Tính năng

- **Type-safe Schema**: Đ<PERSON><PERSON> nghĩa schema GraphQL đầy đủ cho tất cả entities
- **Authentication**: Tích hợp authentication hiện có qua JWT tokens
- **Authorization**: Kiểm soát quyền truy cập dựa trên user roles và ownership
- **Error Handling**: <PERSON><PERSON> lý lỗi chuẩn GraphQL với error codes
- **Pagination**: Hỗ trợ cursor-based pagination theo Relay specification
- **Relations**: Tự động resolve các mối quan hệ giữa entities

## 🔧 Cài đặt Dependencies

Trước khi sử dụng, cần cài đặt các dependencies cần thiết:

```bash
npm install graphql @apollo/server @as-integrations/next graphql-scalars graphql-tag
```

## 📖 Schema Overview

### Entities
- **User**: Quản lý người dùng và preferences
- **Account**: Quản lý tài khoản tài chính
- **Category**: Quản lý danh mục thu chi
- **Transaction**: Quản lý giao dịch tài chính
- **Contact**: Quản lý danh bạ liên hệ
- **AccountType**: Loại tài khoản (tiền mặt, ngân hàng, etc.)
- **ContactType**: Loại liên hệ

### Scalars
- `DateTime`: ISO 8601 datetime string
- `UUID`: UUID string format
- `JSON`: Arbitrary JSON data

## 🔐 Authentication

GraphQL API sử dụng JWT authentication giống như REST API:

```javascript
// Headers
{
  "Authorization": "Bearer <your-jwt-token>"
}
```

## 📝 Example Queries

### Get Current User
```graphql
query Me {
  me {
    id
    name
    email
    role
    preferences {
      currency
      language
      theme
    }
    accounts {
      id
      name
      balance
      currency
      accountType {
        name
        category
      }
    }
  }
}
```

### Get Transactions with Pagination
```graphql
query GetTransactions($first: Int, $after: String) {
  transactions(first: $first, after: $after) {
    edges {
      node {
        id
        kind
        amount
        date
        description
        account {
          name
          accountType {
            name
          }
        }
        category {
          name
          type
        }
      }
      cursor
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
    totalCount
  }
}
```

### Search Accounts
```graphql
query SearchAccounts($search: String, $first: Int) {
  accounts(search: $search, first: $first) {
    edges {
      node {
        id
        name
        balance
        currency
        accountType {
          name
          category
          color
        }
      }
    }
    totalCount
  }
}
```

## ✏️ Example Mutations

### Create Transaction
```graphql
mutation CreateTransaction($input: CreateTransactionInput!) {
  createTransaction(input: $input) {
    id
    kind
    amount
    date
    description
    account {
      id
      name
      balance
    }
    category {
      id
      name
    }
  }
}

# Variables
{
  "input": {
    "kind": "expense",
    "amount": 150000,
    "date": "2024-01-15T10:30:00Z",
    "description": "Lunch at restaurant",
    "accountId": "account-uuid",
    "categoryId": "category-uuid"
  }
}
```

### Update User Preferences
```graphql
mutation UpdatePreferences($input: UpdateUserPreferencesInput!) {
  updateUserPreferences(input: $input) {
    id
    currency
    language
    theme
    dateFormat
  }
}

# Variables
{
  "input": {
    "currency": "USD",
    "theme": "dark",
    "language": "en"
  }
}
```

### Create Account
```graphql
mutation CreateAccount($input: CreateAccountInput!) {
  createAccount(input: $input) {
    id
    name
    balance
    currency
    accountType {
      name
      category
    }
  }
}

# Variables
{
  "input": {
    "name": "Savings Account",
    "accountTypeId": "account-type-uuid",
    "balance": 1000000,
    "currency": "VND"
  }
}
```

## 🚨 Error Handling

GraphQL API trả về lỗi theo chuẩn GraphQL với các error codes:

```json
{
  "errors": [
    {
      "message": "Authentication required",
      "extensions": {
        "code": "UNAUTHENTICATED"
      }
    }
  ]
}
```

### Error Codes
- `VALIDATION_ERROR`: Lỗi validation dữ liệu đầu vào
- `NOT_FOUND`: Resource không tồn tại
- `UNAUTHENTICATED`: Chưa đăng nhập
- `FORBIDDEN`: Không có quyền truy cập
- `CONFLICT`: Xung đột dữ liệu (duplicate, etc.)
- `INTERNAL_ERROR`: Lỗi server

## 🔄 Migration từ REST API

### Before (REST)
```javascript
// Get user accounts
const accounts = await fetch('/api/accounts', {
  headers: { 'Authorization': `Bearer ${token}` }
})

// Get account details
const account = await fetch(`/api/accounts/${accountId}`, {
  headers: { 'Authorization': `Bearer ${token}` }
})
```

### After (GraphQL)
```javascript
// Get user accounts with details in one request
const query = `
  query GetAccountsWithDetails {
    accounts {
      edges {
        node {
          id
          name
          balance
          accountType {
            name
            category
          }
          transactions(first: 5) {
            edges {
              node {
                id
                amount
                description
                date
              }
            }
          }
        }
      }
    }
  }
`

const response = await fetch('/api/graphql', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({ query })
})
```

## 🛠️ Development

### GraphQL Playground
Trong development mode, có thể truy cập GraphQL Playground tại:
```
http://localhost:3000/api/graphql
```

### Schema Introspection
Schema introspection được bật trong development để hỗ trợ tools như GraphQL Playground, Apollo Studio, etc.

## 📚 Tài liệu tham khảo

- [GraphQL Specification](https://spec.graphql.org/)
- [Apollo Server Documentation](https://www.apollographql.com/docs/apollo-server/)
- [Relay Cursor Connections Specification](https://relay.dev/graphql/connections.htm)
