"use client"

import React, { useState } from "react"
import { MessageCircle, Sparkles } from "lucide-react"
import { But<PERSON> } from "./button"

interface AIAssistantButtonProps {
  onClick: () => void
  isOpen: boolean
}

export function AIAssistantButton({ onClick, isOpen }: AIAssistantButtonProps) {
  const [isHovered, setIsHovered] = useState(false)

  if (isOpen) return null

  return (
    <div className="fixed bottom-6 right-6 z-[50] lg:right-8">
      {/* Tooltip */}
      {isHovered && (
        <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg whitespace-nowrap">
          AI Assistant - Hỗ trợ tài chính
          <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
        </div>
      )}

      {/* Main Button */}
      <button
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className="w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 border-2 border-white flex items-center justify-center"
      >
        <div className="relative">
          <MessageCircle className="w-6 h-6 text-white" />
          <Sparkles className="w-3 h-3 text-white absolute -top-1 -right-1 animate-pulse" />
        </div>
      </button>

      {/* Pulse Animation */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-ping opacity-20 pointer-events-none"></div>
    </div>
  )
}
