"use client"

import { useState, useEffect } from "react"
import { useLanguage } from "../contexts/LanguageContext"
import { useAuth } from "../contexts/AuthContext"
import { ProtectedRoute } from "../components/auth/ProtectedRoute"
import { DashboardLayout, GridSection, ContentSection } from "../components/layout/ResponsiveLayout"
import { DataCard } from "../components/DataCard"
import { SummaryChart } from "../components/SummaryChart"
import { DashboardApiService } from "@/services/client"
import { formatVND, formatCurrencyCompact } from "@/lib/utils/currency"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  CreditCard,
  PiggyBank,
  Target,
  AlertTriangle,
  Loader2
} from "lucide-react"

function DashboardPageContent() {
  const { t } = useLanguage()
  const { user } = useAuth()
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        const response = await DashboardApiService.getDashboardStats()
        if (response.success) {
          setDashboardData(response.data)
        } else {
          setError('Failed to load dashboard data')
        }
      } catch (err) {
        setError('Failed to load dashboard data')
        console.error('Dashboard error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Using shared currency utilities
  const formatCurrency = formatVND
  const formatCompact = formatCurrencyCompact

  // Loading state
  if (loading) {
    return (
      <DashboardLayout stats={[]}>
        <ContentSection>
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading dashboard...</span>
          </div>
        </ContentSection>
      </DashboardLayout>
    )
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout stats={[]}>
        <ContentSection>
          <div className="flex items-center justify-center py-12">
            <AlertTriangle className="h-8 w-8 text-red-500" />
            <span className="ml-2 text-red-600 dark:text-red-400">{error}</span>
          </div>
        </ContentSection>
      </DashboardLayout>
    )
  }

  // Extract data from API response
  const overview = dashboardData?.overview || {}
  const recentTransactions = dashboardData?.recentTransactions || []
  const topCategories = dashboardData?.topCategories || []
  const budgetOverview = dashboardData?.budgetOverview || []
  const weeklyTrend = dashboardData?.weeklyTrend || []
  const alerts = dashboardData?.alerts || []

  return (
    <DashboardLayout
      stats={[
        {
          label: t('dashboard.total_balance'),
          value: formatCurrency(overview.totalBalance || 0),
          icon: <DollarSign size={14} className="text-blue-500" />
        },
        {
          label: "Thu nhập tháng",
          value: formatCurrency(overview.currentMonthIncome || 0),
          icon: <TrendingUp size={14} className="text-green-500" />
        },
        {
          label: "Chi tiêu tháng",
          value: formatCurrency(overview.currentMonthExpenses || 0),
          icon: <TrendingDown size={14} className="text-red-500" />
        }
      ]}
    >
      {/* Stats Cards */}
      <ContentSection title="Tổng quan tài chính" subtitle="Thống kê chi tiết tháng này">
        <GridSection cols={{ default: 1, sm: 2, lg: 3, xl: 5 }} gap="3">
            <DataCard
              title="Monthly Income"
              value={formatCurrency(overview.currentMonthIncome || 0)}
              change={overview.incomeGrowth ? `${overview.incomeGrowth > 0 ? '+' : ''}${overview.incomeGrowth.toFixed(1)}%` : '+0%'}
              positive={overview.incomeGrowth > 0}
              negative={overview.incomeGrowth < 0}
              icon={<TrendingUp size={16} />}
              gradient="bg-gradient-to-br from-emerald-500 to-teal-600"
            />
            <DataCard
              title="Monthly Expense"
              value={formatCurrency(overview.currentMonthExpenses || 0)}
              change={overview.expenseGrowth ? `${overview.expenseGrowth > 0 ? '+' : ''}${overview.expenseGrowth.toFixed(1)}%` : '+0%'}
              positive={overview.expenseGrowth < 0}
              negative={overview.expenseGrowth > 0}
              icon={<TrendingDown size={16} />}
              gradient="bg-gradient-to-br from-red-500 to-pink-600"
            />
            <DataCard
              title="Net Income"
              value={formatCurrency(overview.currentMonthNet || 0)}
              change={overview.netGrowth ? `${overview.netGrowth > 0 ? '+' : ''}${overview.netGrowth.toFixed(1)}%` : '+0%'}
              positive={(overview.currentMonthNet || 0) > 0}
              negative={(overview.currentMonthNet || 0) < 0}
              icon={<DollarSign size={16} />}
              gradient="bg-gradient-to-br from-blue-500 to-indigo-600"
            />
            <DataCard
              title="Savings Rate"
              value={overview.currentMonthIncome > 0 ? `${Math.round(((overview.currentMonthNet || 0) / overview.currentMonthIncome) * 100)}%` : '0%'}
              change="+5%"
              positive={(overview.currentMonthNet || 0) > 0}
              icon={<PiggyBank size={16} />}
              gradient="bg-gradient-to-br from-purple-500 to-violet-600"
            />
            <DataCard
              title="Active Accounts"
              value={overview.activeAccounts?.toString() || '0'}
              change={`${overview.totalTransactions || 0} transactions`}
              positive
              icon={<Target size={16} />}
              gradient="bg-gradient-to-br from-pink-500 to-rose-600"
            />
        </GridSection>
      </ContentSection>

      {/* Alerts */}
      {alerts.length > 0 && (
        <ContentSection>
          <div className="space-y-2">
            {alerts.map((alert: any, index: number) => (
              <div
                key={index}
                className={`rounded-lg p-3 border ${
                  alert.severity === 'high'
                    ? "bg-gradient-to-r from-red-500/10 via-pink-500/10 to-rose-500/10 border-red-200/50 dark:border-red-700/50"
                    : "bg-gradient-to-r from-yellow-500/10 via-amber-500/10 to-orange-500/10 border-yellow-200/50 dark:border-yellow-700/50"
                }`}
              >
                <div className="flex items-start gap-2">
                  <div
                    className={`p-1.5 rounded-lg ${
                      alert.severity === 'high' ? "bg-red-500" : "bg-yellow-500"
                    }`}
                  >
                    <AlertTriangle size={16} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-xs font-semibold text-gray-900 dark:text-gray-100 mb-1">
                      {alert.severity === 'high' ? "🚨" : "⚠️"} {alert.type.replace('_', ' ').toUpperCase()}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {alert.message}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ContentSection>
      )}

      {/* Chart and Quick Actions */}
      <ContentSection>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
          {/* Chart */}
          <div className="lg:col-span-2">
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm p-3 rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-300">
              <SummaryChart data={weeklyTrend} />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-2">
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm p-3 rounded-lg border border-gray-200/50 dark:border-gray-700/50">
              <h3 className="text-xs font-semibold text-gray-900 dark:text-gray-100 mb-2">Quick Actions</h3>
              <div className="space-y-1.5">
                <button className="w-full flex items-center gap-2 p-1.5 rounded-lg bg-gradient-to-r from-emerald-500/10 to-teal-500/10 hover:from-emerald-500/20 hover:to-teal-500/20 transition-all duration-200 group">
                  <div className="p-1 bg-emerald-500 rounded transition-colors">
                    <ArrowUpRight size={10} className="text-white" />
                  </div>
                  <div className="text-left">
                    <p className="text-xs font-medium text-gray-900 dark:text-gray-100">Add Income</p>
                  </div>
                </button>

                <button className="w-full flex items-center gap-2 p-1.5 rounded-lg bg-gradient-to-r from-red-500/10 to-pink-500/10 hover:from-red-500/20 hover:to-pink-500/20 transition-all duration-200 group">
                  <div className="p-1 bg-red-500 rounded transition-colors">
                    <ArrowDownRight size={10} className="text-white" />
                  </div>
                  <div className="text-left">
                    <p className="text-xs font-medium text-gray-900 dark:text-gray-100">Add Expense</p>
                  </div>
                </button>

                <button className="w-full flex items-center gap-2 p-1.5 rounded-lg bg-gradient-to-r from-blue-500/10 to-indigo-500/10 hover:from-blue-500/20 hover:to-indigo-500/20 transition-all duration-200 group">
                  <div className="p-1 bg-blue-500 rounded transition-colors">
                    <CreditCard size={10} className="text-white" />
                  </div>
                  <div className="text-left">
                    <p className="text-xs font-medium text-gray-900 dark:text-gray-100">New Account</p>
                  </div>
                </button>

                <button className="w-full flex items-center gap-2 p-1.5 rounded-lg bg-gradient-to-r from-pink-500/10 to-rose-500/10 hover:from-pink-500/20 hover:to-rose-500/20 transition-all duration-200 group">
                  <div className="p-1 bg-pink-500 rounded transition-colors">
                    <Target size={10} className="text-white" />
                  </div>
                  <div className="text-left">
                    <p className="text-xs font-medium text-gray-900 dark:text-gray-100">Set Limit</p>
                  </div>
                </button>
              </div>
            </div>

            {/* Recent Activity Preview */}
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm p-3 rounded-lg border border-gray-200/50 dark:border-gray-700/50">
              <h3 className="text-xs font-semibold text-gray-900 dark:text-gray-100 mb-2">Recent Activity</h3>
              <div className="space-y-1.5">
                {recentTransactions.slice(0, 5).map((transaction: any) => (
                  <div key={transaction.id} className="flex items-center gap-2 p-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <div className={`w-1 h-1 rounded-full ${
                      transaction.kind === 'income' ? 'bg-green-500' :
                      transaction.kind === 'expense' ? 'bg-red-500' : 'bg-blue-500'
                    }`}></div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-gray-900 dark:text-gray-100">{transaction.name}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(transaction.date).toLocaleDateString('vi-VN')}
                      </p>
                    </div>
                    <span className={`text-xs font-medium ${
                      transaction.kind === 'income' ? 'text-green-600 dark:text-green-400' :
                      transaction.kind === 'expense' ? 'text-red-500 dark:text-red-400' : 'text-blue-500 dark:text-blue-400'
                    }`}>
                      {transaction.kind === 'income' ? '+' : transaction.kind === 'expense' ? '-' : ''}
                      {formatCurrency(transaction.amount)}
                    </span>
                  </div>
                ))}
                {recentTransactions.length === 0 && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center py-2">
                    No recent transactions
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </ContentSection>
    </DashboardLayout>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <DashboardPageContent />
    </ProtectedRoute>
  )
}
