"use client"

import React, { createContext, useContext, useState, useCallback, useEffect } from "react"

export interface TaskbarItem {
  id: string
  type: 'account' | 'transaction' | 'contact' | 'category' | 'budget'
  title: string
  subtitle?: string
  data?: any
  isEditing?: boolean
  timestamp: number
}

interface TaskbarContextType {
  items: TaskbarItem[]
  addItem: (item: Omit<TaskbarItem, 'timestamp'>) => void
  removeItem: (id: string) => void
  updateItem: (id: string, updates: Partial<TaskbarItem>) => void
  getItem: (id: string) => TaskbarItem | undefined
  clearAll: () => void
  restoreItem: (id: string) => void
  minimizeItem: (id: string) => void
}

const TaskbarContext = createContext<TaskbarContextType | undefined>(undefined)

const STORAGE_KEY = 'sidebar-taskbar-items'
const MAX_ITEMS = 10

export function TaskbarProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<TaskbarItem[]>([])
  const [mounted, setMounted] = useState(false)

  // Load from localStorage on mount
  useEffect(() => {
    setMounted(true)
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsedItems = JSON.parse(stored)
        // Filter out items older than 24 hours
        const validItems = parsedItems.filter((item: TaskbarItem) => {
          const age = Date.now() - item.timestamp
          return age < 24 * 60 * 60 * 1000 // 24 hours
        })
        setItems(validItems)
      }
    } catch (error) {
      console.error('Error loading taskbar items:', error)
    }
  }, [])

  // Save to localStorage whenever items change
  useEffect(() => {
    if (mounted) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(items))
      } catch (error) {
        console.error('Error saving taskbar items:', error)
      }
    }
  }, [items, mounted])

  const addItem = useCallback((newItem: Omit<TaskbarItem, 'timestamp'>) => {
    setItems(prev => {
      // Remove existing item with same id if exists
      const filtered = prev.filter(item => item.id !== newItem.id)
      
      // Add new item with timestamp
      const itemWithTimestamp = {
        ...newItem,
        timestamp: Date.now()
      }
      
      // Keep only the most recent MAX_ITEMS
      const updated = [itemWithTimestamp, ...filtered].slice(0, MAX_ITEMS)
      
      return updated
    })
  }, [])

  const removeItem = useCallback((id: string) => {
    setItems(prev => prev.filter(item => item.id !== id))
  }, [])

  const updateItem = useCallback((id: string, updates: Partial<TaskbarItem>) => {
    setItems(prev => prev.map(item => 
      item.id === id 
        ? { ...item, ...updates, timestamp: Date.now() }
        : item
    ))
  }, [])

  const getItem = useCallback((id: string) => {
    return items.find(item => item.id === id)
  }, [items])

  const clearAll = useCallback(() => {
    setItems([])
  }, [])

  const restoreItem = useCallback((id: string) => {
    // This will be handled by individual sidebar components
    // We just update the timestamp to move it to the top
    updateItem(id, {})
  }, [updateItem])

  const minimizeItem = useCallback((id: string) => {
    // Update timestamp to keep it fresh
    updateItem(id, {})
  }, [updateItem])

  const value: TaskbarContextType = {
    items,
    addItem,
    removeItem,
    updateItem,
    getItem,
    clearAll,
    restoreItem,
    minimizeItem
  }

  return (
    <TaskbarContext.Provider value={value}>
      {children}
    </TaskbarContext.Provider>
  )
}

export function useTaskbar() {
  const context = useContext(TaskbarContext)
  if (context === undefined) {
    throw new Error('useTaskbar must be used within a TaskbarProvider')
  }
  return context
}

// Utility functions for creating taskbar items
export function createTaskbarItem(
  type: TaskbarItem['type'],
  id: string,
  title: string,
  data?: any,
  isEditing?: boolean,
  subtitle?: string
): Omit<TaskbarItem, 'timestamp'> {
  return {
    id,
    type,
    title,
    subtitle,
    data,
    isEditing
  }
}

// Helper to generate unique IDs for taskbar items
export function generateTaskbarId(type: string, entityId?: string | number): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  
  if (entityId) {
    return `${type}-${entityId}-${timestamp}-${random}`
  }
  
  return `${type}-new-${timestamp}-${random}`
}

// Helper to get icon for taskbar item type
export function getTaskbarIcon(type: TaskbarItem['type']): string {
  switch (type) {
    case 'account':
      return '🏦'
    case 'transaction':
      return '💰'
    case 'contact':
      return '👤'
    case 'category':
      return '📁'
    case 'budget':
      return '📊'
    default:
      return '📄'
  }
}

// Helper to get display title for taskbar item
export function getTaskbarDisplayTitle(item: TaskbarItem): string {
  const action = item.isEditing ? 'Edit' : 'Add'
  const typeLabel = item.type.charAt(0).toUpperCase() + item.type.slice(1)
  
  if (item.subtitle) {
    return `${action} ${typeLabel}: ${item.subtitle}`
  }
  
  return `${action} ${typeLabel}`
}
