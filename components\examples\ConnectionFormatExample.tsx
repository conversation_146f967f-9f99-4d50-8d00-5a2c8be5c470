/**
 * Example Component: GraphQL Connection Format Usage
 * 
 * This component demonstrates how to use GraphQL connection format
 * in frontend components instead of PaginatedResponse
 */

'use client'

import React, { useState, useEffect } from 'react'
import { UserApiService } from '@/services/client'
import { extractNodes, extractPaginationInfo, useConnectionData } from '@/lib/utils/connection-helpers'
import type { UserConnectionResponse } from '@/types/graphql-connections'
import type { User } from '@/lib/generated/prisma'

// ==================== EXAMPLE 1: BASIC CONNECTION USAGE ====================

export function BasicConnectionExample() {
  const [response, setResponse] = useState<UserConnectionResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true)
      setError(null)
      
      try {
        // API now returns ConnectionResponse instead of PaginatedResponse
        const result = await UserApiService.getUsers({ first: 10 })
        setResponse(result)
      } catch (err: any) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [])

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  if (!response?.success || !response.data) return <div>No data</div>

  // Extract nodes from connection
  const users = extractNodes(response.data)
  const paginationInfo = extractPaginationInfo(response.data)

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Basic Connection Example</h2>
      
      {/* Display pagination info */}
      <div className="text-sm text-gray-600">
        Total: {paginationInfo.totalCount} | 
        Current: {paginationInfo.currentCount} | 
        Has Next: {paginationInfo.hasNextPage ? 'Yes' : 'No'}
      </div>

      {/* Display users */}
      <div className="grid gap-2">
        {users.map(user => (
          <div key={user.id} className="p-3 border rounded">
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-gray-600">{user.email}</div>
          </div>
        ))}
      </div>
    </div>
  )
}

// ==================== EXAMPLE 2: PAGINATION WITH CURSORS ====================

export function CursorPaginationExample() {
  const [response, setResponse] = useState<UserConnectionResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [cursors, setCursors] = useState<{
    after?: string | null
    before?: string | null
  }>({})

  const fetchUsers = async (after?: string | null, before?: string | null) => {
    setLoading(true)
    
    try {
      const result = await UserApiService.getUsers({
        first: 5,
        after: after || undefined,
        before: before || undefined
      })
      setResponse(result)
      setCursors({ after, before })
    } catch (err: any) {
      console.error('Failed to fetch users:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  if (!response?.success || !response.data) return <div>No data</div>

  const connection = response.data
  const users = extractNodes(connection)
  const { hasNextPage, hasPreviousPage, endCursor, startCursor } = connection.pageInfo

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Cursor Pagination Example</h2>
      
      {/* Users list */}
      <div className="grid gap-2">
        {loading && <div>Loading...</div>}
        {users.map(user => (
          <div key={user.id} className="p-3 border rounded">
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-gray-600">{user.email}</div>
          </div>
        ))}
      </div>

      {/* Pagination controls */}
      <div className="flex gap-2">
        <button
          onClick={() => fetchUsers(undefined, startCursor)}
          disabled={!hasPreviousPage || loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Previous
        </button>
        
        <button
          onClick={() => fetchUsers(endCursor)}
          disabled={!hasNextPage || loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>

      {/* Debug info */}
      <div className="text-xs text-gray-500">
        <div>Start Cursor: {startCursor}</div>
        <div>End Cursor: {endCursor}</div>
        <div>Has Next: {hasNextPage ? 'Yes' : 'No'}</div>
        <div>Has Previous: {hasPreviousPage ? 'Yes' : 'No'}</div>
      </div>
    </div>
  )
}

// ==================== EXAMPLE 3: USING HELPER HOOK ====================

export function HelperHookExample() {
  const [apiResponse, setApiResponse] = useState<{
    data?: any
    loading?: boolean
    error?: any
  }>({ loading: true })

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await UserApiService.getUsers({ first: 8 })
        setApiResponse({
          data: result.success ? result.data : null,
          loading: false,
          error: result.success ? null : result.error
        })
      } catch (err: any) {
        setApiResponse({
          data: null,
          loading: false,
          error: err.message
        })
      }
    }

    fetchData()
  }, [])

  // Use helper hook to extract connection data
  const { items, totalCount, hasNextPage, loading, error } = useConnectionData(apiResponse)

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Helper Hook Example</h2>
      
      <div className="text-sm text-gray-600">
        Showing {items.length} of {totalCount} users
        {hasNextPage && ' (more available)'}
      </div>

      <div className="grid gap-2">
        {items.map((user: User) => (
          <div key={user.id} className="p-3 border rounded">
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-gray-600">{user.email}</div>
          </div>
        ))}
      </div>
    </div>
  )
}

// ==================== EXAMPLE 4: BACKWARD COMPATIBILITY ====================

export function BackwardCompatibilityExample() {
  const [users, setUsers] = useState<User[]>([])
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [loading, setLoading] = useState(false)

  const fetchUsers = async (page: number = 1) => {
    setLoading(true)
    
    try {
      // Get connection response
      const response = await UserApiService.getUsers({ first: 10 })
      
      if (response.success && response.data) {
        // Extract nodes for backward compatibility
        const userNodes = extractNodes(response.data)
        setUsers(userNodes)
        
        // Convert to legacy pagination format if needed
        const paginationInfo = extractPaginationInfo(response.data)
        setPagination({
          total: paginationInfo.totalCount,
          page,
          limit: 10,
          totalPages: Math.ceil(paginationInfo.totalCount / 10),
          hasNext: paginationInfo.hasNextPage,
          hasPrev: paginationInfo.hasPreviousPage
        })
      }
    } catch (err: any) {
      console.error('Failed to fetch users:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Backward Compatibility Example</h2>
      
      {/* Legacy pagination display */}
      <div className="text-sm text-gray-600">
        Page {pagination.page} of {pagination.totalPages} 
        ({pagination.total} total items)
      </div>

      {/* Users list */}
      <div className="grid gap-2">
        {loading && <div>Loading...</div>}
        {users.map(user => (
          <div key={user.id} className="p-3 border rounded">
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-gray-600">{user.email}</div>
          </div>
        ))}
      </div>

      {/* Legacy pagination controls */}
      <div className="flex gap-2">
        <button
          onClick={() => fetchUsers(pagination.page - 1)}
          disabled={!pagination.hasPrev || loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Previous
        </button>
        
        <button
          onClick={() => fetchUsers(pagination.page + 1)}
          disabled={!pagination.hasNext || loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  )
}

// ==================== MAIN EXAMPLE COMPONENT ====================

export default function ConnectionFormatExample() {
  const [activeExample, setActiveExample] = useState<string>('basic')

  const examples = [
    { id: 'basic', name: 'Basic Connection', component: BasicConnectionExample },
    { id: 'cursor', name: 'Cursor Pagination', component: CursorPaginationExample },
    { id: 'hook', name: 'Helper Hook', component: HelperHookExample },
    { id: 'compat', name: 'Backward Compatibility', component: BackwardCompatibilityExample }
  ]

  const ActiveComponent = examples.find(ex => ex.id === activeExample)?.component || BasicConnectionExample

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">GraphQL Connection Format Examples</h1>
      
      {/* Example selector */}
      <div className="flex gap-2 flex-wrap">
        {examples.map(example => (
          <button
            key={example.id}
            onClick={() => setActiveExample(example.id)}
            className={`px-4 py-2 rounded ${
              activeExample === example.id
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {example.name}
          </button>
        ))}
      </div>

      {/* Active example */}
      <div className="border rounded-lg p-6">
        <ActiveComponent />
      </div>
    </div>
  )
}
