"use client"

import { useState } from "react"

import { PageHeader } from "@/components/shared/PageHeader"
import { DataCard } from "@/components/DataCard"
import { UserApiService, AccountApiService, TransactionApiService } from "@/services/client"
import { Users, CreditCard, ArrowUpDown, Settings, Database, Shield, Activity } from "lucide-react"

export default function AdminPage() {

  // Mock data for now - TODO: Replace with real API calls
  const totalUsers = 0
  const adminUsers = 0
  const totalAccounts = 0
  const totalTransactions = 0
  const totalCategoryGoal = 0
  const activeUsers = 0

  return (
    <div className="min-h-screen">
      <main className="p-3 lg:p-4 space-y-3">
        <PageHeader
          title="Admin Dashboard"
          description="System overview and management"
        />

          {/* System Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <DataCard
              title="Total Users"
              value={totalUsers.toString()}
              change={`${adminUsers} admins`}
              icon={<Users size={24} />}
              gradient="bg-gradient-to-br from-blue-500 to-indigo-600"
            />
            <DataCard
              title="Active Users"
              value={activeUsers.toString()}
              change="Last 7 days"
              positive
              icon={<Activity size={24} />}
              gradient="bg-gradient-to-br from-green-500 to-emerald-600"
            />
            <DataCard
              title="Total Accounts"
              value={totalAccounts.toString()}
              icon={<CreditCard size={24} />}
              gradient="bg-gradient-to-br from-purple-500 to-violet-600"
            />
            <DataCard
              title="Transactions"
              value={totalTransactions.toString()}
              change="This month"
              icon={<ArrowUpDown size={24} />}
              gradient="bg-gradient-to-br from-orange-500 to-red-600"
            />
          </div>

          {/* Admin Tools */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            {/* System Settings */}
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 rounded-xl">
                  <Settings size={24} />
                </div>
                <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">System Settings</h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Maintenance Mode</span>
                  <button className="w-12 h-6 bg-gray-300 dark:bg-gray-600 rounded-full relative">
                    <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-transform"></div>
                  </button>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">User Registration</span>
                  <button className="w-12 h-6 bg-emerald-500 rounded-full relative">
                    <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-transform"></div>
                  </button>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Email Notifications</span>
                  <button className="w-12 h-6 bg-emerald-500 rounded-full relative">
                    <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-transform"></div>
                  </button>
                </div>
              </div>
            </div>

            {/* Security */}
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 rounded-xl">
                  <Shield size={24} />
                </div>
                <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Security</h3>
              </div>
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Failed Login Attempts</span>
                    <span className="text-xs text-red-600 dark:text-red-400">3</span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Last 24 hours</p>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Active Sessions</span>
                    <span className="text-xs text-green-600 dark:text-green-400">{activeUsers}</span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Currently online</p>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Last Backup</span>
                    <span className="text-xs text-blue-600 dark:text-blue-400">2 hours ago</span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Automatic backup</p>
                </div>
              </div>
            </div>
          </div>

          {/* Database Stats */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-3 bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 rounded-xl">
                <Database size={24} />
              </div>
              <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Database Statistics</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">{totalUsers}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Users</p>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">{totalAccounts}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Accounts</p>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">{totalTransactions}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">Transactions</p>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">{totalCategoryGoal}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">CategoryGoal</p>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4">Recent System Activity</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-xs font-medium text-gray-900 dark:text-gray-100">New user registered</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400"><EMAIL> - 2 hours ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-xs font-medium text-gray-900 dark:text-gray-100">Database backup completed</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Automatic backup - 2 hours ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-xs font-medium text-gray-900 dark:text-gray-100">System update available</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Version 2.1.0 - 1 day ago</p>
                </div>
              </div>
            </div>
          </div>
        </main>
    </div>
  );
}
