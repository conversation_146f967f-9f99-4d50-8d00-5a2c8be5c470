/**
 * GraphQL Operations for User Management
 * 
 * Queries and mutations for user operations
 */

import { gql } from '@apollo/client'

// ==================== FRAGMENTS ====================

export const USER_FRAGMENT = gql`
  fragment UserFragment on User {
    id
    name
    email
    avatar
    role
    description
    isActive
    createdAt
    updatedAt
  }
`

export const USER_WITH_PREFERENCES_FRAGMENT = gql`
  fragment UserWithPreferencesFragment on User {
    ...UserFragment
    preferences {
      id
      currency
      language
      theme
      dateFormat
      numberFormat
      isActive
      createdAt
      updatedAt
    }
  }
  ${USER_FRAGMENT}
`

// ==================== QUERIES ====================

export const GET_USERS = gql`
  query GetUsers(
    $first: Int
    $after: String
    $search: String
    $role: UserRole
    $isActive: Boolean
  ) {
    users(
      first: $first
      after: $after
      search: $search
      role: $role
      isActive: $isActive
    ) {
      edges {
        node {
          ...UserWithPreferencesFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${USER_WITH_PREFERENCES_FRAGMENT}
`

export const GET_USER_BY_ID = gql`
  query GetUserById($id: UUID!) {
    user(id: $id) {
      ...UserWithPreferencesFragment
      accounts {
        id
        name
        balance
        currency
        accountType {
          name
          category
        }
      }
      categories {
        id
        name
        type
        color
      }
      contacts {
        id
        name
        email
        phone
      }
    }
  }
  ${USER_WITH_PREFERENCES_FRAGMENT}
`

export const SEARCH_USERS = gql`
  query SearchUsers($search: String!, $first: Int = 10) {
    users(search: $search, first: $first) {
      edges {
        node {
          ...UserFragment
        }
      }
      totalCount
    }
  }
  ${USER_FRAGMENT}
`

// ==================== MUTATIONS ====================

export const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      ...UserWithPreferencesFragment
    }
  }
  ${USER_WITH_PREFERENCES_FRAGMENT}
`

export const UPDATE_USER = gql`
  mutation UpdateUser($id: UUID!, $input: UpdateUserInput!) {
    updateUser(id: $id, input: $input) {
      ...UserWithPreferencesFragment
    }
  }
  ${USER_WITH_PREFERENCES_FRAGMENT}
`

export const DELETE_USER = gql`
  mutation DeleteUser($id: UUID!) {
    deleteUser(id: $id)
  }
`

export const UPDATE_USER_PREFERENCES = gql`
  mutation UpdateUserPreferences($input: UpdateUserPreferencesInput!) {
    updateUserPreferences(input: $input) {
      id
      currency
      language
      theme
      dateFormat
      numberFormat
      updatedAt
    }
  }
`
