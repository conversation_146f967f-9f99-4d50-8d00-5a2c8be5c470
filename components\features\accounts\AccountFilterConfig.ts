import { Account } from '@/lib/generated/prisma'

// Simplified account filter configuration
export const accountFilters = [
  {
    key: 'accountTypeId',
    label: '<PERSON>ại tài khoản',
    type: 'select' as const,
    options: [
      { value: 'cash', label: 'Tiền mặt' },
      { value: 'bank', label: '<PERSON>ân hàng' },
      { value: 'credit', label: 'Thẻ tín dụng' },
      { value: 'investment', label: 'Đầu tư' }
    ]
  },
  {
    key: 'currency',
    label: 'Tiền tệ',
    type: 'select' as const,
    options: [
      { value: 'VND', label: 'VND' },
      { value: 'USD', label: 'USD' },
      { value: 'EUR', label: 'EUR' }
    ]
  },
  {
    key: 'isActive',
    label: 'Trạng thái',
    type: 'select' as const,
    options: [
      { value: 'true', label: 'Hoạt động' },
      { value: 'false', label: 'Không hoạt động' }
    ]
  }
]

// Account sort options
export const accountSortOptions = [
  { key: 'name', label: 'Tên tài khoản', direction: 'asc' as const },
  { key: 'name', label: 'Tên tà<PERSON> kho<PERSON>n (Z-A)', direction: 'desc' as const },
  { key: 'balance', label: 'Số dư (Thấp đến cao)', direction: 'asc' as const },
  { key: 'balance', label: 'Số dư (Cao đến thấp)', direction: 'desc' as const },
  { key: 'createdAt', label: 'Ngày tạo (Cũ nhất)', direction: 'asc' as const },
  { key: 'createdAt', label: 'Ngày tạo (Mới nhất)', direction: 'desc' as const }
]

// Account filter configuration
export const accountFilterConfig = {
  filters: accountFilters,
  sortOptions: accountSortOptions,
  defaultSort: { key: 'name', direction: 'asc' as const },
  searchFields: ['name', 'description'] as const
}

// Filter functions
export const filterAccounts = (accounts: Account[], filters: Record<string, any>) => {
  return accounts.filter(account => {
    // Account type filter
    if (filters.accountTypeId && account.accountTypeId !== filters.accountTypeId) {
      return false
    }
    
    // Currency filter
    if (filters.currency && account.currency !== filters.currency) {
      return false
    }
    
    // Active status filter
    if (filters.isActive !== undefined) {
      const isActive = filters.isActive === 'true'
      if (account.isActive !== isActive) {
        return false
      }
    }
    
    return true
  })
}

// Sort functions
export const sortAccounts = (accounts: Account[], sortKey: string, direction: 'asc' | 'desc') => {
  return [...accounts].sort((a, b) => {
    let aValue: any = a[sortKey as keyof Account]
    let bValue: any = b[sortKey as keyof Account]
    
    // Handle null/undefined values
    if (aValue == null) aValue = ''
    if (bValue == null) bValue = ''
    
    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }
    
    if (aValue < bValue) return direction === 'asc' ? -1 : 1
    if (aValue > bValue) return direction === 'asc' ? 1 : -1
    return 0
  })
}

// Search function
export const searchAccounts = (accounts: Account[], searchTerm: string) => {
  if (!searchTerm.trim()) return accounts
  
  const term = searchTerm.toLowerCase()
  return accounts.filter(account => 
    account.name.toLowerCase().includes(term) ||
    (account.description && account.description.toLowerCase().includes(term))
  )
}
