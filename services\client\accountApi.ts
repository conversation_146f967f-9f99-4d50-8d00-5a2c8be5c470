/**
 * Account API Service
 *
 * Handles account-related API operations using GraphQL client
 */

import { apolloClient } from '@/lib/graphql/client'
import {
  GET_ACCOUNTS,
  GET_ACCOUNT_BY_ID,
  GET_ACCOUNT_TYPES,
  SEARCH_ACCOUNTS,
  GET_ACCOUNT_SUMMARY,
  CREATE_ACCOUNT,
  UPDATE_ACCOUNT,
  DELETE_ACCOUNT
} from '@/lib/graphql/operations'
import {
  convertToClientError,
  getUserFriendlyErrorMessage
} from '@/lib/graphql/errors'
import type {
  ApiResponse,
  CreateAccountRequest,
  UpdateAccountRequest
} from '@/types/api'
import type {
  Account,
  CreateAccountInput,
  UpdateAccountInput
} from '@/lib/graphql/types'
import type {
  AccountConnectionResponse,
  AccountConnectionParams
} from '@/types/graphql-connections'
import { AccountWithRelations } from '@/types/entities'
import { ApiClient } from './apiClient'

export class AccountApiService {

  // ==================== ACCOUNT CRUD OPERATIONS ====================

  /**
   * Get all accounts with filtering and pagination
   */
  static async getAccounts(params?: AccountConnectionParams): Promise<AccountConnectionResponse> {
    try {
      const { first = 25, after, search, accountTypeId, isActive } = params || {}

      const { data } = await apolloClient.query({
        query: GET_ACCOUNTS,
        variables: {
          first,
          after,
          search,
          accountTypeId,
          isActive
        },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.accounts,
        message: 'Accounts retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch accounts:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get account with relations by ID
   */
  static async getAccountWithRelations(id: string): Promise<ApiResponse<Account>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_ACCOUNT_BY_ID,
        variables: { id },
        fetchPolicy: 'cache-first'
      })

      if (!data.account) {
        throw new Error('Account not found')
      }

      return {
        data: data.account,
        success: true,
        message: 'Account retrieved successfully'
      }
    } catch (error: any) {
      console.error(`Failed to fetch account ${id}:`, error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get account by ID
   */
  static async getAccountById(id: string): Promise<ApiResponse<Account>> {
    return this.getAccountWithRelations(id)
  }

  /**
   * Create new account
   */
  static async createAccount(data: CreateAccountRequest): Promise<ApiResponse<Account>> {
    try {
      const input: CreateAccountInput = {
        name: data.name,
        description: data.description,
        balance: data.balance || 0,
        currency: data.currency || 'VND',
        color: data.color || '#45B7D1',
        accountTypeId: data.accountTypeId
      }

      const { data: result } = await apolloClient.mutate({
        mutation: CREATE_ACCOUNT,
        variables: { input },
        refetchQueries: ['GetAccounts', 'GetAccountSummary']
      })

      return {
        data: result.createAccount,
        success: true,
        message: 'Account created successfully'
      }
    } catch (error: any) {
      console.error('Failed to create account:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Update account
   */
  static async updateAccount(id: string, data: UpdateAccountRequest): Promise<ApiResponse<Account>> {
    try {
      const input: UpdateAccountInput = {
        name: data.name,
        description: data.description,
        balance: data.balance,
        currency: data.currency,
        color: data.color,
        accountTypeId: data.accountTypeId,
        isActive: data.isActive
      }

      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_ACCOUNT,
        variables: { id, input },
        refetchQueries: ['GetAccounts', 'GetAccountById', 'GetAccountSummary']
      })

      return {
        data: result.updateAccount,
        success: true,
        message: 'Account updated successfully'
      }
    } catch (error: any) {
      console.error(`Failed to update account ${id}:`, error)
      throw convertToClientError(error)
    }
  }

  /**
   * Delete account
   */
  static async deleteAccount(id: string): Promise<ApiResponse<void>> {
    try {
      const { data: result } = await apolloClient.mutate({
        mutation: DELETE_ACCOUNT,
        variables: { id },
        refetchQueries: ['GetAccounts', 'GetAccountSummary']
      })

      return {
        data: undefined,
        success: result.deleteAccount,
        message: 'Account deleted successfully'
      }
    } catch (error: any) {
      console.error(`Failed to delete account ${id}:`, error)
      throw convertToClientError(error)
    }
  }

  // ==================== ACCOUNT STATISTICS ====================

  /**
   * Get account statistics
   */
  static async getAccountStats(): Promise<ApiResponse<{
    totalAccounts: number
    totalBalance: number
    accountsByType: Record<string, number>
    monthlyGrowth: number
  }>> {
    try {
      return await ApiClient.get<ApiResponse<any>>('/accounts/stats')
    } catch (error) {
      console.error('Failed to fetch account stats:', error)
      throw error
    }
  }

  // ==================== ACCOUNT TYPES ====================

  /**
   * Get all account types
   */
  static async getAccountTypes(): Promise<ApiResponse<any[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_ACCOUNT_TYPES,
        fetchPolicy: 'cache-first'
      })

      return {
        data: data.accountTypes,
        success: true,
        message: 'Account types retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch account types:', error)
      throw convertToClientError(error)
    }
  }

  // ==================== CONVENIENCE METHODS ====================

  /**
   * Search accounts by name
   */
  static async searchAccounts(query: string, first: number = 10): Promise<AccountConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: SEARCH_ACCOUNTS,
        variables: { search: query, first },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.accounts,
        message: 'Accounts search completed successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to search accounts:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get active accounts only
   */
  static async getActiveAccounts(params?: Omit<AccountConnectionParams, 'isActive'>): Promise<AccountConnectionResponse> {
    return this.getAccounts({
      ...params,
      isActive: true
    })
  }

  /**
   * Get account summary
   */
  static async getAccountSummary(): Promise<ApiResponse<Account[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_ACCOUNT_SUMMARY,
        fetchPolicy: 'cache-first'
      })

      return {
        data: data.accounts.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Account summary retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch account summary:', error)
      throw convertToClientError(error)
    }
  }
}