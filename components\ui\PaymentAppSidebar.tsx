"use client"

import React, { useState, useEffect } from "react"
import { X, Save, Smartphone, Link } from "lucide-react"
import { BaseSidebar } from "./BaseSidebar"
import { SidebarForm, SidebarInputField, SidebarSelectField } from "./SidebarForm"
import { Account } from "@/lib/generated/prisma"
import { AccountPaymentAppWithRelations } from "@/types/entities"

interface PaymentAppSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: any) => void
  paymentApp?: AccountPaymentAppWithRelations | null
  isEditing: boolean
  accounts?: Account[]
  preSelectedAccountId?: string
}

export function PaymentAppSidebar({
  isOpen,
  onClose,
  onSave,
  paymentApp,
  isEditing,
  accounts = [],
  preSelectedAccountId
}: PaymentAppSidebarProps) {
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  const [formData, setFormData] = useState({
    accountId: preSelectedAccountId || "",
    appName: "",
    appType: "",
    accountIdentifier: "",
    displayName: "",
    isActive: true
  })

  // Initialize form data when paymentApp changes
  useEffect(() => {
    if (paymentApp && isEditing) {
      setFormData({
        accountId: paymentApp.accountId || preSelectedAccountId || "",
        appName: paymentApp.appName || "",
        appType: paymentApp.appType || "",
        accountIdentifier: paymentApp.accountIdentifier || "",
        displayName: paymentApp.displayName || "",
        isActive: paymentApp.isActive ?? true
      })
    } else {
      setFormData({
        accountId: preSelectedAccountId || "",
        appName: "",
        appType: "",
        accountIdentifier: "",
        displayName: "",
        isActive: true
      })
    }
  }, [paymentApp, isEditing, preSelectedAccountId])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.accountId.trim()) {
      newErrors.accountId = "Vui lòng chọn tài khoản"
    }

    if (!formData.appName.trim()) {
      newErrors.appName = "Vui lòng nhập tên ứng dụng"
    }

    if (!formData.appType.trim()) {
      newErrors.appType = "Vui lòng chọn loại ứng dụng"
    }

    if (!formData.accountIdentifier.trim()) {
      newErrors.accountIdentifier = "Vui lòng nhập định danh tài khoản"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const paymentAppData = {
        accountId: formData.accountId,
        appName: formData.appName.trim(),
        appType: formData.appType,
        accountIdentifier: formData.accountIdentifier.trim(),
        displayName: formData.displayName.trim() || formData.appName.trim(),
        isActive: formData.isActive
      }

      await onSave(paymentAppData)
    } catch (error) {
      console.error('Error saving payment app:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  // Account options
  const accountOptions = accounts.map(account => ({
    value: account.id,
    label: account.name
  }))

  // App type options
  const appTypeOptions = [
    { value: "MOMO", label: "MoMo" },
    { value: "ZALOPAY", label: "ZaloPay" },
    { value: "VNPAY", label: "VNPay" },
    { value: "SHOPEEPAY", label: "ShopeePay" },
    { value: "GRABPAY", label: "GrabPay" },
    { value: "AIRPAY", label: "AirPay" },
    { value: "VIETTEL_MONEY", label: "Viettel Money" },
    { value: "VIETTELPAY", label: "ViettelPay" },
    { value: "PAYPAL", label: "PayPal" },
    { value: "OTHER", label: "Khác" }
  ]

  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? "Chỉnh sửa liên kết ứng dụng" : "Thêm liên kết ứng dụng"}
      storageKey="payment-app-sidebar"
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={400}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* Account Selection */}
        <SidebarSelectField
          label="Tài khoản"
          value={formData.accountId}
          onChange={(value: string) => handleInputChange('accountId', value)}
          options={accountOptions}
          error={errors.accountId}
          required
          disabled={!!preSelectedAccountId}
          sidebarWidth={400}
        />

        {/* App Name */}
        <SidebarInputField
          label="Tên ứng dụng"
          value={formData.appName}
          onChange={(value: string) => handleInputChange('appName', value)}
          placeholder="Ví dụ: MoMo, ZaloPay..."
          error={errors.appName}
          required
          sidebarWidth={400}
        />

        {/* App Type */}
        <SidebarSelectField
          label="Loại ứng dụng"
          value={formData.appType}
          onChange={(value: string) => handleInputChange('appType', value)}
          options={appTypeOptions}
          error={errors.appType}
          required
          sidebarWidth={400}
        />

        {/* Account Identifier */}
        <SidebarInputField
          label="Định danh tài khoản"
          value={formData.accountIdentifier}
          onChange={(value: string) => handleInputChange('accountIdentifier', value)}
          placeholder="Số điện thoại, email hoặc ID..."
          error={errors.accountIdentifier}
          required
          sidebarWidth={400}
        />

        {/* Display Name */}
        <SidebarInputField
          label="Tên hiển thị"
          value={formData.displayName}
          onChange={(value: string) => handleInputChange('displayName', value)}
          placeholder="Tên hiển thị (tùy chọn)"
          error={errors.displayName}
          sidebarWidth={400}
        />

        {/* Submit Button */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
            disabled={loading}
          >
            Hủy
          </button>
          <button
            type="submit"
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <Save size={16} className="mr-2" />
            {loading ? "Đang lưu..." : (isEditing ? "Cập nhật" : "Tạo mới")}
          </button>
        </div>
      </SidebarForm>
    </BaseSidebar>
  )
}
