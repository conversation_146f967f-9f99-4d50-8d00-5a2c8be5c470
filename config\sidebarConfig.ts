/**
 * Sidebar Configuration System
 * 
 * Centralized configuration for all sidebar behaviors, styling, and features
 */

export interface SidebarConfig {
  // Dimensions
  DEFAULT_WIDTH: number
  MIN_WIDTH: number
  MAX_WIDTH_PERCENTAGE: number
  
  // Animation
  TRANSITION_DURATION: number
  TRANSITION_EASING: string
  
  // Features
  ENABLE_RESIZE: boolean
  ENABLE_OVERLAY_CLOSE: boolean
  ENABLE_MINIMIZE: boolean
  ENABLE_KEYBOARD_SHORTCUTS: boolean
  
  // Responsive
  MOBILE_BREAKPOINT: number
  TABLET_BREAKPOINT: number
  DESKTOP_BREAKPOINT: number
  
  // Z-Index
  Z_INDEX: {
    OVERLAY: number
    SIDEBAR: number
    RESIZE_HANDLE: number
  }
  
  // Performance
  RESIZE_DEBOUNCE_MS: number
  ANIMATION_FRAME_THROTTLE: boolean
}

export const DEFAULT_SIDEBAR_CONFIG: SidebarConfig = {
  // Dimensions
  DEFAULT_WIDTH: 400,
  MIN_WIDTH: 300,
  MAX_WIDTH_PERCENTAGE: 0.6,
  
  // Animation
  TRANSITION_DURATION: 300,
  TRANSITION_EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  
  // Features
  ENABLE_RESIZE: true,
  ENABLE_OVERLAY_CLOSE: true,
  ENABLE_MINIMIZE: false,
  ENABLE_KEYBOARD_SHORTCUTS: true,
  
  // Responsive
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  DESKTOP_BREAKPOINT: 1280,
  
  // Z-Index
  Z_INDEX: {
    OVERLAY: 55,
    SIDEBAR: 56,
    RESIZE_HANDLE: 57
  },
  
  // Performance
  RESIZE_DEBOUNCE_MS: 100,
  ANIMATION_FRAME_THROTTLE: true
}

// Predefined configurations for different sidebar types
export const SIDEBAR_CONFIGS = {
  // Standard sidebar for forms
  FORM: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 400,
    MIN_WIDTH: 300,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  
  // Wide sidebar for complex forms
  WIDE_FORM: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 600,
    MIN_WIDTH: 500,
    MAX_WIDTH_PERCENTAGE: 0.8
  } as SidebarConfig,
  
  // Narrow sidebar for simple forms
  NARROW: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 350,
    MIN_WIDTH: 280,
    MAX_WIDTH_PERCENTAGE: 0.5
  } as SidebarConfig,
  
  // AI Assistant sidebar
  AI_ASSISTANT: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 400,
    MIN_WIDTH: 300,
    MAX_WIDTH_PERCENTAGE: 0.4,
    ENABLE_MINIMIZE: true
  } as SidebarConfig,
  
  // Transaction sidebar (complex form)
  TRANSACTION: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 500,
    MIN_WIDTH: 400,
    MAX_WIDTH_PERCENTAGE: 0.7
  } as SidebarConfig,

  // Filter sidebar
  FILTER: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 450,
    MIN_WIDTH: 300,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  
  // Contact sidebar
  CONTACT: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 400,
    MIN_WIDTH: 300,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  
  // Account sidebar
  ACCOUNT: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 400,
    MIN_WIDTH: 300,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  
  // Category sidebar
  CATEGORY: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 400,
    MIN_WIDTH: 300,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  
  // Budget sidebar
  BUDGET: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 450,
    MIN_WIDTH: 350,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  

  
  // Spending Limit sidebar
  SPENDING_LIMIT: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 400,
    MIN_WIDTH: 300,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  
  // User sidebar
  USER: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 450,
    MIN_WIDTH: 350,
    MAX_WIDTH_PERCENTAGE: 0.6
  } as SidebarConfig,
  
  // Recurring transaction sidebar
  RECURRING: {
    ...DEFAULT_SIDEBAR_CONFIG,
    DEFAULT_WIDTH: 500,
    MIN_WIDTH: 400,
    MAX_WIDTH_PERCENTAGE: 0.7
  } as SidebarConfig
}

// Utility function to get config by sidebar type
export function getSidebarConfig(type: keyof typeof SIDEBAR_CONFIGS): SidebarConfig {
  return SIDEBAR_CONFIGS[type] || DEFAULT_SIDEBAR_CONFIG
}

// Utility function to merge custom config with defaults
export function mergeSidebarConfig(
  baseConfig: SidebarConfig,
  customConfig: Partial<SidebarConfig>
): SidebarConfig {
  return {
    ...baseConfig,
    ...customConfig,
    Z_INDEX: {
      ...baseConfig.Z_INDEX,
      ...customConfig.Z_INDEX
    }
  }
}

// Responsive breakpoint utilities
export function getBreakpointInfo(width: number, config: SidebarConfig) {
  return {
    isMobile: width < config.MOBILE_BREAKPOINT,
    isTablet: width >= config.MOBILE_BREAKPOINT && width < config.TABLET_BREAKPOINT,
    isDesktop: width >= config.DESKTOP_BREAKPOINT,
    isLargeDesktop: width >= config.DESKTOP_BREAKPOINT
  }
}

// Form layout utilities based on sidebar width
export function getFormLayout(sidebarWidth: number, config: SidebarConfig) {
  const { isMobile, isTablet } = getBreakpointInfo(sidebarWidth, config)
  
  if (isMobile || sidebarWidth < 400) {
    return {
      columns: 1,
      gap: '1rem',
      fieldSize: 'small',
      showSections: false
    }
  }
  
  if (isTablet || sidebarWidth < 600) {
    return {
      columns: 1,
      gap: '1.25rem',
      fieldSize: 'medium',
      showSections: true
    }
  }
  
  if (sidebarWidth < 800) {
    return {
      columns: 2,
      gap: '1.5rem',
      fieldSize: 'medium',
      showSections: true
    }
  }
  
  return {
    columns: 3,
    gap: '1.5rem',
    fieldSize: 'large',
    showSections: true
  }
}

// Animation preferences
export function getAnimationConfig(config: SidebarConfig) {
  const prefersReducedMotion = typeof window !== 'undefined' && 
    window.matchMedia('(prefers-reduced-motion: reduce)').matches
  
  return {
    duration: prefersReducedMotion ? 0 : config.TRANSITION_DURATION,
    easing: config.TRANSITION_EASING,
    enabled: !prefersReducedMotion
  }
}

// Performance optimization utilities
export function shouldUseAnimationFrame(config: SidebarConfig): boolean {
  return config.ANIMATION_FRAME_THROTTLE && 
    typeof window !== 'undefined' && 
    'requestAnimationFrame' in window
}

export function createThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  config: SidebarConfig
): (...args: Parameters<T>) => void {
  if (!shouldUseAnimationFrame(config)) {
    return callback
  }
  
  let frameId: number | null = null
  
  return (...args: Parameters<T>) => {
    if (frameId) {
      cancelAnimationFrame(frameId)
    }
    
    frameId = requestAnimationFrame(() => {
      callback(...args)
      frameId = null
    })
  }
}
