import { BaseTable } from './base';
import { UUID } from './common';

export type {
  AuditLog,
  AuditLogBase,
  AuditLogWithRelations,
  CreateAuditLogInput,
  UpdateAuditLogInput,
  AuditLogFilterInput
};

/**
 * Audit log action types
 */
export enum AuditLogAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PASSWORD_RESET = 'PASSWORD_RESET',
  PERMISSION_CHANGE = 'PERMISSION_CHANGE',
  OTHER = 'OTHER'
}

/**
 * Base interface for AuditLog
 */
interface AuditLogBase extends BaseTable {
  userId: UUID;
  action: AuditLogAction;
  entityType: string;
  entityId: string;
  oldValues: Record<string, unknown> | null;
  newValues: Record<string, unknown> | null;
  ipAddress: string | null;
  userAgent: string | null;
  statusCode: number | null;
  error: string | null;
}

/**
 * AuditLog with all relations
 */
interface AuditLogWithRelations extends AuditLogBase {
  user: UserBase;
}

/**
 * Union type for AuditLog
 */
type AuditLog = AuditLogBase | AuditLogWithRelations;

/**
 * Input for creating a new audit log
 */
interface CreateAuditLogInput {
  userId: UUID;
  action: AuditLogAction;
  entityType: string;
  entityId: string;
  oldValues?: Record<string, unknown> | null;
  newValues?: Record<string, unknown> | null;
  ipAddress?: string | null;
  userAgent?: string | null;
  statusCode?: number | null;
  error?: string | null;
}

/**
 * Input for updating an existing audit log
 */
interface UpdateAuditLogInput {
  id: UUID;
  statusCode?: number | null;
  error?: string | null;
}

/**
 * Input for filtering audit logs
 */
interface AuditLogFilterInput {
  search?: string;
  userId?: UUID;
  action?: AuditLogAction;
  entityType?: string;
  entityId?: string;
  startDate?: string;
  endDate?: string;
  statusCode?: number;
  hasError?: boolean;
}

// Import related types
import { UserBase } from './user';
