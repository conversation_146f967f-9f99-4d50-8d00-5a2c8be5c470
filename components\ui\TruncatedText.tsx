"use client"

import React, { useRef, useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "./Tooltip"

// Hook to check if text is truncated
function useIsTruncated(ref: React.RefObject<HTMLElement>) {
  const [isTruncated, setIsTruncated] = useState(false)

  useEffect(() => {
    const checkTruncation = () => {
      if (ref.current) {
        const element = ref.current
        setIsTruncated(element.scrollWidth > element.clientWidth || element.scrollHeight > element.clientHeight)
      }
    }

    checkTruncation()
    
    // Use ResizeObserver for better performance
    const resizeObserver = new ResizeObserver(checkTruncation)
    if (ref.current) {
      resizeObserver.observe(ref.current)
    }
    
    return () => resizeObserver.disconnect()
  }, [ref])

  return isTruncated
}

// Wrapper component that automatically shows tooltip only when text is truncated
interface TruncatedTextProps {
  text: string
  className?: string
  maxLines?: number
  as?: keyof JSX.IntrinsicElements
  children?: React.ReactNode
}

export function TruncatedText({ 
  text, 
  className = "", 
  maxLines = 1, 
  as: Component = "span",
  children 
}: TruncatedTextProps) {
  const textRef = useRef<HTMLElement>(null)
  const isTruncated = useIsTruncated(textRef)

  const truncateClass = maxLines === 1 ? "truncate" : `line-clamp-${maxLines}`

  const content = children || text

  if (!isTruncated) {
    return React.createElement(
      Component,
      {
        ref: textRef,
        className: `${truncateClass} ${className}`,
      },
      content
    )
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {React.createElement(
            Component,
            {
              ref: textRef,
              className: `${truncateClass} ${className}`,
            },
            content
          )}
        </TooltipTrigger>
        <TooltipContent>
          <div className="max-w-xs break-words">
            {text}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// Simple version for inline use
interface InlineTooltipProps {
  content: string
  children: React.ReactNode
  disabled?: boolean
}

export function InlineTooltip({ content, children, disabled = false }: InlineTooltipProps) {
  if (disabled || !content) {
    return <>{children}</>
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent>
          <div className="max-w-xs break-words">
            {content}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
