"use client"

import React, { useState, useEffect } from "react"
import { User, Building2, Plus, Trash2, Star, Mail, Phone, FileText, DollarSign, CreditCard } from "lucide-react"
import { BaseSidebar } from "./BaseSidebar"
import { SidebarForm, SidebarInputField, SidebarTextareaField } from "./SidebarForm"
import { SearchableCombobox } from "./SearchableCombobox"
import { SidebarActionFooter } from "./SidebarFooter"
import { ContactTypeManager } from "./ContactTypeManager"
import { BasicContactInfo } from "./contact-modal/BasicContactInfo"
import { Contact, ContactType, Account } from "@/lib/generated/prisma"

// Default contact types for the system
const defaultContactTypes: ContactType[] = [
  {
    id: "person",
    name: "<PERSON><PERSON> nhân",
    description: "<PERSON>ên hệ cá nhân",
    color: "#10b981",
    icon: "user",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "business",
    name: "<PERSON><PERSON><PERSON> nghiệ<PERSON>",
    description: "<PERSON><PERSON><PERSON> hệ doanh nghiệp",
    color: "#3b82f6",
    icon: "building2",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "family",
    name: "Gia đình",
    description: "Thành viên gia đình",
    color: "#f59e0b",
    icon: "heart",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]
import { getSidebarConfig } from "../../config/sidebarConfig"

interface ContactSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (contact: Contact) => void
  contact?: Contact | null
  isEditing: boolean
}

interface FormData {
  name: string
  phone: string
  email: string
  description: string
  contactType: ContactType | undefined
}

interface FormErrors {
  name?: string
  phone?: string
  email?: string
  accounts?: string
}

export function ContactSidebar({
  isOpen,
  onClose,
  onSave,
  contact,
  isEditing
}: ContactSidebarProps) {
  const [idCounter, setIdCounter] = useState(1)

  const [formData, setFormData] = useState<FormData>({
    name: "",
    phone: "",
    email: "",
    description: "",
    contactType: defaultContactTypes[0]
  })

  const [accounts, setAccounts] = useState<Account[]>([])
  const [contactTypes, setContactTypes] = useState<ContactType[]>(defaultContactTypes)
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen) {
      if (contact && isEditing) {
        setFormData({
          name: contact.name || "",
          phone: contact.phone || "",
          email: contact.email || "",
          description: contact.description || "",
          contactType: contact.contactType || defaultContactTypes[0]
        })
        setAccounts(contact.accounts || [])
      } else {
        setFormData({
          name: "",
          phone: "",
          email: "",
          description: "",
          contactType: defaultContactTypes[0]
        })
        setAccounts([])
      }
      setErrors({})
    }
  }, [isOpen, contact, isEditing])

  // Handle ESC key to close sidebar
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey)
      return () => document.removeEventListener('keydown', handleEscKey)
    }
  }, [isOpen])

  const addAccount = () => {
    const newAccount: Account = {
      id: idCounter,
      userId: 1, // Default user ID
      name: "",
      type: "bank",
      balance: 0,
      currency: "VND",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      description: "",
      bankName: "",
      accountNumber: ""
    }
    setIdCounter(prev => prev + 1)
    setAccounts(prev => [...prev, newAccount])
  }

  const updateAccount = (id: number, field: keyof Account, value: any) => {
    setAccounts(prev => prev.map(account =>
      account.id === id ? { ...account, [field]: value, updatedAt: new Date() } : account
    ))
  }

  const deleteAccount = (id: number) => {
    setAccounts(prev => prev.filter(account => account.id !== id))
  }

  // Contact type management functions
  const handleContactTypeSelect = (type: ContactType) => {
    setFormData(prev => ({ ...prev, contactType: type }))
  }

  const handleContactTypeCreate = (newType: Omit<ContactType, 'id'>) => {
    const id = `custom_${Date.now()}`
    const contactType: ContactType = { ...newType, id }
    setContactTypes(prev => [...prev, contactType])
    setFormData(prev => ({ ...prev, contactType }))
  }

  const handleContactTypeUpdate = (id: string, updates: Partial<ContactType>) => {
    setContactTypes(prev => prev.map(type =>
      type.id === id ? { ...type, ...updates } : type
    ))
    if (formData.contactType?.id === id) {
      setFormData(prev => ({
        ...prev,
        contactType: prev.contactType ? { ...prev.contactType, ...updates } : undefined
      }))
    }
  }

  const handleContactTypeDelete = (id: string) => {
    setContactTypes(prev => prev.filter(type => type.id !== id))
    if (formData.contactType?.id === id) {
      setFormData(prev => ({ ...prev, contactType: defaultContactTypes[0] }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Name is required
    if (!formData.name.trim()) {
      newErrors.name = "Name is required"
    }

    // Email validation (if provided)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    // Phone validation (if provided)
    if (formData.phone && !/^[\d\s\-\+\(\)]+$/.test(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number"
    }

    // Validate accounts
    const invalidAccounts = accounts.some(account =>
      !account.name.trim() || !account.type
    )
    if (accounts.length > 0 && invalidAccounts) {
      newErrors.accounts = "Account name and type are required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const contactData: Contact = {
        id: contact?.id || 0,
        userId: contact?.userId || 1, // Default user ID
        name: formData.name.trim(),
        phone: formData.phone.trim() || undefined,
        email: formData.email.trim() || undefined,
        contactType: formData.contactType,
        accounts: accounts,
        description: formData.description.trim() || undefined,
        isActive: contact?.isActive ?? true,
        createdAt: contact?.createdAt || new Date(),
        updatedAt: new Date()
      }

      if (isEditing && contact) {
        // Update existing contact
        const response = await fetch(`/api/contacts/${contact.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(contactData)
        })

        if (!response.ok) {
          throw new Error('Failed to update contact')
        }

        const updatedContact = await response.json()
        onSave(updatedContact)
      } else {
        // Create new contact
        const response = await fetch('/api/contacts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(contactData)
        })

        if (!response.ok) {
          throw new Error('Failed to create contact')
        }

        const newContact = await response.json()
        onSave(newContact)
      }

      handleClose()
    } catch (error) {
      console.error('Error saving contact:', error)
      // You could add a toast notification here
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      name: "",
      phone: "",
      email: "",
      description: "",
      contactType: defaultContactTypes[0]
    })
    setAccounts([])
    setErrors({})
    onClose()
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const config = getSidebarConfig('CONTACT')

  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa liên hệ" : "Thêm liên hệ mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${contact?.name}` : "Tạo liên hệ mới trong danh bạ"}
      storageKey="contactSidebarWidth"
      config={{
        DEFAULT_WIDTH: 450,
        MIN_WIDTH: 400,
        MAX_WIDTH_PERCENTAGE: 0.6
      }}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={handleSubmit}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={!formData.name.trim()}
          sidebarWidth={450}
        />
      }
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={450}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* Contact Name */}
        <SidebarInputField
          label="Tên liên hệ"
          value={formData.name}
          onChange={(value) => handleInputChange('name', value)}
          placeholder="Nhập tên liên hệ"
          required
          error={errors.name}
          icon={<User size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={450}
        />

        {/* Contact Type Manager */}
        <div className="col-span-full">
          <ContactTypeManager
            contactTypes={contactTypes}
            selectedType={formData.contactType}
            onTypeSelect={handleContactTypeSelect}
            onTypeCreate={handleContactTypeCreate}
            onTypeUpdate={handleContactTypeUpdate}
            onTypeDelete={handleContactTypeDelete}
            disabled={loading}
          />
        </div>

        {/* Phone */}
        <SidebarInputField
          label="Số điện thoại"
          value={formData.phone}
          onChange={(value) => handleInputChange('phone', value)}
          placeholder="Nhập số điện thoại"
          type="tel"
          error={errors.phone}
          icon={<Phone size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={450}
        />

        {/* Email */}
        <SidebarInputField
          label="Email"
          value={formData.email}
          onChange={(value) => handleInputChange('email', value)}
          placeholder="<EMAIL>"
          type="email"
          error={errors.email}
          icon={<Mail size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={450}
        />

        {/* Description */}
        <SidebarTextareaField
          label="Mô tả"
          value={formData.description}
          onChange={(value) => handleInputChange('description', value)}
          placeholder="Mô tả thêm về liên hệ (tùy chọn)"
          rows={3}
          icon={<FileText size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={450}
        />

        {/* Accounts Section */}
        <div className="col-span-full">
          <div className="flex items-center justify-between mb-3">
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
              <Building2 size={14} className="inline mr-1" />
              Tài khoản
            </label>
            <button
              type="button"
              onClick={addAccount}
              disabled={loading}
              className="flex items-center gap-1 px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 disabled:opacity-50"
            >
              <Plus size={12} />
              Thêm tài khoản
            </button>
          </div>

          {accounts.length === 0 ? (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-xs">
              Chưa có tài khoản nào
            </div>
          ) : (
            <div className="space-y-3">
              {accounts.map((account, index) => (
                <div key={account.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                      Tài khoản #{index + 1}
                    </span>

                  </div>

                  <div className="grid grid-cols-1 gap-3">
                    <SidebarInputField
                      label="Tên tài khoản"
                      value={account.name}
                      onChange={(value) => updateAccount(account.id, 'name', value)}
                      placeholder="VD: Vietcombank - Chính"
                      error={errors.accounts && !account.name.trim() ? "Tên tài khoản là bắt buộc" : undefined}
                      icon={<User size={14} />}
                      sidebarWidth={450}
                    />

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-700 flex items-center">
                        <Building2 size={14} className="mr-2" />
                        Loại tài khoản
                      </label>
                      <SearchableCombobox
                        value={account.type}
                        onChange={(value) => updateAccount(account.id, 'type', value)}
                        options={[
                          { value: "cash", label: "Tiền mặt" },
                          { value: "bank", label: "Ngân hàng" },
                          { value: "credit", label: "Thẻ tín dụng" },
                          { value: "investment", label: "Đầu tư" },
                          { value: "custom", label: "Tùy chỉnh" }
                        ]}
                        placeholder="Chọn loại tài khoản..."
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <SidebarInputField
                        label="Số dư"
                        value={account.balance.toString()}
                        onChange={(value) => updateAccount(account.id, 'balance', parseFloat(value) || 0)}
                        placeholder="0"
                        type="number"
                        icon={<DollarSign size={14} />}
                        sidebarWidth={450}
                      />

                      <SidebarInputField
                        label="Tiền tệ"
                        value={account.currency}
                        onChange={(value) => updateAccount(account.id, 'currency', value)}
                        placeholder="VND"
                        icon={<DollarSign size={14} />}
                        sidebarWidth={450}
                      />
                    </div>

                    {account.type === "bank" && (
                      <div className="grid grid-cols-2 gap-3">
                        <SidebarInputField
                          label="Ngân hàng"
                          value={account.bankName || ""}
                          onChange={(value) => updateAccount(account.id, 'bankName', value)}
                          placeholder="VD: Vietcombank"
                          icon={<Building2 size={14} />}
                          sidebarWidth={450}
                        />

                        <SidebarInputField
                          label="Số tài khoản"
                          value={account.accountNumber || ""}
                          onChange={(value) => updateAccount(account.id, 'accountNumber', value)}
                          placeholder="VD: **********"
                          icon={<CreditCard size={14} />}
                          sidebarWidth={450}
                        />
                      </div>
                    )}

                    <SidebarTextareaField
                      label="Mô tả"
                      value={account.description || ""}
                      onChange={(value) => updateAccount(account.id, 'description', value)}
                      placeholder="Mô tả thêm về tài khoản (tùy chọn)"
                      rows={2}
                      icon={<FileText size={14} />}
                      sidebarWidth={450}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </SidebarForm>
    </BaseSidebar>
  )
}

