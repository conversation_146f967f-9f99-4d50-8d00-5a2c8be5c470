/**
 * Account GraphQL Resolvers
 *
 * Resolvers for Account queries and mutations using Prisma directly
 */

import { GraphQLContext, requireAuth, requireOwnershipOrAdmin } from '../context'
import { prisma } from '@/lib/database'
import { GraphQLError } from 'graphql'

interface AccountArgs {
  id: string
}

interface AccountsArgs {
  first?: number
  after?: string
  search?: string
  accountTypeId?: string
  isActive?: boolean
}

interface CreateAccountArgs {
  input: {
    name: string
    description?: string
    balance?: number
    currency?: string
    color?: string
    icon?: string
    accountTypeId: string
  }
}

interface UpdateAccountArgs {
  id: string
  input: {
    name?: string
    description?: string
    balance?: number
    currency?: string
    color?: string
    icon?: string
    accountTypeId?: string
    isActive?: boolean
  }
}

// Helper function to build connection response
function buildConnection<T>(
  items: T[],
  totalCount: number,
  first?: number,
  after?: string
) {
  const edges = items.map((item: any, index) => ({
    node: item,
    cursor: Buffer.from(`${after ? parseInt(Buffer.from(after, 'base64').toString()) + index + 1 : index}`).toString('base64')
  }))

  const hasNextPage = first ? items.length === first : false
  const hasPreviousPage = !!after

  return {
    edges,
    pageInfo: {
      hasNextPage,
      hasPreviousPage,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount
  }
}

export const accountResolvers = {
  Query: {
    accounts: async (_: any, args: AccountsArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const { first = 25, after, search, accountTypeId, isActive } = args

      // Build where clause
      const where: any = {
        userId: user.id
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (accountTypeId) where.accountTypeId = accountTypeId
      if (isActive !== undefined) where.isActive = isActive

      // Convert cursor to skip value
      const skip = after ? parseInt(Buffer.from(after, 'base64').toString()) : 0

      const [accounts, totalCount] = await Promise.all([
        prisma.account.findMany({
          where,
          include: {
            accountType: true,
            contact: true
          },
          skip,
          take: first,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.account.count({ where })
      ])

      return buildConnection(accounts, totalCount, first, after)
    },

    account: async (_: any, args: AccountArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const account = await prisma.account.findFirst({
        where: {
          id: args.id,
          userId: user.id
        },
        include: {
          accountType: true,
          contact: true
        }
      })

      if (!account) {
        throw new GraphQLError('Account not found')
      }

      // Check ownership
      requireOwnershipOrAdmin(context, account.userId)

      return account
    },

    accountTypes: async (_: any, __: any, context: GraphQLContext) => {
      requireAuth(context)

      const accountTypes = await prisma.accountType.findMany({
        where: { isActive: true },
        orderBy: { name: 'asc' }
      })

      return accountTypes
    }
  },

  Mutation: {
    createAccount: async (_: any, args: CreateAccountArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // Verify account type exists
        const accountType = await prisma.accountType.findUnique({
          where: { id: input.accountTypeId }
        })

        if (!accountType) {
          throw new GraphQLError('Account type not found')
        }

        const account = await prisma.account.create({
          data: {
            name: input.name,
            description: input.description,
            balance: input.balance || 0,
            currency: input.currency || 'VND',
            color: input.color || '#45B7D1',
            userId: user.id,
            accountTypeId: input.accountTypeId
          },
          include: {
            accountType: true,
            contact: true
          }
        })

        return account
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to create account')
      }
    },

    updateAccount: async (_: any, args: UpdateAccountArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // First check if account exists and user has permission
        const existingAccount = await prisma.account.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingAccount) {
          throw new GraphQLError('Account not found')
        }

        requireOwnershipOrAdmin(context, existingAccount.userId)

        // Verify account type exists if being updated
        if (input.accountTypeId) {
          const accountType = await prisma.accountType.findUnique({
            where: { id: input.accountTypeId }
          })

          if (!accountType) {
            throw new GraphQLError('Account type not found')
          }
        }

        const account = await prisma.account.update({
          where: { id: args.id },
          data: {
            name: input.name,
            description: input.description,
            balance: input.balance,
            currency: input.currency,
            color: input.color,
            accountTypeId: input.accountTypeId,
            isActive: input.isActive
          },
          include: {
            accountType: true,
            contact: true
          }
        })

        return account
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update account')
      }
    },

    deleteAccount: async (_: any, args: AccountArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        // First check if account exists and user has permission
        const existingAccount = await prisma.account.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingAccount) {
          throw new GraphQLError('Account not found')
        }

        requireOwnershipOrAdmin(context, existingAccount.userId)

        // Soft delete by setting isActive to false
        await prisma.account.update({
          where: { id: args.id },
          data: { isActive: false }
        })

        return true
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to delete account')
      }
    }
  },

  Account: {
    user: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.user) {
        return parent.user
      }

      return await context.prisma.user.findUnique({
        where: { id: parent.userId }
      })
    },

    accountType: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.accountType) {
        return parent.accountType
      }

      return await context.prisma.accountType.findUnique({
        where: { id: parent.accountTypeId }
      })
    },

    transactions: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.transaction.findMany({
        where: { 
          OR: [
            { accountId: parent.id },
            { fromAccountId: parent.id },
            { toAccountId: parent.id }
          ],
          isActive: true 
        },
        include: {
          account: { include: { accountType: true } },
          category: true,
          fromAccount: { include: { accountType: true } },
          toAccount: { include: { accountType: true } }
        },
        orderBy: { date: 'desc' },
        take: 50 // Limit to recent transactions
      })
    },

    transferFromTransactions: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.transaction.findMany({
        where: { 
          fromAccountId: parent.id,
          isActive: true 
        },
        include: {
          account: { include: { accountType: true } },
          category: true,
          fromAccount: { include: { accountType: true } },
          toAccount: { include: { accountType: true } }
        },
        orderBy: { date: 'desc' }
      })
    },

    transferToTransactions: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.transaction.findMany({
        where: { 
          toAccountId: parent.id,
          isActive: true 
        },
        include: {
          account: { include: { accountType: true } },
          category: true,
          fromAccount: { include: { accountType: true } },
          toAccount: { include: { accountType: true } }
        },
        orderBy: { date: 'desc' }
      })
    },

    accountPaymentApps: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.accountPaymentApp.findMany({
        where: { 
          accountId: parent.id,
          isActive: true 
        }
      })
    }
  }
}
