/**
 * GraphQL Connection Helper Utilities
 * 
 * Helper functions to work with GraphQL connection format in frontend components
 */

import type { Connection, Edge, PageInfo } from '@/types/graphql-connections'

// ==================== NODE EXTRACTION ====================

/**
 * Extract nodes from GraphQL connection
 */
export function extractNodes<T>(connection: Connection<T> | null | undefined): T[] {
  if (!connection || !connection.edges) {
    return []
  }
  return connection.edges.map(edge => edge.node)
}

/**
 * Extract edges from GraphQL connection
 */
export function extractEdges<T>(connection: Connection<T> | null | undefined): Edge<T>[] {
  if (!connection || !connection.edges) {
    return []
  }
  return connection.edges
}

// ==================== PAGINATION INFO ====================

/**
 * Extract pagination info from GraphQL connection
 */
export function extractPaginationInfo<T>(connection: Connection<T> | null | undefined): {
  hasNextPage: boolean
  hasPreviousPage: boolean
  totalCount: number
  currentCount: number
  startCursor?: string | null
  endCursor?: string | null
} {
  if (!connection) {
    return {
      hasNextPage: false,
      hasPreviousPage: false,
      totalCount: 0,
      currentCount: 0,
      startCursor: null,
      endCursor: null
    }
  }

  return {
    hasNextPage: connection.pageInfo.hasNextPage,
    hasPreviousPage: connection.pageInfo.hasPreviousPage,
    totalCount: connection.totalCount,
    currentCount: connection.edges.length,
    startCursor: connection.pageInfo.startCursor,
    endCursor: connection.pageInfo.endCursor
  }
}

// ==================== CONNECTION RESPONSE HELPERS ====================

/**
 * Extract connection data from API response
 */
export function extractConnectionData<T>(response: {
  success: boolean
  data?: Connection<T>
  error?: string
}): Connection<T> | null {
  if (!response.success || !response.data) {
    return null
  }
  return response.data
}

/**
 * Extract nodes from API response
 */
export function extractNodesFromResponse<T>(response: {
  success: boolean
  data?: Connection<T>
  error?: string
}): T[] {
  const connection = extractConnectionData(response)
  return extractNodes(connection)
}

// ==================== PAGINATION HELPERS ====================

/**
 * Convert connection to legacy pagination format for backward compatibility
 */
export function connectionToPagination<T>(
  connection: Connection<T> | null | undefined,
  currentPage: number = 1,
  itemsPerPage: number = 25
): {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
} {
  if (!connection) {
    return {
      data: [],
      total: 0,
      page: currentPage,
      limit: itemsPerPage,
      totalPages: 0,
      hasNext: false,
      hasPrev: false
    }
  }

  const totalPages = Math.ceil(connection.totalCount / itemsPerPage)

  return {
    data: extractNodes(connection),
    total: connection.totalCount,
    page: currentPage,
    limit: itemsPerPage,
    totalPages,
    hasNext: connection.pageInfo.hasNextPage,
    hasPrev: connection.pageInfo.hasPreviousPage
  }
}

// ==================== CURSOR HELPERS ====================

/**
 * Generate cursor for pagination
 */
export function generateCursor(index: number): string {
  return Buffer.from(index.toString()).toString('base64')
}

/**
 * Parse cursor to get index
 */
export function parseCursor(cursor: string): number {
  try {
    return parseInt(Buffer.from(cursor, 'base64').toString(), 10)
  } catch {
    return 0
  }
}

/**
 * Calculate offset from cursor and first/last parameters
 */
export function calculateOffset(
  after?: string | null,
  before?: string | null,
  first?: number,
  last?: number
): { offset: number; limit: number } {
  let offset = 0
  let limit = first || last || 25

  if (after) {
    offset = parseCursor(after) + 1
  } else if (before) {
    const beforeIndex = parseCursor(before)
    offset = Math.max(0, beforeIndex - (last || 25))
  }

  return { offset, limit }
}

// ==================== VALIDATION HELPERS ====================

/**
 * Check if object is a valid connection
 */
export function isConnection<T>(obj: any): obj is Connection<T> {
  return (
    obj &&
    typeof obj === 'object' &&
    Array.isArray(obj.edges) &&
    obj.pageInfo &&
    typeof obj.pageInfo === 'object' &&
    typeof obj.totalCount === 'number'
  )
}

/**
 * Check if object is a valid edge
 */
export function isEdge<T>(obj: any): obj is Edge<T> {
  return (
    obj &&
    typeof obj === 'object' &&
    obj.node &&
    typeof obj.cursor === 'string'
  )
}

/**
 * Check if object is valid page info
 */
export function isPageInfo(obj: any): obj is PageInfo {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.hasNextPage === 'boolean' &&
    typeof obj.hasPreviousPage === 'boolean'
  )
}

// ==================== EMPTY CONNECTION HELPERS ====================

/**
 * Create empty connection
 */
export function createEmptyConnection<T>(): Connection<T> {
  return {
    edges: [],
    pageInfo: {
      hasNextPage: false,
      hasPreviousPage: false,
      startCursor: null,
      endCursor: null
    },
    totalCount: 0
  }
}

/**
 * Create connection from array (for testing/mocking)
 */
export function createConnectionFromArray<T>(
  items: T[],
  offset: number = 0,
  limit: number = items.length
): Connection<T> {
  const edges = items.slice(offset, offset + limit).map((item, index) => ({
    node: item,
    cursor: generateCursor(offset + index)
  }))

  return {
    edges,
    pageInfo: {
      hasNextPage: offset + limit < items.length,
      hasPreviousPage: offset > 0,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount: items.length
  }
}

// ==================== REACT HOOKS HELPERS ====================

/**
 * Hook-friendly function to extract data from connection response
 */
export function useConnectionData<T>(response: {
  data?: Connection<T>
  loading?: boolean
  error?: any
}): {
  items: T[]
  totalCount: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  loading: boolean
  error: any
} {
  const connection = response.data
  const items = extractNodes(connection)
  const paginationInfo = extractPaginationInfo(connection)

  return {
    items,
    totalCount: paginationInfo.totalCount,
    hasNextPage: paginationInfo.hasNextPage,
    hasPreviousPage: paginationInfo.hasPreviousPage,
    loading: response.loading || false,
    error: response.error
  }
}
