/**
 * Minimal GraphQL API Route for Testing
 */

import { NextRequest } from 'next/server'
import { ApolloServer } from '@apollo/server'
import { startServerAndCreateNextHandler } from '@as-integrations/next'
import { gql } from 'graphql-tag'

// Minimal schema
const typeDefs = gql`
  type Query {
    hello: String
    health: String
  }
`

// Minimal resolvers
const resolvers = {
  Query: {
    hello: () => 'Hello from GraphQL!',
    health: () => 'OK'
  }
}

// Create Apollo Server
const server = new ApolloServer({
  typeDefs,
  resolvers,
  introspection: true,
  includeStacktraceInErrorResponses: true
})

// Create Next.js handler
const handler = startServerAndCreateNextHandler<NextRequest>(server, {
  context: async () => ({})
})

export { handler as GET, handler as POST }
