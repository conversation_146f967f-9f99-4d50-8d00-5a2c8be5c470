import { BaseTableActiveable } from '../base';
import { CategoryType, UUID } from '../common';

export type {
  Category,
  CategoryBase,
  CategoryWithRelations,
  CreateCategoryInput,
  UpdateCategoryInput,
  MoveCategoryInput,
  CategoryFilterInput,
  CategoryResponse
};

/**
 * Base interface for Category
 */
interface CategoryBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  type: CategoryType;
  color: string;
  icon: string | null;
  userId: UUID;
  parentId: UUID | null;
  isDefault: boolean;
}

/**
 * Category with all relations
 */
interface CategoryWithRelations extends CategoryBase {
  // Relations
  user: UserBase;
  parent: CategoryBase | null;
  children: CategoryBase[];
  
  // Related entities
  categoryGoals: CategoryGoalBase[];
  transactions: TransactionBase[];
  categoryTransactionExecutionInfos: CategoryTransactionExecutionInfoBase[];
  categoryPeriodRecords: CategoryPeriodRecordBase[];
}

/**
 * Union type for Category
 */
type Category = CategoryBase | CategoryWithRelations;

/**
 * Input for creating a new category
 */
interface CreateCategoryInput {
  name: string;
  description?: string | null;
  type: CategoryType;
  color?: string;
  icon?: string | null;
  parentId?: UUID | null;
  isDefault?: boolean;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing category
 */
interface UpdateCategoryInput {
  id: UUID;
  name?: string;
  description?: string | null;
  type?: CategoryType;
  color?: string;
  icon?: string | null;
  parentId?: UUID | null;
  isDefault?: boolean;
  isActive?: boolean;
}

/**
 * Input for moving a category in the hierarchy
 */
interface MoveCategoryInput {
  id: UUID;
  parentId: UUID | null;
  index: number;
}

/**
 * Input for filtering categories
 */
interface CategoryFilterInput {
  search?: string;
  type?: CategoryType;
  parentId?: UUID | null;
  isDefault?: boolean;
  isActive?: boolean;
  includeChildren?: boolean;
  userId?: UUID;
}

/**
 * Response type for category operations
 */
interface CategoryResponse {
  success: boolean;
  message?: string;
  category?: Category;
  errors?: Array<{ message: string; field?: string }>;
}

// Import related types
import { UserBase } from '../user';
import { CategoryGoalBase } from './category-goal';
import { TransactionBase } from '../transaction/transaction';
import { CategoryTransactionExecutionInfoBase } from './category-transaction-execution-info';
import { CategoryPeriodRecordBase } from './category-period-record';
