"use client"

import React, { useState } from "react"
import { LogoSelector } from "./LogoSelector"

interface LogoSelectorFieldProps {
  value: string
  onChange: (value: string) => void
}

export function LogoSelectorField({ value, onChange }: LogoSelectorFieldProps) {
  const [showSelector, setShowSelector] = useState(false)

  const handleLogoChange = (logo: string, emoji?: string) => {
    // If emoji is provided, use emoji, otherwise use logo
    const selectedValue = emoji || logo
    onChange(selectedValue)
  }

  const handleClose = () => {
    setShowSelector(false)
  }

  return (
    <>
      <button
        type="button"
        onClick={() => setShowSelector(true)}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors text-left flex items-center gap-2"
      >
        <span className="text-2xl">{value}</span>
        <span className="text-sm text-gray-500 dark:text-gray-400">Click to change logo</span>
      </button>

      {showSelector && (
        <LogoSelector
          selectedLogo={value.startsWith('data:') ? value : undefined}
          selectedEmoji={!value.startsWith('data:') ? value : undefined}
          onLogoChange={handleLogoChange}
          onClose={handleClose}
        />
      )}
    </>
  )
}
