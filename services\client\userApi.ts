/**
 * User API Service
 *
 * Handles user-related API operations using GraphQL client
 */

import { apolloClient } from '@/lib/graphql/client'
import {
  GET_CURRENT_USER,
  GET_USERS,
  GET_USER_BY_ID,
  SEARCH_USERS,
  CREATE_USER,
  UPDATE_USER,
  DELETE_USER,
  UPDATE_USER_PREFERENCES
} from '@/lib/graphql/operations'
import {
  convertToClientError,
  getUserFriendlyErrorMessage
} from '@/lib/graphql/errors'
import { ApiClient } from './apiClient'
import type {
  ApiResponse,
  CreateUserRequest,
  UpdateUserRequest
} from '@/types/api'
import type {
  User,
  CreateUserInput,
  UpdateUserInput,
  UpdateUserPreferencesInput
} from '@/lib/graphql/types'
import type {
  UserConnectionResponse,
  UserConnectionParams
} from '@/types/graphql-connections'

export class UserApiService {

  // ==================== USER CRUD OPERATIONS ====================
  /**
   * Get all users with optional filtering and pagination
   */
  static async getUsers(params?: UserConnectionParams): Promise<UserConnectionResponse> {
    try {
      const { first = 25, after, search, role, isActive } = params || {}

      const { data } = await apolloClient.query({
        query: GET_USERS,
        variables: {
          first,
          after,
          search,
          role: role?.toUpperCase(),
          isActive
        },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.users,
        message: 'Users retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch users:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(id: string): Promise<ApiResponse<User>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_USER_BY_ID,
        variables: { id },
        fetchPolicy: 'cache-first'
      })

      if (!data.user) {
        throw new Error('User not found')
      }

      return {
        data: data.user,
        success: true,
        message: 'User retrieved successfully'
      }
    } catch (error: any) {
      console.error(`Failed to fetch user ${id}:`, error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get current authenticated user
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CURRENT_USER,
        fetchPolicy: 'cache-first'
      })

      if (!data.me) {
        throw new Error('User not authenticated')
      }

      return {
        data: data.me,
        success: true,
        message: 'Current user retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch current user:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Create new user
   */
  static async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    try {
      const input: CreateUserInput = {
        name: data.name,
        email: data.email,
        password: data.password,
        avatar: data.avatar,
        role: data.role?.toUpperCase() as any
      }

      const { data: result } = await apolloClient.mutate({
        mutation: CREATE_USER,
        variables: { input },
        refetchQueries: ['GetUsers']
      })

      return {
        data: result.createUser,
        success: true,
        message: 'User created successfully'
      }
    } catch (error: any) {
      console.error('Failed to create user:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Update user
   */
  static async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    try {
      const input: UpdateUserInput = {
        name: data.name,
        email: data.email,
        avatar: data.avatar,
        role: data.role?.toUpperCase() as any,
        isActive: data.isActive
      }

      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_USER,
        variables: { id, input },
        refetchQueries: ['GetUsers', 'GetUserById', 'GetCurrentUser']
      })

      return {
        data: result.updateUser,
        success: true,
        message: 'User updated successfully'
      }
    } catch (error: any) {
      console.error(`Failed to update user ${id}:`, error)
      throw convertToClientError(error)
    }
  }

  /**
   * Update current user profile
   */
  static async updateProfile(data: UpdateUserRequest): Promise<ApiResponse<User>> {
    try {
      // Get current user first to get the ID
      const currentUser = await this.getCurrentUser()
      if (!currentUser.data) {
        throw new Error('Current user not found')
      }
      return await this.updateUser(currentUser.data.id, data)
    } catch (error: any) {
      console.error('Failed to update profile:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Delete user
   */
  static async deleteUser(id: string): Promise<ApiResponse<void>> {
    try {
      const { data: result } = await apolloClient.mutate({
        mutation: DELETE_USER,
        variables: { id },
        refetchQueries: ['GetUsers']
      })

      return {
        data: undefined,
        success: result.deleteUser,
        message: 'User deleted successfully'
      }
    } catch (error: any) {
      console.error(`Failed to delete user ${id}:`, error)
      throw convertToClientError(error)
    }
  }

  // ==================== USER MANAGEMENT OPERATIONS ====================

  /**
   * Change user password
   */
  static async changePassword(data: {
    currentPassword: string
    newPassword: string
  }): Promise<ApiResponse<void>> {
    try {
      return await ApiClient.post<ApiResponse<void>>('/users/change-password', data)
    } catch (error) {
      console.error('Failed to change password:', error)
      throw error
    }
  }

  /**
   * Update user preferences (via user endpoint)
   */
  static async updatePreferences(data: {
    language?: string
    currency?: string
    timezone?: string
    notifications?: boolean
  }): Promise<ApiResponse<User>> {
    try {
      const input: UpdateUserPreferencesInput = {
        currency: data.currency,
        language: data.language?.toUpperCase() as any,
        // Note: timezone and notifications are not in GraphQL schema yet
        // You may need to extend the schema or handle differently
      }

      await apolloClient.mutate({
        mutation: UPDATE_USER_PREFERENCES,
        variables: { input },
        refetchQueries: ['GetCurrentUser']
      })

      // Return the updated user (need to fetch it)
      const updatedUser = await this.getCurrentUser()
      return updatedUser
    } catch (error: any) {
      console.error('Failed to update preferences:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Update user preferences by preferences ID
   */
  static async updatePreferencesById(id: string, data: {
    name?: string
    description?: string
    currency?: string
    language?: 'vi' | 'en'
    theme?: 'light' | 'dark' | 'system'
    dateFormat?: string
    numberFormat?: string
    isActive?: boolean
  }): Promise<ApiResponse<any>> {
    try {
      const input: UpdateUserPreferencesInput = {
        name: data.name,
        currency: data.currency,
        language: data.language?.toUpperCase() as any,
        theme: data.theme?.toUpperCase() as any,
        dateFormat: data.dateFormat,
        numberFormat: data.numberFormat
      }

      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_USER_PREFERENCES,
        variables: { input },
        refetchQueries: ['GetCurrentUser']
      })

      return {
        data: result.updateUserPreferences,
        success: true,
        message: 'User preferences updated successfully'
      }
    } catch (error: any) {
      console.error('Failed to update preferences:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get user preferences by preferences ID
   */
  static async getPreferencesById(id: string): Promise<ApiResponse<any>> {
    try {
      return await ApiClient.get<ApiResponse<any>>(`/user-preferences/${id}`)
    } catch (error) {
      console.error(`Failed to fetch preferences ${id}:`, error)
      throw error
    }
  }

  /**
   * Delete user preferences by preferences ID
   */
  static async deletePreferencesById(id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    try {
      return await ApiClient.delete<ApiResponse<{ success: boolean; message: string }>>(`/user-preferences/${id}`)
    } catch (error) {
      console.error(`Failed to delete preferences ${id}:`, error)
      throw error
    }
  }

  // ==================== USER STATISTICS ====================

  /**
   * Get user statistics
   */
  static async getUserStats(): Promise<ApiResponse<{
    totalUsers: number
    activeUsers: number
    newUsersThisMonth: number
    userGrowthRate: number
  }>> {
    try {
      return await ApiClient.get<ApiResponse<any>>('/users/stats')
    } catch (error) {
      console.error('Failed to fetch user stats:', error)
      throw error
    }
  }

  /**
   * Get user activity
   */
  static async getUserActivity(userId?: string): Promise<ApiResponse<{
    lastLogin: string
    loginCount: number
    transactionCount: number
    accountCount: number
  }>> {
    try {
      const endpoint = userId ? `/users/${userId}/activity` : '/users/me/activity'
      return await ApiClient.get<ApiResponse<any>>(endpoint)
    } catch (error) {
      console.error('Failed to fetch user activity:', error)
      throw error
    }
  }

  // ==================== CONVENIENCE METHODS ====================

  /**
   * Search users by query
   */
  static async searchUsers(query: string, first: number = 10): Promise<UserConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: SEARCH_USERS,
        variables: { search: query, first },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.users,
        message: 'Users search completed successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to search users:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Check if email is available
   */
  static async checkEmailAvailability(email: string): Promise<ApiResponse<{ available: boolean }>> {
    try {
      const response = await this.getUsers({ search: email, first: 1 })
      const users = response.success && response.data ? response.data.edges : []
      return {
        success: true,
        data: { available: users.length === 0 }
      }
    } catch (error) {
      console.error('Failed to check email availability:', error)
      throw error
    }
  }
}
