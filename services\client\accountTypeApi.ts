/**
 * Account Type API Service
 *
 * Handles account type operations using REST API and returns connection format
 */
import { ApiClient } from './apiClient'
import type { AccountType } from '@/lib/generated/prisma'
import type {
  ApiResponse,
  CreateAccountTypeRequest,
  UpdateAccountTypeRequest,
  AccountTypeQueryParams
} from '@/types/api'
import type {
  AccountTypeConnectionResponse,
  AccountTypeConnectionParams
} from '@/types/graphql-connections'
import { createConnectionFromArray } from '@/lib/utils/connection-helpers'

export class AccountTypeApiService {
  /**
   * Get all account types using REST API and return connection format
   */
  static async getAccountTypes(params: AccountTypeConnectionParams = {}): Promise<AccountTypeConnectionResponse> {
    try {
      // Convert connection params to REST params
      const { first = 25, after, search, category } = params
      const page = after ? parseInt(after) + 1 : 1
      const limit = first
      
      const restParams: AccountTypeQueryParams = {
        page,
        limit,
        search,
        category
      }

      const response = await ApiClient.get<ApiResponse<AccountType[]>>('/account-types', restParams)
      
      if (response.success && response.data) {
        // Convert to connection format
        const connection = createConnectionFromArray<AccountType>(
          response.data,
          (page - 1) * limit,
          limit
        )
        
        return {
          success: true,
          data: connection,
          message: 'Account types retrieved successfully',
          timestamp: new Date().toISOString()
        }
      } else {
        return {
          success: false,
          data: createConnectionFromArray<AccountType>([], 0, 0),
          error: response.error || 'Failed to fetch account types',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error: any) {
      console.error('Error fetching account types:', error)
      return {
        success: false,
        data: createConnectionFromArray<AccountType>([], 0, 0),
        error: error?.message || 'Failed to fetch account types',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Get account type by ID using REST API
   */
  static async getAccountType(id: string): Promise<ApiResponse<AccountType>> {
    try {
      const response = await ApiClient.get<ApiResponse<AccountType>>(`/account-types/${id}`)
      
      return {
        success: response.success,
        data: response.data,
        error: response.error,
        message: response.success ? 'Account type retrieved successfully' : undefined
      }
    } catch (error: any) {
      console.error(`Failed to fetch account type ${id}:`, error)
      return {
        success: false,
        error: error?.message || 'Failed to fetch account type'
      }
    }
  }

  /**
   * Create new account type using REST API
   */
  static async createAccountType(data: CreateAccountTypeRequest): Promise<ApiResponse<AccountType>> {
    try {
      const response = await ApiClient.post<ApiResponse<AccountType>>('/account-types', data)
      
      return {
        success: response.success,
        data: response.data,
        error: response.error,
        message: response.success ? 'Account type created successfully' : undefined
      }
    } catch (error: any) {
      console.error('Error creating account type:', error)
      return {
        success: false,
        error: error?.message || 'Failed to create account type'
      }
    }
  }

  /**
   * Update account type using REST API
   */
  static async updateAccountType(id: string, data: UpdateAccountTypeRequest): Promise<ApiResponse<AccountType>> {
    try {
      const response = await ApiClient.put<ApiResponse<AccountType>>(`/account-types/${id}`, data)
      
      return {
        success: response.success,
        data: response.data,
        error: response.error,
        message: response.success ? 'Account type updated successfully' : undefined
      }
    } catch (error: any) {
      console.error(`Error updating account type ${id}:`, error)
      return {
        success: false,
        error: error?.message || 'Failed to update account type'
      }
    }
  }

  /**
   * Delete account type using REST API
   */
  static async deleteAccountType(id: string): Promise<ApiResponse<void>> {
    try {
      const response = await ApiClient.delete<ApiResponse<void>>(`/account-types/${id}`)
      
      return {
        success: response.success,
        data: response.data,
        error: response.error,
        message: response.success ? 'Account type deleted successfully' : undefined
      }
    } catch (error: any) {
      console.error(`Error deleting account type ${id}:`, error)
      return {
        success: false,
        error: error?.message || 'Failed to delete account type'
      }
    }
  }

  /**
   * Search account types
   */
  static async searchAccountTypes(query: string, limit: number = 10): Promise<AccountTypeConnectionResponse> {
    return this.getAccountTypes({ search: query, first: limit })
  }
}
