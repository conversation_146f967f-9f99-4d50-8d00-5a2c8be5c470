"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'

export type Language = 'en' | 'vi' | 'zh'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

interface LanguageProviderProps {
  children: React.ReactNode
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('vi')

  // Load language from localStorage on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && ['en', 'vi', 'zh'].includes(savedLanguage)) {
      setLanguageState(savedLanguage)
    }
  }, [])

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    localStorage.setItem('language', lang)
  }

  // Translation function
  const t = (key: string): string => {
    const translations = getTranslations(language)
    return translations[key] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

// Translation data
const getTranslations = (lang: Language): Record<string, string> => {
  const translations = {
    en: {
      // Navigation
      'nav.dashboard': 'Dashboard',
      'nav.transactions': 'Transactions',
      'nav.accounts': 'Accounts',
      'nav.categories': 'Categories',
      'nav.CategoryGoal': 'CategoryGoal',
      'nav.spending_limits': 'Spending Limits',
      'nav.recurring': 'Recurring',
      'nav.tags': 'Tags',
      'nav.analytics': 'Analytics',
      'nav.users': 'Users',
      'nav.admin': 'Admin',
      'nav.settings': 'Settings',

      // Dashboard
      'dashboard.welcome': 'Welcome back',
      'dashboard.financial_overview': 'Financial overview for',
      'dashboard.total_balance': 'Total Balance',
      'dashboard.monthly_income': 'Monthly Income',
      'dashboard.monthly_expense': 'Monthly Expense',
      'dashboard.net_income': 'Net Income',
      'dashboard.spending_limit': 'Spending Limit',
      'dashboard.budget_overview': 'Budget Overview',
      'dashboard.recent_transactions': 'Recent Transactions',

      // Common
      'common.add': 'Add',
      'common.edit': 'Edit',
      'common.delete': 'Delete',
      'common.save': 'Save',
      'common.cancel': 'Cancel',
      'common.close': 'Close',
      'common.search': 'Search',
      'common.filter': 'Filter',
      'common.export': 'Export',
      'common.import': 'Import',
      'common.loading': 'Loading...',
      'common.no_data': 'No data available',
      'common.confirm_delete': 'Are you sure you want to delete this item?',

      // Forms
      'form.name': 'Name',
      'form.description': 'Description',
      'form.amount': 'Amount',
      'form.date': 'Date',
      'form.category': 'Category',
      'form.account': 'Account',
      'form.type': 'Type',
      'form.tags': 'Tags',
      'form.required': 'This field is required',

      // Transaction types
      'transaction.income': 'Income',
      'transaction.expense': 'Expense',
      'transaction.transfer': 'Transfer',

      // Account types
      'account.cash': 'Cash',
      'account.bank': 'Bank',
      'account.credit': 'Credit',
      'account.investment': 'Investment',

      // Languages
      'language.english': 'English',
      'language.vietnamese': 'Tiếng Việt',
      'language.chinese': '中文',

      // Time periods
      'period.daily': 'Daily',
      'period.weekly': 'Weekly',
      'period.monthly': 'Monthly',
      'period.yearly': 'Yearly',

      // Status
      'status.active': 'Active',
      'status.inactive': 'Inactive',
      'status.completed': 'Completed',
      'status.pending': 'Pending',

      // User
      'user.profile': 'User Profile',
      'user.admin_profile': 'Admin Profile',

      // Accounts
      'accounts.title': 'Accounts',
      'accounts.subtitle': 'Manage your financial accounts',
      'accounts.add_account': 'Add Account',
      'accounts.edit_account': 'Edit Account',
      'accounts.account_name': 'Account Name',
      'accounts.account_type': 'Account Type',
      'accounts.balance': 'Balance',
      'accounts.currency': 'Currency',
      'accounts.description': 'Description',
      'accounts.color': 'Color',
      'accounts.logo': 'Logo',
    },
    vi: {
      // Navigation
      'nav.dashboard': 'Tổng quan',
      'nav.transactions': 'Giao dịch',
      'nav.accounts': 'Tài khoản',
      'nav.categories': 'Danh mục',
      'nav.CategoryGoal': 'Ngân sách',
      'nav.spending_limits': 'Hạn mức chi tiêu',
      'nav.recurring': 'Định kỳ',
      'nav.tags': 'Thẻ',
      'nav.analytics': 'Phân tích',
      'nav.users': 'Người dùng',
      'nav.admin': 'Quản trị',
      'nav.settings': 'Cài đặt',

      // Dashboard
      'dashboard.welcome': 'Chào mừng trở lại',
      'dashboard.financial_overview': 'Tổng quan tài chính cho',
      'dashboard.total_balance': 'Tổng số dư',
      'dashboard.monthly_income': 'Thu nhập tháng',
      'dashboard.monthly_expense': 'Chi tiêu tháng',
      'dashboard.net_income': 'Thu nhập ròng',
      'dashboard.spending_limit': 'Hạn mức chi tiêu',
      'dashboard.budget_overview': 'Tổng quan ngân sách',
      'dashboard.recent_transactions': 'Giao dịch gần đây',

      // Common
      'common.add': 'Thêm',
      'common.edit': 'Sửa',
      'common.delete': 'Xóa',
      'common.save': 'Lưu',
      'common.cancel': 'Hủy',
      'common.close': 'Đóng',
      'common.search': 'Tìm kiếm',
      'common.filter': 'Lọc',
      'common.export': 'Xuất',
      'common.import': 'Nhập',
      'common.loading': 'Đang tải...',
      'common.no_data': 'Không có dữ liệu',
      'common.confirm_delete': 'Bạn có chắc chắn muốn xóa mục này?',

      // Forms
      'form.name': 'Tên',
      'form.description': 'Mô tả',
      'form.amount': 'Số tiền',
      'form.date': 'Ngày',
      'form.category': 'Danh mục',
      'form.account': 'Tài khoản',
      'form.type': 'Loại',
      'form.tags': 'Thẻ',
      'form.required': 'Trường này là bắt buộc',

      // Transaction types
      'transaction.income': 'Thu nhập',
      'transaction.expense': 'Chi tiêu',
      'transaction.transfer': 'Chuyển khoản',

      // Account types
      'account.cash': 'Tiền mặt',
      'account.bank': 'Ngân hàng',
      'account.credit': 'Thẻ tín dụng',
      'account.investment': 'Đầu tư',

      // Languages
      'language.english': 'English',
      'language.vietnamese': 'Tiếng Việt',
      'language.chinese': '中文',

      // Time periods
      'period.daily': 'Hàng ngày',
      'period.weekly': 'Hàng tuần',
      'period.monthly': 'Hàng tháng',
      'period.yearly': 'Hàng năm',

      // Status
      'status.active': 'Hoạt động',
      'status.inactive': 'Không hoạt động',
      'status.completed': 'Hoàn thành',
      'status.pending': 'Đang chờ',

      // User
      'user.profile': 'Hồ sơ người dùng',
      'user.admin_profile': 'Hồ sơ quản trị',

      // Accounts
      'accounts.title': 'Tài khoản',
      'accounts.subtitle': 'Quản lý các tài khoản tài chính của bạn',
      'accounts.add_account': 'Thêm tài khoản',
      'accounts.edit_account': 'Sửa tài khoản',
      'accounts.account_name': 'Tên tài khoản',
      'accounts.account_type': 'Loại tài khoản',
      'accounts.balance': 'Số dư',
      'accounts.currency': 'Tiền tệ',
      'accounts.description': 'Mô tả',
      'accounts.color': 'Màu sắc',
      'accounts.logo': 'Logo',
    },
    zh: {
      // Navigation
      'nav.dashboard': '仪表板',
      'nav.transactions': '交易',
      'nav.accounts': '账户',
      'nav.categories': '类别',
      'nav.CategoryGoal': '预算',
      'nav.spending_limits': '支出限额',
      'nav.recurring': '定期',
      'nav.tags': '标签',
      'nav.analytics': '分析',
      'nav.users': '用户',
      'nav.admin': '管理',
      'nav.settings': '设置',

      // Dashboard
      'dashboard.welcome': '欢迎回来',
      'dashboard.financial_overview': '财务概览',
      'dashboard.total_balance': '总余额',
      'dashboard.monthly_income': '月收入',
      'dashboard.monthly_expense': '月支出',
      'dashboard.net_income': '净收入',
      'dashboard.spending_limit': '支出限额',
      'dashboard.budget_overview': '预算概览',
      'dashboard.recent_transactions': '最近交易',

      // Common
      'common.add': '添加',
      'common.edit': '编辑',
      'common.delete': '删除',
      'common.save': '保存',
      'common.cancel': '取消',
      'common.close': '关闭',
      'common.search': '搜索',
      'common.filter': '筛选',
      'common.export': '导出',
      'common.import': '导入',
      'common.loading': '加载中...',
      'common.no_data': '无数据',
      'common.confirm_delete': '您确定要删除此项吗？',

      // Forms
      'form.name': '名称',
      'form.description': '描述',
      'form.amount': '金额',
      'form.date': '日期',
      'form.category': '类别',
      'form.account': '账户',
      'form.type': '类型',
      'form.tags': '标签',
      'form.required': '此字段为必填项',

      // Transaction types
      'transaction.income': '收入',
      'transaction.expense': '支出',
      'transaction.transfer': '转账',

      // Account types
      'account.cash': '现金',
      'account.bank': '银行',
      'account.credit': '信用卡',
      'account.investment': '投资',

      // Languages
      'language.english': 'English',
      'language.vietnamese': 'Tiếng Việt',
      'language.chinese': '中文',

      // Time periods
      'period.daily': '每日',
      'period.weekly': '每周',
      'period.monthly': '每月',
      'period.yearly': '每年',

      // Status
      'status.active': '活跃',
      'status.inactive': '非活跃',
      'status.completed': '已完成',
      'status.pending': '待处理',

      // User
      'user.profile': '用户资料',
      'user.admin_profile': '管理员资料',

      // Accounts
      'accounts.title': '账户',
      'accounts.subtitle': '管理您的财务账户',
      'accounts.add_account': '添加账户',
      'accounts.edit_account': '编辑账户',
      'accounts.account_name': '账户名称',
      'accounts.account_type': '账户类型',
      'accounts.balance': '余额',
      'accounts.currency': '货币',
      'accounts.description': '描述',
      'accounts.color': '颜色',
      'accounts.logo': '标志',
    }
  }

  return translations[lang] || translations.vi
}
