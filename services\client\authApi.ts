/**
 * Authentication API Service
 *
 * Handles authentication-related API operations using ApiClient as base layer
 * Follows the established static class pattern for consistency
 *
 * Features:
 * - User login/logout with JWT tokens
 * - User registration with validation
 * - Token management (access/refresh tokens)
 * - Password management (change, reset)
 * - Authentication state management
 * - Automatic token storage in localStorage
 * - Token validation and expiry checking
 *
 * Usage:
 * ```typescript
 * import { AuthApiService } from '@/services/api'
 *
 * // Login
 * const result = await AuthApiService.login({ email, password })
 * if (result.success) {
 *   const { user, tokens } = result.data
 *   // Tokens are automatically stored
 * }
 *
 * // Get current user
 * const userResult = await AuthApiService.getCurrentUser()
 *
 * // Logout
 * await AuthApiService.logout()
 *
 * // Check authentication status
 * const isAuth = AuthApiService.isAuthenticated()
 * ```
 */

import { ApiClient } from './apiClient'
import type {
  ApiResponse,
  LoginRequest,
  RegisterRequest,
  ChangePasswordRequest,
  LoginResponse,
  RefreshTokenResponse,
  TokenPair
} from '@/types/api'
import type { User } from '@/lib/generated/prisma'

export class AuthApiService {

  // ==================== AUTHENTICATION OPERATIONS ====================

  /**
   * Authenticate user with email and password
   */
  static async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await ApiClient.post<ApiResponse<LoginResponse>>('/auth/login', credentials)
      // ApiClient returns full API response structure
      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  /**
   * Register new user account
   */
  static async register(userData: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await ApiClient.post<ApiResponse<LoginResponse>>('/auth/register', userData)
      return response
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  /**
   * Logout current user
   */
  static async logout(): Promise<ApiResponse<void>> {
    try {
      const response = await ApiClient.post<ApiResponse<void>>('/auth/logout')
      
      // Clear stored tokens from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      }
      
      return response
    } catch (error) {
      console.error('Logout failed:', error)
      // Even if logout fails on server, clear local tokens
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      }
      throw error
    }
  }

  /**
   * Get current authenticated user information
   */
  static async getCurrentUser(): Promise<ApiResponse<Omit<User, 'password'>>> {
    try {
      return await ApiClient.get<ApiResponse<Omit<User, 'password'>>>('/auth/me')
    } catch (error) {
      console.error('Failed to get current user:', error)
      throw error
    }
  }

  /**
   * Refresh JWT access token using refresh token
   */
  static async refreshToken(): Promise<ApiResponse<RefreshTokenResponse>> {
    try {
      const refreshToken = typeof window !== 'undefined' 
        ? localStorage.getItem('refreshToken') 
        : null

      if (!refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await ApiClient.post<ApiResponse<RefreshTokenResponse>>('/auth/refresh', {
        refreshToken
      })

      // Update stored tokens
      if (typeof window !== 'undefined' && response.data?.tokens) {
        localStorage.setItem('accessToken', response.data.tokens.accessToken)
        localStorage.setItem('refreshToken', response.data.tokens.refreshToken)
      }

      return response
    } catch (error) {
      console.error('Token refresh failed:', error)
      // Clear invalid tokens
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      }
      throw error
    }
  }

  // ==================== PASSWORD MANAGEMENT ====================

  /**
   * Change user password
   */
  static async changePassword(passwordData: ChangePasswordRequest): Promise<ApiResponse<void>> {
    try {
      return await ApiClient.post<ApiResponse<void>>('/auth/change-password', passwordData)
    } catch (error) {
      console.error('Password change failed:', error)
      throw error
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<ApiResponse<void>> {
    try {
      return await ApiClient.post<ApiResponse<void>>('/auth/forgot-password', { email })
    } catch (error) {
      console.error('Password reset request failed:', error)
      throw error
    }
  }

  /**   
   * Reset password with token
   */
  static async resetPassword(token: string, newPassword: string): Promise<ApiResponse<void>> {
    try {
      return await ApiClient.post<ApiResponse<void>>('/auth/reset-password', {
        token,
        newPassword
      })
    } catch (error) {
      console.error('Password reset failed:', error)
      throw error
    }
  }

  // ==================== TOKEN MANAGEMENT ====================

  /**
   * Verify if current token is valid
   */
  static async verifyToken(): Promise<ApiResponse<{ valid: boolean }>> {
    try {
      return await ApiClient.get<ApiResponse<{ valid: boolean }>>('/auth/verify')
    } catch (error) {
      console.error('Token verification failed:', error)
      throw error
    }
  }

  /**
   * Get stored access token from localStorage
   */
  static getStoredToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('accessToken')
  }

  /**
   * Get stored refresh token from localStorage
   */
  static getStoredRefreshToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('refreshToken')
  }

  /**
   * Store authentication tokens in localStorage
   */
  static storeTokens(tokens: TokenPair): void {
    if (typeof window === 'undefined') return
    
    localStorage.setItem('accessToken', tokens.accessToken)
    localStorage.setItem('refreshToken', tokens.refreshToken)
  }

  /**
   * Clear stored authentication tokens
   */
  static clearStoredTokens(): void {
    if (typeof window === 'undefined') return
    
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
  }

  /**
   * Check if user is authenticated (has valid token)
   */
  static isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false
    
    const token = localStorage.getItem('accessToken')
    if (!token) return false

    try {
      // Basic JWT token validation (check if not expired)
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Date.now() / 1000
      
      return payload.exp > currentTime
    } catch {
      return false
    }
  }
}
