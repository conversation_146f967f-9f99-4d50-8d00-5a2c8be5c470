/**
 * GraphQL TypeScript Type Definitions
 * 
 * Type definitions for GraphQL schema to ensure type safety
 */

// Scalar types
export type UUID = string
export type DateTime = string
export type JSON = any

// ====================================
// 1. Enum Types
// ====================================

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user'
}

export enum TransactionKind {
  INCOME = 'income',
  EXPENSE = 'expense',
  TRANSFER = 'transfer'
}

export enum CategoryType {
  INCOME = 'income',
  EXPENSE = 'expense',
  TRANSFER = 'transfer'
}

export enum AccountTypeCategory {
  CASH = 'cash',
  BANK = 'bank',
  CREDIT_CARD = 'credit_card',
  INVESTMENT = 'investment',
  LOAN = 'loan',
  OTHER = 'other'
}

export enum Language {
  EN = 'en',
  VI = 'vi'
}

export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system'
}

export enum Period {
  NONE = 'none',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
  CUSTOM = 'custom'
}

// ====================================
// 2. Base Interfaces (No Relations)
// ====================================

export interface UserBase {
  id: UUID
  email: string
  firstName: string
  lastName: string
  avatar?: string
  phoneNumber?: string
  isActive: boolean
  lastLogin?: DateTime
  emailVerified: boolean
  phoneNumberVerified: boolean
  timezone: string
  locale: string
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface UserPreferencesBase {
  id: UUID
  userId: UUID
  name: string
  currency: string
  language: Language
  theme: Theme
  dateFormat?: string
  numberFormat?: string
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
}

export interface AccountTypeBase {
  id: UUID
  name: string
  description?: string
  category: AccountTypeCategory
  color: string
  icon?: string
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
}

export interface AccountBase {
  id: UUID
  name: string
  description?: string
  balance: number
  currency: string
  color: string
  icon?: string
  isActive: boolean
  userId: UUID
  accountTypeId: UUID
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface CategoryBase {
  id: UUID
  name: string
  description?: string
  type: CategoryType 
  color: string
  icon?: string
  parentId?: UUID
  isActive: boolean
  userId: UUID
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface TransactionBase {
  id: UUID
  kind: TransactionKind
  amount: number
  date: DateTime
  description?: string
  location?: string
  note?: string
  attachments: string[]
  isCleared: boolean
  isReconciled: boolean
  userId: UUID
  accountId?: UUID
  categoryId?: UUID
  fromAccountId?: UUID
  toAccountId?: UUID
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface ContactBase {
  id: UUID
  name: string
  email?: string
  phone?: string
  address?: string
  note?: string
  avatar?: string
  userId: UUID
  contactTypeId: UUID
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface RecurringTransactionBase {
  id: UUID
  name: string
  description?: string
  amount: number
  kind: TransactionKind
  frequency: string
  nextDate: DateTime
  endDate?: DateTime
  isActive: boolean
  userId: UUID
  accountId?: UUID
  categoryId?: UUID
  fromAccountId?: UUID
  toAccountId?: UUID
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface CategoryGoalBase {
  id: UUID
  name: string
  description?: string
  targetAmount: number
  currentAmount: number
  period: Period
  startDate: DateTime
  endDate?: DateTime
  isRecurring: boolean
  color: string
  isDefault: boolean
  isActive: boolean
  categoryId: UUID
  userId: UUID
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface BankInfoBase {
  id: UUID
  bankName: string
  accountNumber: string
  accountHolderName: string
  branchName?: string
  swiftCode?: string
  routingNumber?: string
  note?: string
  userId: UUID
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface DebitCardBase {
  id: UUID
  cardNumber: string
  cardHolderName: string
  expiryDate: string
  bankName: string
  cardType?: string
  note?: string
  userId: UUID
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface AccountPaymentAppBase {
  id: UUID
  appName: string
  accountIdentifier: string
  note?: string
  userId: UUID
  accountId: UUID
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

export interface ContactTypeBase {
  id: UUID
  name: string
  description?: string
  color: string
  icon?: string
  isActive: boolean
  createdAt: DateTime
  updatedAt: DateTime
  deletedAt?: DateTime
}

// ====================================
// 3. WithRelations Interfaces
// ====================================

export interface UserWithRelations extends UserBase {
  preferences?: UserPreferencesWithRelations
  accounts: AccountWithRelations[]
  categories: CategoryWithRelations[]
  transactions: TransactionWithRelations[]
  contacts: ContactWithRelations[]
  recurringTransactions: RecurringTransactionWithRelations[]
  categoryGoals: CategoryGoalWithRelations[]
  bankInfos: BankInfoWithRelations[]
  debitCards: DebitCardWithRelations[]
  accountPaymentApps: AccountPaymentAppWithRelations[]
}

export interface UserPreferencesWithRelations extends UserPreferencesBase {
  user: UserWithRelations
}

export interface AccountTypeWithRelations extends AccountTypeBase {
  accounts: AccountWithRelations[]
}

export interface AccountWithRelations extends AccountBase {
  user: UserWithRelations
  accountType: AccountTypeWithRelations
  transactions: TransactionWithRelations[]
  transferFromTransactions: TransactionWithRelations[]
  transferToTransactions: TransactionWithRelations[]
  accountPaymentApps: AccountPaymentAppWithRelations[]
}

export interface CategoryWithRelations extends CategoryBase {
  user: UserWithRelations
  parent?: CategoryWithRelations
  children: CategoryWithRelations[]
  transactions: TransactionWithRelations[]
  categoryGoals: CategoryGoalWithRelations[]
  recurringTransactions: RecurringTransactionWithRelations[]
}

export interface TransactionWithRelations extends TransactionBase {
  user: UserWithRelations
  account?: AccountWithRelations
  category?: CategoryWithRelations
  fromAccount?: AccountWithRelations
  toAccount?: AccountWithRelations
}

export interface ContactWithRelations extends ContactBase {
  user: UserWithRelations
  contactType: ContactTypeWithRelations
}

export interface RecurringTransactionWithRelations extends RecurringTransactionBase {
  user: UserWithRelations
  account?: AccountWithRelations
  category?: CategoryWithRelations
  fromAccount?: AccountWithRelations
  toAccount?: AccountWithRelations
}

export interface CategoryGoalWithRelations extends CategoryGoalBase {
  user: UserWithRelations
  category: CategoryWithRelations
}

export interface BankInfoWithRelations extends BankInfoBase {
  user: UserWithRelations
}

export interface DebitCardWithRelations extends DebitCardBase {
  user: UserWithRelations
}

export interface AccountPaymentAppWithRelations extends AccountPaymentAppBase {
  user: UserWithRelations
  account: AccountWithRelations
}

export interface ContactTypeWithRelations extends ContactTypeBase {
  contacts: ContactWithRelations[]
}

// ====================================
// 4. Common Type Combinations
// ====================================

export type UserWithAccounts = UserBase & {
  accounts: AccountWithRelations[]
}

export type AccountWithBankInfo = AccountBase & {
  bankInfos: BankInfoWithRelations[]
}

export type CategoryWithGoals = CategoryBase & {
  categoryGoals: CategoryGoalBase[]
}

export type CategoryWithGoal = CategoryBase & {
  categoryGoal?: CategoryGoalBase
}

export type TransactionWithCategory = TransactionBase & {
  category?: CategoryWithRelations
}

export type TransactionWithAccounts = TransactionBase & {
  fromAccount?: AccountWithRelations
  toAccount?: AccountWithRelations
}

// ====================================
// 5. Union Types
// ====================================

export type User = UserBase | UserWithRelations
export type UserPreferences = UserPreferencesBase | UserPreferencesWithRelations
export type AccountType = AccountTypeBase | AccountTypeWithRelations
export type Account = AccountBase | AccountWithRelations
export type Category = CategoryBase | CategoryWithRelations
export type Transaction = TransactionBase | TransactionWithRelations
export type Contact = ContactBase | ContactWithRelations
export type RecurringTransaction = RecurringTransactionBase | RecurringTransactionWithRelations
export type CategoryGoal = CategoryGoalBase | CategoryGoalWithRelations
export type BankInfo = BankInfoBase | BankInfoWithRelations
export type DebitCard = DebitCardBase | DebitCardWithRelations
export type AccountPaymentApp = AccountPaymentAppBase | AccountPaymentAppWithRelations
export type ContactType = ContactTypeBase | ContactTypeWithRelations

// ====================================
// 6. Pagination Types
// ====================================

export interface Connection<T> {
  edges: Array<{
    node: T
    cursor: string
  }>
  pageInfo: {
    hasNextPage: boolean
    hasPreviousPage: boolean
    startCursor?: string
    endCursor?: string
  }
  totalCount: number
}

export type UserConnection = Connection<User>
export type AccountConnection = Connection<Account>
export type CategoryConnection = Connection<Category>
export type TransactionConnection = Connection<Transaction>
export type ContactConnection = Connection<Contact>
export type RecurringTransactionConnection = Connection<RecurringTransaction>
export type CategoryGoalConnection = Connection<CategoryGoal>
export type BankInfoConnection = Connection<BankInfo>
export type DebitCardConnection = Connection<DebitCard>
export type AccountPaymentAppConnection = Connection<AccountPaymentApp>
export type ContactTypeConnection = Connection<ContactType>
