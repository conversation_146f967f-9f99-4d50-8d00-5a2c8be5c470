/* Sidebar Smooth Animations */

/* Keyframe Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Disabled sidebar animation classes - using CSS transitions instead
.sidebar-enter {
  animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar-entered {
  transform: translateX(0);
  opacity: 1;
}

.sidebar-exit {
  animation: slideOutRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar-exited {
  transform: translateX(100%);
  opacity: 0;
}

.sidebar-overlay-enter {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sidebar-overlay-exit {
  animation: fadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}
*/

/* Disabled content animations to prevent double animation
.sidebar-content-enter {
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation-delay: 0.1s;
  opacity: 0;
}
*/

/* Smooth Transitions */
.sidebar-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-resize-transition {
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-resize-transition.resizing {
  transition: none;
}

/* Content Area Transitions */
.content-area-transition {
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              margin-right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-area-transition.resizing {
  transition: none;
}

/* Responsive Form Layouts */
.sidebar-form-responsive {
  display: grid;
  gap: 1.5rem;
  padding: 1.5rem;
  padding-bottom: 6rem; /* Space for fixed footer */
}

/* Form Section Styles */
.sidebar-form-section {
  margin-bottom: 2rem;
}

.form-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.form-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-section-description {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.form-section-toggle {
  background: none;
  border: none;
  font-size: 1rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.form-section-toggle:hover {
  background-color: #f3f4f6;
}

.form-section-content {
  display: grid;
  gap: 1.5rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .form-section-header {
    border-bottom-color: #374151;
  }

  .form-section-title {
    color: #d1d5db;
  }

  .form-section-description {
    color: #9ca3af;
  }

  .form-section-toggle {
    color: #9ca3af;
  }

  .form-section-toggle:hover {
    background-color: #374151;
  }
}

/* Narrow sidebar (< 400px) */
.sidebar-narrow .sidebar-form-responsive {
  grid-template-columns: 1fr;
  gap: 1rem;
  padding: 1rem;
}

/* Medium sidebar (400-600px) */
.sidebar-medium .sidebar-form-responsive {
  grid-template-columns: 1fr;
  gap: 1.5rem;
  padding: 1.5rem;
}

/* Wide sidebar (600-800px) */
.sidebar-wide .sidebar-form-responsive {
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  padding: 2rem;
}

/* Extra wide sidebar (> 800px) */
.sidebar-extra-wide .sidebar-form-responsive {
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
}

/* Field Spanning */
.field-span-1 {
  grid-column: span 1;
}

.field-span-2 {
  grid-column: span 2;
}

.field-span-full {
  grid-column: 1 / -1;
}

/* Form Sections */
.form-section {
  display: contents;
}

.form-section-title {
  grid-column: 1 / -1;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.form-section-title:first-child {
  padding-top: 0;
  border-top: none;
}

/* Dark mode */
.dark .form-section-title {
  color: #f3f4f6;
  border-color: #374151;
}

/* Field Groups for Related Fields */
.field-group {
  display: contents;
}

.field-group-horizontal {
  display: flex;
  gap: 1rem;
  align-items: end;
}

.field-group-horizontal > * {
  flex: 1;
}

/* Responsive Field Widths */
.sidebar-field {
  width: 100%;
  max-width: none;
}

/* Prevent overly wide inputs in extra-wide sidebars */
.sidebar-extra-wide .sidebar-field:not(.field-full-width) {
  max-width: 300px;
}

.field-full-width {
  max-width: none !important;
}

/* Animation States */
.sidebar-animating {
  pointer-events: none;
}

/* Overlay Styles */
.sidebar-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 40;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Content Animation Delays */
.content-item {
  opacity: 0;
  transform: translateY(10px);
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.content-item:nth-child(1) { animation-delay: 0ms; }
.content-item:nth-child(2) { animation-delay: 50ms; }
.content-item:nth-child(3) { animation-delay: 100ms; }
.content-item:nth-child(4) { animation-delay: 150ms; }
.content-item:nth-child(5) { animation-delay: 200ms; }
.content-item:nth-child(6) { animation-delay: 250ms; }
.content-item:nth-child(7) { animation-delay: 300ms; }
.content-item:nth-child(8) { animation-delay: 350ms; }

/* Hover Effects */
.sidebar-field:focus-within {
  transform: translateY(-1px);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-button:active {
  transform: translateY(0);
}

/* Sidebar Content - Hidden Scroll */
.sidebar-content {
  overflow-y: auto;
  overflow-x: hidden;
  /* Hide scrollbar for Webkit browsers (Chrome, Safari, Edge) */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.sidebar-content::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Webkit browsers */
}

.sidebar-form-responsive {
  overflow: visible;
  max-height: none;
}

/* Hide scrollbar for form containers */
.sidebar-form-responsive,
.sidebar-form-section {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.sidebar-form-responsive::-webkit-scrollbar,
.sidebar-form-section::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Webkit browsers */
}

/* Ensure form fields behave properly */
.sidebar-form-responsive > * {
  overflow: visible;
}

/* Allow textarea resize but limit to vertical only */
.sidebar-content textarea {
  resize: vertical;
  overflow-y: auto;
}

/* Normal scroll behavior for input elements */
.sidebar-content input,
.sidebar-content select {
  overflow: visible;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  /* Disable sidebar transitions for users who prefer reduced motion */
  .sidebar-transition {
    transition: none !important;
  }

  .sidebar-resize-transition {
    transition: none !important;
  }

  /* Keep overlay transition minimal */
  .sidebar-overlay {
    transition: opacity 0.1s ease !important;
  }
}

/* Global Hidden Scrollbar for All Sidebar Elements */
.sidebar-content *,
.sidebar-form-responsive *,
.sidebar-form-section *,
[class*="sidebar-"] {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

.sidebar-content *::-webkit-scrollbar,
.sidebar-form-responsive *::-webkit-scrollbar,
.sidebar-form-section *::-webkit-scrollbar,
[class*="sidebar-"]::-webkit-scrollbar {
  display: none !important; /* Hide scrollbar for Webkit browsers */
}

/* Mobile Optimizations */
@media (max-width: 1023px) {
  .sidebar-form-responsive {
    grid-template-columns: 1fr !important;
    gap: 1rem;
    padding: 1rem;
  }

  .field-group-horizontal {
    flex-direction: column;
    gap: 1rem;
  }

  .field-span-2,
  .field-span-full {
    grid-column: 1;
  }
}
