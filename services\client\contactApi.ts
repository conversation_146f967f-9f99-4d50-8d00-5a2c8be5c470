/**
 * Contact API Service
 *
 * Handles contact-related API operations using GraphQL client
 */
import { apolloClient } from '@/lib/graphql/client'
import {
  GET_CONTACTS,
  GET_CONTACT_BY_ID,
  GET_CONTACTS_BY_TYPE,
  SEARCH_CONTACTS,
  CREATE_CONTACT,
  UPDATE_CONTACT,
  DELETE_CONTACT
} from '@/lib/graphql/operations'
import {
  convertToClientError,
  getUserFriendlyErrorMessage
} from '@/lib/graphql/errors'
import type {
  ApiResponse,
  CreateContactRequest,
  UpdateContactRequest
} from '@/types/api'
import type {
  Contact,
  ContactConnection,
  CreateContactInput,
  UpdateContactInput
} from '@/lib/graphql/types'
import type {
  ContactConnectionResponse,
  ContactConnectionParams
} from '@/types/graphql-connections'
export class ContactApiService {
  // ==================== CONTACT CRUD OPERATIONS ====================
  /**
   * Get all contacts with filtering and pagination
   */
  static async getContacts(params?: ContactConnectionParams): Promise<ContactConnectionResponse> {
    try {
      const { first = 25, after, search, contactTypeId, isActive } = params || {}
      const { data } = await apolloClient.query({
        query: GET_CONTACTS,
        variables: {
          first,
          after,
          search,
          contactTypeId,
          isActive
        },
        fetchPolicy: 'cache-first'
      })
      return {
        success: true,
        data: data.contacts,
        message: 'contacts retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch contacts:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get contact by ID
   */
  static async getContactById(id: string): Promise<ApiResponse<Contact>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CONTACT_BY_ID,
        variables: { id },
        fetchPolicy: 'cache-first'
      })
      if (!data.contact) {
        throw new Error('Contact not found')
      }
      return {
        data: data.contact,
        success: true,
        message: 'Contact retrieved successfully'
      }
    } catch (error: any) {
      console.error(`Failed to fetch contact ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Create new contact
   */
  static async createContact(data: CreateContactRequest): Promise<ApiResponse<Contact>> {
    try {
      const input: CreateContactInput = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        address: data.address,
        note: data.description, // Map description to note for GraphQL
        contactTypeId: data.contactTypeId
      }
      const { data: result } = await apolloClient.mutate({
        mutation: CREATE_CONTACT,
        variables: { input },
        refetchQueries: ['GetContacts']
      })
      return {
        data: result.createContact,
        success: true,
        message: 'Contact created successfully'
      }
    } catch (error: any) {
      console.error('Failed to create contact:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Update contact
   */
  static async updateContact(id: string, data: UpdateContactRequest): Promise<ApiResponse<Contact>> {
    try {
      const input: UpdateContactInput = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        address: data.address,
        note: data.description, // Map description to note for GraphQL
        contactTypeId: data.contactTypeId,
        isActive: data.isActive
      }
      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_CONTACT,
        variables: { id, input },
        refetchQueries: ['GetContacts', 'GetContactById']
      })
      return {
        data: result.updateContact,
        success: true,
        message: 'Contact updated successfully'
      }
    } catch (error: any) {
      console.error(`Failed to update contact ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Delete contact
   */
  static async deleteContact(id: string): Promise<ApiResponse<void>> {
    try {
      const { data: result } = await apolloClient.mutate({
        mutation: DELETE_CONTACT,
        variables: { id },
        refetchQueries: ['GetContacts']
      })
      return {
        data: undefined,
        success: result.deleteContact,
        message: 'Contact deleted successfully'
      }
    } catch (error: any) {
      console.error(`Failed to delete contact ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  // ==================== CONVENIENCE METHODS ====================
  /**
   * Search contacts by name
   */
  static async searchContacts(query: string, first: number = 10): Promise<ContactConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: SEARCH_CONTACTS,
        variables: { search: query, first },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.contacts,
        message: 'contacts retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to search contacts:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get active contacts only
   */
  static async getActiveContacts(params?: Omit<ContactConnectionParams, 'isActive'>): Promise<ContactConnectionResponse> {
    return this.getContacts({
      ...params,
      isActive: true
    })
  }
  /**
   * Get contacts by type using GraphQL
   */
  static async getContactsByType(contactTypeId: string): Promise<ApiResponse<Contact[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CONTACTS_BY_TYPE,
        variables: { contactTypeId },
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.contacts.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Contacts retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch contacts by type:', error)
      throw convertToClientError(error)
    }
  }
}
