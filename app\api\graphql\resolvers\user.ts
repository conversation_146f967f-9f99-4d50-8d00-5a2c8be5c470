/**
 * User GraphQL Resolvers
 *
 * Resolvers for User queries and mutations using Prisma directly
 */

import { GraphQLContext, requireAuth, requireAdmin, requireOwnershipOrAdmin } from '../context'
import { prisma } from '@/lib/database'
import { GraphQLError } from 'graphql'
import bcrypt from 'bcryptjs'

interface UserArgs {
  id: string
}

interface UsersArgs {
  first?: number
  after?: string
  search?: string
  role?: 'admin' | 'user'
  isActive?: boolean
}

interface CreateUserArgs {
  input: {
    name: string
    email: string
    password: string
    avatar?: string
    role?: 'admin' | 'user'
    description?: string
    preferences?: {
      currency?: string
      language?: 'vi' | 'en'
      theme?: 'light' | 'dark' | 'system'
      dateFormat?: string
      numberFormat?: string
    }
  }
}

interface UpdateUserArgs {
  id: string
  input: {
    name?: string
    email?: string
    avatar?: string
    role?: 'admin' | 'user'
    description?: string
    isActive?: boolean
  }
}

interface UpdateUserPreferencesArgs {
  input: {
    name?: string
    currency?: string
    language?: 'vi' | 'en'
    theme?: 'light' | 'dark' | 'system'
    dateFormat?: string
    numberFormat?: string
  }
}

// Helper function to build connection response
function buildConnection<T>(
  items: T[],
  totalCount: number,
  first?: number,
  after?: string
) {
  const edges = items.map((item: any, index) => ({
    node: item,
    cursor: Buffer.from(`${after ? parseInt(Buffer.from(after, 'base64').toString()) + index + 1 : index}`).toString('base64')
  }))

  const hasNextPage = first ? items.length === first : false
  const hasPreviousPage = !!after

  return {
    edges,
    pageInfo: {
      hasNextPage,
      hasPreviousPage,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount
  }
}

export const userResolvers = {
  Query: {
    me: async (_: any, __: any, context: GraphQLContext) => {
      const user = requireAuth(context)
      return user
    },

    users: async (_: any, args: UsersArgs, context: GraphQLContext) => {
      requireAdmin(context)

      const { first = 25, after, search, role, isActive } = args

      // Build where clause
      const where: any = {}

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (role) where.role = role
      if (isActive !== undefined) where.isActive = isActive

      // Convert cursor to skip value
      const skip = after ? parseInt(Buffer.from(after, 'base64').toString()) : 0

      const [users, totalCount] = await Promise.all([
        prisma.user.findMany({
          where,
          include: {
            preferences: true
          },
          skip,
          take: first,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.user.count({ where })
      ])

      return buildConnection(users, totalCount, first, after)
    },

    user: async (_: any, args: UserArgs, context: GraphQLContext) => {
      const currentUser = requireAuth(context)

      // Users can only view their own profile unless they're admin
      if (currentUser.role !== 'admin' && currentUser.id !== args.id) {
        throw new GraphQLError('Access denied: insufficient permissions')
      }

      const user = await prisma.user.findUnique({
        where: { id: args.id },
        include: {
          preferences: true
        }
      })

      if (!user) {
        throw new GraphQLError('User not found')
      }

      return user
    }
  },

  Mutation: {
    createUser: async (_: any, args: CreateUserArgs, context: GraphQLContext) => {
      requireAdmin(context)

      try {
        const { input } = args

        // Check if user already exists
        const existingUser = await prisma.user.findUnique({
          where: { email: input.email }
        })

        if (existingUser) {
          throw new GraphQLError('User with this email already exists')
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(input.password, 12)

        // Create user with preferences
        const user = await prisma.user.create({
          data: {
            name: input.name,
            email: input.email,
            password: hashedPassword,
            avatar: input.avatar,
            role: input.role || 'user',
            description: input.description,
            preferences: input.preferences ? {
              create: {
                currency: input.preferences.currency || 'VND',
                language: input.preferences.language || 'vi',
                theme: input.preferences.theme || 'system',
                dateFormat: input.preferences.dateFormat || 'DD/MM/YYYY',
                numberFormat: input.preferences.numberFormat || '#,##0'
              }
            } : undefined
          },
          include: {
            preferences: true
          }
        })

        return user
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to create user')
      }
    },

    updateUser: async (_: any, args: UpdateUserArgs, context: GraphQLContext) => {
      const currentUser = requireAuth(context)

      // Users can only update their own profile unless they're admin
      if (currentUser.role !== 'admin' && currentUser.id !== args.id) {
        throw new GraphQLError('Access denied: insufficient permissions')
      }

      try {
        const { input } = args

        // Check if user exists
        const existingUser = await prisma.user.findUnique({
          where: { id: args.id }
        })

        if (!existingUser) {
          throw new GraphQLError('User not found')
        }

        // Check email uniqueness if email is being updated
        if (input.email && input.email !== existingUser.email) {
          const emailExists = await prisma.user.findUnique({
            where: { email: input.email }
          })

          if (emailExists) {
            throw new GraphQLError('User with this email already exists')
          }
        }

        const user = await prisma.user.update({
          where: { id: args.id },
          data: {
            name: input.name,
            email: input.email,
            avatar: input.avatar,
            role: input.role,
            description: input.description,
            isActive: input.isActive
          },
          include: {
            preferences: true
          }
        })

        return user
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update user')
      }
    },

    deleteUser: async (_: any, args: UserArgs, context: GraphQLContext) => {
      requireAdmin(context)

      try {
        // Check if user exists
        const existingUser = await prisma.user.findUnique({
          where: { id: args.id }
        })

        if (!existingUser) {
          throw new GraphQLError('User not found')
        }

        // Soft delete by setting isActive to false
        await prisma.user.update({
          where: { id: args.id },
          data: { isActive: false }
        })

        return true
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to delete user')
      }
    },

    updateUserPreferences: async (_: any, args: UpdateUserPreferencesArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // Upsert user preferences
        const preferences = await prisma.userPreferences.upsert({
          where: { userId: user.id },
          update: {
            currency: input.currency,
            language: input.language,
            theme: input.theme,
            dateFormat: input.dateFormat,
            numberFormat: input.numberFormat
          },
          create: {
            userId: user.id,
            currency: input.currency || 'VND',
            language: input.language || 'vi',
            theme: input.theme || 'system',
            dateFormat: input.dateFormat || 'DD/MM/YYYY',
            numberFormat: input.numberFormat || '#,##0'
          }
        })

        return preferences
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update user preferences')
      }
    }
  },

  User: {
    preferences: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.preferences) {
        return parent.preferences
      }

      return await context.prisma.userPreferences.findUnique({
        where: { userId: parent.id }
      })
    },

    accounts: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.account.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        },
        include: {
          accountType: true
        }
      })
    },

    categories: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.category.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        }
      })
    },

    contacts: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.contact.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        },
        include: {
          contactType: true
        }
      })
    },

    transactions: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.transaction.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        },
        include: {
          account: { include: { accountType: true } },
          category: true,
          fromAccount: { include: { accountType: true } },
          toAccount: { include: { accountType: true } }
        },
        orderBy: { date: 'desc' },
        take: 50 // Limit to recent transactions
      })
    },

    recurringTransactions: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.recurringTransaction.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        }
      })
    },

    categoryGoals: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.categoryGoal.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        }
      })
    },

    bankInfos: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.bankInfo.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        }
      })
    },

    debitCards: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.debitCard.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        }
      })
    },

    accountPaymentApps: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.accountPaymentApp.findMany({
        where: { 
          userId: parent.id,
          isActive: true 
        }
      })
    }
  }
}
