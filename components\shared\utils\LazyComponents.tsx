"use client"

import React, { Suspense, lazy } from 'react'
import { Skeleton } from '@/components/ui/skeleton'

// Loading fallback components
export const CardSkeleton = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
    <div className="space-y-3">
      <div className="flex items-center gap-3">
        <Skeleton className="w-10 h-10 rounded-full" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      </div>
      <Skeleton className="h-3 w-full" />
      <Skeleton className="h-3 w-2/3" />
      <div className="flex gap-2 pt-2">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
      </div>
    </div>
  </div>
)

export const SidebarSkeleton = () => (
  <div className="w-full h-full bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700">
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Skeleton className="h-6 w-1/2" />
        <Skeleton className="h-4 w-3/4" />
      </div>
      
      {/* Form fields */}
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
      
      {/* Footer */}
      <div className="flex gap-2 pt-4">
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-20" />
      </div>
    </div>
  </div>
)

export const FilterSkeleton = () => (
  <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3">
    <div className="flex items-center gap-2 flex-wrap">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-8 w-24" />
      <Skeleton className="h-8 w-24" />
      <Skeleton className="h-8 w-20" />
    </div>
  </div>
)

export const ListSkeleton = ({ count = 8 }: { count?: number }) => (
  <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
    {Array.from({ length: count }).map((_, i) => (
      <CardSkeleton key={i} />
    ))}
  </div>
)

// Lazy loaded components - Fixed import paths
export const LazyFilterSystem = lazy(() =>
  import('@/components/shared/filters/FilterSystem').then(module => ({ default: module.FilterSystem }))
)

export const LazyEnhancedGenericList = lazy(() =>
  import('@/components/shared/lists/EnhancedGenericList').then(module => ({ default: module.EnhancedGenericList }))
)

// Sidebar components - Fixed import paths
export const LazyCategorySidebar = lazy(() =>
  import('@/components/ui/CategorySidebar').then(module => ({ default: module.CategorySidebar }))
)

export const LazyContactSidebar = lazy(() =>
  import('@/components/ui/ContactSidebar').then(module => ({ default: module.ContactSidebar }))
)

export const LazyTransactionSidebar = lazy(() =>
  import('@/components/ui/TransactionSidebar').then(module => ({ default: module.TransactionSidebar }))
)

export const LazyAccountSidebar = lazy(() =>
  import('@/components/ui/AccountSidebar').then(module => ({ default: module.AccountSidebar }))
)

// Feature components
export const LazyCategoryCard = lazy(() =>
  import('@/components/features/categories/CategoryCard').then(module => ({ default: module.CategoryCard }))
)

export const LazyContactCard = lazy(() =>
  import('@/components/features/contacts/ContactCard').then(module => ({ default: module.ContactCard }))
)

export const LazyTransactionCard = lazy(() =>
  import('@/components/features/transactions/TransactionCard').then(module => ({ default: module.TransactionCard }))
)

export const LazyAccountCard = lazy(() =>
  import('@/components/features/accounts/AccountCard').then(module => ({ default: module.AccountCard }))
)

// Chart components (heavy)
export const LazySummaryChart = lazy(() =>
  import('@/components/SummaryChart').then(module => ({ default: module.SummaryChart }))
)

// HOC for wrapping lazy components with Suspense
export function withLazyLoading<T extends object>(
  LazyComponent: React.LazyExoticComponent<React.ComponentType<T>>,
  fallback?: React.ReactNode
) {
  return function LazyWrapper(props: T) {
    return (
      <Suspense fallback={fallback || <CardSkeleton />}>
        <LazyComponent {...props} />
      </Suspense>
    )
  }
}

// Specific wrapped components ready to use
export const CategoryCardLazy = withLazyLoading(LazyCategoryCard, <CardSkeleton />)
export const ContactCardLazy = withLazyLoading(LazyContactCard, <CardSkeleton />)
export const TransactionCardLazy = withLazyLoading(LazyTransactionCard, <CardSkeleton />)
export const AccountCardLazy = withLazyLoading(LazyAccountCard, <CardSkeleton />)

export const FilterSystemLazy = withLazyLoading(LazyFilterSystem, <FilterSkeleton />)
export const EnhancedGenericListLazy = withLazyLoading(LazyEnhancedGenericList, <ListSkeleton />)

export const CategorySidebarLazy = withLazyLoading(LazyCategorySidebar, <SidebarSkeleton />)
export const ContactSidebarLazy = withLazyLoading(LazyContactSidebar, <SidebarSkeleton />)
export const TransactionSidebarLazy = withLazyLoading(LazyTransactionSidebar, <SidebarSkeleton />)
export const AccountSidebarLazy = withLazyLoading(LazyAccountSidebar, <SidebarSkeleton />)

export const SummaryChartLazy = withLazyLoading(LazySummaryChart, 
  <div className="bg-white dark:bg-gray-800 rounded-lg border p-6">
    <Skeleton className="h-64 w-full" />
  </div>
)

// Preload functions for better UX
export const preloadComponents = {
  categoryCard: () => import('@/components/features/categories/CategoryCard'),
  contactCard: () => import('@/components/features/contacts/ContactCard'),
  transactionCard: () => import('@/components/features/transactions/TransactionCard'),
  accountCard: () => import('@/components/features/accounts/AccountCard'),
  filterSystem: () => import('@/shared/filters/FilterSystem'),
  enhancedList: () => import('@/shared/lists/EnhancedGenericList'),
  categorySidebar: () => import('@/ui/CategorySidebar'),
  contactSidebar: () => import('@/ui/ContactSidebar'),
  transactionSidebar: () => import('@/ui/TransactionSidebar'),
  accountSidebar: () => import('@/ui/AccountSidebar'),
  summaryChart: () => import('@/components/SummaryChart')
}

// Hook for preloading components on hover or focus
export const usePreloadComponent = () => {
  const preload = (componentName: keyof typeof preloadComponents) => {
    preloadComponents[componentName]().catch(console.error)
  }

  return { preload }
}

// Error boundary for lazy components
export class LazyComponentErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError() {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component loading error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="text-red-800 dark:text-red-200 text-sm">
            Không thể tải component. Vui lòng thử lại.
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Wrapper component that combines error boundary and suspense
export function LazyComponentWrapper({ 
  children, 
  fallback, 
  errorFallback 
}: {
  children: React.ReactNode
  fallback?: React.ReactNode
  errorFallback?: React.ReactNode
}) {
  return (
    <LazyComponentErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback || <CardSkeleton />}>
        {children}
      </Suspense>
    </LazyComponentErrorBoundary>
  )
}
