"use client"

import React from "react"
import { Building2, <PERSON>, Trash2, <PERSON>, <PERSON><PERSON>ff, CreditCard, Smartphone } from "lucide-react"
import { AccountType } from "@/lib/generated/prisma"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface AccountTypeCardProps {
  accountType: AccountType
  onEdit?: (accountType: AccountType) => void
  onDelete?: (accountType: AccountType) => void
  onToggleActive?: (accountType: AccountType) => void
  showActions?: boolean
  compact?: boolean
}

export function AccountTypeCard({
  accountType,
  onEdit,
  onDelete,
  onToggleActive,
  showActions = true,
  compact = false
}: AccountTypeCardProps) {
  return (
    <div className="group bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200">
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: accountType.color }}
            />
            <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
              {accountType.name}
            </h3>
          </div>
          
          {showActions && (
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleActive?.(accountType)}
                className="h-6 w-6 p-0"
                title={accountType.isActive ? "Ẩn loại tài khoản" : "Hiển thị loại tài khoản"}
              >
                {accountType.isActive ? (
                  <Eye size={12} className="text-green-600" />
                ) : (
                  <EyeOff size={12} className="text-gray-400" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit?.(accountType)}
                className="h-6 w-6 p-0"
                title="Chỉnh sửa"
              >
                <Edit size={12} className="text-blue-600" />
              </Button>
              {!accountType.isDefault && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete?.(accountType)}
                  className="h-6 w-6 p-0"
                  title="Xóa"
                >
                  <Trash2 size={12} className="text-red-600" />
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Description */}
        {accountType.description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
            {accountType.description}
          </p>
        )}

        {/* Badges */}
        <div className="flex flex-wrap gap-1 mb-3">
          {accountType.isDefault && (
            <Badge variant="secondary" className="text-xs">
              Mặc định
            </Badge>
          )}
          {accountType.isBankAccount && (
            <Badge variant="outline" className="text-xs">
              <CreditCard size={10} className="mr-1" />
              Ngân hàng
            </Badge>
          )}
          {accountType.isPaymentApp && (
            <Badge variant="outline" className="text-xs">
              <Smartphone size={10} className="mr-1" />
              Ví điện tử
            </Badge>
          )}
          {!accountType.isActive && (
            <Badge variant="destructive" className="text-xs">
              Ẩn
            </Badge>
          )}
        </div>

        {/* Footer */}
        {!compact && (
          <div className="text-xs text-gray-500 dark:text-gray-400 border-t border-gray-100 dark:border-gray-700 pt-2">
            <div className="flex justify-between items-center">
              <span>ID: {accountType.id.slice(0, 8)}...</span>
              <span>
                {new Date(accountType.createdAt).toLocaleDateString('vi-VN')}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
