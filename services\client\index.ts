/**
 * API Services Index
 * 
 * Central export point for all API services and utilities
 */

// ==================== CORE API CLIENT ====================

import { ApiClient, ApiClientError, NetworkError, ValidationError } from './apiClient'

export {
  ApiClient,
  ApiClientError,
  NetworkError,
  ValidationError
} from './apiClient'


// ==================== CONVENIENCE FUNCTIONS ====================

/**
 * Set authentication token for the API client
 */
export const setAuthToken = (token: string): void => {
  ApiClient.setAuthToken(token)
}

/**
 * Remove authentication token from the API client
 */
export const removeAuthToken = (): void => {
  ApiClient.removeAuthToken()
}

/**
 * Check if API client is authenticated
 */
export const isAuthenticated = (): boolean => {
  return ApiClient.isAuthenticated()
}

/**
 * Get API base URL
 */
export const getApiBaseUrl = (): string => {
  return ApiClient.getBaseURL()
}

/**
 * Set API base URL
 */
export const setApiBaseUrl = (url: string): void => {
  ApiClient.setBaseURL(url)
}

/**
 * Set API timeout
 */
export const setApiTimeout = (timeout: number): void => {
  ApiClient.setTimeout(timeout)
}

// ==================== SPECIALIZED API SERVICES ====================

export { AuthApiService } from './authApi'
export { UserApiService } from './userApi'
export { UserPreferencesApiService } from './userPreferencesApi'
export { AccountApiService } from './accountApi'
export { AccountTypeApiService } from './accountTypeApi'
export { TransactionApiService } from './transactionApi'
export { ContactApiService } from './contactApi'
export { CategoryApiService } from './categoryApi'
export { CategoryGoalApiService  } from "./categoryGoalApi"
export { RecurringTransactionApiService } from './recurringTransactionApi'
export { DashboardApiService } from './dashboardApi'
export { DebitCardApiService } from './debitCardApi'
export { AccountPaymentAppApiService } from './accountPaymentAppApi'

// ==================== API UTILITIES ====================

/**
 * Initialize API client with authentication token
 */
export const initializeApi = (token?: string): void => {
  if (token) {
    setAuthToken(token)
  }
}

/**
 * Clear API authentication
 */
export const clearApiAuth = (): void => {
  removeAuthToken()
}

/**
 * Check if API client is authenticated
 */
export const isApiAuthenticated = (): boolean => {
  return ApiClient.isAuthenticated()
}

/**
 * Health check for API
 */
export const checkApiHealth = async () => {
  try {
    const response = await ApiClient.get('/health')
    return response
  } catch (error) {
    console.error('API health check failed:', error)
    throw error
  }
}

// ==================== API CONFIGURATION ====================

export interface ApiConfig {
  baseURL?: string
  timeout?: number
  token?: string
}

/**
 * Configure API client
 */
export const configureApi = (config: ApiConfig): void => {
  if (config.baseURL) {
    ApiClient.setBaseURL(config.baseURL)
  }

  if (config.timeout) {
    ApiClient.setTimeout(config.timeout)
  }

  if (config.token) {
    setAuthToken(config.token)
  }
}

// ==================== ERROR HANDLING UTILITIES ====================

/**
 * Check if error is an API client error
 */
export const isApiError = (error: any): error is ApiClientError => {
  return error instanceof ApiClientError
}

/**
 * Check if error is a network error
 */
export const isNetworkError = (error: any): error is NetworkError => {
  return error instanceof NetworkError
}

/**
 * Check if error is a validation error
 */
export const isValidationError = (error: any): error is ValidationError => {
  return error instanceof ValidationError
}

/**
 * Extract error message from API error
 */
export const getApiErrorMessage = (error: any): string => {
  if (isApiError(error)) {
    return error.message
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return 'An unknown error occurred'
}

/**
 * Extract error status code from API error
 */
export const getApiErrorStatus = (error: any): number | undefined => {
  if (isApiError(error)) {
    return error.status
  }
  
  return undefined
}

/**
 * Extract error code from API error
 */
export const getApiErrorCode = (error: any): string | undefined => {
  if (isApiError(error)) {
    return error.code
  }
  
  return undefined
}

// ==================== RESPONSE UTILITIES ====================

/**
 * Check if API response is successful
 */
export const isApiResponseSuccess = (response: any): boolean => {
  return response && response.success === true
}

/**
 * Extract data from API response
 */
export const getApiResponseData = <T>(response: any): T | null => {
  if (isApiResponseSuccess(response)) {
    return response.data
  }
  
  return null
}

/**
 * Extract pagination info from paginated response
 */
export const getApiResponsePagination = (response: any) => {
  if (isApiResponseSuccess(response) && response.pagination) {
    return response.pagination
  }
  
  return null
}

// ==================== RETRY UTILITIES ====================

/**
 * Retry API call with exponential backoff
 */
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall()
    } catch (error) {
      lastError = error
      
      // Don't retry on validation errors or client errors (4xx)
      if (isApiError(error) && error.status && error.status >= 400 && error.status < 500) {
        throw error
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break
      }
      
      // Wait before retrying with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}

// ==================== CACHE UTILITIES ====================

const apiCache = new Map<string, { data: any; timestamp: number; ttl: number }>()

/**
 * Cache API response
 */
export const cacheApiResponse = (key: string, data: any, ttl: number = 300000): void => {
  apiCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  })
}

/**
 * Get cached API response
 */
export const getCachedApiResponse = <T>(key: string): T | null => {
  const cached = apiCache.get(key)
  
  if (!cached) {
    return null
  }
  
  const isExpired = Date.now() - cached.timestamp > cached.ttl
  
  if (isExpired) {
    apiCache.delete(key)
    return null
  }
  
  return cached.data
}

/**
 * Clear API cache
 */
export const clearApiCache = (): void => {
  apiCache.clear()
}

/**
 * Clear expired cache entries
 */
export const clearExpiredApiCache = (): void => {
  const now = Date.now()
  
  for (const [key, cached] of apiCache.entries()) {
    const isExpired = now - cached.timestamp > cached.ttl
    
    if (isExpired) {
      apiCache.delete(key)
    }
  }
}
