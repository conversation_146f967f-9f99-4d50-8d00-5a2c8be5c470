"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { <PERSON>ricCard, EntityCard } from "@/components/shared/cards/GenericCard"
import { Contact, Account } from "@/lib/generated/prisma"

// Use the proper type from entities
import { ContactWithAccounts } from '@/types/entities'
import { CardAction } from "@/types/ui"
import { 
  Edit, Trash2, Eye, EyeOff, ExternalLink, User, Phone, Mail, 
  Building2, CreditCard, MapPin, FileText 
} from "lucide-react"

interface ContactCardProps {
  contact: ContactWithAccounts
  onEdit?: (contact: ContactWithAccounts) => void
  onToggleActive?: (contact: ContactWithAccounts) => void
  onView?: (contact: ContactWithAccounts) => void
  showStats?: boolean
  transactionCount?: number
  totalAmount?: number
  variant?: 'default' | 'compact' | 'detailed'
}

export function ContactCard({
  contact,
  onEdit,
  onToggleActive,
  onView,
  showStats = false,
  transactionCount = 0,
  totalAmount = 0,
  variant = 'default'
}: ContactCardProps) {
  const router = useRouter()

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  // Handle view contact
  const handleViewContact = () => {
    if (onView) {
      onView(contact)
    } else {
      try {
        router.push(`/contacts/${contact.id}`)
      } catch (error) {
        console.error('ContactCard: Router navigation failed:', error)
        window.location.href = `/contacts/${contact.id}`
      }
    }
  }

  // Define card actions
  const actions: CardAction<Contact>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: handleViewContact,
      variant: "outline"
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    },
    {
      label: contact.isActive ? "Ẩn" : "Hiện",
      icon: contact.isActive ? EyeOff : Eye,
      onClick: onToggleActive || (() => {}),
      variant: "outline",
      hidden: !onToggleActive
    },

  ]

  // Render contact content
  const renderContactContent = () => (
    <div className="space-y-3">
      {/* Header with name and contact type */}
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {/* Avatar */}
          <div
            className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-medium text-sm`}
            style={{ backgroundColor: contact.contactType?.color || '#6B7280' }}
          >
            {contact.contactType?.icon || contact.name.charAt(0).toUpperCase()}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight truncate">
              {contact.name}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {contact.contactType?.name || 'Unknown Type'}
            </p>
          </div>
        </div>

        {/* Status indicator */}
        {!contact.isActive && (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
            Đã ẩn
          </span>
        )}
      </div>

      {/* Contact information */}
      <div className="space-y-2">
        {contact.phone && (
          <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <Phone size={12} />
            <span className="truncate">{contact.phone}</span>
          </div>
        )}
        
        {contact.email && (
          <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <Mail size={12} />
            <span className="truncate">{contact.email}</span>
          </div>
        )}
        
        {contact.address && (
          <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <MapPin size={12} />
            <span className="truncate">{contact.address}</span>
          </div>
        )}
      </div>

      {/* Bank accounts */}
      {contact.accounts && contact.accounts.length > 0 && (
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
            <CreditCard size={12} />
            <span className="font-medium">Tài khoản ({contact.accounts.length})</span>
          </div>
          <div className="space-y-1">
            {contact.accounts.slice(0, 2).map((account, index) => (
              <div key={account.id} className="flex items-center justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-400 truncate flex-1">
                  {account.name}
                </span>
                <span className="font-medium text-gray-900 dark:text-gray-100 ml-2">
                  {formatCurrency(account.balance)}
                </span>
              </div>
            ))}
            {contact.accounts.length > 2 && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                +{contact.accounts.length - 2} tài khoản khác
              </div>
            )}
          </div>
        </div>
      )}

      {/* Transaction stats */}
      {showStats && (
        <div className="flex items-center justify-between text-xs pt-2 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
            <FileText size={12} />
            <span className="font-semibold text-gray-900 dark:text-gray-100">
              {transactionCount}
            </span>
            <span>giao dịch</span>
          </div>
          <div className="font-semibold text-blue-600 dark:text-blue-400">
            {formatCurrency(totalAmount)}
          </div>
        </div>
      )}

      {/* Notes preview */}
      {false && (
        <div className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
          {contact.notes}
        </div>
      )}
    </div>
  )

  return (
    <GenericCard
      item={contact}
      renderContent={renderContactContent}
      actions={actions}
      onClick={handleViewContact}
      variant={variant}
      hoverable={true}
      className={`h-full ${!contact.isActive ? "opacity-60" : ""}`}
    />
  )
}

// Alternative implementation using EntityCard
export function SimpleContactCard({
  contact,
  onEdit,
  onToggleActive,
  onView,
  showStats = false,
  transactionCount = 0,
  totalAmount = 0
}: ContactCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const actions: CardAction<Contact>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: onView || (() => {}),
      variant: "outline",
      hidden: !onView
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    }
  ]

  const metadata = [
    ...(contact.phone ? [{
      label: "Điện thoại",
      value: contact.phone,
      icon: Phone
    }] : []),
    ...(contact.email ? [{
      label: "Email",
      value: contact.email,
      icon: Mail
    }] : []),
    ...(contact.accounts?.length ? [{
      label: "Tài khoản",
      value: contact.accounts.length,
      icon: CreditCard
    }] : []),
    ...(showStats ? [{
      label: "Giao dịch",
      value: transactionCount,
      icon: FileText
    }, {
      label: "Tổng tiền",
      value: formatCurrency(totalAmount),
      icon: Building2
    }] : [])
  ]

  return (
    <EntityCard
      item={contact}
      title={contact.name}
      subtitle={contact.contactType?.name || 'Unknown Type'}
      description={undefined}
      status={{
        label: contact.isActive ? "Hoạt động" : "Đã ẩn",
        color: contact.isActive ? "green" : "gray"
      }}
      metadata={metadata}
      avatar={{
        fallback: contact.contactType?.icon || contact.name.charAt(0).toUpperCase(),
        color: contact.contactType?.color || '#6B7280'
      }}
      actions={actions}
      onClick={onView}
      className={!contact.isActive ? "opacity-60" : ""}
    />
  )
}

export default ContactCard
