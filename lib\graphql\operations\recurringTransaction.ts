/**
 * GraphQL Operations for RecurringTransaction Management
 * 
 * Queries and mutations for recurring transaction operations
 */

import { gql } from '@apollo/client'

// ==================== FRAGMENTS ====================

export const RECURRING_TRANSACTION_FRAGMENT = gql`
  fragment RecurringTransactionFragment on RecurringTransaction {
    id
    name
    description
    amount
    kind
    frequency
    startDate
    endDate
    isActive
    userId
    accountId
    categoryId
    contactId
    fromAccountId
    toAccountId
    createdAt
    updatedAt
  }
`

export const RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT = gql`
  fragment RecurringTransactionWithRelationsFragment on RecurringTransaction {
    ...RecurringTransactionFragment
    user {
      id
      name
      email
    }
    account {
      id
      name
      currency
      accountType {
        name
        category
        color
      }
    }
    category {
      id
      name
      type
      color
      icon
    }
    contact {
      id
      name
      email
      phone
      contactType {
        name
        color
      }
    }
    fromAccount {
      id
      name
      currency
      accountType {
        name
        category
        color
      }
    }
    toAccount {
      id
      name
      currency
      accountType {
        name
        category
        color
      }
    }
  }
  ${RECURRING_TRANSACTION_FRAGMENT}
`

// ==================== QUERIES ====================

export const GET_RECURRING_TRANSACTIONS = gql`
  query GetRecurringTransactions(
    $first: Int
    $after: String
    $search: String
    $kind: TransactionKind
    $frequency: String
    $isActive: Boolean
  ) {
    recurringTransactions(
      first: $first
      after: $after
      search: $search
      kind: $kind
      frequency: $frequency
      isActive: $isActive
    ) {
      edges {
        node {
          ...RecurringTransactionWithRelationsFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_RECURRING_TRANSACTION_BY_ID = gql`
  query GetRecurringTransactionById($id: UUID!) {
    recurringTransaction(id: $id) {
      ...RecurringTransactionWithRelationsFragment
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_ACTIVE_RECURRING_TRANSACTIONS = gql`
  query GetActiveRecurringTransactions($first: Int = 100) {
    recurringTransactions(first: $first, isActive: true) {
      edges {
        node {
          ...RecurringTransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_RECURRING_TRANSACTIONS_BY_KIND = gql`
  query GetRecurringTransactionsByKind($kind: TransactionKind!, $first: Int = 50) {
    recurringTransactions(kind: $kind, first: $first, isActive: true) {
      edges {
        node {
          ...RecurringTransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_RECURRING_TRANSACTIONS_BY_FREQUENCY = gql`
  query GetRecurringTransactionsByFrequency($frequency: String!, $first: Int = 50) {
    recurringTransactions(frequency: $frequency, first: $first, isActive: true) {
      edges {
        node {
          ...RecurringTransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const SEARCH_RECURRING_TRANSACTIONS = gql`
  query SearchRecurringTransactions($search: String!, $first: Int = 20) {
    recurringTransactions(search: $search, first: $first) {
      edges {
        node {
          ...RecurringTransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

// ==================== MUTATIONS ====================

export const CREATE_RECURRING_TRANSACTION = gql`
  mutation CreateRecurringTransaction($input: CreateRecurringTransactionInput!) {
    createRecurringTransaction(input: $input) {
      ...RecurringTransactionWithRelationsFragment
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const UPDATE_RECURRING_TRANSACTION = gql`
  mutation UpdateRecurringTransaction($id: UUID!, $input: UpdateRecurringTransactionInput!) {
    updateRecurringTransaction(id: $id, input: $input) {
      ...RecurringTransactionWithRelationsFragment
    }
  }
  ${RECURRING_TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const DELETE_RECURRING_TRANSACTION = gql`
  mutation DeleteRecurringTransaction($id: UUID!) {
    deleteRecurringTransaction(id: $id)
  }
`
