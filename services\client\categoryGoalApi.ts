/**
 * CategoryGoal API Service
 *
 * Handles category goal-related API operations using GraphQL client
 */
import { apolloClient } from '@/lib/graphql/client'
import {
  GET_CATEGORY_GOALS,
  GET_CATEGORY_GOAL_BY_ID,
  GET_CATEGORY_GOALS_BY_CATEGORY,
  GET_ACTIVE_CATEGORY_GOALS,
  SEARCH_CATEGORY_GOALS,
  CREATE_CATEGORY_GOAL,
  UPDATE_CATEGORY_GOAL,
  DELETE_CATEGORY_GOAL
} from '@/lib/graphql/operations'
import {
  convertToClientError,
  getUserFriendlyErrorMessage
} from '@/lib/graphql/errors'
import type {
  ApiResponse
} from '@/types/api'
import type {
  CategoryGoal,
  CategoryGoalConnection,
  CreateCategoryGoalInput,
  UpdateCategoryGoalInput,
  Period
} from '@/lib/graphql/types'
import type {
  CategoryGoalConnectionResponse,
  CategoryGoalConnectionParams
} from '@/types/graphql-connections'
export interface CategoryGoalConnectionParams {
  page?: number
  limit?: number
  search?: string
  categoryId?: string
  period?: Period
  isActive?: boolean
}
export interface CreateCategoryGoalRequest {
  name: string
  description?: string
  amount: number
  period: Period
  startDate: string
  endDate?: string
  color?: string
  categoryId: string
}
export interface UpdateCategoryGoalRequest {
  name?: string
  description?: string
  amount?: number
  period?: Period
  startDate?: string
  endDate?: string
  color?: string
  categoryId?: string
  isActive?: boolean
}
export class CategoryGoalApiService {
  // ==================== CATEGORY GOAL CRUD OPERATIONS ====================
  /**
   * Get all category goals with filtering and pagination
   */
  static async getCategoryGoals(params?: CategoryGoalConnectionParams): Promise<CategoryGoalConnectionResponse> {
    try {
      const { first = 25, after, search, categoryId, period, isActive } = params || {}
      const { data } = await apolloClient.query({
        query: GET_CATEGORY_GOALS,
        variables: {
          first,
          after,
          search,
          categoryId,
          period,
          isActive
        },
        fetchPolicy: 'cache-first'
      })
      return {
        success: true,
        data: data.categoryGoals,
        message: 'categoryGoals retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch category goals:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Alias for backward compatibility
   */
  static async getCategoryGoal(params?: CategoryGoalConnectionParams): Promise<CategoryGoalConnectionResponse> {
    return this.getCategoryGoals(params)
  }
  /**
   * Get category goal by ID
   */
  static async getCategoryGoalById(id: string): Promise<ApiResponse<CategoryGoal>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CATEGORY_GOAL_BY_ID,
        variables: { id },
        fetchPolicy: 'cache-first'
      })
      if (!data.categoryGoal) {
        throw new Error('Category goal not found')
      }
      return {
        data: data.categoryGoal,
        success: true,
        message: 'Category goal retrieved successfully'
      }
    } catch (error: any) {
      console.error(`Failed to fetch category goal ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Alias for backward compatibility
   */
  static async getBudgetById(id: string): Promise<ApiResponse<CategoryGoal>> {
    return this.getCategoryGoalById(id)
  }
  /**
   * Get category goals by category
   */
  static async getCategoryGoalsByCategory(categoryId: string): Promise<ApiResponse<CategoryGoal[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CATEGORY_GOALS_BY_CATEGORY,
        variables: { categoryId },
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.categoryGoals.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Category goals retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch category goals by category:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Alias for backward compatibility
   */
  static async getCategoryGoalByCategory(categoryId: string): Promise<ApiResponse<CategoryGoal[]>> {
    return this.getCategoryGoalsByCategory(categoryId)
  }
  /**
   * Create new category goal
   */
  static async createCategoryGoal(data: CreateCategoryGoalRequest): Promise<ApiResponse<CategoryGoal>> {
    try {
      const input: CreateCategoryGoalInput = {
        name: data.name,
        description: data.description,
        amount: data.amount,
        period: data.period,
        startDate: data.startDate,
        endDate: data.endDate,
        color: data.color || '#45B7D1',
        categoryId: data.categoryId
      }
      const { data: result } = await apolloClient.mutate({
        mutation: CREATE_CATEGORY_GOAL,
        variables: { input },
        refetchQueries: ['GetCategoryGoals', 'GetActiveCategoryGoals']
      })
      return {
        data: result.createCategoryGoal,
        success: true,
        message: 'Category goal created successfully'
      }
    } catch (error: any) {
      console.error('Failed to create category goal:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Alias for backward compatibility
   */
  static async createBudget(data: CreateCategoryGoalRequest): Promise<ApiResponse<CategoryGoal>> {
    return this.createCategoryGoal(data)
  }
  /**
   * Update category goal
   */
  static async updateCategoryGoal(id: string, data: UpdateCategoryGoalRequest): Promise<ApiResponse<CategoryGoal>> {
    try {
      const input: UpdateCategoryGoalInput = {
        name: data.name,
        description: data.description,
        amount: data.amount,
        period: data.period,
        startDate: data.startDate,
        endDate: data.endDate,
        color: data.color,
        categoryId: data.categoryId,
        isActive: data.isActive
      }
      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_CATEGORY_GOAL,
        variables: { id, input },
        refetchQueries: ['GetCategoryGoals', 'GetCategoryGoalById', 'GetActiveCategoryGoals']
      })
      return {
        data: result.updateCategoryGoal,
        success: true,
        message: 'Category goal updated successfully'
      }
    } catch (error: any) {
      console.error(`Failed to update category goal ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Alias for backward compatibility
   */
  static async updateBudget(id: string, data: UpdateCategoryGoalRequest): Promise<ApiResponse<CategoryGoal>> {
    return this.updateCategoryGoal(id, data)
  }
  /**
   * Delete category goal
   */
  static async deleteCategoryGoal(id: string): Promise<ApiResponse<void>> {
    try {
      const { data: result } = await apolloClient.mutate({
        mutation: DELETE_CATEGORY_GOAL,
        variables: { id },
        refetchQueries: ['GetCategoryGoals', 'GetActiveCategoryGoals']
      })
      return {
        data: undefined,
        success: result.deleteCategoryGoal,
        message: 'Category goal deleted successfully'
      }
    } catch (error: any) {
      console.error(`Failed to delete category goal ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Alias for backward compatibility
   */
  static async deleteBudget(id: string): Promise<ApiResponse<void>> {
    return this.deleteCategoryGoal(id)
  }
  // ==================== CONVENIENCE METHODS ====================
  /**
   * Get active category goals
   */
  static async getActiveCategoryGoals(): Promise<ApiResponse<CategoryGoal[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_ACTIVE_CATEGORY_GOALS,
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.categoryGoals.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Active category goals retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch active category goals:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Search category goals by name
   */
  static async searchCategoryGoals(query: string, first: number = 10): Promise<CategoryGoalConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: SEARCH_CATEGORY_GOALS,
        variables: { search: query, first },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.categoryGoals,
        message: 'categoryGoals retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to search category goals:', error)
      throw convertToClientError(error)
    }
  }
}
