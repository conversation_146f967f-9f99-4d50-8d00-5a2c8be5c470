"use client"

import { AuthApiService, ApiClient } from '@/services/client'
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'


// ==================== TYPES ====================

export interface User {
  id: string
  name: string
  email: string
  avatar?: string | null
  role: 'admin' | 'user'
  isActive: boolean
  createdAt: string
  updatedAt: string
  preferences?: UserPreferences | null
}

export interface UserPreferences {
  id: string
  userId: string
  currency: string
  language: string
  theme: string
  dateFormat?: string
  numberFormat?: string
  createdAt: string
  updatedAt: string
}

export interface AuthState {
  user: User | null
  preferences: UserPreferences | null
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  updateUser: (userData: Partial<User>) => Promise<boolean>
  updatePreferences: (preferencesData: Partial<UserPreferences>) => Promise<boolean>
  refreshUser: () => Promise<void>
  clearError: () => void
}

// ==================== CONTEXT ====================

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// ==================== PROVIDER ====================

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, setState] = useState<AuthState>({
    user: null,
    preferences: null,
    isLoading: true,
    isAuthenticated: false,
    error: null
  })

  // Initialize auth state from localStorage
  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // Check if user is authenticated using AuthApiService
      if (!AuthApiService.isAuthenticated()) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isAuthenticated: false,
          user: null,
          preferences: null
        }))
        return
      }

      // Set auth token for ApiClient
      const token = AuthApiService.getStoredToken()
      if (token) {
        ApiClient.setAuthToken(token)
      }

      // Fetch current user data using AuthApiService
      const result = await AuthApiService.getCurrentUser()

      if (!result.success) {
        // Invalid token or user not found
        AuthApiService.clearStoredTokens()
        setState(prev => ({
          ...prev,
          isLoading: false,
          isAuthenticated: false,
          user: null,
          preferences: null,
          error: 'Session expired. Please login again.'
        }))
        return
      }

      const user = result.data as User
      const preferences = user?.preferences || null

      setState(prev => ({
        ...prev,
        user,
        preferences,
        isLoading: false,
        isAuthenticated: true,
        error: null
      }))

    } catch (error) {
      console.error('Auth initialization error:', error)
      clearAuthData()
      setState(prev => ({
        ...prev,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        preferences: null,
        error: 'Failed to initialize authentication'
      }))
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      const result = await AuthApiService.login({ email, password })
      console.log('Login API result:', result)

      if (!result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Login failed'
        }))
        return false
      }

      const { user, tokens } = result.data!

      // Validate tokens structure
      if (!tokens || !tokens.accessToken || !tokens.refreshToken) {
        console.error('Invalid tokens received from API:', tokens)
        setState(prev => ({
          ...prev,
          error: 'Invalid authentication response from server'
        }))
        return false
      }

      // Store tokens using AuthApiService
      AuthApiService.storeTokens(tokens)

      // Set auth token for ApiClient
      ApiClient.setAuthToken(tokens.accessToken)

      const preferences = user?.preferences || null

      setState(prev => ({
        ...prev,
        user,
        preferences,
        isLoading: false,
        isAuthenticated: true,
        error: null
      }))

      return true
    } catch (error) {
      console.error('Login error:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Login failed. Please try again.'
      }))
      return false
    }
  }

  const logout = async () => {
    try {
      // Call logout API
      await AuthApiService.logout()
    } catch (error) {
      console.error('Logout API call failed:', error)
      // Continue with local logout even if API call fails
    }

    // Clear auth tokens and API client auth
    AuthApiService.clearStoredTokens()
    ApiClient.removeAuthToken()

    setState({
      user: null,
      preferences: null,
      isLoading: false,
      isAuthenticated: false,
      error: null
    })
  }

  const updateUser = async (userData: Partial<User>): Promise<boolean> => {
    if (!state.user) return false

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // TODO: Call real API to update user
      // For now, just update local state
      const updatedUser = { ...state.user, ...userData, updatedAt: new Date().toISOString() }

      setState(prev => ({
        ...prev,
        user: updatedUser,
        isLoading: false,
        error: null
      }))

      return true
    } catch (error) {
      console.error('Update user error:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to update user profile'
      }))
      return false
    }
  }

  const updatePreferences = async (preferencesData: Partial<UserPreferences>): Promise<boolean> => {
    if (!state.user) return false

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // Call real API to update preferences
      if (state.preferences?.id) {
        // Update existing preferences by ID
        const response = await fetch(`/api/user-preferences/${state.preferences.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          },
          body: JSON.stringify(preferencesData)
        })

        if (!response.ok) {
          throw new Error('Failed to update preferences')
        }

        const result = await response.json()

        if (result.success && result.data) {
          setState(prev => ({
            ...prev,
            preferences: result.data,
            isLoading: false,
            error: null
          }))
          return true
        } else {
          throw new Error(result.message || 'Failed to update preferences')
        }
      } else {
        // Update via user preferences endpoint
        const response = await fetch(`/api/users/me/preferences`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          },
          body: JSON.stringify(preferencesData)
        })

        if (!response.ok) {
          throw new Error('Failed to update preferences')
        }

        const result = await response.json()

        if (result.success && result.data) {
          setState(prev => ({
            ...prev,
            preferences: result.data,
            isLoading: false,
            error: null
          }))
          return true
        } else {
          throw new Error(result.message || 'Failed to update preferences')
        }
      }
    } catch (error) {
      console.error('Update preferences error:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update preferences'
      }))
      return false
    }
  }

  const refreshUser = async (): Promise<void> => {
    if (!state.user) return

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      const userResponse = await AuthApiService.getCurrentUser()
      if (userResponse.success) {
        const user = userResponse.data as User
        const preferences = user?.preferences || null

        setState(prev => ({
          ...prev,
          user,
          preferences,
          isLoading: false,
          error: null
        }))
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to refresh user data'
        }))
      }
    } catch (error) {
      console.error('Refresh user error:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to refresh user data'
      }))
    }
  }

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }))
  }

  const clearAuthData = () => {
    AuthApiService.clearStoredTokens()
    ApiClient.removeAuthToken()
  }

  const contextValue: AuthContextType = {
    ...state,
    login,
    logout,
    updateUser,
    updatePreferences,
    refreshUser,
    clearError
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// ==================== HOOK ====================

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// ==================== UTILITIES ====================

export const getCurrentUserId = (): string | null => {
  // This would need to be extracted from the JWT token
  const token = AuthApiService.getStoredToken()
  if (!token) return null

  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload.userId || null
  } catch {
    return null
  }
}

export const getAuthToken = (): string | null => {
  return AuthApiService.getStoredToken()
}

export const isAuthenticated = (): boolean => {
  return AuthApiService.isAuthenticated()
}
