
export type { BulkTransactionInput, CreateTransactionInput, Transaction, TransactionBase, TransactionFilterInput, TransactionResponse, TransactionWithRelations, UpdateTransactionInput };
import { AccountBase } from './account';
import { AccountWithRelations } from './account/account';
import { BaseTableActiveable } from './base';
import { CategoryBase } from './category';
import { DateTime, OmitOnCreate, OmitOnEdit, TransactionKind, UUID } from './common';
import { UserBase } from './user';
/**
 * Base interface for Transaction
 */
interface TransactionBase extends BaseTableActiveable {
  id: UUID;
  kind: TransactionKind;
  amount: number;
  date: DateTime;
  description?: string;
  location?: string;
  note?: string;
  attachments: string[]; // options
  isCleared?: boolean;// options
  isReconciled?: boolean; // options
  userId: UUID; // relation
  accountId?: UUID; // relation
  categoryId?: UUID; // relation
  // the account of user can connect to many Account (paymentApp), DebitCard so need to include connectedAccountId and connectedDebitCardId 
  // to make sure the transaction is connected to the correct account
  connectedAccountId?: UUID; // relation 
  connectedDebitCardId?: UUID; // relation // of owner account ()
  fromAccountId?: UUID; // relation
  toAccountId?: UUID; // relation
}



/**
 * Transaction with all possible relations
 */
interface TransactionWithRelations extends TransactionBase {
  user: UserBase;
  account?: AccountWithRelations;
  category?: CategoryBase;
  fromAccount?: AccountBase;
  toAccount?: AccountBase;
}

/**
 * Union type for Transaction
 */
type Transaction = TransactionBase | TransactionWithRelations;

/**
 * Input type for creating a new transaction
 */
interface CreateTransactionInput extends OmitOnCreate<TransactionBase> {

}

/**
 * Input type for updating a transaction
 */
interface UpdateTransactionInput extends OmitOnEdit<TransactionBase> {

}

/**
 * Input type for transaction filters
 */
interface TransactionFilterInput {
  search?: string;
  kind?: TransactionKind;
  minAmount?: number;
  maxAmount?: number;
  startDate?: DateTime;
  endDate?: DateTime;
  accountId?: UUID;
  categoryId?: UUID;
}

/**
 * Input type for bulk transaction operations
 */
interface BulkTransactionInput {
  ids: UUID[];
  isCleared?: boolean;
  isReconciled?: boolean;
  categoryId?: UUID;
  accountId?: UUID;
  isActive?: boolean;
}

/**
 * Response type for transaction operations
 */
interface TransactionResponse {
  success: boolean;
  message?: string;
  transaction?: Transaction;
  errors?: Array<{ message: string; field?: string }>;
}


