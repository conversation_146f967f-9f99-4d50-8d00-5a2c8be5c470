"use client"

import React from "react"
import { Edit, Trash2, Target, AlertTriangle, CheckCircle, Calendar, TrendingUp } from "lucide-react"

interface Budget {
  id: number
  name: string
  description?: string
  amount: number
  spent: number
  period: "monthly" | "weekly" | "yearly"
  categoryId?: number
  categoryName?: string
  categoryColor?: string
  startDate: string
  endDate: string
  isActive?: boolean
}

interface BudgetCardProps {
  budget: Budget
  onEdit?: (budget: Budget) => void
  formatCurrency?: (amount: number) => string
}

export function BudgetCard({
  budget,
  onEdit,
  formatCurrency = (amount) => `${amount.toLocaleString()}₫`
}: BudgetCardProps) {
  const spentPercentage = budget.amount > 0 ? (budget.spent / budget.amount) * 100 : 0
  const remaining = budget.amount - budget.spent
  
  const getStatusColor = () => {
    if (spentPercentage >= 100) return "text-red-600 dark:text-red-400"
    if (spentPercentage >= 80) return "text-orange-600 dark:text-orange-400"
    return "text-green-600 dark:text-green-400"
  }

  const getProgressColor = () => {
    if (spentPercentage >= 100) return "bg-red-500"
    if (spentPercentage >= 80) return "bg-orange-500"
    return "bg-green-500"
  }

  const getStatusIcon = () => {
    if (spentPercentage >= 100) return <AlertTriangle size={16} className="text-red-500" />
    if (spentPercentage >= 80) return <AlertTriangle size={16} className="text-orange-500" />
    return <CheckCircle size={16} className="text-green-500" />
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN", { 
      day: "2-digit", 
      month: "2-digit" 
    })
  }

  return (
    <div className="group bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/50 dark:hover:border-gray-600/50 transition-all duration-200">
      {/* Mobile Layout */}
      <div className="block md:hidden space-y-4">
        {/* Header Row */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <Target size={16} className="text-blue-500 flex-shrink-0" />
              <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate text-sm">
                {budget.name}
              </h3>
            </div>
            {budget.description && (
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {budget.description}
              </p>
            )}
            {budget.categoryName && (
              <div className="flex items-center gap-1.5 mt-1">
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: budget.categoryColor || "#6B7280" }}
                />
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  {budget.categoryName}
                </span>
              </div>
            )}
          </div>

          {/* Status Icon */}
          <div className="ml-3">
            {getStatusIcon()}
          </div>
        </div>

        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Progress</span>
            <span className={`font-medium ${getStatusColor()}`}>
              {spentPercentage.toFixed(1)}%
            </span>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
              style={{ width: `${Math.min(spentPercentage, 100)}%` }}
            />
          </div>

          <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
            <span>Spent: {formatCurrency(budget.spent)}</span>
            <span>Budget: {formatCurrency(budget.amount)}</span>
          </div>
          
          <div className="text-center">
            <span className={`text-sm font-medium ${remaining >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {remaining >= 0 ? 'Remaining: ' : 'Over budget: '}
              {formatCurrency(Math.abs(remaining))}
            </span>
          </div>
        </div>

        {/* Period & Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <Calendar size={12} />
            <span className="capitalize">{budget.period}</span>
            <span>•</span>
            <span>{formatDate(budget.startDate)} - {formatDate(budget.endDate)}</span>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {onEdit && (
              <button
                onClick={() => onEdit(budget)}
                className="p-1.5 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                title="Edit budget"
              >
                <Edit size={14} />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:block space-y-3">
        {/* Header Row */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <Target size={20} className="text-blue-500 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                {budget.name}
              </h3>
              {budget.description && (
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {budget.description}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-4 flex-shrink-0">
            {/* Category */}
            {budget.categoryName && (
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: budget.categoryColor || "#6B7280" }}
                />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {budget.categoryName}
                </span>
              </div>
            )}

            {/* Period */}
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Calendar size={14} />
              <span className="capitalize">{budget.period}</span>
            </div>

            {/* Status */}
            {getStatusIcon()}

            {/* Actions */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {onEdit && (
                <button
                  onClick={() => onEdit(budget)}
                  className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                  title="Edit budget"
                >
                  <Edit size={16} />
                </button>
              )}

            </div>
          </div>
        </div>

        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {formatCurrency(budget.spent)} / {formatCurrency(budget.amount)}
              </span>
              <span className={`text-sm font-medium ${getStatusColor()}`}>
                {spentPercentage.toFixed(1)}%
              </span>
            </div>
            <span className={`text-sm font-medium ${remaining >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {remaining >= 0 ? 'Remaining: ' : 'Over budget: '}
              {formatCurrency(Math.abs(remaining))}
            </span>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
              style={{ width: `${Math.min(spentPercentage, 100)}%` }}
            />
          </div>

          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{formatDate(budget.startDate)} - {formatDate(budget.endDate)}</span>
            <div className="flex items-center gap-1">
              <TrendingUp size={12} />
              <span>Budget period</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
