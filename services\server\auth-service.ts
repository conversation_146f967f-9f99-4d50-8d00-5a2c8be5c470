/**
 * Authentication Service
 * 
 * Handles user authentication, token management, and authorization
 */

import bcrypt from 'bcryptjs'
import { NextRequest } from 'next/server'
import { generateTokenPair, verifyToken, JWTPayload } from '@/lib/auth/jwt'
import { AUTH_CONFIG } from '@/lib/auth/config'
import { prisma } from '@/lib/prisma'
import { BaseService } from './base-service'
import { UnauthorizedError, ValidationError, NotFoundError } from '@/lib/api/utils'

// ==================== TYPES ====================

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  role?: 'admin' | 'user'
}

export interface LoginResponse {
  user: {
    id: string
    name: string
    email: string
    role: string
    avatar?: string
  }
  tokens: {
    accessToken: string
    refreshToken: string
    expiresIn: number
  }
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

// ==================== AUTH SERVICE ====================

export class AuthService extends BaseService {
  
  /**
   * User login with email and password
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    const { email, password } = data

    // Validate input
    this.validateRequired(data, ['email', 'password'])

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
      select: {
        id: true,
        name: true,
        email: true,
        password: true,
        role: true,
        avatar: true,
        isActive: true,
        loginAttempts: true,
        lockedUntil: true
      }
    })

    if (!user) {
      throw new UnauthorizedError('Invalid email or password')
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedError('Account is deactivated')
    }

    // Check if account is locked
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      const remainingTime = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 60000)
      throw new UnauthorizedError(`Account is locked. Try again in ${remainingTime} minutes`)
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password)
    
    if (!isPasswordValid) {
      // Increment login attempts
      await this.handleFailedLogin(user.id)
      throw new UnauthorizedError('Invalid email or password')
    }

    // Reset login attempts on successful login
    await this.resetLoginAttempts(user.id)

    // Generate tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role as 'admin' | 'user'
    }

    const tokens = generateTokenPair(tokenPayload)

    return {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar || undefined
      },
      tokens
    }
  }

  /**
   * User registration
   */
  static async register(data: RegisterRequest): Promise<LoginResponse> {
    const { name, email, password, role = 'user' } = data

    // Validate input
    this.validateRequired(data, ['name', 'email', 'password'])

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (existingUser) {
      throw new ValidationError('User with this email already exists')
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, AUTH_CONFIG.BCRYPT_SALT_ROUNDS)

    // Create user with transaction
    const user = await prisma.$transaction(async (tx) => {
      // Create user
      const newUser = await tx.user.create({
        data: {
          name: this.sanitizeString(name),
          email: email.toLowerCase(),
          password: hashedPassword,
          role,
          isActive: true
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          avatar: true
        }
      })

      // Create default user preferences
      await tx.userPreferences.create({
        data: {
          userId: newUser.id,
          name: `${newUser.name} Preferences`,
          currency: 'VND',
          language: 'vi',
          theme: 'light'
        }
      })

      return newUser
    })

  

    // Generate tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role as 'admin' | 'user'
    }

    const tokens = generateTokenPair(tokenPayload)

    return {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar || undefined
      },
      tokens
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(refreshToken: string): Promise<LoginResponse> {
    try {
      // Verify refresh token
      const payload = verifyToken(refreshToken)

      // Check if user still exists and is active
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          avatar: true,
          isActive: true
        }
      })

      if (!user || !user.isActive) {
        throw new UnauthorizedError('User not found or inactive')
      }

      // Generate new token pair
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        role: user.role as 'admin' | 'user'
      }

      const tokens = generateTokenPair(tokenPayload)

      return {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          avatar: user.avatar || undefined
        },
        tokens
      }
    } catch (error) {
      throw new UnauthorizedError('Invalid or expired refresh token')
    }
  }

  /**
   * Verify request and extract user info
   */
  static async verifyRequest(request: NextRequest): Promise<{ userId: string; role: string; email: string }> {
    // Extract token from Authorization header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedError('Authorization token required')
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    try {
      // Verify token
      const payload = verifyToken(token)

      // Check if user still exists and is active
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: { id: true, isActive: true }
      })

      if (!user || !user.isActive) {
        throw new UnauthorizedError('User not found or inactive')
      }

      return {
        userId: payload.userId,
        role: payload.role,
        email: payload.email
      }
    } catch (error) {
      if (error instanceof UnauthorizedError) {
        throw error
      }
      throw new UnauthorizedError('Invalid or expired token')
    }
  }

  /**
   * Change user password
   */
  static async changePassword(
    userId: string, 
    data: ChangePasswordRequest
  ): Promise<{ message: string }> {
    const { currentPassword, newPassword } = data

    // Validate input
    this.validateRequired(data, ['currentPassword', 'newPassword'])

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, password: true }
    })

    if (!user) {
      throw new NotFoundError('User')
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
    
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedError('Current password is incorrect')
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, AUTH_CONFIG.BCRYPT_SALT_ROUNDS)

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword }
    })

    return { message: 'Password changed successfully' }
  }

  /**
   * Handle failed login attempt
   */
  private static async handleFailedLogin(userId: string): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { loginAttempts: true }
    })

    const attempts = (user?.loginAttempts || 0) + 1
    const updateData: any = { loginAttempts: attempts }

    // Lock account if max attempts reached
    if (attempts >= AUTH_CONFIG.MAX_LOGIN_ATTEMPTS) {
      updateData.lockedUntil = new Date(Date.now() + AUTH_CONFIG.LOCKOUT_TIME)
      updateData.loginAttempts = 0 // Reset attempts after locking
    }

    await prisma.user.update({
      where: { id: userId },
      data: updateData
    })
  }

  /**
   * Reset login attempts on successful login
   */
  private static async resetLoginAttempts(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: {
        loginAttempts: 0,
        lockedUntil: null
      }
    })
  }
}

export default AuthService
