generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model UserInformation {
  id         String    @id @default(uuid()) @db.Uuid
  userId     String    @unique @db.Uuid
  lastLogin  DateTime?
  loginCount Int       @default(0)
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_information")
}

model UserPreferences {
  id           String    @id @default(uuid()) @db.Uuid
  name         String
  description  String?
  isActive     Boolean   @default(true)
  userId       String    @unique @db.Uuid
  currency     String    @default("VND")
  language     Language  @default(vi)
  theme        Theme     @default(system)
  dateFormat   String?   @default("DD/MM/YYYY")
  numberFormat String?   @default("1,000.00")
  timezone     String?   @default("Asia/Ho_Chi_Minh")
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model User {
  id          String    @id @default(uuid()) @db.Uuid
  email       String    @unique
  password    String
  role        String    @default("user")
  isActive    Boolean   @default(true)
  isVerified  Boolean   @default(false)
  lastLoginAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  // Relations
  userInformation                   UserInformation?
  accounts                          Account[]
  auditLogs                         AuditLog[]
  bankInfos                         BankInfo[]
  categories                        Category[]
  categoryGoals                     CategoryGoal[]
  contacts                          Contact[]
  categoryTransactionExecutionInfos CategoryTransactionExecutionInfo[]
  categoryPeriodRecords             CategoryPeriodRecord[]
  transactions                      Transaction[]
  preferences                       UserPreferences?
  debitCards                        DebitCard[]
  paymentApps                       PaymentApp[]

  @@map("users")
}

model AccountType {
  id          String          @id @default(uuid()) @db.Uuid
  name        String
  description String?
  color       String          @default("#45B7D1")
  type        AccountTypeType @default(none)
  isActive    Boolean         @default(true)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  deletedAt   DateTime?

  // Relations
  accounts Account[]

  @@map("account_types")
}

model Account {
  id            String    @id @default(uuid()) @db.Uuid
  name          String
  description   String?
  accountTypeId String    @db.Uuid
  balance       Float     @default(0)
  currency      String    @default("VND")
  color         String    @default("#45B7D1")
  userId        String    @db.Uuid
  contactId     String?   @db.Uuid
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deletedAt     DateTime?

  // Relations
  accountType            AccountType             @relation(fields: [accountTypeId], references: [id])
  contact                Contact?                @relation(fields: [contactId], references: [id])
  user                   User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  bankInfo               BankInfo?
  debitCards             DebitCard[]
  accountDebitCardLinks  AccountDebitCardLink[]
  accountPaymentAppLinks AccountPaymentAppLink[]
  categoryGoals          CategoryGoal[]

  // Transaction Relations - Fixed relation names to match Transaction model
  transferFromTransactions  Transaction[] @relation("TransferFromAccount")
  transferToTransactions    Transaction[] @relation("TransferToAccount")
  connectedFromTransactions Transaction[] @relation("ConnectedFromAccountTransactions")
  connectedToTransactions   Transaction[] @relation("ConnectedToAccountTransactions")

  // Indexes
  @@index([userId])
  @@index([accountTypeId])
  @@index([contactId])
  @@index([isActive])
  @@index([createdAt])
  @@map("accounts")
}

model DebitCard {
  id             String    @id @default(uuid()) @db.Uuid
  cardNumber     String?   @unique
  cardHolderName String?
  bankName       String?
  note           String?
  userId         String    @db.Uuid
  accountId      String?   @db.Uuid
  isActive       Boolean   @default(true)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?

  // Relations
  user                  User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  account               Account?               @relation(fields: [accountId], references: [id])
  accountDebitCardLinks AccountDebitCardLink[]

  // Transaction Relations - Fixed relation names to match Transaction model
  connectedFromTransactions Transaction[] @relation("ConnectedFromDebitCardTransactions")
  connectedToTransactions   Transaction[] @relation("ConnectedToDebitCardTransactions")

  @@map("debit_cards")
}

model PaymentApp {
  id          String    @id @default(uuid()) @db.Uuid
  name        String // Momo, ZaloPay, ViettelPay, etc.
  description String?
  color       String    @default("#45B7D1")
  userId      String    @db.Uuid
  accountName String? // Account name in the payment app
  phoneNumber String? // Phone number linked to payment app
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  // Relations
  user                   User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  accountPaymentAppLinks AccountPaymentAppLink[]

  // Transaction Relations - Fixed relation names to match Transaction model
  connectedFromTransactions Transaction[] @relation("ConnectedFromPaymentAppTransactions")
  connectedToTransactions   Transaction[] @relation("ConnectedToPaymentAppTransactions")

  @@map("payment_apps")
}

model AccountDebitCardLink {
  id          String    @id @default(uuid()) @db.Uuid
  accountId   String    @db.Uuid
  debitCardId String    @db.Uuid
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  // Relations
  account   Account   @relation(fields: [accountId], references: [id], onDelete: Cascade)
  debitCard DebitCard @relation(fields: [debitCardId], references: [id], onDelete: Cascade)

  @@unique([accountId, debitCardId])
  @@map("account_debit_card_links")
}

model AccountPaymentAppLink {
  id           String    @id @default(uuid()) @db.Uuid
  accountId    String    @db.Uuid
  paymentAppId String    @db.Uuid
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  // Relations
  account    Account    @relation(fields: [accountId], references: [id], onDelete: Cascade)
  paymentApp PaymentApp @relation(fields: [paymentAppId], references: [id], onDelete: Cascade)

  @@unique([accountId, paymentAppId])
  @@map("account_payment_app_links")
}

model BankInfo {
  id                String    @id @default(uuid()) @db.Uuid
  userId            String    @db.Uuid
  accountId         String    @unique @db.Uuid
  bankName          String?
  bankCode          String? // Bank code for identification
  accountNumber     String?
  accountHolderName String?
  branchName        String?
  note              String?
  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  deletedAt         DateTime?

  // Relations
  account Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("bank_info")
}

model ContactType {
  id          String    @id @default(uuid()) @db.Uuid
  name        String
  description String?
  color       String    @default("#45B7D1")
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  // Relations
  contacts Contact[]

  @@map("contact_types")
}

model Contact {
  id            String    @id @default(uuid()) @db.Uuid
  name          String
  description   String?
  contactTypeId String    @db.Uuid
  phone         String?
  email         String?
  address       String?
  isActive      Boolean   @default(true)
  userId        String    @db.Uuid
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deletedAt     DateTime?

  // Relations
  accounts    Account[]
  contactType ContactType @relation(fields: [contactTypeId], references: [id])
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([contactTypeId])
  @@map("contacts")
}

model Category {
  id          String       @id @default(uuid()) @db.Uuid
  name        String
  description String?
  type        CategoryType @default(none)
  color       String       @default("#45B7D1")
  userId      String       @db.Uuid
  parentId    String?      @db.Uuid
  isDefault   Boolean      @default(false)
  isActive    Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  deletedAt   DateTime?

  // Relations
  parent                            Category?                          @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children                          Category[]                         @relation("CategoryHierarchy")
  user                              User                               @relation(fields: [userId], references: [id], onDelete: Cascade)
  categoryGoals                     CategoryGoal[]
  transactions                      Transaction[]
  categoryTransactionExecutionInfos CategoryTransactionExecutionInfo[]
  categoryPeriodRecords             CategoryPeriodRecord[]

  @@index([userId])
  @@index([parentId])
  @@index([type])
  @@index([isActive])
  @@map("categories")
}

model Transaction {
  id           String          @id @default(uuid()) @db.Uuid
  type         TransactionType
  amount       Float
  date         DateTime
  description  String?
  location     String?
  note         String?
  attachments  String[]        @default([])
  tags         String[]        @default([])
  isCleared    Boolean         @default(false)
  isReconciled Boolean         @default(false)
  userId       String          @db.Uuid
  isActive     Boolean         @default(true)
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  deletedAt    DateTime?

  // Account relations
  categoryId                String? @db.Uuid
  fromAccountId             String? @db.Uuid
  toAccountId               String? @db.Uuid
  connectedFromAccountId    String? @db.Uuid
  connectedToAccountId      String? @db.Uuid
  connectedFromDebitCardId  String? @db.Uuid
  connectedToDebitCardId    String? @db.Uuid
  connectedFromPaymentAppId String? @db.Uuid
  connectedToPaymentAppId   String? @db.Uuid

  // Relations
  category                          Category?                          @relation(fields: [categoryId], references: [id])
  fromAccount                       Account?                           @relation("TransferFromAccount", fields: [fromAccountId], references: [id])
  toAccount                         Account?                           @relation("TransferToAccount", fields: [toAccountId], references: [id])
  connectedFromAccount              Account?                           @relation("ConnectedFromAccountTransactions", fields: [connectedFromAccountId], references: [id])
  connectedToAccount                Account?                           @relation("ConnectedToAccountTransactions", fields: [connectedToAccountId], references: [id])
  connectedFromDebitCard            DebitCard?                         @relation("ConnectedFromDebitCardTransactions", fields: [connectedFromDebitCardId], references: [id])
  connectedToDebitCard              DebitCard?                         @relation("ConnectedToDebitCardTransactions", fields: [connectedToDebitCardId], references: [id])
  connectedFromPaymentApp           PaymentApp?                        @relation("ConnectedFromPaymentAppTransactions", fields: [connectedFromPaymentAppId], references: [id])
  connectedToPaymentApp             PaymentApp?                        @relation("ConnectedToPaymentAppTransactions", fields: [connectedToPaymentAppId], references: [id])
  user                              User                               @relation(fields: [userId], references: [id], onDelete: Cascade)
  categoryTransactionExecutionInfos CategoryTransactionExecutionInfo[]

  // Indexes
  @@index([userId])
  @@index([categoryId])
  @@index([fromAccountId])
  @@index([toAccountId])
  @@index([connectedFromAccountId])
  @@index([connectedToAccountId])
  @@index([connectedFromDebitCardId])
  @@index([connectedToDebitCardId])
  @@index([connectedFromPaymentAppId])
  @@index([connectedToPaymentAppId])
  @@index([type])
  @@index([date])
  @@index([isActive])
  @@map("transactions")
}

model CategoryTransactionExecutionInfo {
  id              String    @id @default(uuid()) @db.Uuid
  transactionId   String    @db.Uuid
  categoryId      String    @db.Uuid
  categoryGoalId  String?   @db.Uuid
  userId          String    @db.Uuid
  allocatedAmount Float? // Amount allocated to this category for this transaction
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  deletedAt       DateTime?

  // Relations
  transaction  Transaction   @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  category     Category      @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  categoryGoal CategoryGoal? @relation(fields: [categoryGoalId], references: [id])

  @@index([transactionId])
  @@index([categoryId])
  @@index([userId])
  @@map("category_transaction_execution_info")
}

model CategoryPeriodRecord {
  id             String    @id @default(uuid()) @db.Uuid
  categoryGoalId String    @db.Uuid
  categoryId     String    @db.Uuid
  userId         String    @db.Uuid
  targetAmount   Float?    @map("target_amount")
  reachedAmount  Float?    @map("reached_amount")
  periodStart    DateTime? @map("period_start")
  periodEnd      DateTime? @map("period_end")
  isActive       Boolean   @default(true)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?

  // Relations
  categoryGoal CategoryGoal @relation(fields: [categoryGoalId], references: [id], onDelete: Cascade)
  category     Category     @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([categoryGoalId])
  @@index([categoryId])
  @@index([userId])
  @@map("category_period_records")
}

model CategoryGoal {
  id           String    @id @default(uuid()) @db.Uuid
  name         String?
  description  String?
  targetAmount Float?    @map("target_amount")
  period       Period?   @default(none)
  startDate    DateTime?
  endDate      DateTime?
  transferFrom String?   @db.Uuid // Account ID to transfer from
  transferTo   String?   @db.Uuid // Account ID to transfer to
  isRecurring  Boolean   @default(false)
  isDefault    Boolean   @default(false)
  isActive     Boolean   @default(true)
  userId       String    @db.Uuid
  categoryId   String?   @db.Uuid
  accountId    String?   @db.Uuid
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  // Relations
  categoryTransactionExecutionInfos CategoryTransactionExecutionInfo[]
  categoryPeriodRecords             CategoryPeriodRecord[]
  category                          Category?                          @relation(fields: [categoryId], references: [id])
  user                              User                               @relation(fields: [userId], references: [id], onDelete: Cascade)
  account                           Account?                           @relation(fields: [accountId], references: [id])

  @@index([userId])
  @@index([categoryId])
  @@index([accountId])
  @@index([isActive])
  @@index([startDate])
  @@index([endDate])
  @@map("category_goals")
}

model AuditLog {
  id        String      @id @default(uuid()) @db.Uuid
  action    AuditAction
  tableName String
  recordId  String      @db.Uuid
  oldValues Json?
  newValues Json?
  userId    String      @db.Uuid
  ipAddress String?
  userAgent String?
  createdAt DateTime    @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([tableName])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}

// Enums
enum Language {
  vi
  en
  zh
  ja
  ko
}

enum Theme {
  light
  dark
  system
}

enum CategoryType {
  income
  expense
  transfer
  none
}

enum TransactionType {
  income
  expense
  transfer
}

enum AuditAction {
  create
  update
  delete
  restore
}

enum Period {
  none
  daily
  weekly
  monthly
  quarterly
  yearly
  custom
}

enum AccountTypeType {
  none
  bank
  payment_app
}
