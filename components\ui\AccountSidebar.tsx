"use client"

import { Building2, <PERSON><PERSON>ard, DollarSign, FileText, Smartphone, Wallet } from "lucide-react"
import React, { useEffect, useRef, useState } from "react"
import { BaseSidebar } from "./BaseSidebar"
import { SearchableCombobox } from "./SearchableCombobox"
import { SidebarActionFooter } from "./SidebarFooter"
import { SidebarForm, SidebarInputField, SidebarTextareaField } from "./SidebarForm"

import { Account, AccountType } from "@/lib/generated/prisma"
import { AccountPaymentAppApiService, AccountTypeApiService, DebitCardApiService } from "@/services/client"
import { AccountPaymentAppWithRelations, AccountWithRelations, DebitCardWithRelations } from "@/types/entities"
import { DebitCardCard } from "./DebitCardCard"
import { PaymentAppLinkCard } from "./PaymentAppLinkCard"

// Account interface is now imported from generated Prisma types

interface AccountSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (account: AccountWithRelations) => void
  account?: AccountWithRelations | null
  isEditing: boolean

}

function getAccountTypeLabel(accountType: AccountType) {
  return{
    name: accountType.name,
    type: accountType.isBankAccount ? "Ngân hàng" : accountType.isPaymentApp ? "Ứng dụng thanh toán" : "Tiền mặt"
  }
}

interface FormData {
  name: string
  type: string // Support dynamic account types
  balance: string
  currency: string
  description: string
  bankName: string
  accountNumber: string
  debitCardsIdLinked: string[]
  paymentAppsIdLinked: string[]
}

interface FormErrors {
  name?: string
  type?: string
  balance?: string
  currency?: string
  description?: string
  bankName?: string
  accountNumber?: string
  debitCardsIdLinked?: string[]
  paymentAppsIdLinked?: string[]
}

// Account types are now managed dynamically





export function AccountSidebar({
  isOpen,
  onClose,
  onSave,
  account,
  isEditing,
}: AccountSidebarProps) {
  // first load the account with relations full Infor 
  // Account types management
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([])
  const [selectedAccountType, setSelectedAccountType] = useState<AccountType | null>(null)
  const [loadingAccountTypes, setLoadingAccountTypes] = useState(true)

  // Refs to prevent duplicate API calls
  const accountTypesLoadedRef = useRef(false)
  const linkedItemsLoadedRef = useRef<string | null>(null) // Track which account's items are loaded
  const availableItemsLoadedRef = useRef<string | null>(null) // Track which account's available items are loaded

  // Debit cards and payment app links
  const [debitCards, setDebitCards] = useState<DebitCardWithRelations[]>([])
  const [paymentAppLinks, setPaymentAppLinks] = useState<AccountPaymentAppWithRelations[]>([])
  const [loadingDebitCards, setLoadingDebitCards] = useState(false)
  const [loadingPaymentApps, setLoadingPaymentApps] = useState(false)

  // Available items for linking
  const [availableDebitCards, setAvailableDebitCards] = useState<DebitCardWithRelations[]>([])
  const [availablePaymentApps, setAvailablePaymentApps] = useState<Account[]>([])
  const [loadingAvailableItems, setLoadingAvailableItems] = useState(false)
  const loadedLinkedItemsRef = useRef(false)

  // Error handling
  const [errorMessage, setErrorMessage] = useState<string | null>(null)



  const [formData, setFormData] = useState<FormData>({
    name: "",
    type: "cash",
    balance: "",
    currency: "VND",
    description: "",
    bankName: "",
    accountNumber: "",
    debitCardsIdLinked: [],
    paymentAppsIdLinked: []
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
 
  // Load account types on component mount - only once
  useEffect(() => {
    const loadAccountTypes = async () => {
      // Prevent duplicate calls
      if (accountTypesLoadedRef.current) {
        return
      }

      try {
        accountTypesLoadedRef.current = true
        setLoadingAccountTypes(true)
        console.log('🔄 Loading account types...')

        const response = await AccountTypeApiService.getActiveAccountTypes()
        if (response.success && response.data) {
          setAccountTypes(response.data)
          console.log('✅ Account types loaded:', response.data.length)

          // Set default selected type to first cash type (by name) or first available
          const defaultType = response.data.find(type => type.name === 'Tiền mặt') || response.data[0]
          if (defaultType) {
            setSelectedAccountType(defaultType)
          }
        }
      } catch (error) {
        console.error('❌ Failed to load account types:', error)
        // Reset ref on error so it can retry
        accountTypesLoadedRef.current = false
      } finally {
        setLoadingAccountTypes(false)
      }
    }

 

    // Only load if we haven't loaded yet
    if (!accountTypesLoadedRef.current && accountTypes.length === 0) {
      loadAccountTypes()
    }
  
  }, []) // Empty dependency array - only run once




  // ESC key handler
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey)
      return () => document.removeEventListener('keydown', handleEscKey)
    }
  }, [isOpen])

  // Load debit cards and payment app links for editing account - only once per account
  const loadAccountRelatedData = async (accountId: string, forceReload = false) => {
    if (!selectedAccountType?.isBankAccount) {
      console.log('⏭️ Skipping loadAccountRelatedData - not a bank account')
      return
    }

    // Prevent duplicate calls unless forced reload
    if (!forceReload && linkedItemsLoadedRef.current === accountId) {
      console.log('⏭️ Skipping loadAccountRelatedData - already loaded for account:', accountId)
      return
    }

    try {
      console.log('📥 Loading account related data for:', accountId)
      linkedItemsLoadedRef.current = accountId
      setLoadingDebitCards(true)
      setLoadingPaymentApps(true)

      const [debitCardsResponse, paymentAppsResponse] = await Promise.all([
        DebitCardApiService.getDebitCardsByAccount(accountId),
        AccountPaymentAppApiService.getPaymentAppsByAccount(accountId)
      ])

      console.log('📥 Loaded debit cards:', debitCardsResponse)
      console.log('📥 Loaded payment apps:', paymentAppsResponse)

      setDebitCards(Array.isArray(debitCardsResponse) ? debitCardsResponse as DebitCardWithRelations[] : [])
      setPaymentAppLinks(Array.isArray(paymentAppsResponse) ? paymentAppsResponse as AccountPaymentAppWithRelations[] : [])



      console.log('✅ Account related data loaded and state updated')
    } catch (error) {
      console.error('❌ Error loading account related data:', error)
      // Reset ref on error so it can retry
      linkedItemsLoadedRef.current = null
    } finally {
      setLoadingDebitCards(false)
      setLoadingPaymentApps(false)
    }
  }

  // Load available items for linking - only once per account
  const loadAvailableItems = async (forceReload = false) => {
    if (!selectedAccountType?.isBankAccount || !account?.id) {
      console.log('⏭️ Skipping loadAvailableItems - not a bank account or no account ID')
      return
    }

    // Prevent duplicate calls unless forced reload
    if (!forceReload && availableItemsLoadedRef.current === account.id) {
      console.log('⏭️ Skipping loadAvailableItems - already loaded for account:', account.id)
      return
    }

    try {
      console.log('📥 Loading available items for account:', account.id)
      availableItemsLoadedRef.current = account.id
      setLoadingAvailableItems(true)

      const [availableCardsResponse, availableAppsResponse] = await Promise.all([
        // Load available debit cards (not linked to current account)
        DebitCardApiService.getAvailableDebitCards(account.id),
        // Load available payment apps (not linked to current account)
        AccountPaymentAppApiService.getAvailablePaymentApps(account.id)
      ])

      console.log('📥 Loaded available cards response:', availableCardsResponse)
      console.log('📥 Loaded available apps response:', availableAppsResponse)

      // Extract data from API responses
      const availableCards = availableCardsResponse.success ? (availableCardsResponse.data || []) : []
      const availableApps = availableAppsResponse.success ? (availableAppsResponse.data || []) : []

      setAvailableDebitCards(Array.isArray(availableCards) ? availableCards as DebitCardWithRelations[] : [])
      setAvailablePaymentApps(Array.isArray(availableApps) ? availableApps : [])

      console.log('✅ Available items loaded and state updated')

    } catch (error) {
      console.error('❌ Error loading available items:', error)
      setAvailableDebitCards([])
      setAvailablePaymentApps([])
      // Reset ref on error so it can retry
      availableItemsLoadedRef.current = null
    } finally {
      setLoadingAvailableItems(false)
    }
  }

  // Initialize form data when sidebar opens or account changes
  useEffect(() => {
    if (isOpen) {
      if (account && isEditing) {
        console.log('Editing account:', account)
        console.log('Available accountTypes:', accountTypes)
        console.log('Looking for accountTypeId:', account.accountTypeId)

        const newFormData = {
          name: account.name,
          type: account.accountTypeId,
          balance: account.balance.toString(),
          currency: account.currency,
          description: account.description || "",
          bankName: account.bankInfo?.bankName || "",
          accountNumber: account.bankInfo?.accountNumber || "",
          debitCardsIdLinked: debitCards.map(card => card.id) || [],
          paymentAppsIdLinked: paymentAppLinks.map(link => link.paymentAppId) || []
        }
        console.log('Setting formData for edit:', newFormData)
        console.log('Account bankInfo:', account.bankInfo)
        setFormData(newFormData)

        // Set selected account type
        const accountType = accountTypes.find(t => t.id === account.accountTypeId)
        console.log('Found accountType:', accountType)

        if (accountType) {
          console.log('Setting selectedAccountType to:', accountType)
          setSelectedAccountType(accountType)

          // Load related data if it's a bank account
          if (accountType.isBankAccount) {
            loadAccountRelatedData(account.id)
          }
        } else {
          console.warn('Account type not found for id:', account.accountTypeId)
          console.log('Setting selectedAccountType to first available:', accountTypes[0])
          setSelectedAccountType(accountTypes[0])
        }
      } else {
        // Find default cash type or use first available
        const defaultType = accountTypes.find(type => type.name === 'Tiền mặt') || accountTypes[0]
        const initialFormData = {
          name: "",
          type: defaultType?.id || "",
          balance: "",
          currency: "VND",
          description: "",
          bankName: "",
          accountNumber: "",
          debitCardsIdLinked: [],
          paymentAppsIdLinked: []
        }
        console.log('Initializing new account form with:', initialFormData)
        console.log('Setting selectedAccountType to:', defaultType)
        setFormData(initialFormData)
        setSelectedAccountType(defaultType || null)

        // Clear related data
        setDebitCards([])
        setPaymentAppLinks([])
      }
      setErrors({})
    }
  }, [isOpen, account, isEditing, accountTypes])

  // Load linked and available items when sidebar opens for editing a bank account
  useEffect(() => {
    if (isOpen && isEditing && selectedAccountType?.isBankAccount && account?.id) {
      // Load linked items (only once per account)
      loadAccountRelatedData(account.id)
      // Load available items (only once per account)
      loadAvailableItems()
    }
  }, [isOpen, isEditing, selectedAccountType?.isBankAccount, account?.id])

  // Reset pending changes when sidebar closes
  useEffect(() => {
    if (!isOpen) {

      setErrorMessage(null)

      // Reset loading refs when sidebar closes
      linkedItemsLoadedRef.current = null
      availableItemsLoadedRef.current = null
    }
  }, [isOpen])

  // Account type management functions
  const handleTypeSelect = (type: AccountType) => {
    console.log('handleTypeSelect called with:', type)
    console.log('Current selectedAccountType before update:', selectedAccountType)
    setSelectedAccountType(type)
    setFormData(prev => {
      const newFormData = { ...prev, type: type.id }
      console.log('Updated formData:', newFormData)
      return newFormData
    })
  }



  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) { 
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) newErrors.name = "Tên tài khoản là bắt buộc"
    if (!formData.type || !selectedAccountType) newErrors.type = "Vui lòng chọn loại tài khoản"
    if (!formData.balance || isNaN(parseFloat(formData.balance))) {
      newErrors.balance = "Số dư phải là số hợp lệ"
    }
    if (!formData.currency) newErrors.currency = "Loại tiền tệ là bắt buộc"

    // Validate bank fields if account type is a bank account
    if (selectedAccountType?.isBankAccount) {
      if (!formData.bankName.trim()) newErrors.bankName = "Tên ngân hàng là bắt buộc"
      if (!formData.accountNumber.trim()) newErrors.accountNumber = "Số tài khoản là bắt buộc"
    }

    console.log('Validation - formData.type:', formData.type)
    console.log('Validation - selectedAccountType:', selectedAccountType)
    console.log('Validation - isBankAccount:', selectedAccountType?.isBankAccount)
    console.log('Validation errors:', newErrors)
    console.log('Validation result:', Object.keys(newErrors).length === 0)

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Link handlers - optimistic updates only, no immediate API calls
  const handleLinkDebitCard = (debitCardId: string, accountId: string) => {
    // Find the card to be linked for optimistic update
    const cardToLink = availableDebitCards.find(card => card.id === debitCardId)
    if (!cardToLink) {
      console.error('❌ Card not found in available cards:', debitCardId)
      return
    }

    console.log('🔗 Optimistic debit card linking:', { debitCardId, accountId })

    // Optimistic update: immediately move card from available to linked
    setAvailableDebitCards(prev => (prev || []).filter(card => card.id !== debitCardId))
    setDebitCards(prev => [...(prev || []), { ...cardToLink, accountId } as DebitCardWithRelations])


    console.log('✅ Debit card optimistically linked - will save on form submit')
  }

  const handleLinkPaymentApp = (paymentAppId: string, accountId: string) => {
    // Find the app to be linked for optimistic update
    const appToLink = availablePaymentApps.find(app => app.id === paymentAppId)
    if (!appToLink) {
      console.error('❌ Payment app not found in available apps:', paymentAppId)
      return
    }

    console.log('🔗 Optimistic payment app linking:', { paymentAppId, accountId })

    // Optimistic update: immediately move app from available to linked
    setAvailablePaymentApps(prev => prev.filter(app => app.id !== paymentAppId))
    const newLink: AccountPaymentAppWithRelations = {
      id: `temp-${Date.now()}`, // Temporary ID
      accountId,
      paymentAppId,
      paymentApp: appToLink,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: '', // Will be set by server
      deletedAt: null
    }
    setPaymentAppLinks(prev => [...(prev || []), newLink])



    console.log('✅ Payment app optimistically linked - will save on form submit')
  }

  // Unlink handlers - optimistic updates only, no immediate API calls
  const handleUnlinkDebitCard = (debitCard: DebitCardWithRelations) => {
    console.log('🔗 Optimistic debit card unlinking:', debitCard.id, 'from account:', account?.id)

    // Optimistic update: immediately move card from linked to available
    setDebitCards(prev => (prev || []).filter(card => card.id !== debitCard.id))
    setAvailableDebitCards(prev => [...(prev || []), { ...debitCard }])

    // Track pending change
    // update formData
    setFormData(prev => ({ ...prev, debitCardsIdLinked: prev.debitCardsIdLinked.filter(id => id !== debitCard.id) }))

    console.log('✅ Debit card optimistically unlinked - will save on form submit')
  }

  const handleUnlinkPaymentApp = (paymentAppLink: AccountPaymentAppWithRelations) => {
    console.log('🔗 Optimistic payment app unlinking:', paymentAppLink.paymentAppId, 'from account:', account?.id)

    // Optimistic update: immediately move app from linked to available
    setPaymentAppLinks(prev => (prev || []).filter(link => link.id !== paymentAppLink.id))
    if (paymentAppLink.paymentApp) {
      setAvailablePaymentApps(prev => [...(prev || []), paymentAppLink.paymentApp!])
    }


    console.log('✅ Payment app optimistically unlinked - will save on form submit')
  }

 

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    console.log('handleSubmit called')
    console.log('Current formData:', formData)
    console.log('Current selectedAccountType:', selectedAccountType)

    if (!validateForm()) {
      console.log('Validation failed, returning early')
      return
    }

    console.log('Validation passed, proceeding with save')
    setLoading(true)
    try {
      console.log('Creating accountData object...')

      // Create bankInfo object if bank fields are provided and account type is a bank account
      let bankInfo = undefined
      if (selectedAccountType?.isBankAccount && (formData.bankName.trim() || formData.accountNumber.trim())) {
        bankInfo = {
          bankName: formData.bankName.trim() || undefined,
          accountNumber: formData.accountNumber.trim() || undefined
        }
      }

      const accountData: any = {
        id: account?.id || undefined,
        name: formData.name.trim(),
        accountTypeId: formData.type, // This is what handleSaveAccount expects
        balance: parseFloat(formData.balance),
        currency: formData.currency,
        description: formData.description.trim() || undefined,
        bankInfo: bankInfo,
        isActive: true,
        debitCardsIdLinked: formData.debitCardsIdLinked,
        paymentAppsIdLinked: formData.paymentAppsIdLinked
      }

      console.log('AccountData created:', accountData)
      console.log('Calling onSave...')
      onSave(accountData)
      console.log('onSave called successfully')


      handleClose()
    } catch (error) {
      console.error('Error saving account:', error)
      console.error('Error details:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      name: "",
      type: "cash",
      balance: "",
      currency: "VND",
      description: "",
      bankName: "",
      accountNumber: "",
      debitCardsIdLinked: [],
      paymentAppsIdLinked: []
    })
    setErrors({})


    setErrorMessage(null)

    // Reset loading refs
    linkedItemsLoadedRef.current = null
    availableItemsLoadedRef.current = null

    onClose()
  }



  // Sidebar configuration can be added here if needed



  const currencies = [
    { value: "VND", label: "VND (Vietnamese Dong)" },
    { value: "USD", label: "USD (US Dollar)" },
    { value: "EUR", label: "EUR (Euro)" }
  ]

  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa tài khoản" : "Thêm tài khoản mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${account?.name}` : "Tạo tài khoản mới để quản lý tài chính"}
      storageKey="accountSidebarWidth"
      config={{
        DEFAULT_WIDTH: 400,
        MIN_WIDTH: 350,
        MAX_WIDTH_PERCENTAGE: 0.6
      }}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={() => {
            const fakeEvent = { preventDefault: () => {} } as React.FormEvent
            handleSubmit(fakeEvent)
          }}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={
            !formData.name.trim() ||
            !formData.type ||
            !formData.balance ||
            (selectedAccountType?.isBankAccount && (!formData.bankName.trim() || !formData.accountNumber.trim()))
          }
          sidebarWidth={400}
        />
      }
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={400}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* Error Message */}
        {errorMessage && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{errorMessage}</p>
          </div>
        )}

        {/* Account Name */}
        <SidebarInputField
          label="Tên tài khoản"
          value={formData.name}
          onChange={(value) => handleInputChange('name', value)}
          placeholder="Nhập tên tài khoản"
          required
          error={errors.name}
          icon={<Wallet size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />

        {/* Account Type */}
        <div className="col-span-full space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <Building2 size={14} className="mr-2" />
            Loại tài khoản *
          </label>
          <SearchableCombobox
            value={selectedAccountType?.id || ""}
            onChange={(value) => {
              const accountType = accountTypes.find(type => type.id === value)
              if (accountType) {
                handleTypeSelect(accountType)
              }
            }}
            options={accountTypes.map(type => ({
              value: type.id,
              label: getAccountTypeLabel(type)
            }))}
            placeholder="Chọn loại tài khoản..."
            disabled={loading}
          />
          {errors.type && (
            <p className="text-xs text-red-500">{errors.type}</p>
          )}
        </div>

        {/* Currency */}
        {/* HIDDEN FOR NOW */}
        {/* <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <DollarSign size={14} className="mr-2" />
            Tiền tệ *
          </label>
          <SearchableCombobox
            value={formData.currency}
            onChange={(value) => handleInputChange('currency', value)}
            options={currencies}
            placeholder="Chọn tiền tệ..."
            disabled={loading}
          />
          {errors.currency && (
            <p className="text-xs text-red-500">{errors.currency}</p>
          )}
        </div> */}

        {/* Balance */}
        <SidebarInputField
          label="Số dư ban đầu"
          value={formData.balance}
          onChange={(value) => handleInputChange('balance', value)}
          placeholder="0"
          type="number"
          required
          error={errors.balance}
          icon={<DollarSign size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />

        {/* Bank Fields - Only show for bank account types */}
        {selectedAccountType?.isBankAccount && (
          <>
            {/* Bank Name */}
            <SidebarInputField
              label="Tên ngân hàng"
              value={formData.bankName}
              onChange={(value) => handleInputChange('bankName', value)}
              placeholder="Vietcombank, BIDV, Techcombank..."
              required
              error={errors.bankName}
              icon={<Building2 size={14} />}
              disabled={loading}
              span="full"
              sidebarWidth={400}
            />

            {/* Account Number */}
            <SidebarInputField
              label="Số tài khoản"
              value={formData.accountNumber}
              onChange={(value) => handleInputChange('accountNumber', value)}
              placeholder="**********"
              required
              error={errors.accountNumber}
              icon={<CreditCard size={14} />}
              disabled={loading}
              span="single"
              sidebarWidth={400}
            />


          </>
        )}


        {/* Description */}
        <SidebarTextareaField
          label="Mô tả"
          value={formData.description}
          onChange={(value) => handleInputChange('description', value)}
          placeholder="Mô tả thêm về tài khoản (tùy chọn)"
          rows={3}
          icon={<FileText size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />

        {/* Debit Cards Section - Only show for bank accounts when editing */}
        {isEditing && account && selectedAccountType?.isBankAccount && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900 flex items-center">
                <CreditCard size={16} className="mr-2" />
                Thẻ ghi nợ ({debitCards.length})
              </h4>
              <div className="relative">
                <SearchableCombobox
                  value=""
                  onChange={(debitCardId) => {
                    if (debitCardId && account?.id) {
                      handleLinkDebitCard(debitCardId, account.id)
                    }
                  }}
                  options={Array.isArray(availableDebitCards) ? availableDebitCards.map(card => ({
                    value: card.id,
                    label: `${card.cardName} (${card.cardNumber.slice(-4)})`
                  })) : []}
                  placeholder={loadingAvailableItems ? "Đang tải thẻ có sẵn..." : "Liên kết thẻ có sẵn..."}
                  disabled={loading || loadingAvailableItems || !account?.id}
                  className="text-sm"
                />
              </div>
            </div>

            {loadingDebitCards ? (
              <div className="text-sm text-gray-500">Đang tải...</div>
            ) : debitCards.length > 0 ? (
              <div className="space-y-2">
                {debitCards.map((card) => (
                  <DebitCardCard
                    key={card.id}
                    debitCard={card}
                    compact={true}
                    onEdit={ undefined}
                    onDelete={() => handleUnlinkDebitCard(card)}
                    onUnlink={() => handleUnlinkDebitCard(card)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-500 italic">
                Chưa có thẻ ghi nợ nào được liên kết
              </div>
            )}
          </div>
        )}

        {/* Payment App Links Section - Only show for bank accounts when editing */}
        {isEditing && account && selectedAccountType?.isBankAccount && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900 flex items-center">
                <Smartphone size={16} className="mr-2" />
                Ứng dụng thanh toán ({paymentAppLinks.length})
              </h4>
              <div className="relative">
                <SearchableCombobox
                  value=""
                  onChange={(paymentAppId) => {
                    if (paymentAppId && account?.id) {
                      handleLinkPaymentApp(paymentAppId, account.id)
                    }
                  }}
                  options={Array.isArray(availablePaymentApps) ? availablePaymentApps.map(app => ({
                    value: app.id,
                    label: `${app.name} (${app.description || 'Ứng dụng thanh toán'})`
                  })) : []}
                  placeholder={loadingAvailableItems ? "Đang tải ứng dụng có sẵn..." : "Liên kết ứng dụng có sẵn..."}
                  disabled={loading || loadingAvailableItems || !account?.id}
                  className="text-sm"
                />
              </div>
            </div>

            {loadingPaymentApps ? (
              <div className="text-sm text-gray-500">Đang tải...</div>
            ) : paymentAppLinks.length > 0 ? (
              <div className="space-y-2">
                {paymentAppLinks.map((link) => (
                  <PaymentAppLinkCard
                    key={link.id}
                    link={link}
                    compact={true}
                    viewMode="bank-to-app"
                    onUnlink={() => handleUnlinkPaymentApp(link)}
                    onToggleStatus={() => {/* TODO: Handle toggle status */}}
                  />
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-500 italic">
                Chưa có ứng dụng thanh toán nào được liên kết
              </div>
            )}
          </div>
        )}
      </SidebarForm>
    </BaseSidebar>
  )
}


