"use client"

import React, { useState, useRef, useEffect } from "react"
import { ChevronDown, User, Plus, Check } from "lucide-react"
import { Contact } from "@/lib/generated/prisma"

interface ContactSelectorProps {
  value: string
  onChange: (value: string) => void
  contacts: Contact[]
  placeholder?: string
  disabled?: boolean
  onContactSelect?: (contact: Contact) => void
  showCreateOption?: boolean
  onCreateContact?: () => void
  className?: string
}

export function ContactSelector({
  value,
  onChange,
  contacts,
  placeholder = "Chọn liên hệ",
  disabled = false,
  onContactSelect,
  showCreateOption = false,
  onCreateContact,
  className = "",
}: ContactSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchValue, setSearchValue] = useState("")
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Handle selection
  const handleSelect = (contactId: string, contact: Contact) => {
    onChange(contactId)
    if (onContactSelect) {
      onContactSelect(contact)
    }
    setIsOpen(false)
  }

  // Filter contacts by search term
  const filteredContacts = contacts.filter((contact) => {
    if (!searchValue) return true
    return contact.name.toLowerCase().includes(searchValue.toLowerCase())
  })

  // Find the selected contact
  const selectedContact = contacts.find((c) => c.id.toString() === value)

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        type="button"
        onClick={() => {
          if (!disabled) {
            setIsOpen(!isOpen)
            // Focus search input when opening
            if (!isOpen) {
              setTimeout(() => {
                inputRef.current?.focus()
              }, 10)
            }
          }
        }}
        className={`w-full flex items-center justify-between p-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-left transition-colors ${
          disabled
            ? "opacity-60 cursor-not-allowed"
            : "hover:border-gray-400 dark:hover:border-gray-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
        }`}
        disabled={disabled}
      >
        {selectedContact ? (
          <div className="flex items-center gap-2">
            <User size={16} className="text-gray-400" />
            <span className="text-gray-900 dark:text-gray-100 truncate max-w-[90%]">
              {selectedContact.name}
            </span>
          </div>
        ) : (
          <span className="text-gray-500 dark:text-gray-400">{placeholder}</span>
        )}
        <ChevronDown
          size={18}
          className={`text-gray-500 transition-transform ${isOpen ? "transform rotate-180" : ""}`}
        />
      </button>

      {isOpen && (
        <div className="absolute z-20 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden">
          <div className="p-2 border-b border-gray-200 dark:border-gray-700">
            <input
              ref={inputRef}
              type="text"
              placeholder="Tìm kiếm..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-700 border-none rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="max-h-64 overflow-y-auto p-1">
            {filteredContacts.length > 0 ? (
              filteredContacts.map((contact) => (
                <div
                  key={contact.id}
                  onClick={() => handleSelect(contact.id.toString(), contact)}
                  className={`flex items-center gap-2 px-3 py-2 cursor-pointer rounded transition-colors ${
                    contact.id.toString() === value
                      ? "bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                      : "hover:bg-gray-100 dark:hover:bg-gray-700/70"
                  }`}
                >
                  <User
                    size={16}
                    className={`flex-shrink-0 ${
                      contact.id.toString() === value
                        ? "text-blue-500"
                        : "text-gray-400 dark:text-gray-500"
                    }`}
                  />
                  <div className="flex-grow truncate">
                    <div className="font-medium text-sm">{contact.name}</div>
                    {contact.email && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {contact.email}
                      </div>
                    )}
                  </div>
                  {contact.id.toString() === value && (
                    <Check size={16} className="text-blue-500" />
                  )}
                </div>
              ))
            ) : (
              <div className="p-3 text-center text-gray-500 dark:text-gray-400 text-sm">
                Không tìm thấy liên hệ
              </div>
            )}

            {showCreateOption && onCreateContact && (
              <div
                onClick={onCreateContact}
                className="flex items-center gap-2 px-3 py-2 cursor-pointer rounded transition-colors border-t border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/70 text-blue-600 dark:text-blue-400 mt-1"
              >
                <Plus size={16} className="text-blue-500" />
                <div className="font-medium text-sm">Tạo liên hệ mới</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

interface AccountSelectorProps {
  contact: Contact | null
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  className?: string
}

export function BankAccountSelector({
  contact,
  value,
  onChange,
  disabled = false,
  className = ""
}: AccountSelectorProps) {
  if (!contact || !contact.accounts || contact.accounts.length === 0) {
    return (
      <div className={`text-sm text-gray-500 dark:text-gray-400 italic ${className}`}>
        Không có thông tin tài khoản ngân hàng
      </div>
    )
  }

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      <option value="">Chọn tài khoản ngân hàng</option>
      {contact.accounts.map((account) => (
        <option key={account.id} value={account.id.toString()}>
          {account.bankName || account.name} {account.accountNumber ? `- ${account.accountNumber}` : ''}
        </option>
      ))}
    </select>
  )
}

interface NewContactDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (permanent: boolean) => void
  contactName: string
  loading?: boolean
}

export function NewContactDialog({
  isOpen,
  onClose,
  onConfirm,
  contactName,
  loading = false
}: NewContactDialogProps) {
  const [addToPermanent, setAddToPermanent] = useState(true)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 w-full max-w-md">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Tạo liên hệ mới
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Liên hệ "{contactName}" chưa tồn tại trong danh bạ. Bạn có muốn tạo mới?
          </p>
          
          <div className="flex items-center gap-2 mb-6">
            <input
              type="checkbox"
              id="addToPermanent"
              checked={addToPermanent}
              onChange={(e) => setAddToPermanent(e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              disabled={loading}
            />
            <label htmlFor="addToPermanent" className="text-sm text-gray-700 dark:text-gray-300">
              Thêm vào danh bạ vĩnh viễn
            </label>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
            >
              Hủy
            </button>
            <button
              type="button"
              onClick={() => onConfirm(addToPermanent)}
              disabled={loading}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Đang tạo...
                </>
              ) : (
                addToPermanent ? 'Tạo & Lưu' : 'Chỉ lưu giao dịch'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
