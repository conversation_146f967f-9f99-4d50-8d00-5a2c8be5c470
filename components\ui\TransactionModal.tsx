"use client"

import React, { useState, useEffect } from "react"
import { Modal } from "./Modal"
import { But<PERSON> } from "./button"
import {
  DollarSign,
  Calendar,
  Clock,
  Building2,
  CreditCard,
  User,
  FileText,
  ArrowUpDown,
  Plus,
  Trash2,
  Wallet,
  Tag
} from "lucide-react"
import { FormField, InputField, SelectField, CheckboxField, CardField } from "./FormField"
import { ContactSelector, BankAccountSelector, NewContactDialog } from "./ContactSelector"
import { AddEditContactModal } from "./AddEditContactModal"
import type { Transaction, Account, Category, Contact } from "@/lib/generated/prisma"
import type { TransactionKind } from "@/lib/generated/prisma"


interface TransactionModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (transaction: Transaction) => void
  transaction?: Transaction | null
  isEditing: boolean
  accounts: Account[]
  categories: Category[]
  contacts: Contact[]
  onContactCreated?: (contact: Contact) => void
}

interface FormData {
  accountId: string
  categoryId: string
  amount: string
  type: "income" | "expense" | "transfer"
  description: string
  date: string
  time: string
  transferTo: string
  transferToContactId: string
  transferToBankAccountId: string
  transferToNote: string
  transferFromContactId: string
  transferFromBankAccountId: string
  transferFromNote: string
  isRecurring: boolean
}

interface FormErrors {
  accountId?: string
  categoryId?: string
  amount?: string
  description?: string
  transferTo?: string
  date?: string
  time?: string
}

export function TransactionModal({
  isOpen,
  onClose,
  onSave,
  transaction,
  isEditing,
  accounts,
  categories,
  contacts,
  onContactCreated
}: TransactionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    accountId: "",
    categoryId: "",
    amount: "",
    type: "expense",
    description: "",
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    transferTo: "",
    transferToContactId: "",
    transferToBankAccountId: "",
    transferToNote: "",
    transferFromContactId: "",
    transferFromNote: "",
    transferFromBankAccountId: "",
    isRecurring: false
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  const [showNewContactDialog, setShowNewContactDialog] = useState(false)
  const [showAddContactModal, setShowAddContactModal] = useState(false)
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [selectedTransferFromContact, setSelectedTransferFromContact] = useState<Contact | null>(null)
  const [pendingContactName, setPendingContactName] = useState("")

  // Initialize form data when modal opens or transaction changes
  useEffect(() => {
    if (isOpen) {
      if (transaction && isEditing) {
        setFormData({
          accountId: transaction.accountId.toString(),
          categoryId: transaction.categoryId.toString(),
          amount: transaction.amount.toString(),
          type: transaction.kind,
          description: transaction.description,
          date: transaction.date.toISOString().split('T')[0],
          time: transaction.date.toTimeString().slice(0, 5),
          transferTo: transaction.transferTo || "",
          transferToContactId: transaction.transferToDetails?.contactId?.toString() || "",
          transferToNote: transaction.transferToDetails?.note || "",
          transferFromContactId: transaction.fromAccount?.contactId?.toString() || "",
          transferFromNote: transaction.fromAccount?.note || "",
          isRecurring: false /* TODO: Add recurring support */
        })

        // Set selected contacts for editing
        if (transaction.transferToDetails?.contactId) {
          const contact = contacts.find(c => c.id === transaction.transferToDetails?.contactId)
          setSelectedContact(contact || null)
        }
        if (transaction.fromAccount?.contactId) {
          const contact = contacts.find(c => c.id === transaction.fromAccount?.contactId)
          setSelectedTransferFromContact(contact || null)
        }
      } else {
        // Reset form for new transaction
        setFormData({
          accountId: "",
          categoryId: "",
          amount: "",
          type: "expense",
          description: "",
          date: new Date().toISOString().split('T')[0],
          time: new Date().toTimeString().slice(0, 5),
          transferTo: "",
          transferToContactId: "",
          transferToBankAccountId: "",
          transferToNote: "",
          transferFromContactId: "",
          transferFromBankAccountId: "",
          transferFromNote: "",
          isRecurring: false
        })
        setSelectedContact(null)
        setSelectedTransferFromContact(null)
      }
      setErrors({})
    }
  }, [isOpen, transaction, isEditing, contacts])

  // Filter categories based on transaction type
  const availableCategories = categories.filter(cat => 
    formData.type === 'transfer' ? true : cat.type === formData.type
  )

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.accountId) newErrors.accountId = "Account is required"
    if (!formData.categoryId && formData.type !== 'transfer') newErrors.categoryId = "Category is required"
    if (!formData.amount || parseFloat(formData.amount) <= 0) newErrors.amount = "Valid amount is required"
    if (!formData.description.trim()) newErrors.description = "Description is required"
    if (formData.type === 'transfer' && !formData.transferTo.trim()) newErrors.transferTo = "Transfer recipient is required"
    if (!formData.date) newErrors.date = "Date is required"
    if (!formData.time) newErrors.time = "Time is required"

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    // Check if we need to create a new contact for transfer
    if (formData.type === 'transfer' && formData.transferTo) {
      const existingContact = contacts.find(contact =>
        contact.name.toLowerCase() === formData.transferTo.toLowerCase()
      )

      if (!existingContact && formData.transferTo.trim()) {
        setShowNewContactDialog(true)
        return
      }
    }

    await processTransaction()
  }

  const processTransaction = async () => {
    setLoading(true)

    try {
      // Combine date and time
      const combinedDateTime = new Date(`${formData.date}T${formData.time}:00`)

      // Prepare transfer details
      const transferToDetails: TransferDetails | undefined = formData.transferToContactId ? {
        contactId: parseInt(formData.transferToContactId),
        note: formData.transferToNote.trim() || undefined
      } : undefined

      const transferFromDetails: TransferDetails | undefined = formData.transferFromContactId ? {
        contactId: parseInt(formData.transferFromContactId),
        note: formData.transferFromNote.trim() || undefined
      } : undefined

      const transactionData: Transaction = {
        id: transaction?.id || 0,
        userId: 1, // Default user
        accountId: parseInt(formData.accountId),
        categoryId: parseInt(formData.categoryId),
        amount: parseFloat(formData.amount),
        type: formData.type,
        description: formData.description.trim(),
        date: combinedDateTime,
        transferTo: formData.transferTo.trim() || undefined,
        transferToDetails,
        transferFromDetails,

        isRecurring: formData.isRecurring,
        recurringId: transaction?.recurringId,
        createdAt: transaction?.createdAt || new Date(),
        updatedAt: new Date()
      }

      onSave(transactionData)
      handleClose()
    } catch (error) {
      console.error('Error saving transaction:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      accountId: "",
      categoryId: "",
      amount: "",
      type: "expense",
      description: "",
      date: new Date().toISOString().split('T')[0],
      time: new Date().toTimeString().slice(0, 5),
      transferTo: "",
      transferToContactId: "",
      transferToBankAccountId: "",
      transferToNote: "",
      transferFromContactId: "",
      transferFromBankAccountId: "",
      transferFromNote: "",
      isRecurring: false
    })
    setErrors({})
    setShowNewContactDialog(false)
    onClose()
  }



  const handleCreateContact = () => {
    setPendingContactName(formData.transferTo)
    setShowAddContactModal(true)
  }

  const handleContactCreated = (newContact: Contact) => {
    // Close the contact modal
    setShowAddContactModal(false)

    // Update the contacts list in parent component
    onContactCreated?.(newContact)

    // Auto-select the new contact
    setSelectedContact(newContact)
    handleInputChange('transferTo', newContact.name)
    handleInputChange('transferToContactId', newContact.id.toString())

    // Auto-select default bank account if available
    const defaultAccount = newContact.accounts?.[0]
    if (defaultAccount) {
      handleInputChange('transferToBankAccountId', defaultAccount.id.toString())
    }

    // Clear pending contact name
    setPendingContactName("")
  }

  const handleCloseContactModal = () => {
    setShowAddContactModal(false)
    setPendingContactName("")
  }

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title={isEditing ? "Chỉnh sửa giao dịch" : "Thêm giao dịch mới"}
        subtitle={isEditing ? "Cập nhật thông tin giao dịch của bạn" : "Tạo một giao dịch mới trong hệ thống"}
        size="6xl"
        loading={loading}
      >
        <form onSubmit={handleSubmit} className="p-4 sm:p-5 lg:p-6 space-y-5 lg:space-y-6">
          {/* Transaction Type Selector */}
          <div className="flex items-center gap-1 p-1 bg-gray-100/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
            {(['expense', 'income', 'transfer'] as const).map((type) => (
              <button
                key={type}
                type="button"
                onClick={() => handleInputChange('type', type)}
                className={`flex-1 py-2.5 px-3 sm:px-4 rounded-xl text-xs sm:text-sm font-medium transition-all duration-300 ${
                  formData.type === type
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-lg shadow-gray-200/50 dark:shadow-gray-900/50 transform scale-105 border border-gray-200/50 dark:border-gray-600/50'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/30'
                }`}
              >
                <span className="hidden sm:inline">
                  {type === 'expense' ? 'Chi tiêu' : type === 'income' ? 'Thu nhập' : 'Chuyển khoản'}
                </span>
                <span className="sm:hidden">
                  {type === 'expense' ? 'Chi' : type === 'income' ? 'Thu' : 'CK'}
                </span>
              </button>
            ))}
          </div>

          {/* Main Form Content - Multi-column layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            {/* Left Column - Basic Information */}
            <div className="space-y-4 order-1">
              <CardField title="Thông tin cơ bản" icon={<FileText size={16} />}>
                {/* Account Selection */}
                <FormField
                  label="Tài khoản"
                  required
                  error={errors.accountId}
                  icon={<Wallet size={14} />}
                >
                  <SelectField
                    value={formData.accountId}
                    onChange={(value) => handleInputChange('accountId', value)}
                    options={accounts.map(account => ({
                      value: account.id,
                      label: `${account.name} (${account.type})`
                    }))}
                    placeholder="Chọn tài khoản"
                    disabled={loading}
                  />
                </FormField>

                {/* Category Selection - Hide for transfers */}
                {formData.type !== 'transfer' && (
                  <FormField
                    label="Danh mục"
                    required
                    error={errors.categoryId}
                    icon={<Tag size={14} />}
                  >
                    <SelectField
                      value={formData.categoryId}
                      onChange={(value) => handleInputChange('categoryId', value)}
                      options={availableCategories.map(category => ({
                        value: category.id,
                        label: category.name,
                        icon: category.icon
                      }))}
                      placeholder="Chọn danh mục"
                      disabled={loading}
                    />
                  </FormField>
                )}

                {/* Description */}
                <FormField
                  label="Mô tả"
                  required
                  error={errors.description}
                  icon={<FileText size={14} />}
                >
                  <InputField
                    value={formData.description}
                    onChange={(value) => handleInputChange('description', value)}
                    placeholder="Nhập mô tả giao dịch"
                    disabled={loading}
                  />
                </FormField>
              </CardField>
            </div>

            {/* Right Column - Amount & Date/Time */}
            <div className="space-y-4 order-2 lg:order-2">
              <CardField title="Số tiền & Thời gian" icon={<DollarSign size={16} />}>
                {/* Amount */}
                <FormField
                  label="Số tiền"
                  required
                  error={errors.amount}
                  icon={<DollarSign size={14} />}
                >
                  <InputField
                    type="number"
                    value={formData.amount}
                    onChange={(value) => handleInputChange('amount', value)}
                    placeholder="0"
                    min="0"
                    step="0.01"
                    disabled={loading}
                  />
                </FormField>

                {/* Date and Time */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <FormField
                    label="Ngày"
                    required
                    error={errors.date}
                    icon={<Calendar size={14} />}
                  >
                    <InputField
                      type="date"
                      value={formData.date}
                      onChange={(value) => handleInputChange('date', value)}
                      disabled={loading}
                    />
                  </FormField>

                  <FormField
                    label="Giờ"
                    required
                    error={errors.time}
                    icon={<Clock size={14} />}
                  >
                    <InputField
                      type="time"
                      value={formData.time}
                      onChange={(value) => handleInputChange('time', value)}
                      disabled={loading}
                    />
                  </FormField>
                </div>

                {/* Recurring checkbox */}
                <CheckboxField
                  checked={formData.isRecurring}
                  onChange={(checked) => handleInputChange('isRecurring', checked)}
                  label="Giao dịch định kỳ"
                  disabled={loading}
                  className="mt-4"
                />
              </CardField>
            </div>
          </div>

          {/* Transfer Fields - Full width when transfer type is selected */}
          {formData.type === 'transfer' && (
            <div className="lg:col-span-2 order-3">
              <CardField
                title="Thông tin chuyển khoản"
                icon={<ArrowUpDown size={16} className="text-blue-600 dark:text-blue-400" />}
                gradient={true}
                className="animate-in slide-in-from-top-2 duration-300"
              >
              {/* Transfer To */}
              <FormField
                label="Chuyển đến"
                required
                error={errors.transferTo}
                icon={<User size={14} />}
              >
                <ContactSelector
                  value={formData.transferTo}
                  onChange={(value) => handleInputChange('transferTo', value)}
                  contacts={contacts}
                  placeholder="Nhập tên người nhận hoặc chọn từ danh bạ"
                  disabled={loading}
                  onContactSelect={(contact) => {
                    setSelectedContact(contact)
                    handleInputChange('transferToContactId', contact.id.toString())

                  }}
                  onCreateContact={handleCreateContact}
                />
              </FormField>



              {/* Transfer Note */}
              <FormField
                label="Ghi chú chuyển khoản"
                icon={<FileText size={14} />}
              >
                <InputField
                  value={formData.transferToNote}
                  onChange={(value) => handleInputChange('transferToNote', value)}
                  placeholder="Ghi chú thêm (tùy chọn)"
                  disabled={loading}
                />
              </FormField>
              </CardField>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3 pt-5 border-t border-gray-200/30 dark:border-gray-700/30 order-4 lg:col-span-2">


            <div className="flex items-center gap-3 order-1 sm:order-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
                className="flex-1 sm:flex-none transition-all duration-200 text-sm rounded-xl border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                Hủy
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 transition-all duration-200 text-sm rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg shadow-blue-500/25"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span className="hidden sm:inline">Đang lưu...</span>
                    <span className="sm:hidden">Lưu...</span>
                  </>
                ) : (
                  <>
                    <Plus size={14} />
                    <span className="hidden sm:inline">{isEditing ? 'Cập nhật' : 'Thêm mới'}</span>
                    <span className="sm:hidden">{isEditing ? 'Sửa' : 'Thêm'}</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </Modal>

      {/* New Contact Creation Dialog */}
      <NewContactDialog
        isOpen={showNewContactDialog}
        onClose={() => setShowNewContactDialog(false)}
        onConfirm={async (permanent) => {
          setShowNewContactDialog(false)
          // Here you would typically create the contact if permanent is true
          // For now, just proceed with the transaction
          console.log('Creating contact permanently:', permanent)
          await processTransaction()
        }}
        contactName={formData.transferTo}
        loading={loading}
      />

      {/* Add/Edit Contact Modal */}
      <AddEditContactModal
        isOpen={showAddContactModal}
        onClose={handleCloseContactModal}
        onSave={handleContactCreated}
        contact={null}
        isEditing={false}
      />
    </>
  )
}
