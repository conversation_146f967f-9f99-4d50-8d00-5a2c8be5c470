/**
 * Base Service Class
 *
 * Provides common functionality for all Prisma services with strong typing
 */

import { PaginatedResponse } from '@/types/api'

// Generic types for Prisma operations
export type PrismaModel<T = any> = {
  findMany: (args?: any) => Promise<T[]>
  findUnique: (args: any) => Promise<T | null>
  findFirst: (args?: any) => Promise<T | null>
  create: (args: any) => Promise<T>
  createMany: (args: any) => Promise<{ count: number }>
  update: (args: any) => Promise<T>
  upsert: (args: any) => Promise<T>
  delete: (args: any) => Promise<T>
  count: (args?: any) => Promise<number>
  aggregate: (args?: any) => Promise<any>
  groupBy: (args?: any) => Promise<any>
}

// Pagination parameters type
export interface PaginationOptions {
  skip: number
  take: number
}

// Where condition type for better type safety
export type WhereCondition<T = any> = T extends { where?: infer W } ? W : any

// Include/Select options type
export type IncludeOptions<T = any> = T extends { include?: infer I } ? I : any
export type SelectOptions<T = any> = T extends { select?: infer S } ? S : any

// Order by options type
export type OrderByOptions<T = any> = T extends { orderBy?: infer O } ? O : any

// Search field configuration
export interface SearchFieldConfig {
  field: string
  mode?: 'insensitive' | 'default'
}

// Batch update operation
export interface BatchUpdateOperation<T = any> {
  where: WhereCondition<T>
  data: Partial<T>
}

// Statistics result type
export interface ModelStats {
  total: number
  active: number
  inactive: number
}

// Validation field configuration
export interface ValidationField {
  name: string
  required?: boolean
  type?: 'string' | 'number' | 'boolean' | 'date' | 'email'
  minLength?: number
  maxLength?: number
  pattern?: RegExp
}

export class BaseService {

  /**
   * Build pagination parameters for Prisma queries
   */
  static buildPagination(page: number, limit: number): PaginationOptions {
    const skip = (page - 1) * limit
    return {
      skip,
      take: limit
    }
  }

  /**
   * Build paginated data result (without response wrapper)
   */
  static buildPaginatedData<T>(
    items: T[],
    total: number,
    page: number,
    limit: number
  ): PaginatedResponse<T> {
    return {
      success: true,
      data:items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Find many records with relations - Optimized
   */
  static async findManyWithRelations<T, TModel extends PrismaModel<T>>(
    model: TModel,
    where: WhereCondition<T>,
    include: IncludeOptions<T>,
    orderBy: OrderByOptions<T>,
    pagination: PaginationOptions
  ): Promise<T[]> {
    const query: any = {
      where,
      orderBy,
      ...pagination
    }

    // Only add include if it has properties (avoid empty includes)
    if (include && Object.keys(include).length > 0) {
      query.include = include
    }

    // Limit max results for performance
    if (query.take) {
      query.take = Math.min(query.take, 100)
    }

    return model.findMany(query)
  }

  /**
   * Find unique record with relations
   */
  static async findUniqueWithRelations<T, TModel extends PrismaModel<T>>(
    model: TModel,
    where: WhereCondition<T>,
    include?: IncludeOptions<T>
  ): Promise<T | null> {
    return model.findUnique({
      where,
      include
    })
  }

  /**
   * Count records with filters
   */
  static async countWithFilters<T, TModel extends PrismaModel<T>>(
    model: TModel,
    where: WhereCondition<T>
  ): Promise<number> {
    return model.count({ where })
  }

  /**
   * Soft delete a record
   */
  static async softDelete<T extends { id: string }, TModel extends PrismaModel<T>>(
    model: TModel,
    id: string
  ): Promise<boolean> {
    try {
      await model.update({
        where: { id },
        data: { isActive: false }
      })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Validate required fields with enhanced type safety
   */
  static validateRequired<T extends Record<string, any>>(
    data: T,
    fields: (keyof T)[]
  ): void {
    for (const field of fields) {
      const value = data[field]
      if (value === undefined || value === null ||
          (typeof value === 'string' && value.trim() === '')) {
        throw new Error(`${String(field)} is required`)
      }
    }
  }

  /**
   * Advanced validation with field configuration
   */
  static validateFields<T extends Record<string, any>>(
    data: T,
    validationConfig: ValidationField[]
  ): void {
    for (const config of validationConfig) {
      const value = data[config.name]

      // Required field validation
      if (config.required && (value === undefined || value === null ||
          (typeof value === 'string' && value.trim() === ''))) {
        throw new Error(`${config.name} is required`)
      }

      // Skip further validation if field is not required and empty
      if (!config.required && (value === undefined || value === null)) {
        continue
      }

      // Type validation
      if (config.type && value !== undefined && value !== null) {
        switch (config.type) {
          case 'string':
            if (typeof value !== 'string') {
              throw new Error(`${config.name} must be a string`)
            }
            if (config.minLength && value.length < config.minLength) {
              throw new Error(`${config.name} must be at least ${config.minLength} characters`)
            }
            if (config.maxLength && value.length > config.maxLength) {
              throw new Error(`${config.name} must be at most ${config.maxLength} characters`)
            }
            if (config.pattern && !config.pattern.test(value)) {
              throw new Error(`${config.name} format is invalid`)
            }
            break
          case 'number':
            if (typeof value !== 'number' || isNaN(value)) {
              throw new Error(`${config.name} must be a valid number`)
            }
            break
          case 'boolean':
            if (typeof value !== 'boolean') {
              throw new Error(`${config.name} must be a boolean`)
            }
            break
          case 'date':
            if (!(value instanceof Date) && isNaN(Date.parse(value))) {
              throw new Error(`${config.name} must be a valid date`)
            }
            break
          case 'email':
            if (typeof value !== 'string' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              throw new Error(`${config.name} must be a valid email address`)
            }
            break
        }
      }
    }
  }

  /**
   * Sanitize string input with enhanced options
   */
  static sanitizeString(input: string | null | undefined): string {
    if (!input) return ''
    return input.toString().trim()
  }

  /**
   * Build search conditions for Prisma with type safety
   */
  static buildSearchConditions<T>(
    search: string,
    fields: SearchFieldConfig[] | string[]
  ): WhereCondition<T> {
    if (!search || !fields.length) return {} as WhereCondition<T>

    const searchFields = fields.map(field =>
      typeof field === 'string'
        ? { field, mode: 'insensitive' as const }
        : field
    )

    return {
      OR: searchFields.map(({ field, mode = 'insensitive' }) => ({
        [field]: {
          contains: search,
          mode
        }
      }))
    } as WhereCondition<T>
  }

  /**
   * Merge where conditions with type safety
   */
  static mergeWhereConditions<T>(
    ...conditions: (WhereCondition<T> | undefined | null)[]
  ): WhereCondition<T> {
    return conditions.reduce<WhereCondition<T>>((merged, condition) => {
      if (!condition) return merged

      // Handle OR conditions merging
      const mergedAny = merged as any
      const conditionAny = condition as any

      if (mergedAny.OR && conditionAny.OR) {
        return Object.assign({}, merged, condition, {
          OR: [...mergedAny.OR, ...conditionAny.OR]
        }) as WhereCondition<T>
      }

      return Object.assign({}, merged, condition) as WhereCondition<T>
    }, {} as WhereCondition<T>)
  }

  /**
   * Format date for database with validation
   */
  static formatDate(date: string | Date | null | undefined): Date | null {
    if (!date) return null

    const parsedDate = new Date(date)
    if (isNaN(parsedDate.getTime())) {
      throw new Error('Invalid date format')
    }

    return parsedDate
  }

  /**
   * Generate unique slug with enhanced options
   */
  static generateSlug(text: string, options?: {
    maxLength?: number
    separator?: string
    lowercase?: boolean
  }): string {
    const { maxLength = 50, separator = '-', lowercase = true } = options || {}

    let slug = text
    if (lowercase) {
      slug = slug.toLowerCase()
    }

    slug = slug
      .replace(/[^a-zA-Z0-9]+/g, separator)
      .replace(new RegExp(`^${separator}+|${separator}+$`, 'g'), '')

    if (maxLength && slug.length > maxLength) {
      slug = slug.substring(0, maxLength).replace(new RegExp(`${separator}+$`), '')
    }

    return slug
  }

  /**
   * Check if user owns resource with type safety
   */
  static async checkOwnership<T extends Record<string, any>, TModel extends PrismaModel<T>>(
    model: TModel,
    resourceId: string,
    userId: string,
    userField: keyof T = 'userId' as keyof T
  ): Promise<boolean> {
    const resource = await model.findUnique({
      where: { id: resourceId },
      select: { [userField]: true }
    })

    return Boolean(resource && resource[userField] === userId)
  }

  /**
   * Get default pagination params with type safety
   */
  static getDefaultPagination(): { page: number; limit: number } {
    return {
      page: 1,
      limit: 25
    }
  }

  /**
   * Validate pagination params with enhanced validation
   */
  static validatePagination(
    page: number | string | undefined,
    limit: number | string | undefined,
    options?: { maxLimit?: number; minLimit?: number }
  ): { page: number; limit: number } {
    const { maxLimit = 100, minLimit = 1 } = options || {}

    const parsedPage = typeof page === 'string' ? parseInt(page, 10) : page || 1
    const parsedLimit = typeof limit === 'string' ? parseInt(limit, 10) : limit || 25

    if (isNaN(parsedPage) || parsedPage < 1) {
      throw new Error('Page must be a positive integer')
    }

    if (isNaN(parsedLimit) || parsedLimit < minLimit || parsedLimit > maxLimit) {
      throw new Error(`Limit must be between ${minLimit} and ${maxLimit}`)
    }

    return { page: parsedPage, limit: parsedLimit }
  }

  /**
   * Build order by clause with type safety
   */
  static buildOrderBy<T>(
    sortBy?: keyof T | string,
    sortOrder: 'asc' | 'desc' = 'desc'
  ): OrderByOptions<T> {
    if (!sortBy) return { createdAt: 'desc' } as OrderByOptions<T>

    return { [sortBy]: sortOrder } as OrderByOptions<T>
  }

  /**
   * Handle database errors with enhanced error mapping
   */
  static handleDatabaseError(error: any): never {
    console.error('Database error:', error)

    // Prisma error codes mapping
    const errorMap: Record<string, string> = {
      'P2002': 'Duplicate entry found',
      'P2025': 'Record not found',
      'P2003': 'Foreign key constraint failed',
      'P2014': 'The change you are trying to make would violate the required relation',
      'P2016': 'Query interpretation error',
      'P2017': 'The records for relation are not connected',
      'P2018': 'The required connected records were not found',
      'P2019': 'Input error',
      'P2020': 'Value out of range for the type',
      'P2021': 'The table does not exist in the current database',
      'P2022': 'The column does not exist in the current database'
    }

    const message = errorMap[error.code] || 'Database operation failed'
    throw new Error(message)
  }

  /**
   * Batch update records with type safety and transaction support
   */
  static async batchUpdate<T, TModel extends PrismaModel<T>>(
    model: TModel,
    updates: BatchUpdateOperation<T>[],
    options?: {
      continueOnError?: boolean
      useTransaction?: boolean
    }
  ): Promise<{ successCount: number; errors: Error[] }> {
    const { continueOnError = true, useTransaction = false } = options || {}
    let successCount = 0
    const errors: Error[] = []

    const executeUpdates = async () => {
      for (const update of updates) {
        try {
          await model.update(update)
          successCount++
        } catch (error) {
          const err = error instanceof Error ? error : new Error(String(error))
          errors.push(err)
          console.error('Batch update error:', err)

          if (!continueOnError) {
            throw err
          }
        }
      }
    }

    if (useTransaction) {
      // Note: This would require prisma transaction context
      // For now, just execute sequentially
      await executeUpdates()
    } else {
      await executeUpdates()
    }

    return { successCount, errors }
  }

  /**
   * Get record statistics with type safety
   */
  static async getStats<T, TModel extends PrismaModel<T>>(
    model: TModel,
    where: WhereCondition<T> = {} as WhereCondition<T>
  ): Promise<ModelStats> {
    const [total, active, inactive] = await Promise.all([
      model.count({ where }),
      model.count({ where: Object.assign({}, where, { isActive: true }) }),
      model.count({ where: Object.assign({}, where, { isActive: false }) })
    ])

    return { total, active, inactive }
  }

  /**
   * Create multiple records with type safety
   */
  static async createMany<T, TModel extends PrismaModel<T>>(
    model: TModel,
    data: Partial<T>[],
    options?: { skipDuplicates?: boolean }
  ): Promise<{ count: number }> {
    return model.createMany({
      data,
      skipDuplicates: options?.skipDuplicates || false
    })
  }

  /**
   * Upsert record with type safety
   */
  static async upsert<T, TModel extends PrismaModel<T>>(
    model: TModel,
    where: WhereCondition<T>,
    create: Partial<T>,
    update: Partial<T>
  ): Promise<T> {
    return model.upsert({
      where,
      create,
      update
    })
  }

  /**
   * Find first record with relations
   */
  static async findFirstWithRelations<T, TModel extends PrismaModel<T>>(
    model: TModel,
    where: WhereCondition<T>,
    include?: IncludeOptions<T>,
    orderBy?: OrderByOptions<T>
  ): Promise<T | null> {
    return model.findFirst({
      where,
      include,
      orderBy
    })
  }

  /**
   * Aggregate data with type safety
   */
  static async aggregate<T, TModel extends PrismaModel<T>>(
    model: TModel,
    options: {
      where?: WhereCondition<T>
      _count?: boolean | Record<string, boolean>
      _sum?: Record<string, boolean>
      _avg?: Record<string, boolean>
      _min?: Record<string, boolean>
      _max?: Record<string, boolean>
    }
  ): Promise<any> {
    return model.aggregate(options)
  }
}
