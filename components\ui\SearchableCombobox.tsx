"use client"

import React, { useState, useRef, useEffect } from "react"
import { ChevronDown, Search, X } from "lucide-react"

interface Option {
  value: string
  label: string | {
    name: string
    type: string
  }
}

interface SearchableComboboxProps {
  options: Option[]
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  allowClear?: boolean
}

export function SearchableCombobox({
  options,
  value,
  onChange,
  placeholder = "Chọn...",
  className = "",
  disabled = false,
  allowClear = true
}: SearchableComboboxProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const listRef = useRef<HTMLUListElement>(null)

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    typeof option.label === 'string' ? option.label.toLowerCase().includes(searchTerm.toLowerCase()) : option.label.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Get selected option
  const selectedOption = options.find(option => option.value === value)

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm("")
        setHighlightedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          setHighlightedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          event.preventDefault()
          setHighlightedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          )
          break
        case 'Enter':
          event.preventDefault()
          if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
            onChange(filteredOptions[highlightedIndex].value)
            setIsOpen(false)
            setSearchTerm("")
            setHighlightedIndex(-1)
          }
          break
        case 'Escape':
          setIsOpen(false)
          setSearchTerm("")
          setHighlightedIndex(-1)
          break
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, highlightedIndex, filteredOptions, onChange])

  // Scroll highlighted item into view
  useEffect(() => {
    if (highlightedIndex >= 0 && listRef.current) {
      const highlightedElement = listRef.current.children[highlightedIndex] as HTMLElement
      if (highlightedElement) {
        highlightedElement.scrollIntoView({ block: 'nearest' })
      }
    }
  }, [highlightedIndex])

  const handleToggle = () => {
    if (disabled) return
    setIsOpen(!isOpen)
    if (!isOpen) {
      setTimeout(() => inputRef.current?.focus(), 0)
    }
  }

  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue)
    setIsOpen(false)
    setSearchTerm("")
    setHighlightedIndex(-1)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange("")
    setSearchTerm("")
  }

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Trigger */}
      <div
        onClick={handleToggle}
        className={`
          w-full px-3 py-2.5 text-sm border border-gray-200 dark:border-gray-700 rounded-xl
          bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-gray-100
          focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:border-blue-500
          focus-within:bg-white dark:focus-within:bg-gray-800 transition-all duration-200
          cursor-pointer flex items-center justify-between gap-2
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-300 dark:hover:border-gray-600'}
          ${isOpen ? 'ring-2 ring-blue-500/20 border-blue-500 bg-white dark:bg-gray-800' : ''}
        `}
      >
        <span className={`flex-1 truncate ${!selectedOption ? 'text-gray-500 dark:text-gray-400' : 'text-gray-900 dark:text-gray-100'}`}>
          {selectedOption ? (typeof selectedOption.label === 'string' ? selectedOption.label : selectedOption.label.name) : placeholder}
        </span>
        {/* if have type, show it at the end of the label with small text and lightless color; and alway right  align */}
        {selectedOption && typeof selectedOption.label !== 'string' && (
          <span className="text-xs text-gray-500 dark:text-gray-400 text-right">
            {selectedOption.label.type}
          </span>
        )}

        <div className="flex items-center gap-1">
          {allowClear && selectedOption && (
            <button
              onClick={handleClear}
              className="p-0.5 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-all duration-200"
            >
              <X size={12} className="text-gray-500 dark:text-gray-400" />
            </button>
          )}
          <ChevronDown
            size={16}
            className={`text-gray-500 dark:text-gray-400 transition-all duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-[9999] max-h-60 overflow-hidden">
          {/* Search input */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400" />
              <input
                ref={inputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setHighlightedIndex(-1)
                }}
                placeholder="Tìm kiếm..."
                className="w-full px-3 py-2.5 pl-10 text-sm border border-gray-200 dark:border-gray-700 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white dark:focus:bg-gray-800 transition-all duration-200"
              />
            </div>
          </div>

          {/* Options list */}
          <ul ref={listRef} className="max-h-40 overflow-y-auto">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <li
                  key={option.value}
                  onClick={() => handleOptionSelect(option.value)}
                  className={`
                    px-3 py-2.5 text-sm cursor-pointer transition-all duration-200
                    ${index === highlightedIndex ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''}
                    ${option.value === value ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium' : 'text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700'}
                  `}
                >
                  <div className="flex items-center justify-between">
                    {typeof option.label === 'string' ? option.label : option.label.name}
                    {typeof option.label !== 'string' && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {option.label.type}
                      </span>
                    )}

                  </div>
                </li>
              ))
            ) : (
              <li className="px-3 py-2.5 text-sm text-gray-500 dark:text-gray-400 italic">
                Không tìm thấy kết quả
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  )
}
