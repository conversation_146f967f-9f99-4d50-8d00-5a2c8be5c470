// Filter Configuration for Transactions

import { FilterConfig, FilterDefinition, SortOption } from '@/types/ui'
import { Transaction } from '@/lib/generated/prisma'

// Define filter options
const typeOptions = [
  { value: 'all', label: 'Tất cả loại', count: 0 },
  { value: 'income', label: 'Thu nhập', count: 0 },
  { value: 'expense', label: 'Chi tiêu', count: 0 },
  { value: 'transfer', label: 'Chuyển khoản', count: 0 }
]




// Define filter definitions
const transactionFilters: FilterDefinition<Transaction>[] = [
  {
    key: 'type',
    label: 'Loại giao dịch',
    type: 'select',
    options: typeOptions,
    predicate: (transaction, value) => {
      if (value === 'all') return true
      return transaction.kind === value
    },
    defaultValue: 'all'
  },
  {
    key: 'dateRange',
    label: '<PERSON><PERSON><PERSON>ng thời gian',
    type: 'daterange',
    predicate: (transaction, value) => {
      if (!value || (!value.from && !value.to)) return true
      const transactionDate = new Date(transaction.date)
      if (value.from && transactionDate < new Date(value.from)) return false
      if (value.to && transactionDate > new Date(value.to)) return false
      return true
    }
  },
  {
    key: 'amountRange',
    label: 'Khoảng số tiền',
    type: 'numberrange',
    predicate: (transaction, value) => {
      if (!value || (!value.min && !value.max)) return true
      if (value.min && transaction.amount < value.min) return false
      if (value.max && transaction.amount > value.max) return false
      return true
    }
  }
]

// Define sort options
const transactionSortOptions: SortOption<Transaction>[] = [
  {
    key: 'date_desc',
    label: 'Ngày (Mới nhất)',
    compareFn: (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  },
  {
    key: 'date_asc',
    label: 'Ngày (Cũ nhất)',
    compareFn: (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  },
  {
    key: 'amount_desc',
    label: 'Số tiền (Cao nhất)',
    compareFn: (a, b) => b.amount - a.amount
  },
  {
    key: 'amount_asc',
    label: 'Số tiền (Thấp nhất)',
    compareFn: (a, b) => a.amount - b.amount
  },
  {
    key: 'description_asc',
    label: 'Mô tả (A-Z)',
    compareFn: (a, b) => (a?.description || '').localeCompare(b?.description || '', 'vi')
  },
  {
    key: 'description_desc',
    label: 'Mô tả (Z-A)',
    compareFn: (a, b) => (b?.description || '').localeCompare(a?.description || '', 'vi')
  },
  {
    key: 'type_asc',
    label: 'Loại (A-Z)',
    compareFn: (a, b) => {
      if (a.kind === b.kind) {
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      }
      return a.kind.localeCompare(b.kind, 'vi')
    }
  },
  {
    key: 'created_desc',
    label: 'Tạo gần nhất',
    compareFn: (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  },
  {
    key: 'updated_desc',
    label: 'Cập nhật gần nhất',
    compareFn: (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  }
]

// Create filter configuration
export const transactionFilterConfig: FilterConfig<Transaction> = {
  searchFields: ['description', 'note', 'location'],
  filters: transactionFilters,
  sortOptions: transactionSortOptions,
  defaultSort: transactionSortOptions[0] // Date desc (newest first)
}

// Helper function to update filter options with counts
export const updateTransactionFilterCounts = (
  config: FilterConfig<Transaction>,
  transactions: Transaction[]
): FilterConfig<Transaction> => {
  const updatedFilters = config.filters.map(filter => {
    if (!filter.options) return filter

    const updatedOptions = filter.options.map(option => {
      let count = 0
      
      if (filter.key === 'type') {
        if (option.value === 'all') count = transactions.length
        else count = transactions.filter(t => t.kind === option.value).length
    
      } 

      return { ...option, count }
    })

    return { ...filter, options: updatedOptions }
  })

  return { ...config, filters: updatedFilters }
}

// Helper functions for common filters
export const getDateRangePresets = () => [
  {
    label: 'Hôm nay',
    value: {
      from: new Date().toISOString().split('T')[0],
      to: new Date().toISOString().split('T')[0]
    }
  },
  {
    label: 'Tuần này',
    value: {
      from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      to: new Date().toISOString().split('T')[0]
    }
  },
  {
    label: 'Tháng này',
    value: {
      from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
      to: new Date().toISOString().split('T')[0]
    }
  },
  {
    label: 'Tháng trước',
    value: {
      from: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).toISOString().split('T')[0],
      to: new Date(new Date().getFullYear(), new Date().getMonth(), 0).toISOString().split('T')[0]
    }
  }
]

export const getAmountRangePresets = () => [
  { label: 'Dưới 100K', value: { min: 0, max: 100000 } },
  { label: '100K - 500K', value: { min: 100000, max: 500000 } },
  { label: '500K - 1M', value: { min: 500000, max: 1000000 } },
  { label: '1M - 5M', value: { min: 1000000, max: 5000000 } },
  { label: 'Trên 5M', value: { min: 5000000, max: null } }
]

// Export individual pieces for flexibility
export { 
  transactionFilters, 
  transactionSortOptions, 
  typeOptions, 
   
}
export default transactionFilterConfig
