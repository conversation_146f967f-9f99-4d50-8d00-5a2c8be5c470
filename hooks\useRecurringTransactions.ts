import { useState, useEffect, useCallback } from 'react'
import { RecurringTransactionApiService } from '@/services/client'
import type { RecurringTransaction } from '@/lib/generated/prisma'
import type { RecurringTransactionQueryParams, CreateRecurringTransactionRequest, UpdateRecurringTransactionRequest } from '@/types/api'

interface UseRecurringTransactionsReturn {
  recurringTransactions: RecurringTransaction[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createRecurringTransaction: (data: CreateRecurringTransactionRequest) => Promise<boolean>
  updateRecurringTransaction: (id: number, data: UpdateRecurringTransactionRequest) => Promise<boolean>
  deleteRecurringTransaction: (id: number) => Promise<boolean>
  toggleActive: (id: number) => Promise<boolean>
}

export function useRecurringTransactions(params?: RecurringTransactionQueryParams): UseRecurringTransactionsReturn {
  const [recurringTransactions, setRecurringTransactions] = useState<RecurringTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchRecurringTransactions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await RecurringTransactionApiService.getRecurringTransactions(params)
      
      if (response.success) {
        setRecurringTransactions(response.data)
      } else {
        setError('Failed to fetch recurring transactions')
      }
    } catch (err) {
      setError('Error fetching recurring transactions')
      console.error('Error fetching recurring transactions:', err)
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    fetchRecurringTransactions()
  }, [fetchRecurringTransactions])

  const createRecurringTransaction = useCallback(async (data: CreateRecurringTransactionRequest): Promise<boolean> => {
    try {
      const response = await recurringTransactionService.create(data)
      if (response.success) {
        await fetchRecurringTransactions() // Refresh list
        return true
      } else {
        setError('Failed to create recurring transaction')
        return false
      }
    } catch (err) {
      setError('Error creating recurring transaction')
      console.error('Error creating recurring transaction:', err)
      return false
    }
  }, [fetchRecurringTransactions])

  const updateRecurringTransaction = useCallback(async (id: number, data: UpdateRecurringTransactionRequest): Promise<boolean> => {
    try {
      const response = await recurringTransactionService.update(id, data)
      if (response.success) {
        // Update local state optimistically
        setRecurringTransactions(prev => prev.map(rt => 
          rt.id === id ? { ...rt, ...data, updatedAt: new Date() } : rt
        ))
        return true
      } else {
        setError('Failed to update recurring transaction')
        return false
      }
    } catch (err) {
      setError('Error updating recurring transaction')
      console.error('Error updating recurring transaction:', err)
      return false
    }
  }, [])

  const deleteRecurringTransaction = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await recurringTransactionService.delete(id)
      if (response.success) {
        // Remove from local state
        setRecurringTransactions(prev => prev.filter(rt => rt.id !== id))
        return true
      } else {
        setError('Failed to delete recurring transaction')
        return false
      }
    } catch (err) {
      setError('Error deleting recurring transaction')
      console.error('Error deleting recurring transaction:', err)
      return false
    }
  }, [])

  const toggleActive = useCallback(async (id: number): Promise<boolean> => {
    const transaction = recurringTransactions.find(rt => rt.id === id)
    if (!transaction) return false

    return updateRecurringTransaction(id, { isActive: !transaction.isActive })
  }, [recurringTransactions, updateRecurringTransaction])

  return {
    recurringTransactions,
    loading,
    error,
    refetch: fetchRecurringTransactions,
    createRecurringTransaction,
    updateRecurringTransaction,
    deleteRecurringTransaction,
    toggleActive
  }
}

export default useRecurringTransactions
