/**
 * Dashboard API Service
 *
 * Handles dashboard and statistics operations using GraphQL client
 */

import { apolloClient } from '@/lib/graphql/client'
import {
  GET_ACCOUNTS,
  GET_TRANSACTIONS,
  GET_CATEGORIES,
  GET_USERS
} from '@/lib/graphql/operations'
import {
  convertToClientError,
  getUserFriendlyErrorMessage
} from '@/lib/graphql/errors'
import type { ApiResponse } from '@/types/api'

export class DashboardApiService {

  // ==================== DASHBOARD STATISTICS ====================

  /**
   * Get comprehensive dashboard statistics using GraphQL
   */
  static async getDashboardStats(): Promise<ApiResponse<{
    totalBalance: number
    monthlyIncome: number
    monthlyExpense: number
    accountsCount: number
    transactionsCount: number
    categoriesCount: number
    recentTransactions: any[]
    topCategories: any[]
    accountBalances: any[]
  }>> {
    try {
      // Fetch data from multiple GraphQL queries
      const [accountsData, transactionsData, categoriesData] = await Promise.all([
        apolloClient.query({
          query: GET_ACCOUNTS,
          variables: { first: 100, isActive: true },
          fetchPolicy: 'cache-first'
        }),
        apolloClient.query({
          query: GET_TRANSACTIONS,
          variables: { first: 100 },
          fetchPolicy: 'cache-first'
        }),
        apolloClient.query({
          query: GET_CATEGORIES,
          variables: { first: 100, isActive: true },
          fetchPolicy: 'cache-first'
        })
      ])

      const accounts = accountsData.data.accounts.edges.map((edge: any) => edge.node)
      const transactions = transactionsData.data.transactions.edges.map((edge: any) => edge.node)
      const categories = categoriesData.data.categories.edges.map((edge: any) => edge.node)

      // Calculate statistics
      const totalBalance = accounts.reduce((sum: number, account: any) => sum + (account.balance || 0), 0)
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()

      const monthlyTransactions = transactions.filter((t: any) => {
        const transactionDate = new Date(t.date)
        return transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear
      })

      const monthlyIncome = monthlyTransactions
        .filter((t: any) => t.kind === 'INCOME')
        .reduce((sum: number, t: any) => sum + t.amount, 0)

      const monthlyExpense = monthlyTransactions
        .filter((t: any) => t.kind === 'EXPENSE')
        .reduce((sum: number, t: any) => sum + t.amount, 0)

      const recentTransactions = transactions
        .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 10)

      const stats = {
        totalBalance,
        monthlyIncome,
        monthlyExpense,
        accountsCount: accounts.length,
        transactionsCount: transactions.length,
        categoriesCount: categories.length,
        recentTransactions,
        topCategories: categories.slice(0, 5),
        accountBalances: accounts.map((account: any) => ({
          id: account.id,
          name: account.name,
          balance: account.balance,
          currency: account.currency
        }))
      }

      return {
        data: stats,
        success: true,
        message: 'Dashboard statistics retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch dashboard stats:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get financial overview using GraphQL
   */
  static async getFinancialOverview(): Promise<ApiResponse<{
    totalIncome: number
    totalExpense: number
    netWorth: number
    savingsRate: number
    monthlyTrend: any[]
  }>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_TRANSACTIONS,
        variables: { first: 1000 },
        fetchPolicy: 'cache-first'
      })

      const transactions = data.transactions.edges.map((edge: any) => edge.node)

      const totalIncome = transactions
        .filter((t: any) => t.kind === 'INCOME')
        .reduce((sum: number, t: any) => sum + t.amount, 0)

      const totalExpense = transactions
        .filter((t: any) => t.kind === 'EXPENSE')
        .reduce((sum: number, t: any) => sum + t.amount, 0)

      const netWorth = totalIncome - totalExpense
      const savingsRate = totalIncome > 0 ? ((totalIncome - totalExpense) / totalIncome) * 100 : 0

      return {
        data: {
          totalIncome,
          totalExpense,
          netWorth,
          savingsRate,
          monthlyTrend: []
        },
        success: true,
        message: 'Financial overview retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch financial overview:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get spending analysis using GraphQL
   */
  static async getSpendingAnalysis(): Promise<ApiResponse<{
    topCategories: any[]
    monthlySpending: any[]
    categoryBreakdown: any[]
    spendingTrends: any[]
  }>> {
    try {
      const [transactionsData, categoriesData] = await Promise.all([
        apolloClient.query({
          query: GET_TRANSACTIONS,
          variables: { first: 1000, kind: 'EXPENSE' },
          fetchPolicy: 'cache-first'
        }),
        apolloClient.query({
          query: GET_CATEGORIES,
          variables: { first: 100, type: 'EXPENSE' },
          fetchPolicy: 'cache-first'
        })
      ])

      const transactions = transactionsData.data.transactions.edges.map((edge: any) => edge.node)
      const categories = categoriesData.data.categories.edges.map((edge: any) => edge.node)

      return {
        data: {
          topCategories: categories.slice(0, 5),
          monthlySpending: [],
          categoryBreakdown: [],
          spendingTrends: []
        },
        success: true,
        message: 'Spending analysis retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch spending analysis:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get budget overview using GraphQL (placeholder - requires CategoryGoal implementation)
   */
  static async getBudgetOverview(): Promise<ApiResponse<{
    totalBudget: number
    totalSpent: number
    budgetUtilization: number
    categoryCategoryGoal: any[]
    budgetAlerts: any[]
  }>> {
    try {
      // This would use CategoryGoal queries when available
      return {
        data: {
          totalBudget: 0,
          totalSpent: 0,
          budgetUtilization: 0,
          categoryCategoryGoal: [],
          budgetAlerts: []
        },
        success: true,
        message: 'Budget overview retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch budget overview:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get account summary using GraphQL
   */
  static async getAccountSummary(): Promise<ApiResponse<{
    totalBalance: number
    accountsByType: any[]
    recentActivity: any[]
    balanceHistory: any[]
  }>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_ACCOUNTS,
        variables: { first: 100, isActive: true },
        fetchPolicy: 'cache-first'
      })

      const accounts = data.accounts.edges.map((edge: any) => edge.node)
      const totalBalance = accounts.reduce((sum: number, account: any) => sum + (account.balance || 0), 0)

      return {
        data: {
          totalBalance,
          accountsByType: [],
          recentActivity: [],
          balanceHistory: []
        },
        success: true,
        message: 'Account summary retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch account summary:', error)
      throw convertToClientError(error)
    }
  }

  /**
   * Get recent activity using GraphQL
   */
  static async getRecentActivity(limit: number = 10): Promise<ApiResponse<{
    transactions: any[]
    accounts: any[]
    categories: any[]
  }>> {
    try {
      const [transactionsData, accountsData, categoriesData] = await Promise.all([
        apolloClient.query({
          query: GET_TRANSACTIONS,
          variables: { first: limit },
          fetchPolicy: 'cache-first'
        }),
        apolloClient.query({
          query: GET_ACCOUNTS,
          variables: { first: limit },
          fetchPolicy: 'cache-first'
        }),
        apolloClient.query({
          query: GET_CATEGORIES,
          variables: { first: limit },
          fetchPolicy: 'cache-first'
        })
      ])

      return {
        data: {
          transactions: transactionsData.data.transactions.edges.map((edge: any) => edge.node),
          accounts: accountsData.data.accounts.edges.map((edge: any) => edge.node),
          categories: categoriesData.data.categories.edges.map((edge: any) => edge.node)
        },
        success: true,
        message: 'Recent activity retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch recent activity:', error)
      throw convertToClientError(error)
    }
  }
}




