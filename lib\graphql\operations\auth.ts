/**
 * GraphQL Operations for Authentication
 * 
 * Queries and mutations for user authentication
 */

import { gql } from '@apollo/client'

// ==================== QUERIES ====================

export const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    me {
      id
      name
      email
      avatar
      role
      description
      isActive
      createdAt
      updatedAt
      preferences {
        id
        currency
        language
        theme
        dateFormat
        numberFormat
        isActive
        createdAt
        updatedAt
      }
    }
  }
`

// ==================== MUTATIONS ====================

// Note: Login and register are typically handled via REST API
// since they need to return JWT tokens and GraphQL mutations
// might not be the best fit for initial authentication.
// However, we can include them for completeness.

export const LOGIN_MUTATION = gql`
  mutation Login($email: String!, $password: String!) {
    login(email: $email, password: $password) {
      user {
        id
        name
        email
        avatar
        role
      }
      tokens {
        accessToken
        refreshToken
        expiresIn
      }
    }
  }
`

export const REGISTER_MUTATION = gql`
  mutation Register($input: RegisterInput!) {
    register(input: $input) {
      user {
        id
        name
        email
        avatar
        role
      }
      tokens {
        accessToken
        refreshToken
        expiresIn
      }
    }
  }
`

export const REFRESH_TOKEN_MUTATION = gql`
  mutation RefreshToken($refreshToken: String!) {
    refreshToken(refreshToken: $refreshToken) {
      user {
        id
        name
        email
        avatar
        role
      }
      tokens {
        accessToken
        refreshToken
        expiresIn
      }
    }
  }
`

export const CHANGE_PASSWORD_MUTATION = gql`
  mutation ChangePassword($currentPassword: String!, $newPassword: String!) {
    changePassword(currentPassword: $currentPassword, newPassword: $newPassword) {
      success
      message
    }
  }
`


