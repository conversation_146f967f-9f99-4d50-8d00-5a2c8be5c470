import { BaseTableActiveable } from '../base';
import { UUID } from '../common';
import { UserBase } from '../user';
import { AccountBase } from './account';
import { TransactionBase } from '../transaction/transaction';
import { AccountDebitCardLink } from './account';

export type {
  DebitCard,
  DebitCardBase,
  DebitCardWithRelations,
  CreateDebitCardInput,
  UpdateDebitCardInput,
  DebitCardFilterInput
};

/**
 * Base interface for DebitCard
 */
interface DebitCardBase extends BaseTableActiveable {
  cardNumber: string | null;
  cardHolderName: string | null;
  bankName: string | null;
  expiryDate: string | null; // ISO date string
  cardType: string | null;
  note: string | null;
  userId: UUID;
  accountId: UUID | null;
}

/**
 * DebitCard with all relations
 */
interface DebitCardWithRelations extends DebitCardBase {
  user: UserBase;
  account: AccountBase | null;
  transactions: TransactionBase[];
  accountDebitCardLinks: AccountDebitCardLink[];
}

/**
 * Union type for DebitCard
 */
type DebitCard = DebitCardBase | DebitCardWithRelations;

/**
 * Input for creating a new debit card
 */
interface CreateDebitCardInput {
  cardNumber?: string | null;
  cardHolderName?: string | null;
  bankName?: string | null;
  expiryDate?: string | null; // ISO date string
  cardType?: string | null;
  note?: string | null;
  accountId?: UUID | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing debit card
 */
interface UpdateDebitCardInput {
  id: UUID;
  cardNumber?: string | null;
  cardHolderName?: string | null;
  bankName?: string | null;
  expiryDate?: string | null; // ISO date string
  cardType?: string | null;
  note?: string | null;
  accountId?: UUID | null;
  isActive?: boolean;
}

/**
 * Input for filtering debit cards
 */
interface DebitCardFilterInput {
  search?: string;
  userId?: UUID;
  accountId?: UUID | null;
  bankName?: string;
  cardType?: string;
  isActive?: boolean;
  hasExpired?: boolean;
}
