import { useState, useEffect } from 'react'
import type { ResponsiveGridCols } from '@/types/ui'

// Use ResponsiveGridCols from types instead of local interface
type GridConfig = ResponsiveGridCols

interface UseResponsiveGridOptions {
  baseGrid?: GridConfig
  sidebarOpen?: boolean
  sidebarWidth?: number
  minCardWidth?: number // Minimum card width in pixels (default: 280px)
  // Note: Grid will always have minimum 1 column regardless of constraints
}

export function useResponsiveGrid({
  baseGrid = { default: 3, sm: 2, md: 3, lg: 3, xl: 4, "2xl": 4 },
  sidebarOpen = false,
  sidebarWidth = 400,
  minCardWidth = 280
}: UseResponsiveGridOptions) {
  const [windowWidth, setWindowWidth] = useState(0)

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    // Set initial width
    handleResize()

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Re-calculate when sidebar state changes
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Sidebar state changed:', { sidebarOpen, sidebarWidth })
    }
  }, [sidebarOpen, sidebarWidth])
  
  // Calculate effective width (subtract sidebar if open)
  const effectiveWidth = sidebarOpen ? windowWidth - sidebarWidth : windowWidth

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 ResponsiveGrid Debug:', {
      windowWidth,
      sidebarOpen,
      sidebarWidth,
      effectiveWidth,
      minCardWidth
    })
  }

  // Calculate optimal columns based on effective width
  const calculateOptimalColumns = (width: number): GridConfig => {
    const maxColumns = Math.max(1, Math.floor(width / minCardWidth)) // Ensure minimum 1 column

    // Define breakpoints (adjusted for sidebar)
    const breakpoints = {
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      "2xl": 1536
    }

    // Adjust base grid based on available width, ensuring minimum 1 column
    const adjustedGrid: GridConfig = {
      default: Math.max(1, Math.min(baseGrid?.default || 3, maxColumns)),
      sm: Math.max(1, Math.min(baseGrid?.sm || 2, maxColumns)),
      md: Math.max(1, Math.min(baseGrid?.md || 3, maxColumns)),
      lg: Math.max(1, Math.min(baseGrid?.lg || 3, maxColumns)),
      xl: Math.max(1, Math.min(baseGrid?.xl || 4, maxColumns)),
      "2xl": Math.max(1, Math.min(baseGrid?.["2xl"] || 4, maxColumns))
    }
    
    // Further reduce if sidebar is open and width is constrained
    if (sidebarOpen) {
      // More aggressive reduction when sidebar is open
      if (effectiveWidth < 600) {
        // Very narrow: force single column
        adjustedGrid.sm = 1
        adjustedGrid.md = 1
        adjustedGrid.lg = 1
        adjustedGrid.xl = 1
        adjustedGrid["2xl"] = 1
      } else if (effectiveWidth < 900) {
        // Narrow: max 2 columns
        adjustedGrid.lg = Math.max(1, Math.min(adjustedGrid.lg || 3, 2))
        adjustedGrid.xl = Math.max(1, Math.min(adjustedGrid.xl || 4, 2))
        adjustedGrid["2xl"] = Math.max(1, Math.min(adjustedGrid["2xl"] || 4, 2))
      } else if (effectiveWidth < 1200) {
        // Medium: max 3 columns
        adjustedGrid.lg = Math.max(1, Math.min(adjustedGrid.lg || 3, 3))
        adjustedGrid.xl = Math.max(1, Math.min(adjustedGrid.xl || 4, 3))
        adjustedGrid["2xl"] = Math.max(1, Math.min(adjustedGrid["2xl"] || 4, 3))
      } else {
        // Wide: allow 4 columns but still constrained
        adjustedGrid.xl = Math.max(1, Math.min(adjustedGrid.xl || 4, 4))
        adjustedGrid["2xl"] = Math.max(1, Math.min(adjustedGrid["2xl"] || 4, 4))
      }
    }
    
    // Debug logging for grid calculation
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Grid Calculation:', {
        maxColumns,
        baseGrid,
        adjustedGrid,
        sidebarOpen,
        effectiveWidth
      })
    }

    return adjustedGrid
  }

  const responsiveGrid = calculateOptimalColumns(effectiveWidth)
  
  // Generate Tailwind classes
  const getGridClasses = (): string => {
    const { default: def, sm, md, lg, xl, "2xl": xxl } = responsiveGrid

    // Ensure all values are at least 1
    const safeDefault = Math.max(1, def)
    const safeSm = Math.max(1, sm || safeDefault)
    const safeMd = Math.max(1, md || safeSm)
    const safeLg = Math.max(1, lg || safeMd)
    const safeXl = Math.max(1, xl || safeLg)
    const safeXxl = Math.max(1, xxl || safeXl)

    const classes = [`grid gap-4 grid-cols-${safeDefault}`]

    if (safeSm !== safeDefault) classes.push(`sm:grid-cols-${safeSm}`)
    if (safeMd !== safeSm) classes.push(`md:grid-cols-${safeMd}`)
    if (safeLg !== safeMd) classes.push(`lg:grid-cols-${safeLg}`)
    if (safeXl !== safeLg) classes.push(`xl:grid-cols-${safeXl}`)
    if (safeXxl !== safeXl) classes.push(`2xl:grid-cols-${safeXxl}`)

    const finalClasses = classes.join(' ')

    // Debug logging for final classes
    if (process.env.NODE_ENV === 'development') {
      console.log('🎨 Final Grid Classes:', {
        responsiveGrid,
        safeValues: { safeDefault, safeSm, safeMd, safeLg, safeXl, safeXxl },
        finalClasses
      })
    }

    return finalClasses
  }

  return {
    gridConfig: responsiveGrid,
    gridClasses: getGridClasses(),
    effectiveWidth,
    sidebarAdjusted: sidebarOpen
  }
}

// Preset configurations for common use cases
export const gridPresets = {
  // Standard 4-column layout
  standard: {
    default: 1,
    sm: 2,
    lg: 3,
    xl: 4,
    "2xl": 4
  } as GridConfig,
  
  // Compact 3-column layout
  compact: {
    default: 1,
    sm: 2,
    lg: 3,
    xl: 3,
    "2xl": 3
  } as GridConfig,
  
  // Dense 5-column layout for small cards
  dense: {
    default: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
    "2xl": 6
  } as GridConfig,
  
  // Wide 2-column layout for detailed cards
  wide: {
    default: 1,
    sm: 1,
    lg: 2,
    xl: 2,
    "2xl": 2
  } as GridConfig
}
