/**
 * User Preferences API Service
 *
 * Handles user preferences-related API operations using ApiClient as base layer
 */

import { ApiClient } from './apiClient'
import type { ApiResponse, PaginatedResponse } from '@/types/api'
import type { UserPreferences, Language, Theme } from '@/lib/generated/prisma'

// ==================== TYPES ====================

export interface CreateUserPreferencesRequest {
  userId: string
  name?: string
  description?: string
  currency?: string
  language?: Language
  theme?: Theme
  dateFormat?: string
  numberFormat?: string
}

export interface UpdateUserPreferencesRequest {
  name?: string
  description?: string
  currency?: string
  language?: Language
  theme?: Theme
  dateFormat?: string
  numberFormat?: string
  isActive?: boolean
}

export interface UserPreferencesQueryParams {
  page?: number
  limit?: number
  search?: string
  language?: Language
  theme?: Theme
  isActive?: boolean
}

export class UserPreferencesApiService {

  // ==================== CRUD OPERATIONS ====================

  /**
   * Get all user preferences (admin function)
   */
  static async getAllUserPreferences(params?: UserPreferencesQueryParams): Promise<PaginatedResponse<UserPreferences>> {
    try {
      const queryParams = new URLSearchParams()
      
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.search) queryParams.append('search', params.search)
      if (params?.language) queryParams.append('language', params.language)
      if (params?.theme) queryParams.append('theme', params.theme)
      if (params?.isActive !== undefined) queryParams.append('isActive', params.isActive.toString())

      const url = `/user-preferences${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      return await ApiClient.get<PaginatedResponse<UserPreferences>>(url)
    } catch (error) {
      console.error('Failed to fetch user preferences:', error)
      throw error
    }
  }

  /**
   * Get user preferences by preferences ID
   */
  static async getUserPreferencesById(id: string): Promise<ApiResponse<UserPreferences>> {
    try {
      return await ApiClient.get<ApiResponse<UserPreferences>>(`/user-preferences/${id}`)
    } catch (error) {
      console.error(`Failed to fetch user preferences ${id}:`, error)
      throw error
    }
  }

  /**
   * Create user preferences
   */
  static async createUserPreferences(data: CreateUserPreferencesRequest): Promise<ApiResponse<UserPreferences>> {
    try {
      return await ApiClient.post<ApiResponse<UserPreferences>>('/user-preferences', data)
    } catch (error) {
      console.error('Failed to create user preferences:', error)
      throw error
    }
  }

  /**
   * Update user preferences by preferences ID
   */
  static async updateUserPreferences(id: string, data: UpdateUserPreferencesRequest): Promise<ApiResponse<UserPreferences>> {
    try {
      return await ApiClient.patch<ApiResponse<UserPreferences>>(`/user-preferences/${id}`, data)
    } catch (error) {
      console.error(`Failed to update user preferences ${id}:`, error)
      throw error
    }
  }

  /**
   * Delete user preferences by preferences ID (soft delete)
   */
  static async deleteUserPreferences(id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    try {
      return await ApiClient.delete<ApiResponse<{ success: boolean; message: string }>>(`/user-preferences/${id}`)
    } catch (error) {
      console.error(`Failed to delete user preferences ${id}:`, error)
      throw error
    }
  }

  // ==================== CONVENIENCE METHODS ====================

  /**
   * Get current user's preferences
   */
  static async getCurrentUserPreferences(): Promise<ApiResponse<UserPreferences>> {
    try {
      return await ApiClient.get<ApiResponse<UserPreferences>>('/users/me/preferences')
    } catch (error) {
      console.error('Failed to fetch current user preferences:', error)
      throw error
    }
  }

  /**
   * Update current user's preferences
   */
  static async updateCurrentUserPreferences(data: UpdateUserPreferencesRequest): Promise<ApiResponse<UserPreferences>> {
    try {
      return await ApiClient.post<ApiResponse<UserPreferences>>('/users/me/preferences', data)
    } catch (error) {
      console.error('Failed to update current user preferences:', error)
      throw error
    }
  }

  /**
   * Reset user preferences to defaults
   */
  static async resetUserPreferences(userId: string): Promise<ApiResponse<UserPreferences>> {
    try {
      return await ApiClient.post<ApiResponse<UserPreferences>>(`/users/${userId}/preferences/reset`, {})
    } catch (error) {
      console.error(`Failed to reset user preferences for user ${userId}:`, error)
      throw error
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get available languages
   */
  static getAvailableLanguages(): Array<{ value: Language; label: string }> {
    return [
      { value: 'vi', label: 'Tiếng Việt' },
      { value: 'en', label: 'English' }
    ]
  }

  /**
   * Get available themes
   */
  static getAvailableThemes(): Array<{ value: Theme; label: string }> {
    return [
      { value: 'light', label: 'Sáng' },
      { value: 'dark', label: 'Tối' },
      { value: 'system', label: 'Theo hệ thống' }
    ]
  }

  /**
   * Get available currencies
   */
  static getAvailableCurrencies(): Array<{ value: string; label: string; symbol: string }> {
    return [
      { value: 'VND', label: 'Việt Nam Đồng', symbol: '₫' },
      { value: 'USD', label: 'US Dollar', symbol: '$' },
      { value: 'EUR', label: 'Euro', symbol: '€' },
      { value: 'JPY', label: 'Japanese Yen', symbol: '¥' }
    ]
  }

  /**
   * Format currency based on preferences
   */
  static formatCurrency(amount: number, preferences?: UserPreferences): string {
    const currency = preferences?.currency || 'VND'
    const currencyInfo = this.getAvailableCurrencies().find(c => c.value === currency)
    
    if (currency === 'VND') {
      return `${amount.toLocaleString('vi-VN')} ${currencyInfo?.symbol || '₫'}`
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  /**
   * Format date based on preferences
   */
  static formatDate(date: Date | string, preferences?: UserPreferences): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const language = preferences?.language || 'vi'
    const dateFormat = preferences?.dateFormat
    
    if (dateFormat) {
      // Custom date format logic could be implemented here
      return dateObj.toLocaleDateString(language === 'vi' ? 'vi-VN' : 'en-US')
    }
    
    return dateObj.toLocaleDateString(language === 'vi' ? 'vi-VN' : 'en-US')
  }
}
