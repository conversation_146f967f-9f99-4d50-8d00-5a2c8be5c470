"use client"

import React, { useState, useEffect } from "react"
import { BaseSidebar } from "./BaseSidebar"
import { <PERSON>barForm, SidebarInputField, SidebarFormField } from "./SidebarForm"
import { ContactAccountSelector } from "./ContactBankAccountSelector"
import { Button } from "./button"
import { useResizableSidebar } from "@/hooks/useResizableSidebar"
import { getSidebarConfig } from "../../config/sidebarConfig"
import { Building2, Tag, Calendar, DollarSign, ArrowUpDown, Filter } from "lucide-react"
import { SearchableCombobox } from "./SearchableCombobox"
import { Contact } from "@/lib/generated/prisma"

interface Account {
  id: number
  name: string
  type: string
  balance: number
  currency: string
  color?: string
  logo?: string
}

interface Category {
  id: number
  name: string
  type: "income" | "expense"
  color: string
  icon: string
}

interface FilterState {
  selectedAccount: string
  selectedCategory: string
  selectedTransferFromContacts: string[]
  selectedTransferFromAccounts: string[]
  selectedTransferToContacts: string[]
  selectedTransferToAccounts: string[]
  dateFrom: string
  dateTo: string
  amountFrom: string
  amountTo: string
}

interface FilterSidebarProps {
  isOpen: boolean
  onClose: () => void
  onApplyFilters: (filters: FilterState) => void
  onClearFilters: () => void
  accounts: Account[]
  categories: Category[]
  contacts: Contact[]
  initialFilters: FilterState
}

export function FilterSidebar({
  isOpen,
  onClose,
  onApplyFilters,
  onClearFilters,
  accounts,
  categories,
  contacts,
  initialFilters
}: FilterSidebarProps) {
  const config = getSidebarConfig('FILTER')
  
  // Dynamic sidebar width using resizable hook
  const {
    sidebarWidth,
    isResizing
  } = useResizableSidebar({
    storageKey: 'filterSidebarWidth',
    isOpen,
    config: {
      DEFAULT_WIDTH: 450,
      MIN_WIDTH: 300,
      MAX_WIDTH_PERCENTAGE: 0.6
    }
  })

  // Filter state with safe defaults
  const [filters, setFilters] = useState<FilterState>({
    selectedAccount: initialFilters?.selectedAccount || "all",
    selectedCategory: initialFilters?.selectedCategory || "all",
    selectedTransferFromContacts: initialFilters?.selectedTransferFromContacts || [],
    selectedTransferFromAccounts: initialFilters?.selectedTransferFromAccounts || [],
    selectedTransferToContacts: initialFilters?.selectedTransferToContacts || [],
    selectedTransferToAccounts: initialFilters?.selectedTransferToAccounts || [],
    dateFrom: initialFilters?.dateFrom || "",
    dateTo: initialFilters?.dateTo || "",
    amountFrom: initialFilters?.amountFrom || "",
    amountTo: initialFilters?.amountTo || ""
  })

  // Update filters when initialFilters change
  useEffect(() => {
    if (initialFilters) {
      setFilters({
        selectedAccount: initialFilters.selectedAccount || "all",
        selectedCategory: initialFilters.selectedCategory || "all",
        selectedTransferFromContacts: initialFilters.selectedTransferFromContacts || [],
        selectedTransferFromAccounts: initialFilters.selectedTransferFromAccounts || [],
        selectedTransferToContacts: initialFilters.selectedTransferToContacts || [],
        selectedTransferToAccounts: initialFilters.selectedTransferToAccounts || [],
        dateFrom: initialFilters.dateFrom || "",
        dateTo: initialFilters.dateTo || "",
        amountFrom: initialFilters.amountFrom || "",
        amountTo: initialFilters.amountTo || ""
      })
    }
  }, [initialFilters])

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Handle apply filters
  const handleApply = () => {
    onApplyFilters(filters)
    onClose()
  }

  // Handle clear filters
  const handleClear = () => {
    const clearedFilters: FilterState = {
      selectedAccount: "all",
      selectedCategory: "all",
      selectedTransferFromContacts: [],
      selectedTransferFromAccounts: [],
      selectedTransferToContacts: [],
      selectedTransferToAccounts: [],
      dateFrom: "",
      dateTo: "",
      amountFrom: "",
      amountTo: ""
    }
    setFilters(clearedFilters)
    onClearFilters()
    onClose()
  }

  // Check if any filters are active
  const hasActiveFilters =
    filters.selectedAccount !== "all" ||
    filters.selectedCategory !== "all" ||
    (filters.selectedTransferFromContacts?.length || 0) > 0 ||
    (filters.selectedTransferFromAccounts?.length || 0) > 0 ||
    (filters.selectedTransferToContacts?.length || 0) > 0 ||
    (filters.selectedTransferToAccounts?.length || 0) > 0 ||
    filters.dateFrom ||
    filters.dateTo ||
    filters.amountFrom ||
    filters.amountTo

  // Check if data is loading
  const isDataLoading = !accounts || !categories || !contacts

  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={onClose}
      title="Bộ lọc giao dịch"
      subtitle="Tùy chỉnh các tiêu chí lọc để tìm giao dịch"
      storageKey="filterSidebarWidth"
      config={{
        DEFAULT_WIDTH: 450,
        MIN_WIDTH: 300,
        MAX_WIDTH_PERCENTAGE: 0.6
      }}
      footerContent={
        <div className="flex gap-3">
          <Button
            type="button"
            onClick={onClose}
            variant="outline"
            className="flex-1"
          >
            Hủy
          </Button>

          {hasActiveFilters && (
            <Button
              type="button"
              onClick={handleClear}
              variant="destructive"
              className="flex-1"
            >
              Xóa tất cả
            </Button>
          )}

          <Button
            type="button"
            onClick={handleApply}
            className="flex-1"
          >
            Áp dụng bộ lọc
          </Button>
        </div>
      }
    >
      <SidebarForm sidebarWidth={sidebarWidth} isResizing={isResizing} isOpen={isOpen}>
        {/* Account and Category Section - Responsive grid with better breakpoints */}
        <div className={`grid gap-4 ${
          sidebarWidth > 800 ? 'grid-cols-3' :
          sidebarWidth > 600 ? 'grid-cols-2' :
          'grid-cols-1'
        }`}>
          <SidebarFormField
            label="Tài khoản"
            icon={<Building2 size={16} />}
            span="single"
            sidebarWidth={sidebarWidth}
          >
            <SearchableCombobox
              value={filters.selectedAccount}
              onChange={(value) => handleFilterChange('selectedAccount', value)}
              options={[
                { value: "all", label: "Tất cả tài khoản" },
                ...(accounts?.map((account) => ({
                  value: account.id.toString(),
                  label: account.name
                })) || [])
              ]}
              placeholder="Chọn tài khoản"
              allowClear={false}
            />
          </SidebarFormField>

          <SidebarFormField
            label="Danh mục"
            icon={<Tag size={16} />}
            span="single"
            sidebarWidth={sidebarWidth}
          >
            <SearchableCombobox
              value={filters.selectedCategory}
              onChange={(value) => handleFilterChange('selectedCategory', value)}
              options={[
                { value: "all", label: "Tất cả danh mục" },
                ...(categories?.map((category) => ({
                  value: category.id.toString(),
                  label: category.name
                })) || [])
              ]}
              placeholder="Chọn danh mục"
              allowClear={false}
            />
          </SidebarFormField>
        </div>

        {/* Transfer From and Transfer To Section - Responsive layout */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className={`grid gap-6 ${sidebarWidth > 700 ? 'grid-cols-2' : 'grid-cols-1'}`}>
            <SidebarFormField
              label="Chuyển từ"
              icon={<ArrowUpDown size={16} />}
              span="single"
              sidebarWidth={sidebarWidth}
            >
              <ContactAccountSelector
                contacts={contacts || []}
                selectedContactIds={filters.selectedTransferFromContacts}
                selectedAccountIds={filters.selectedTransferFromAccounts}
                onContactChange={(contactIds) => handleFilterChange('selectedTransferFromContacts', contactIds)}
                onAccountChange={(accountIds) => handleFilterChange('selectedTransferFromAccounts', accountIds)}
                showUserContact={true}
                userContactLabel="Tôi"
              />
            </SidebarFormField>

            <SidebarFormField
              label="Chuyển đến"
              icon={<ArrowUpDown size={16} />}
              span="single"
              sidebarWidth={sidebarWidth}
            >
              <ContactAccountSelector
                contacts={contacts || []}
                selectedContactIds={filters.selectedTransferToContacts}
                selectedAccountIds={filters.selectedTransferToAccounts}
                onContactChange={(contactIds) => handleFilterChange('selectedTransferToContacts', contactIds)}
                onAccountChange={(accountIds) => handleFilterChange('selectedTransferToAccounts', accountIds)}
                showUserContact={true}
                userContactLabel="Tôi"
              />
            </SidebarFormField>
          </div>
        </div>

        {/* Date Range and Amount Range Section - Enhanced responsive layout */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className={`grid gap-4 ${
            sidebarWidth > 800 ? 'grid-cols-4' :
            sidebarWidth > 600 ? 'grid-cols-2' :
            'grid-cols-1'
          }`}>
            {sidebarWidth > 800 ? (
              // Wide layout: separate fields
              <>
                <SidebarFormField
                  label="Từ ngày"
                  icon={<Calendar size={16} />}
                  span="single"
                  sidebarWidth={sidebarWidth}
                >
                  <SidebarInputField
                    label="Từ ngày"
                    type="date"
                    value={filters.dateFrom}
                    onChange={(value) => handleFilterChange('dateFrom', value)}
                    placeholder="Từ ngày"
                    sidebarWidth={sidebarWidth}
                  />
                </SidebarFormField>
                <SidebarFormField
                  label="Đến ngày"
                  icon={<Calendar size={16} />}
                  span="single"
                  sidebarWidth={sidebarWidth}
                >
                  <SidebarInputField
                    label="Đến ngày"
                    type="date"
                    value={filters.dateTo}
                    onChange={(value) => handleFilterChange('dateTo', value)}
                    placeholder="Đến ngày"
                    sidebarWidth={sidebarWidth}
                  />
                </SidebarFormField>
              </>
            ) : (
              // Compact layout: grouped fields
              <SidebarFormField
                label="Khoảng thời gian"
                icon={<Calendar size={16} />}
                span="single"
                sidebarWidth={sidebarWidth}
              >
                <div className="grid grid-cols-2 gap-3">
                  <SidebarInputField
                    label="Từ ngày"
                    type="date"
                    value={filters.dateFrom}
                    onChange={(value) => handleFilterChange('dateFrom', value)}
                    placeholder="Từ ngày"
                    sidebarWidth={sidebarWidth}
                  />
                  <SidebarInputField
                    label="Đến ngày"
                    type="date"
                    value={filters.dateTo}
                    onChange={(value) => handleFilterChange('dateTo', value)}
                    placeholder="Đến ngày"
                    sidebarWidth={sidebarWidth}
                  />
                </div>
              </SidebarFormField>
            )}

            {sidebarWidth > 800 ? (
              // Wide layout: separate fields
              <>
                <SidebarFormField
                  label="Từ số tiền"
                  icon={<DollarSign size={16} />}
                  span="single"
                  sidebarWidth={sidebarWidth}
                >
                  <SidebarInputField
                    label="Từ số tiền"
                    type="number"
                    value={filters.amountFrom}
                    onChange={(value) => handleFilterChange('amountFrom', value)}
                    placeholder="Từ số tiền"
                    sidebarWidth={sidebarWidth}
                  />
                </SidebarFormField>
                <SidebarFormField
                  label="Đến số tiền"
                  icon={<DollarSign size={16} />}
                  span="single"
                  sidebarWidth={sidebarWidth}
                >
                  <SidebarInputField
                    label="Đến số tiền"
                    type="number"
                    value={filters.amountTo}
                    onChange={(value) => handleFilterChange('amountTo', value)}
                    placeholder="Đến số tiền"
                    sidebarWidth={sidebarWidth}
                  />
                </SidebarFormField>
              </>
            ) : (
              // Compact layout: grouped fields
              <SidebarFormField
                label="Khoảng số tiền"
                icon={<DollarSign size={16} />}
                span="single"
                sidebarWidth={sidebarWidth}
              >
                <div className="grid grid-cols-2 gap-3">
                  <SidebarInputField
                    label="Từ số tiền"
                    type="number"
                    value={filters.amountFrom}
                    onChange={(value) => handleFilterChange('amountFrom', value)}
                    placeholder="Từ số tiền"
                    sidebarWidth={sidebarWidth}
                  />
                  <SidebarInputField
                    label="Đến số tiền"
                    type="number"
                    value={filters.amountTo}
                    onChange={(value) => handleFilterChange('amountTo', value)}
                    placeholder="Đến số tiền"
                    sidebarWidth={sidebarWidth}
                  />
                </div>
              </SidebarFormField>
            )}
          </div>
        </div>
      </SidebarForm>
    </BaseSidebar>
  )
}
