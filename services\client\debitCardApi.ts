/**
 * DebitCard API Client Service
 * 
 * Client-side service for debit card operations
 */

import { ApiClient } from './apiClient'
import { PaginatedResponse, ApiResponse } from '@/types/api'
import { DebitCardWithRelations } from '@/types/entities'

type DebitCard = Omit<DebitCardWithRelations, 'account' | 'user'>

// ==================== TYPES ====================

export interface DebitCardFilters {
  search?: string
  accountId?: string
  cardType?: string
  isActive?: boolean
}

export interface DebitCardQueryParams extends DebitCardFilters {
  page?: number
  limit?: number
}

export interface CreateDebitCardRequest {
  accountId: string
  cardName: string
  cardNumber: string
  cardType?: string
}

export interface UpdateDebitCardRequest {
  cardName?: string
  cardNumber?: string
  cardType?: string
  accountId?: string | null // Allow linking/unlinking from accounts
  isActive?: boolean
}

export type DebitCardListResponse = PaginatedResponse<DebitCard>

// ==================== API CLIENT ====================

export class DebitCardApiService {
  private static readonly BASE_URL = '/debit-cards'

  /**
   * Get all debit cards with pagination and filtering
   */
  static async getDebitCards(params: DebitCardQueryParams = {}): Promise<DebitCardListResponse> {
    const searchParams = new URLSearchParams()
    
    if (params.page) searchParams.set('page', params.page.toString())
    if (params.limit) searchParams.set('limit', params.limit.toString())
    if (params.search) searchParams.set('search', params.search)
    if (params.accountId) searchParams.set('accountId', params.accountId)
    if (params.cardType) searchParams.set('cardType', params.cardType)
    if (params.isActive !== undefined) searchParams.set('isActive', params.isActive.toString())

    const url = `${this.BASE_URL}?${searchParams.toString()}`
    return ApiClient.get<DebitCardListResponse>(url)
  }

  /**
   * Get debit card by ID
   */
  static async getDebitCard(id: string): Promise<ApiResponse<DebitCard>> {
    return ApiClient.get<ApiResponse<DebitCard>>(`${this.BASE_URL}/${id}`)
  }

  /**
   * Create new debit card
   */
  static async createDebitCard(data: CreateDebitCardRequest): Promise<ApiResponse<DebitCard>> {
    return ApiClient.post<ApiResponse<DebitCard>>(this.BASE_URL, data)
  }

  /**
   * Update debit card
   */
  static async updateDebitCard(id: string, data: UpdateDebitCardRequest): Promise<ApiResponse<DebitCard>> {
    return ApiClient.put<ApiResponse<DebitCard>>(`${this.BASE_URL}/${id}`, data)
  }

  /**
   * Delete debit card (soft delete)
   */
  static async deleteDebitCard(id: string): Promise<{ success: boolean }> {
    return ApiClient.delete<{ success: boolean }>(`${this.BASE_URL}/${id}`)
  }

  /**
   * Get debit cards for specific account
   */
  static async getDebitCardsByAccount(accountId: string): Promise<ApiResponse<DebitCardWithRelations[]>> {
    return ApiClient.get<ApiResponse<DebitCardWithRelations[]>>(`/accounts/${accountId}/debit-cards`)
  }

  /**
   * Create debit card for specific account
   */
  static async createDebitCardForAccount(
    accountId: string,
    data: Omit<CreateDebitCardRequest, 'accountId'>
  ): Promise<ApiResponse<DebitCard>> {
    return ApiClient.post<ApiResponse<DebitCard>>(`/accounts/${accountId}/debit-cards`, data)
  }

  /**
   * Get available debit cards for linking (not linked to any account or linked to different account)
   */
  static async getAvailableDebitCards(excludeAccountId?: string): Promise<ApiResponse<DebitCard[]>> {
    const searchParams = new URLSearchParams()
    if (excludeAccountId) {
      searchParams.set('excludeAccountId', excludeAccountId)
    }

    const url = `${this.BASE_URL}/available?${searchParams.toString()}`
    return await ApiClient.get<ApiResponse<DebitCard[]>>(url)
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get active debit cards only
   */
  static async getActiveDebitCards(): Promise<DebitCardListResponse> {
    return this.getDebitCards({ isActive: true })
  }

  /**
   * Get debit cards by card type
   */
  static async getDebitCardsByType(cardType: string): Promise<DebitCardListResponse> {
    return this.getDebitCards({ cardType })
  }

  /**
   * Search debit cards
   */
  static async searchDebitCards(query: string): Promise<DebitCardListResponse> {
    return this.getDebitCards({ search: query })
  }

  /**
   * Check if debit card exists
   */
  static async debitCardExists(id: string): Promise<boolean> {
    try {
      await this.getDebitCard(id)
      return true
    } catch {
      return false
    }
  }

  /**
   * Validate card number format (client-side)
   */
  static validateCardNumber(cardNumber: string): boolean {
    // Remove spaces and dashes
    const cleaned = cardNumber.replace(/[\s-]/g, '')
    
    // Check if it's all digits and has valid length (13-19 digits)
    if (!/^\d{13,19}$/.test(cleaned)) {
      return false
    }

    // Luhn algorithm validation
    let sum = 0
    let isEven = false
    
    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i])
      
      if (isEven) {
        digit *= 2
        if (digit > 9) {
          digit -= 9
        }
      }
      
      sum += digit
      isEven = !isEven
    }
    
    return sum % 10 === 0
  }

  /**
   * Format card number for display (mask all but last 4 digits)
   */
  static formatCardNumber(cardNumber: string): string {
    if (cardNumber.length < 4) return cardNumber
    const lastFour = cardNumber.slice(-4)
    const masked = '*'.repeat(Math.max(0, cardNumber.length - 4))
    return masked + lastFour
  }

  /**
   * Get card type from card number
   */
  static getCardType(cardNumber: string): string {
    const cleaned = cardNumber.replace(/[\s-]/g, '')
    
    if (/^4/.test(cleaned)) return 'VISA'
    if (/^5[1-5]/.test(cleaned)) return 'MASTERCARD'
    if (/^3[47]/.test(cleaned)) return 'AMEX'
    if (/^35/.test(cleaned)) return 'JCB'
    
    return 'UNKNOWN'
  }
}
