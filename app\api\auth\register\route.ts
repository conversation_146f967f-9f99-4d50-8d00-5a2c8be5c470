import { NextRequest } from 'next/server'
import { z } from 'zod'
import {
  withError<PERSON><PERSON><PERSON>,
  successResponse,
  validateRequest
} from '@/lib/api/utils'
import { AuthService } from '@/services/server'

// ==================== VALIDATION SCHEMAS ====================

const registerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  role: z.enum(['admin', 'user']).default('user'),
  preferences: z.object({
    currency: z.string().default('VND'),
    language: z.enum(['vi', 'en']).default('vi'),
    theme: z.enum(['light', 'dark', 'system']).default('system'),
    dateFormat: z.string().optional(),
    numberFormat: z.string().optional()
  }).optional()
})

// ==================== POST /api/auth/register ====================

export const POST = withErrorHandler(async (request: NextRequest) => {
  const data = await validateRequest(request, registerSchema)

  // Call AuthService to handle registration
  const result = await AuthService.register(data)

  return successResponse(result, 201)
})

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
