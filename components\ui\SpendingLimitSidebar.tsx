"use client"

import React, { useState, useEffect } from "react"
import { DollarSign, Calendar, FileText, FolderTree, AlertTriangle } from "lucide-react"
import { BaseSidebar } from "./BaseSidebar"
import { SidebarForm, SidebarInputField, SidebarTextareaField } from "./SidebarForm"
import { SearchableCombobox } from "./SearchableCombobox"
import { SidebarActionFooter } from "./SidebarFooter"
import { getSidebarConfig } from "../../config/sidebarConfig"

interface SpendingLimit {
  id: number
  userId: number
  name: string
  amount: number
  period: string
  categoryId?: number
  description?: string
  startDate: Date
  endDate?: Date
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

interface Category {
  id: number
  name: string
  type: string
  color: string
  logo: string
}

interface SpendingLimitSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (spendingLimit: SpendingLimit) => void
  spendingLimit?: SpendingLimit | null
  categories: Category[]
  isEditing: boolean
}

interface FormData {
  name: string
  amount: string
  period: string
  categoryId: string
  description: string
  startDate: string
  endDate: string
}

interface FormErrors {
  name?: string
  amount?: string
  period?: string
  startDate?: string
  endDate?: string
}

const SPENDING_PERIODS = [
  { value: "daily", label: "Hàng ngày" },
  { value: "weekly", label: "Hàng tuần" },
  { value: "monthly", label: "Hàng tháng" },
  { value: "quarterly", label: "Hàng quý" },
  { value: "yearly", label: "Hàng năm" }
]

export function SpendingLimitSidebar({
  isOpen,
  onClose,
  onSave,
  spendingLimit,
  categories = [],
  isEditing
}: SpendingLimitSidebarProps) {
  const config = getSidebarConfig('SPENDING_LIMIT')
  
  const [formData, setFormData] = useState<FormData>({
    name: "",
    amount: "",
    period: "monthly",
    categoryId: "",
    description: "",
    startDate: "",
    endDate: ""
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  
  // Initialize form data when sidebar opens or spending limit changes
  useEffect(() => {
    if (isOpen) {
      if (spendingLimit && isEditing) {
        setFormData({
          name: spendingLimit.name,
          amount: spendingLimit.amount.toString(),
          period: spendingLimit.period,
          categoryId: spendingLimit.categoryId?.toString() || "",
          description: spendingLimit.description || "",
          startDate: spendingLimit.startDate.toISOString().split('T')[0],
          endDate: spendingLimit.endDate?.toISOString().split('T')[0] || ""
        })
      } else {
        const now = new Date()
        setFormData({
          name: "",
          amount: "",
          period: "monthly",
          categoryId: "",
          description: "",
          startDate: now.toISOString().split('T')[0],
          endDate: ""
        })
      }
      setErrors({})
    }
  }, [isOpen, spendingLimit, isEditing])
  
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "Tên giới hạn chi tiêu là bắt buộc"
    }
    
    if (!formData.amount.trim()) {
      newErrors.amount = "Số tiền là bắt buộc"
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = "Số tiền phải là số dương"
    }
    
    if (!formData.period) {
      newErrors.period = "Chu kỳ là bắt buộc"
    }
    
    if (!formData.startDate) {
      newErrors.startDate = "Ngày bắt đầu là bắt buộc"
    }
    
    if (formData.endDate && formData.startDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
      newErrors.endDate = "Ngày kết thúc phải sau ngày bắt đầu"
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setLoading(true)
    try {
      const spendingLimitData: SpendingLimit = {
        id: spendingLimit?.id || 0,
        userId: spendingLimit?.userId || 1,
        name: formData.name.trim(),
        amount: Number(formData.amount),
        period: formData.period,
        categoryId: formData.categoryId ? Number(formData.categoryId) : undefined,
        description: formData.description.trim() || undefined,
        startDate: new Date(formData.startDate),
        endDate: formData.endDate ? new Date(formData.endDate) : undefined,
        isActive: spendingLimit?.isActive ?? true,
        createdAt: spendingLimit?.createdAt || new Date(),
        updatedAt: new Date()
      }
      
      onSave(spendingLimitData)
      handleClose()
    } catch (error) {
      console.error('Error saving spending limit:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const handleClose = () => {
    const now = new Date()
    setFormData({
      name: "",
      amount: "",
      period: "monthly",
      categoryId: "",
      description: "",
      startDate: now.toISOString().split('T')[0],
      endDate: ""
    })
    setErrors({})
    onClose()
  }
  

  
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }
  
  const categoryOptions = categories
    .filter(cat => cat.type === 'expense')
    .map(cat => ({
      value: cat.id.toString(),
      label: `${cat.logo} ${cat.name}`
    }))
  
  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa giới hạn chi tiêu" : "Thêm giới hạn chi tiêu mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${spendingLimit?.name}` : "Tạo giới hạn chi tiêu để kiểm soát ngân sách"}
      storageKey="spendingLimitSidebarWidth"
      config={config}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={handleSubmit}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={!formData.name.trim() || !formData.amount.trim()}
          sidebarWidth={400}
        />
      }
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={400}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* Spending Limit Name */}
        <SidebarInputField
          label="Tên giới hạn chi tiêu"
          value={formData.name}
          onChange={(value) => handleInputChange('name', value)}
          placeholder="Nhập tên giới hạn chi tiêu"
          required
          error={errors.name}
          icon={<AlertTriangle size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />
        
        {/* Amount and Period */}
        <SidebarInputField
          label="Số tiền giới hạn"
          value={formData.amount}
          onChange={(value) => handleInputChange('amount', value)}
          placeholder="0"
          type="number"
          required
          error={errors.amount}
          icon={<DollarSign size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={400}
        />
        
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <Calendar size={14} className="mr-2" />
            Chu kỳ *
          </label>
          <SearchableCombobox
            value={formData.period}
            onChange={(value) => handleInputChange('period', value)}
            options={SPENDING_PERIODS}
            placeholder="Chọn chu kỳ..."
            disabled={loading}
          />
          {errors.period && (
            <p className="text-xs text-red-500">{errors.period}</p>
          )}
        </div>
        
        {/* Category */}
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <FolderTree size={14} className="mr-2" />
            Danh mục
          </label>
          <SearchableCombobox
            value={formData.categoryId}
            onChange={(value) => handleInputChange('categoryId', value)}
            options={[
              { value: "", label: "Tất cả danh mục" },
              ...categoryOptions
            ]}
            placeholder="Chọn danh mục..."
            disabled={loading}
          />
        </div>
        
        {/* Date Range */}
        <SidebarInputField
          label="Ngày bắt đầu"
          value={formData.startDate}
          onChange={(value) => handleInputChange('startDate', value)}
          type="date"
          required
          error={errors.startDate}
          icon={<Calendar size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={400}
        />
        
        <SidebarInputField
          label="Ngày kết thúc"
          value={formData.endDate}
          onChange={(value) => handleInputChange('endDate', value)}
          type="date"
          error={errors.endDate}
          icon={<Calendar size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={400}
        />
        
        {/* Description */}
        <SidebarTextareaField
          label="Mô tả"
          value={formData.description}
          onChange={(value) => handleInputChange('description', value)}
          placeholder="Mô tả chi tiết về giới hạn chi tiêu (tùy chọn)"
          rows={3}
          icon={<FileText size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />
      </SidebarForm>
    </BaseSidebar>
  )
}
