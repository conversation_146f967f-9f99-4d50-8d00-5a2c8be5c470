/**
 * Transaction API Service
 *
 * Handles transaction-related API operations using GraphQL client
 */
import { apolloClient } from '@/lib/graphql/client'
import {
  GET_TRANSACTIONS,
  GET_TRANSACTION_BY_ID,
  GET_TRANSACTIONS_BY_KIND,
  GET_TRANSACTIONS_BY_ACCOUNT,
  GET_TRANSACTIONS_BY_CATEGORY,
  GET_TRANSACTIONS_BY_DATE_RANGE,
  SEARCH_TRANSACTIONS,
  CREATE_TRANSACTION,
  UPDATE_TRANSACTION,
  DELETE_TRANSACTION
} from '@/lib/graphql/operations'
import {
  convertToClientError,
  getUserFriendlyErrorMessage
} from '@/lib/graphql/errors'
import type {
  ApiResponse,
  CreateTransactionRequest,
  CreateIncomeExpenseTransactionRequest,
  CreateTransferTransactionRequest,
  UpdateTransactionRequest
} from '@/types/api'
import type {
  Transaction,
  TransactionConnection,
  CreateTransactionInput,
  UpdateTransactionInput,
  TransactionKind
} from '@/lib/graphql/types'
import type {
  TransactionConnectionResponse,
  TransactionConnectionParams
} from '@/types/graphql-connections'
export class TransactionApiService {
  // ==================== TRANSACTION CRUD OPERATIONS ====================
  /**
   * Get all transactions with filtering and pagination
   */
  static async getTransactions(params?: TransactionConnectionParams): Promise<TransactionConnectionResponse> {
    try {
      const { 
        first = 25, 
        after, 
        search, 
        kind, 
        accountId, 
        categoryId, 
        contactId,
        startDate, 
        endDate, 
        minAmount, 
        maxAmount
      } = params || {}
      
      const { data } = await apolloClient.query({
        query: GET_TRANSACTIONS,
        variables: {
          first,
          after,
          search,
          kind: kind ? kind.toUpperCase() : undefined,
          accountId,
          categoryId,
          contactId,
          startDate,
          endDate,
          minAmount,
          maxAmount
        },
        fetchPolicy: 'cache-first'
      })
      return {
        success: true,
        data: data.transactions,
        message: 'transactions retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch transactions:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get transaction by ID
   */
  static async getTransactionById(id: string): Promise<ApiResponse<Transaction>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_TRANSACTION_BY_ID,
        variables: { id },
        fetchPolicy: 'cache-first'
      })
      if (!data.transaction) {
        throw new Error('Transaction not found')
      }
      return {
        data: data.transaction,
        success: true,
        message: 'Transaction retrieved successfully'
      }
    } catch (error: any) {
      console.error(`Failed to fetch transaction ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Create new transaction
   */
  static async createTransaction(data: CreateTransactionRequest): Promise<ApiResponse<Transaction>> {
    try {
      // Handle different transaction types based on the discriminated union
      const baseInput = {
        amount: data.amount,
        date: data.date,
        description: data.description,
        location: data.location,
        note: data.note,
        attachments: data.attachments || []
      };
      
      // Type guard to check if it's an income/expense transaction
      const isIncomeExpense = (t: CreateTransactionRequest): t is CreateIncomeExpenseTransactionRequest => 
        'accountId' in t && 'categoryId' in t;
      
      // Type guard to check if it's a transfer transaction
      const isTransfer = (t: CreateTransactionRequest): t is CreateTransferTransactionRequest => 
        'fromAccountId' in t && 'toAccountId' in t;
      
      let input: CreateTransactionInput;
      
      if (isIncomeExpense(data)) {
        // Handle income/expense transaction
        input = {
          ...baseInput,
          kind: data.kind.toUpperCase() as TransactionKind,
          accountId: data.accountId,
          categoryId: data.categoryId
        };
      } else if (isTransfer(data)) {
        // Handle transfer transaction
        input = {
          ...baseInput,
          kind: 'TRANSFER' as TransactionKind,
          fromAccountId: data.fromAccountId,
          toAccountId: data.toAccountId
        };
      } else {
        throw new Error('Invalid transaction type');
      }
      const { data: result } = await apolloClient.mutate({
        mutation: CREATE_TRANSACTION,
        variables: { input },
        refetchQueries: ['GetTransactions', 'GetTransactionsByAccount', 'GetTransactionsByCategory']
      })
      return {
        data: result.createTransaction,
        success: true,
        message: 'Transaction created successfully'
      }
    } catch (error: any) {
      console.error('Failed to create transaction:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Update transaction
   */
  static async updateTransaction(id: string, data: UpdateTransactionRequest): Promise<ApiResponse<Transaction>> {
    try {
      const input: UpdateTransactionInput = {
        amount: data.amount,
        date: data.date,
        description: data.description,
        location: data.location,
        note: data.note,
        attachments: data.attachments,
        isCleared: data.isCleared,
        isReconciled: data.isReconciled,
        accountId: data.accountId,
        categoryId: data.categoryId,
        fromAccountId: data.fromAccountId,
        toAccountId: data.toAccountId,
        isActive: data.isActive
      }
      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_TRANSACTION,
        variables: { id, input },
        refetchQueries: ['GetTransactions', 'GetTransactionById', 'GetTransactionsByAccount', 'GetTransactionsByCategory']
      })
      return {
        data: result.updateTransaction,
        success: true,
        message: 'Transaction updated successfully'
      }
    } catch (error: any) {
      console.error(`Failed to update transaction ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Delete transaction
   */
  static async deleteTransaction(id: string): Promise<ApiResponse<void>> {
    try {
      const { data: result } = await apolloClient.mutate({
        mutation: DELETE_TRANSACTION,
        variables: { id },
        refetchQueries: ['GetTransactions', 'GetTransactionsByAccount', 'GetTransactionsByCategory']
      })
      return {
        data: undefined,
        success: result.deleteTransaction,
        message: 'Transaction deleted successfully'
      }
    } catch (error: any) {
      console.error(`Failed to delete transaction ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  // ==================== CONVENIENCE METHODS ====================
  /**
   * Search transactions by description
   */
  static async searchTransactions(query: string, first: number = 10): Promise<TransactionConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: SEARCH_TRANSACTIONS,
        variables: { search: query, first },
        fetchPolicy: 'cache-first'
      })
      return {
        success: true,
        data: data.transactions,
        message: 'transactions retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to search transactions:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get income transactions only
   */
  static async getIncomeTransactions(params?: Omit<TransactionConnectionParams, 'kind'>): Promise<TransactionConnectionResponse> {
    return this.getTransactions({
      ...params,
      kind: 'income'
    })
  }
  /**
   * Get expense transactions only
   */
  static async getExpenseTransactions(params?: Omit<TransactionConnectionParams, 'kind'>): Promise<TransactionConnectionResponse> {
    return this.getTransactions({
      ...params,
      kind: 'expense'
    })
  }
  /**
   * Get transfer transactions only
   */
  static async getTransferTransactions(params?: Omit<TransactionConnectionParams, 'kind'>): Promise<TransactionConnectionResponse> {
    return this.getTransactions({
      ...params,
      kind: 'transfer'
    })
  }
  /**
   * Get transactions by account using GraphQL
   */
  static async getTransactionsByAccount(accountId: string, params?: Omit<TransactionConnectionParams, 'accountId'>): Promise<TransactionConnectionResponse> {
    try {
      const { first = 25, after } = params || {}
      const { data } = await apolloClient.query({
        query: GET_TRANSACTIONS_BY_ACCOUNT,
        variables: {
          accountId,
          first,
          after
        },
        fetchPolicy: 'cache-first'
      })
      return {
        success: true,
        data: data.transactions,
        message: 'transactions retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch transactions by account:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get transactions by category using GraphQL
   */
  static async getTransactionsByCategory(categoryId: string, params?: Omit<TransactionConnectionParams, 'categoryId'>): Promise<TransactionConnectionResponse> {
    try {
      const { first = 25, after } = params || {}
      const { data } = await apolloClient.query({
        query: GET_TRANSACTIONS_BY_CATEGORY,
        variables: {
          categoryId,
          first,
          after
        },
        fetchPolicy: 'cache-first'
      })
      return {
        success: true,
        data: data.transactions,
        message: 'transactions retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch transactions by category:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get recent transactions
   */
  static async getRecentTransactions(limit: number = 10): Promise<TransactionConnectionResponse> {
    return this.getTransactions({
      first: limit
    })
  }
  /**
   * Get transactions by kind using GraphQL
   */
  static async getTransactionsByKind(kind: 'income' | 'expense' | 'transfer'): Promise<TransactionConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: GET_TRANSACTIONS_BY_KIND,
        variables: { kind: kind.toUpperCase() as TransactionKind, first: 50 }, // Added first parameter for pagination
        fetchPolicy: 'cache-first'
      })
      
      // Return the connection response directly
      return data.transactions
    } catch (error: any) {
      console.error('Failed to fetch transactions by kind:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get transactions by date range using GraphQL
   */
  static async getTransactionsByDateRange(startDate: string, endDate: string, limit: number = 50): Promise<TransactionConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: GET_TRANSACTIONS_BY_DATE_RANGE,
        variables: { startDate, endDate, first: limit },
        fetchPolicy: 'cache-first'
      })
      
      // Return the connection response directly
      return data.transactions
    } catch (error: any) {
      console.error('Failed to fetch transactions by date range:', error)
      throw convertToClientError(error)
    }
  }
}
