/**
 * Toast Provider Component
 * 
 * Provides toast notifications for the entire application
 */

"use client"

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { 
  Toast, 
  ToastProvider as RadixToastProvider, 
  ToastViewport, 
  ToastTitle, 
  ToastDescription, 
  ToastClose 
} from './Toast'
import { CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'

export type ToastType = 'success' | 'error' | 'warning' | 'info'

export interface ToastMessage {
  id: string
  type: ToastType
  title?: string
  message: string
  duration?: number
}

interface ToastContextType {
  toasts: ToastMessage[]
  showToast: (toast: Omit<ToastMessage, 'id'>) => string
  dismissToast: (id: string) => void
  clearAllToasts: () => void
  showSuccess: (message: string, title?: string, duration?: number) => string
  showError: (message: string, title?: string, duration?: number) => string
  showWarning: (message: string, title?: string, duration?: number) => string
  showInfo: (message: string, title?: string, duration?: number) => string
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

interface ToastProviderProps {
  children: React.ReactNode
}

export function AppToastProvider({ children }: ToastProviderProps) {
  const [toasts, setToasts] = useState<ToastMessage[]>([])

  const showToast = useCallback((toast: Omit<ToastMessage, 'id'>) => {
    const id = Date.now().toString() + Math.random().toString(36).substring(2)
    const newToast: ToastMessage = {
      id,
      duration: 5000,
      ...toast
    }
    
    setToasts(prev => [...prev, newToast])
    return id
  }, [])

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  const clearAllToasts = useCallback(() => {
    setToasts([])
  }, [])

  // Convenience methods
  const showSuccess = useCallback((message: string, title?: string, duration?: number) => {
    return showToast({ type: 'success', message, title, duration })
  }, [showToast])

  const showError = useCallback((message: string, title?: string, duration?: number) => {
    return showToast({ type: 'error', message, title, duration })
  }, [showToast])

  const showWarning = useCallback((message: string, title?: string, duration?: number) => {
    return showToast({ type: 'warning', message, title, duration })
  }, [showToast])

  const showInfo = useCallback((message: string, title?: string, duration?: number) => {
    return showToast({ type: 'info', message, title, duration })
  }, [showToast])

  // Auto-dismiss toasts after their duration
  useEffect(() => {
    const timers = toasts.map(toast => {
      if (toast.duration && toast.duration > 0) {
        return setTimeout(() => {
          dismissToast(toast.id)
        }, toast.duration)
      }
      return undefined
    })

    return () => {
      timers.forEach(timer => {
        if (timer) clearTimeout(timer)
      })
    }
  }, [toasts, dismissToast])

  const value = {
    toasts,
    showToast,
    dismissToast,
    clearAllToasts,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }

  return (
    <ToastContext.Provider value={value}>
      <RadixToastProvider>
        {children}
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            variant={toast.type === 'error' ? 'destructive' : 'default'}
            onOpenChange={(open) => {
              if (!open) dismissToast(toast.id)
            }}
          >
            <div className="flex items-start gap-3">
              {toast.type === 'success' && <CheckCircle className="h-5 w-5 text-green-500" />}
              {toast.type === 'error' && <AlertCircle className="h-5 w-5 text-red-500" />}
              {toast.type === 'warning' && <AlertTriangle className="h-5 w-5 text-orange-500" />}
              {toast.type === 'info' && <Info className="h-5 w-5 text-blue-500" />}
              <div className="flex-1">
                {toast.title && <ToastTitle>{toast.title}</ToastTitle>}
                <ToastDescription>{toast.message}</ToastDescription>
              </div>
            </div>
            <ToastClose />
          </Toast>
        ))}
        <ToastViewport />
      </RadixToastProvider>
    </ToastContext.Provider>
  )
}
