import { CategoryGoalApiService } from '@/services/client'
import type {
  CategoryGoalQueryParams,
  CategoryGoalWithProgress,
  CreateCategoryGoalRequest,
  UpdateCategoryGoalRequest
} from '@/services/server'
import { useCallback, useEffect, useState } from 'react'

interface UseCategoryGoalsParams {
  page?: number
  limit?: number
  search?: string
  categoryId?: string
  isActive?: boolean
}

interface UseCategoryGoalsReturn {
  categoryGoals: CategoryGoalWithProgress[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createCategoryGoal: (data: CreateCategoryGoalRequest) => Promise<boolean>
  updateCategoryGoal: (id: string, data: UpdateCategoryGoalRequest) => Promise<boolean>
  deleteCategoryGoal: (id: string) => Promise<boolean>
  toggleActive: (id: string) => Promise<boolean>
}

export function useCategoryGoals(params?: CategoryGoalQueryParams): UseCategoryGoalsReturn {
  const [categoryGoals, setCategoryGoals] = useState<CategoryGoalWithProgress[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCategoryGoals = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await CategoryGoalApiService.getCategoryGoal(params)
      
      if (response.success && response.data) {
        setCategoryGoals(response.data.data || [])
      } else {
        setError('Failed to fetch category goals')
      }
    } catch (err) {
      setError('Error fetching category goals')
      console.error('Error fetching category goals:', err)
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    fetchCategoryGoals()
  }, [fetchCategoryGoals])

  const createCategoryGoal = useCallback(async (data: CreateCategoryGoalRequest): Promise<boolean> => {
    try {
      const response = await CategoryGoalApiService.createBudget(data)
      if (response.success) {
        await fetchCategoryGoals() // Refresh list
        return true
      } else {
        setError('Failed to create category goal')
        return false
      }
    } catch (err) {
      setError('Error creating category goal')
      console.error('Error creating category goal:', err)
      return false
    }
  }, [fetchCategoryGoals])

  const updateCategoryGoal = useCallback(async (id: string, data: UpdateCategoryGoalRequest): Promise<boolean> => {
    try {
      const response = await CategoryGoalApiService.updateBudget(id, data)
      if (response.success) {
        // Update local state optimistically
        setCategoryGoals(prev => prev.map(goal => 
          goal.id === id ? { ...goal, ...data, updatedAt: new Date() } : goal
        ))
        return true
      } else {
        setError('Failed to update category goal')
        return false
      }
    } catch (err) {
      setError('Error updating category goal')
      console.error('Error updating category goal:', err)
      return false
    }
  }, [])

  const deleteCategoryGoal = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await CategoryGoalApiService.deleteBudget(id)
      if (response.success) {
        // Remove from local state
        setCategoryGoals(prev => prev.filter(goal => goal.id !== id))
        return true
      } else {
        setError('Failed to delete category goal')
        return false
      }
    } catch (err) {
      setError('Error deleting category goal')
      console.error('Error deleting category goal:', err)
      return false
    }
  }, [])

  const toggleActive = useCallback(async (id: string): Promise<boolean> => {
    const goal = categoryGoals.find(g => g.id === id)
    if (!goal) return false

    return updateCategoryGoal(id, { isActive: !goal.isActive })
  }, [categoryGoals, updateCategoryGoal])

  return {
    categoryGoals,
    loading,
    error,
    refetch: fetchCategoryGoals,
    createCategoryGoal,
    updateCategoryGoal,
    deleteCategoryGoal,
    toggleActive
  }
}

// Legacy export for backward compatibility
export const useCategoryGoal = useCategoryGoals
