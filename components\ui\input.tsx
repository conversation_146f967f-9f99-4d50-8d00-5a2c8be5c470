"use client"

import type React from "react"

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean
}

export function Input({ className = "", error = false, ...props }: InputProps) {
  const baseClasses =
    "w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm transition-all duration-200"

  const borderClasses = error
    ? "border-red-300 dark:border-red-600 focus:ring-red-500"
    : "border-gray-300 dark:border-gray-600 focus:ring-blue-500"

  return <input className={`${baseClasses} ${borderClasses} ${className}`} {...props} />
}
