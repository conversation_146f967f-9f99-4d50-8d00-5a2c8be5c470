import prisma from "@/lib/prisma"
import { BaseService } from "./base-service"
import { CategoryType } from "@/lib/graphql"


export type CategoryPaginationParams = {
        page?: number
        limit?: number
        isActive?: boolean
        type?: CategoryType
        sort?: 'asc' | 'desc'
}
export default class CategoryService extends BaseService {
        static getCategories({ page = 1, limit = 25 }: CategoryPaginationParams) {
                const defaultPagination = this.getDefaultPagination()
                return this.findManyWithRelations(prisma.category, {
                        isActive: true
                }, {
                        include: {
                                user: true
                        }
                }, {
                        createdAt: 'desc'
                }, {
                        skip: defaultPagination.page,
                        take: defaultPagination.limit
                })
        }
}

