/**
 * GraphQL Operations for Contact Management
 * 
 * Queries and mutations for contact operations
 */

import { gql } from '@apollo/client'

// ==================== FRAGMENTS ====================

export const CONTACT_TYPE_FRAGMENT = gql`
  fragment ContactTypeFragment on ContactType {
    id
    name
    description
    color
    icon
    isActive
    createdAt
    updatedAt
  }
`

export const CONTACT_FRAGMENT = gql`
  fragment ContactFragment on Contact {
    id
    name
    email
    phone
    address
    note
    avatar
    userId
    contactTypeId
    isActive
    createdAt
    updatedAt
  }
`

export const CONTACT_WITH_TYPE_FRAGMENT = gql`
  fragment ContactWithTypeFragment on Contact {
    ...ContactFragment
    contactType {
      ...ContactTypeFragment
    }
  }
  ${CONTACT_FRAGMENT}
  ${CONTACT_TYPE_FRAGMENT}
`

// ==================== QUERIES ====================

export const GET_CONTACT_TYPES = gql`
  query GetContactTypes {
    contactTypes {
      ...ContactTypeFragment
    }
  }
  ${CONTACT_TYPE_FRAGMENT}
`

export const GET_CONTACTS = gql`
  query GetContacts(
    $first: Int
    $after: String
    $search: String
    $contactTypeId: UUID
    $isActive: Boolean
  ) {
    contacts(
      first: $first
      after: $after
      search: $search
      contactTypeId: $contactTypeId
      isActive: $isActive
    ) {
      edges {
        node {
          ...ContactWithTypeFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${CONTACT_WITH_TYPE_FRAGMENT}
`

export const GET_CONTACT_BY_ID = gql`
  query GetContactById($id: UUID!) {
    contact(id: $id) {
      ...ContactWithTypeFragment
      user {
        id
        name
        email
      }
    }
  }
  ${CONTACT_WITH_TYPE_FRAGMENT}
`

export const SEARCH_CONTACTS = gql`
  query SearchContacts($search: String!, $first: Int = 20) {
    contacts(search: $search, first: $first) {
      edges {
        node {
          ...ContactWithTypeFragment
        }
      }
      totalCount
    }
  }
  ${CONTACT_WITH_TYPE_FRAGMENT}
`

export const GET_CONTACTS_BY_TYPE = gql`
  query GetContactsByType($contactTypeId: UUID!, $first: Int = 50) {
    contacts(contactTypeId: $contactTypeId, first: $first, isActive: true) {
      edges {
        node {
          ...ContactWithTypeFragment
        }
      }
      totalCount
    }
  }
  ${CONTACT_WITH_TYPE_FRAGMENT}
`

// ==================== MUTATIONS ====================

export const CREATE_CONTACT = gql`
  mutation CreateContact($input: CreateContactInput!) {
    createContact(input: $input) {
      ...ContactWithTypeFragment
    }
  }
  ${CONTACT_WITH_TYPE_FRAGMENT}
`

export const UPDATE_CONTACT = gql`
  mutation UpdateContact($id: UUID!, $input: UpdateContactInput!) {
    updateContact(id: $id, input: $input) {
      ...ContactWithTypeFragment
    }
  }
  ${CONTACT_WITH_TYPE_FRAGMENT}
`

export const DELETE_CONTACT = gql`
  mutation DeleteContact($id: UUID!) {
    deleteContact(id: $id)
  }
`
