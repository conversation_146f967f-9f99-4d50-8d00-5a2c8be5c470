/**
 * GraphQL Resolvers
 * 
 * Main resolvers file that combines all resolver modules
 */

import { userResolvers } from './user'
import { accountResolvers } from './account'
import { categoryResolvers } from './category'
import { transactionResolvers } from './transaction'
import { contactResolvers } from './contact'
import { categoryGoalResolvers } from './categoryGoal'
import { recurringTransactionResolvers } from './recurringTransaction'
import { scalarResolvers } from './scalars'
import { createCategoryWithGoal, updateCategoryWithGoal, getCategoryWithGoal as categoryWithGoal, getCategoriesWithGoals as categoriesWithGoals } from './categoryWithGoal'

export const resolvers = {
  ...scalarResolvers,
  Query: {
    ...userResolvers.Query,
    ...accountResolvers.Query,
    ...categoryResolvers.Query,
    ...transactionResolvers.Query,
    ...contactResolvers.Query,
    categoryWithGoal,
    categoriesWithGoals,
    ...categoryGoalResolvers.Query,
    ...recurringTransactionResolvers.Query
  },
  Mutation: {
    ...userResolvers.Mutation,
    ...accountResolvers.Mutation,
    ...categoryResolvers.Mutation,
    ...transactionResolvers.Mutation,
    ...contactResolvers.Mutation,
    createCategoryWithGoal,
    updateCategoryWithGoal,
    ...categoryGoalResolvers.Mutation,
    ...recurringTransactionResolvers.Mutation
  },
  User: userResolvers.User,
  Account: accountResolvers.Account,
  Category: categoryResolvers.Category,
  Transaction: transactionResolvers.Transaction,
  Contact: contactResolvers.Contact,
  CategoryGoal: categoryGoalResolvers.CategoryGoal,
  RecurringTransaction: recurringTransactionResolvers.RecurringTransaction
}
