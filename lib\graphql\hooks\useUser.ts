/**
 * User GraphQL Hooks
 * 
 * React hooks for user-related GraphQL operations
 */

import { useQuery, useMutation, useApolloClient } from '@apollo/client'
import {
  GET_CURRENT_USER,
  GET_USERS,
  GET_USER_BY_ID,
  CREATE_USER,
  UPDATE_USER,
  DELETE_USER,
  UPDATE_USER_PREFERENCES
} from '../operations'
import { convertToClientError } from '../errors'
import { extractNodes, extractPaginationInfo, useConnectionData } from '@/lib/utils/connection-helpers'
import type {
  User,
  UserConnection,
  CreateUserInput,
  UpdateUserInput,
  UpdateUserPreferencesInput
} from '../types'

// ==================== QUERY HOOKS ====================

/**
 * Hook to get current authenticated user
 */
export function useCurrentUser() {
  return useQuery(GET_CURRENT_USER, {
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  })
}

/**
 * Hook to get users with pagination and connection helpers
 */
export function useUsers(variables?: {
  first?: number
  after?: string
  search?: string
  role?: string
  isActive?: boolean
}) {
  const queryResult = useQuery(GET_USERS, {
    variables,
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.users,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.users,
    pageInfo: queryResult.data?.users?.pageInfo
  }
}

/**
 * Hook to get user by ID
 */
export function useUser(id: string) {
  return useQuery(GET_USER_BY_ID, {
    variables: { id },
    errorPolicy: 'all',
    skip: !id
  })
}

// ==================== MUTATION HOOKS ====================

/**
 * Hook to create user
 */
export function useCreateUser() {
  return useMutation(CREATE_USER, {
    refetchQueries: ['GetUsers'],
    onError: (error) => {
      console.error('Create user error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to update user
 */
export function useUpdateUser() {
  return useMutation(UPDATE_USER, {
    refetchQueries: ['GetUsers', 'GetUserById', 'GetCurrentUser'],
    onError: (error) => {
      console.error('Update user error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to delete user
 */
export function useDeleteUser() {
  return useMutation(DELETE_USER, {
    refetchQueries: ['GetUsers'],
    onError: (error) => {
      console.error('Delete user error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to update user preferences
 */
export function useUpdateUserPreferences() {
  return useMutation(UPDATE_USER_PREFERENCES, {
    refetchQueries: ['GetCurrentUser'],
    onError: (error) => {
      console.error('Update preferences error:', convertToClientError(error))
    }
  })
}

// ==================== UTILITY HOOKS ====================

/**
 * Hook to refresh user data
 */
export function useRefreshUser() {
  const client = useApolloClient()
  
  return () => {
    client.refetchQueries({
      include: ['GetCurrentUser', 'GetUsers']
    })
  }
}

/**
 * Hook to clear user cache
 */
export function useClearUserCache() {
  const client = useApolloClient()
  
  return () => {
    client.cache.evict({ fieldName: 'me' })
    client.cache.evict({ fieldName: 'users' })
    client.cache.gc()
  }
}
