"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { TrendingUp, TrendingDown, BarChart3, Activity, Grid3X3, List } from "lucide-react"
import { Category, Transaction } from "@/lib/generated/prisma"
import { PageHeader } from "@/components/shared/PageHeader"
import { CategoryApiService, TransactionApiService, CategoryGoalApiService } from "@/services/client"
import { CategoryGoalWithProgress } from "@/services/server"

// Define CategoryCycleInstance type
interface CategoryCycleInstance {
  id: number
  cycleId: number
  categoryId: string
  periodStart: Date
  periodEnd: Date
  budgetAmount: number
  actualAmount: number
  transactionCount: number
  status: 'under' | 'over' | 'on-track'
  variance: number
  variancePercentage: number
  periodLabel?: string
  progressPercentage?: number
  budget?: CategoryGoalWithProgress // Real budget data
}

// CategoryCycleCard component
const CategoryCycleCard = ({ instance, category }: { instance: CategoryCycleInstance; category: Category }) => (
  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
    <div className="flex items-center justify-between mb-3">
      <h3 className="font-medium text-gray-900 dark:text-gray-100">
        {instance.periodLabel || `${instance.periodStart.toLocaleDateString()} - ${instance.periodEnd.toLocaleDateString()}`}
      </h3>
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
        instance.status === 'on-track' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
        instance.status === 'over' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      }`}>
        {instance.status === 'on-track' ? 'Đúng kế hoạch' : instance.status === 'over' ? 'Vượt ngân sách' : 'Dưới ngân sách'}
      </span>
    </div>
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span className="text-gray-600 dark:text-gray-400">Số giao dịch:</span>
        <span className="font-medium">{instance.transactionCount}</span>
      </div>
      <div className="flex justify-between text-sm">
        <span className="text-gray-600 dark:text-gray-400">Tổng tiền:</span>
        <span className="font-medium">{instance.actualAmount.toLocaleString()} VND</span>
      </div>
    </div>
  </div>
)

// CategoryCycleListItem component
const CategoryCycleListItem = ({ instance, category }: { instance: CategoryCycleInstance; category: Category }) => (
  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3">
    <div className="flex items-center justify-between">
      <div>
        <h4 className="font-medium text-gray-900 dark:text-gray-100">
          {instance.periodLabel || `${instance.periodStart.toLocaleDateString()}`}
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {instance.transactionCount} giao dịch • {instance.actualAmount.toLocaleString()} VND
        </p>
      </div>
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
        instance.status === 'on-track' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
        instance.status === 'over' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      }`}>
        {instance.status === 'on-track' ? 'OK' : instance.status === 'over' ? 'Vượt' : 'Dưới'}
      </span>
    </div>
  </div>
)

export default function CategoryDetailPage() {
  const params: { id?: string } = useParams()
  const router = useRouter()
  const categoryId = params.id
  
  // Data states
  const [category, setCategory] = useState<Category | null>(null)
  const [cycleInstances, setCycleInstances] = useState<CategoryCycleInstance[]>([])
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [CategoryGoal, setCategoryGoal] = useState<CategoryGoalWithProgress[]>([])
  const [loading, setLoading] = useState(true)
  const [totalTransactions, setTotalTransactions] = useState(0)
  const [totalAmount, setTotalAmount] = useState(0)

  // Filter states
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear())
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [selectedPeriod, setSelectedPeriod] = useState<"monthly" | "yearly">("monthly")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // Generate cycle instances from transaction history
  const generateCycleInstancesFromTransactions = (category: Category, year: number, period: "monthly" | "yearly", CategoryGoal: CategoryGoalWithProgress[] = []) => {
    const categoryTransactions = transactions.filter((t: Transaction) => t.categoryId === category.id)
    const instances: CategoryCycleInstance[] = []

    if (period === "monthly") {
      // Generate monthly instances
      for (let month = 0; month < 12; month++) {
        const periodStart = new Date(year, month, 1)
        const periodEnd = new Date(year, month + 1, 0, 23, 59, 59)

        const monthTransactions = categoryTransactions.filter((t: Transaction) => {
          const transactionDate = new Date(t.date)
          return transactionDate >= periodStart && transactionDate <= periodEnd
        })

        const actualAmount = monthTransactions.reduce((sum: number, t: Transaction) => sum + t.amount, 0)
        const transactionCount = monthTransactions.length

        // Find matching budget for this period
        const matchingBudget = CategoryGoal.find(budget => {
          const CategoryGoaltart = new Date(budget.startDate)
          const budgetEnd = budget.endDate ? new Date(budget.endDate) : new Date(CategoryGoaltart.getFullYear(), CategoryGoaltart.getMonth() + 1, 0)
          return CategoryGoaltart <= periodEnd && budgetEnd >= periodStart
        })

        const budgetAmount = matchingBudget?.amount || 0
        const progressPercentage = budgetAmount > 0 ? Math.round((actualAmount / budgetAmount) * 100) : 0
        const variance = actualAmount - budgetAmount
        const variancePercentage = budgetAmount > 0 ? Math.round((variance / budgetAmount) * 100) : 0

        let status: 'under' | 'over' | 'on-track'
        if (progressPercentage >= 100) {
          status = 'over'
        } else if (progressPercentage >= 80) {
          status = 'on-track'
        } else {
          status = 'under'
        }

        instances.push({
          id: Date.now() + month,
          cycleId: 0,
          categoryId: category.id,
          periodStart,
          periodEnd,
          budgetAmount,
          periodLabel: `Tháng ${month + 1}/${year}`,
          actualAmount,
          transactionCount,
          progressPercentage,
          status,
          variance,
          variancePercentage,
          budget: matchingBudget
        })
      }
    } else {
      // Generate yearly instance
      const periodStart = new Date(year, 0, 1)
      const periodEnd = new Date(year, 11, 31, 23, 59, 59)

      const yearTransactions = categoryTransactions.filter((t: Transaction) => {
        const transactionDate = new Date(t.date)
        return transactionDate >= periodStart && transactionDate <= periodEnd
      })

      const actualAmount = yearTransactions.reduce((sum: number, t: Transaction) => sum + t.amount, 0)
      const transactionCount = yearTransactions.length

      // Find matching budget for this year
      const matchingBudget = CategoryGoal.find(budget => {
        const CategoryGoaltart = new Date(budget.startDate)
        const budgetEnd = budget.endDate ? new Date(budget.endDate) : new Date(CategoryGoaltart.getFullYear() + 1, 0, 0)
        return CategoryGoaltart <= periodEnd && budgetEnd >= periodStart
      })

      const budgetAmount = matchingBudget?.amount || 0
      const progressPercentage = budgetAmount > 0 ? Math.round((actualAmount / budgetAmount) * 100) : 0
      const variance = actualAmount - budgetAmount
      const variancePercentage = budgetAmount > 0 ? Math.round((variance / budgetAmount) * 100) : 0

      let status: 'under' | 'over' | 'on-track'
      if (progressPercentage >= 100) {
        status = 'over'
      } else if (progressPercentage >= 80) {
        status = 'on-track'
      } else {
        status = 'under'
      }

      instances.push({
        id: Date.now(),
        cycleId: 0,
        categoryId: category.id,
        periodStart,
        periodEnd,
        budgetAmount,
        periodLabel: `Năm ${year}`,
        actualAmount,
        transactionCount,
        progressPercentage,
        status,
        variance,
        variancePercentage,
        budget: matchingBudget
      })
    }

    return instances
  }

  // Load data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      try {
        if (!params.id) {
          router.push('/categories')
          return
        }
        // Load category from API
        const categoryResponse = await CategoryApiService.getCategoryById(params.id )
        if (!categoryResponse.success || !categoryResponse.data) {
          router.push('/categories')
          return
        }
        setCategory(categoryResponse.data)

        // Load transactions for this category
        const transactionsResponse = await TransactionApiService.getTransactions({
          categoryId: params.id,
          page: 1,
          limit: 1000
        })

        // Handle PaginatedResponse structure
        const categoryTransactions = transactionsResponse.success && transactionsResponse.data ?
          ((transactionsResponse.data as any).data || transactionsResponse.data || []) : []
        setTransactions(categoryTransactions)

        // Load CategoryGoal for this category
        const CategoryGoalResponse = await CategoryGoalApiService.getCategoryGoalByCategory(params.id)
        const categoryCategoryGoal = CategoryGoalResponse.success && CategoryGoalResponse.data ?
          CategoryGoalResponse.data : []
        setCategoryGoal(categoryCategoryGoal)

        // Generate cycle instances from transaction history with budget data
        const instances = generateCycleInstancesFromTransactions(categoryResponse.data, selectedYear, selectedPeriod, categoryCategoryGoal)
        setCycleInstances(instances)

        // Calculate totals
        setTotalTransactions(categoryTransactions.length)
        setTotalAmount(categoryTransactions.reduce((sum: number, t: Transaction) => sum + t.amount, 0))

      } catch (error) {
        console.error('Error loading category data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [categoryId, selectedYear, selectedPeriod, router])

  // Filter cycle instances
  const filteredInstances = cycleInstances.filter(instance => {
    if (selectedStatus !== "all" && instance.status !== selectedStatus) {
      return false
    }
    return true
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-3 lg:p-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!category) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-3 lg:p-4">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Không tìm thấy danh mục
          </h2>
          <button
            onClick={() => router.push('/categories')}
            className="text-blue-600 hover:text-blue-700 dark:text-blue-400"
          >
            Quay lại danh sách danh mục
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-3 lg:p-4 space-y-3">
      {/* Header */}
      <PageHeader
        title={category.name}
        description={`Lịch sử giao dịch ${category.type === 'income' ? 'thu nhập' : 'chi tiêu'}`}
        showBackButton={true}
        onBack={() => router.push('/categories')}
        actions={[
          {
            label: 'Từ giao dịch',
            onClick: () => {},
            variant: 'outline' as const,
            icon: <BarChart3 size={16} />
          }
        ]}
      />

      {/* Compact Category Details */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">

          {/* Category Identity */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: category.color || '#6B7280' }}
              />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {category.name}
              </h2>
            </div>

            <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
              category.type === "income"
                ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                : "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"
            }`}>
              {category.type === "income" ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
              {category.type === "income" ? "Thu nhập" : "Chi tiêu"}
            </div>

            <div className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${
              category.isActive
                ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                : "bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400"
            }`}>
              <div className={`w-1.5 h-1.5 rounded-full ${category.isActive ? "bg-green-500" : "bg-gray-400"}`} />
              {category.isActive ? "Hoạt động" : "Ẩn"}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {totalTransactions}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Giao dịch
              </div>
            </div>

            <div className="text-center">
              <div className={`text-xl font-bold ${
                category.type === "income"
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}>
                {new Intl.NumberFormat("vi-VN", {
                  style: "currency",
                  currency: "VND",
                  notation: "compact",
                  maximumFractionDigits: 1
                }).format(totalAmount)}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Tổng tiền
              </div>
            </div>

            <div className="text-center">
              <div className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {filteredInstances.filter(i => i.status === "on-track").length}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {selectedPeriod === "monthly" ? "Tháng" : "Năm"} có GD
              </div>
            </div>
          </div>
        </div>

        {category.description && (
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {category.description}
            </p>
          </div>
        )}
      </div>

      {/* Compact Filters */}
      <div className="flex items-center justify-between gap-2 text-sm">
        <div className="flex items-center gap-2">
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-xs"
          >
            {[2023, 2024, 2025].map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>

          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as "monthly" | "yearly")}
            className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-xs"
          >
            <option value="monthly">Tháng</option>
            <option value="yearly">Năm</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-xs"
          >
            <option value="all">Tất cả</option>
            <option value="completed">Có GD</option>
            <option value="not_started">Không GD</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {filteredInstances.length} kỳ
          </div>

          {/* View Toggle */}
          <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded overflow-hidden">
            <button
              onClick={() => setViewMode("grid")}
              className={`p-1.5 text-xs transition-colors ${
                viewMode === "grid"
                  ? "bg-blue-500 text-white"
                  : "bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
              title="Grid view"
            >
              <Grid3X3 size={14} />
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`p-1.5 text-xs transition-colors ${
                viewMode === "list"
                  ? "bg-blue-500 text-white"
                  : "bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
              title="List view"
            >
              <List size={14} />
            </button>
          </div>
        </div>
      </div>

      {/* Transaction History by Period */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredInstances.length === 0 ? (
            <div className="col-span-full text-center py-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <Activity size={32} className="mx-auto text-gray-400 mb-3" />
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                Chưa có giao dịch
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Không có giao dịch trong {selectedPeriod === "monthly" ? "các tháng" : "năm"} {selectedYear}
              </p>
            </div>
          ) : (
            filteredInstances.map(instance => (
              <CategoryCycleCard
                key={instance.id}
                instance={instance}
                category={category}
              />
            ))
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {filteredInstances.length === 0 ? (
            <div className="text-center py-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <Activity size={32} className="mx-auto text-gray-400 mb-3" />
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                Chưa có giao dịch
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Không có giao dịch trong {selectedPeriod === "monthly" ? "các tháng" : "năm"} {selectedYear}
              </p>
            </div>
          ) : (
            filteredInstances.map(instance => (
              <CategoryCycleListItem
                key={instance.id}
                instance={instance}
                category={category}
              />
            ))
          )}
        </div>
      )}
    </div>
  )
}
