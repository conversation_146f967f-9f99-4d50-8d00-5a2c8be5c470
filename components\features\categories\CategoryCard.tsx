"use client"

import { EntityCard, GenericCard } from "@/components/shared/cards/GenericCard"
import { CategoryWithGoal } from "@/lib/graphql/types"
import { CardAction } from "@/types/ui"
import {
  BarChart3,
  DollarSign,
  Edit,
  ExternalLink,
  Eye, EyeOff,
  Target
} from "lucide-react"
import { useRouter } from "next/navigation"

interface CategoryCardProps {
  category: CategoryWithGoal
  onEdit?: (category: CategoryWithGoal) => void
  onToggleActive?: (category: CategoryWithGoal) => void
  onView?: (category: CategoryWithGoal) => void
  showUsageStats?: boolean
  usageCount?: number
  totalAmount?: number
  variant?: 'default' | 'compact' | 'detailed'
}

export function CategoryCard({
  category,
  onEdit,
  onToggleActive,
  onView,
  showUsageStats = false,
  usageCount = 0,
  totalAmount = 0,
  variant = 'default'
}: CategoryCardProps) {
  const router = useRouter()

  // Format currency helper
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  // Handle view category
  const handleViewCategory = () => {
    if (onView) {
      onView(category)
    } else {
      try {
        router.push(`/categories/${category.id}`)
      } catch (error) {
        console.error('CategoryCard: Router navigation failed:', error)
        window.location.href = `/categories/${category.id}`
      }
    }
  }

  // Calculate progress for budget/target
  const calculateProgress = () => {
    if (!showUsageStats || totalAmount === 0) return 0
    
    const target = category.type === "expense" 
      ? category.categoryGoal?.targetAmount || 0 
      : category.categoryGoal?.targetAmount || 0
    
    if (!target) return 0
    
    return Math.min((totalAmount / target) * 100, 100)
  }

  // Get progress color
  const getProgressColor = () => {
    const progress = calculateProgress()
    if (category.type === "expense") {
      if (progress >= 90) return "bg-red-500"
      if (progress >= 75) return "bg-yellow-500"
      return "bg-blue-500"
    } else {
      if (progress >= 100) return "bg-green-500"
      if (progress >= 75) return "bg-green-400"
      return "bg-gray-400"
    }
  }

  // Define card actions
  const actions: CardAction<CategoryWithGoal>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: handleViewCategory,
      variant: "outline"
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    },
    {
      label: category.isActive ? "Ẩn" : "Hiện",
      icon: category.isActive ? EyeOff : Eye,
      onClick: onToggleActive || (() => {}),
      variant: "outline",
      hidden: !onToggleActive
    }
  ]

  // Render category content
  const renderCategoryContent = () => (
    <div className="space-y-3">
      {/* Header with name, type, and status */}
      <div className="flex items-start justify-between gap-2">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {/* Color indicator */}
          <div
            className="w-3 h-3 rounded-full flex-shrink-0"
            style={{ backgroundColor: category.color || '#6B7280' }}
          />
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight truncate">
              {category.name}
            </h3>
            {category.description && (
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5">
                {category.description}
              </p>
            )}
          </div>
        </div>

        {/* Type badge */}
        <div className="flex flex-col items-end gap-1">
          <span className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
            category.type === "income"
              ? "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
              : "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400"
          }`}>
            {category.type === "income" ? "Thu nhập" : "Chi tiêu"}
          </span>
          
          {/* Status indicator */}
          {!category.isActive && (
            <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
              Đã ẩn
            </span>
          )}
        </div>
      </div>

      {/* Budget/Target or Usage Stats */}
      {(category.categoryGoal || showUsageStats) && (
        <div className="space-y-2">
          {/* Budget/Target info */}
          {(category.categoryGoal) && (
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                <Target size={12} />
                <span className="font-medium">
                  {category.type === "expense" ? "Ngân sách" : "Mục tiêu"}
                </span>
                <span className="text-gray-500 dark:text-gray-400">
                  /{category.type === "expense" 
                    ? category.categoryGoal?.period 
                    : category.categoryGoal?.period}
                </span>
              </div>
              <div className={`font-semibold ${
                category.type === "expense"
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-green-600 dark:text-green-400"
              }`}>
                {formatCurrency(
                  category.type === "expense" 
                    ? category.categoryGoal?.targetAmount || 0
                    : category.categoryGoal?.targetAmount || 0
                )}
              </div>
            </div>
          )}

          {/* Usage stats */}
          {showUsageStats && (
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                <BarChart3 size={12} />
                <span className="font-semibold text-gray-900 dark:text-gray-100">
                  {usageCount}
                </span>
                <span>giao dịch</span>
              </div>
              <div className={`font-semibold ${
                category.type === "income"
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}>
                {formatCurrency(totalAmount)}
              </div>
            </div>
          )}

          {/* Progress bar */}
          {(category.categoryGoal) && 
           showUsageStats && 
           totalAmount > 0 && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-400">Tiến độ</span>
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  {calculateProgress().toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div
                  className={`h-1.5 rounded-full transition-all duration-300 ${getProgressColor()}`}
                  style={{ width: `${calculateProgress()}%` }}
                />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )

  return (
    <GenericCard
      item={category}
      renderContent={renderCategoryContent}
      actions={actions}
      onClick={handleViewCategory}
      variant={variant}
      hoverable={true}
      className={`h-full ${!category.isActive ? "opacity-60" : ""}`}
    />
  )
}

// Alternative implementation using EntityCard for simpler cases
export function SimpleCategoryCard({
  category,
  onEdit,
  onToggleActive,
  onView,
  showUsageStats = false,
  usageCount = 0,
  totalAmount = 0
}: CategoryCardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const actions: CardAction<CategoryWithGoal>[] = [
    {
      label: "Xem",
      icon: ExternalLink,
      onClick: onView || (() => {}),
      variant: "outline",
      hidden: !onView
    },
    {
      label: "Sửa",
      icon: Edit,
      onClick: onEdit || (() => {}),
      variant: "outline",
      hidden: !onEdit
    }
  ]

  const metadata = [
    ...(category.categoryGoal ? [{
      label: "Mục tiêu",
      value: formatCurrency(
        category.categoryGoal?.targetAmount || 0
      ),
      icon: Target
    }] : []),
    ...(showUsageStats ? [{
      label: "Giao dịch",
      value: usageCount,
      icon: BarChart3
    }, {
      label: "Tổng tiền",
      value: formatCurrency(totalAmount),
      icon: DollarSign
    }] : [])
  ]

  return (
    <EntityCard
      item={category}
      title={category.name}
      subtitle={category.type === "income" ? "Thu nhập" : "Chi tiêu"}
      description={category.description || undefined}
      status={{
        label: category.isActive ? "Hoạt động" : "Đã ẩn",
        color: category.isActive ? "green" : "gray"
      }}
      metadata={metadata}
      avatar={{
        fallback: category.icon || "📁",
        color: category.color
      }}
      actions={actions}
      onClick={onView}
      className={!category.isActive ? "opacity-60" : ""}
    />
  )
}

export default CategoryCard
