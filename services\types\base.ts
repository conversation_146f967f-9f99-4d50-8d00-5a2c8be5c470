
import { DateTime } from './common';

export type { BaseTable, BaseTableActiveable, BaseTableWithUser };

/**
 * Base interface for all database tables
 */
interface BaseTable {
  id: string;
  createdAt: DateTime;
  updatedAt: DateTime;
  deletedAt: DateTime | null;
}

/**
 * Base interface for tables with isActive flag
 */
interface BaseTableActiveable extends BaseTable {
  isActive: boolean;
}

/**
 * Base interface for user-owned entities
 */
interface BaseTableWithUser extends BaseTableActiveable {
  userId: string;
}

