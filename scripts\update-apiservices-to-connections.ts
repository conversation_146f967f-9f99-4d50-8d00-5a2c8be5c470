/**
 * <PERSON><PERSON><PERSON> to update ApiService files to use GraphQL Connection format
 * 
 * This script updates all ApiService files to:
 * 1. Remove convertConnectionToPaginatedResponse helper functions
 * 2. Update return types from PaginatedResponse to ConnectionResponse
 * 3. Update method signatures to use ConnectionParams
 * 4. Update method implementations to return connection format directly
 */

import fs from 'fs'
import path from 'path'

const servicesDir = path.join(process.cwd(), 'services', 'client')

// List of ApiService files to update
const apiServiceFiles = [
  'categoryApi.ts',
  'transactionApi.ts', 
  'contactApi.ts',
  'categoryGoalApi.ts',
  'recurringTransactionApi.ts',
  'accountTypeApi.ts'
]

// Mapping of old types to new types
const typeMapping = {
  'PaginatedResponse<Category>': 'CategoryConnectionResponse',
  'PaginatedResponse<Transaction>': 'TransactionConnectionResponse', 
  'PaginatedResponse<Contact>': 'ContactConnectionResponse',
  'PaginatedResponse<CategoryGoal>': 'CategoryGoalConnectionResponse',
  'PaginatedResponse<RecurringTransaction>': 'RecurringTransactionConnectionResponse',
  'PaginatedResponse<AccountType>': 'AccountTypeConnectionResponse',
  'CategoryQueryParams': 'CategoryConnectionParams',
  'TransactionQueryParams': 'TransactionConnectionParams',
  'ContactQueryParams': 'ContactConnectionParams', 
  'CategoryGoalQueryParams': 'CategoryGoalConnectionParams',
  'RecurringTransactionQueryParams': 'RecurringTransactionConnectionParams',
  'AccountTypeQueryParams': 'AccountTypeConnectionParams'
}

// Import statements to add
const connectionImports = {
  'categoryApi.ts': `import type {
  CategoryConnectionResponse,
  CategoryConnectionParams
} from '@/types/graphql-connections'`,
  'transactionApi.ts': `import type {
  TransactionConnectionResponse,
  TransactionConnectionParams
} from '@/types/graphql-connections'`,
  'contactApi.ts': `import type {
  ContactConnectionResponse,
  ContactConnectionParams
} from '@/types/graphql-connections'`,
  'categoryGoalApi.ts': `import type {
  CategoryGoalConnectionResponse,
  CategoryGoalConnectionParams
} from '@/types/graphql-connections'`,
  'recurringTransactionApi.ts': `import type {
  RecurringTransactionConnectionResponse,
  RecurringTransactionConnectionParams
} from '@/types/graphql-connections'`,
  'accountTypeApi.ts': `import type {
  AccountTypeConnectionResponse,
  AccountTypeConnectionParams
} from '@/types/graphql-connections'`
}

function updateApiServiceFile(filePath: string, fileName: string) {
  console.log(`📝 Updating ${fileName}...`)
  
  let content = fs.readFileSync(filePath, 'utf8')
  
  // 1. Remove convertConnectionToPaginatedResponse helper function
  const helperFunctionRegex = /\/\/ Helper function to convert GraphQL connection to paginated response[\s\S]*?^}/m
  content = content.replace(helperFunctionRegex, '')
  
  // 2. Update imports - remove PaginatedResponse and QueryParams, add Connection types
  content = content.replace(
    /import type \{[\s\S]*?\} from '@\/types\/api'/,
    (match) => {
      return match
        .replace(/,?\s*PaginatedResponse,?/, '')
        .replace(/,?\s*\w+QueryParams,?/g, '')
        .replace(/,\s*\}/g, '\n}')
        .replace(/\{\s*,/g, '{')
    }
  )
  
  // Add connection imports
  const connectionImport = (connectionImports as Record<string, string>)[fileName];
  if (connectionImport) {
    const importIndex = content.lastIndexOf("} from '@/lib/graphql/types/generated'")
    if (importIndex !== -1) {
      const insertIndex = content.indexOf('\n', importIndex) + 1
      content = content.slice(0, insertIndex) + connectionImport + '\n' + content.slice(insertIndex)
    }
  }
  
  // 3. Update method signatures and return types
  Object.entries(typeMapping).forEach(([oldType, newType]) => {
    const regex = new RegExp(oldType.replace(/[<>]/g, '\\$&'), 'g')
    content = content.replace(regex, newType)
  })
  
  // 4. Update method implementations
  // Replace page/limit parameters with first/after
  content = content.replace(
    /const \{ page = 1, limit = 25, ([^}]+) \} = params \|\| \{\}/g,
    'const { first = 25, after, $1 } = params || {}'
  )
  
  // Update GraphQL query variables
  content = content.replace(
    /variables: \{[\s\S]*?first: limit,[\s\S]*?after: page > 1 \? Buffer\.from\(\(\(page - 1\) \* limit\)\.toString\(\)\)\.toString\('base64'\) : undefined,/g,
    'variables: {\n          first,\n          after,'
  )
  
  // Replace convertConnectionToPaginatedResponse calls
  content = content.replace(
    /return convertConnectionToPaginatedResponse\(data\.(\w+), page, limit\)/g,
    `return {
        success: true,
        data: data.$1,
        message: '$1 retrieved successfully',
        timestamp: new Date().toISOString()
      }`
  )
  
  // Update search methods
  content = content.replace(
    /static async search\w+\(query: string, limit: number = 10\)/g,
    'static async search$&(query: string, first: number = 10)'
  )
  
  content = content.replace(
    /variables: \{ search: query, first: limit \}/g,
    'variables: { search: query, first }'
  )
  
  // 5. Clean up extra whitespace and formatting
  content = content.replace(/\n\n\n+/g, '\n\n')
  content = content.replace(/^\s*\n/gm, '')
  
  fs.writeFileSync(filePath, content)
  console.log(`✅ Updated ${fileName}`)
}

function main() {
  console.log('🚀 Starting ApiService files update to GraphQL Connection format...')
  
  for (const fileName of apiServiceFiles) {
    const filePath = path.join(servicesDir, fileName)
    
    if (fs.existsSync(filePath)) {
      try {
        updateApiServiceFile(filePath, fileName)
      } catch (error) {
        console.error(`❌ Error updating ${fileName}:`, error)
      }
    } else {
      console.warn(`⚠️  File not found: ${fileName}`)
    }
  }
  
  console.log('🎉 ApiService files update completed!')
  console.log('')
  console.log('📋 Next steps:')
  console.log('1. Review the updated files for any manual fixes needed')
  console.log('2. Update frontend components to use connection format')
  console.log('3. Test the updated API services')
}

if (require.main === module) {
  main()
}

export { main as updateApiServicesToConnections }
