/**
 * Resolvers for Category with Goal operations
 * 
 * Handles the business logic for category and goal management
 */

import { Prisma } from "@/lib/generated/prisma"
import { GraphQLError } from "graphql"
import { GraphQLContext, requireAuth, requireOwnershipOrAdmin } from "../context"
import { Category, CategoryBase, CategoryGoalWithRelations, Period } from "../types"


type CategoryWithGoal = CategoryBase & {
  categoryGoal: CategoryGoalWithRelations | null
}

/**
 * Create a new category with an associated goal
 */
export const createCategoryWithGoal = async (
  _: any,
  { input }: { input: any },
  context: GraphQLContext
): Promise<CategoryWithGoal> => {
  const user = requireAuth(context)
  const { goal, ...categoryData } = input

  try {
    return await context.prisma.$transaction(async (prisma: Prisma.TransactionClient) => {
      // 1. Create the category
      const category = await prisma.category.create({
        data: {
          ...categoryData,
          userId: user.id,
        },
      })

      // 2. Create the associated goal
      const categoryGoal = await prisma.categoryGoal.create({
        data: {
          ...goal,
          categoryId: category.id,
          userId: user.id,
          color: goal.color || categoryData.color,
        },
      })

      // 3. Return the category with its goal
      return {
        ...category,
        categoryGoal,
      }
    })
  } catch (error) {
    console.error('Failed to create category with goal:', error)
    throw new GraphQLError('Failed to create category with goal')
  }
}

/**
 * Update an existing category and its associated goal
 */
export const updateCategoryWithGoal = async (
  _: any,
  { id, input }: { id: string; input: any },
  context: GraphQLContext
): Promise<CategoryWithGoal> => {
  const user = requireAuth(context)
  const { goal, ...categoryData } = input
  id = id ?? categoryData.id ?? categoryData.categoryId
  if (!id) throw new GraphQLError('Category ID is required')
  try {
    // 1. Verify the category exists and belongs to the user
    const existingCategory = await context.prisma.category.findUnique({
      where: { id },
      include: { categoryGoals: true },
    })

    if (!existingCategory) {
      throw new GraphQLError('Category not found')
    }

    requireOwnershipOrAdmin(context, existingCategory.userId)

    return await context.prisma.$transaction(async (prisma: Prisma.TransactionClient) => {
      // 2. Update the category
      const updatedCategory = await prisma.category.update({
        where: { id },
        data: categoryData,
      })

      // 3. Update or create the goal
      let categoryGoal: CategoryGoalWithRelations | null = null

      if (goal) {
        const existingGoal = existingCategory.categoryGoals[0] // Get the first goal if exists

        if (existingGoal) {
          // Update existing goal
          categoryGoal = await prisma.categoryGoal.update({
            where: { id: existingGoal.id },
            data: {
              ...goal,
              // Only update color if explicitly provided in the goal input
              color: goal.color !== undefined ? goal.color : existingGoal.color,
            },
          })
        } else if (Object.keys(goal).length > 0) {
          // Create new goal if goal data is provided but no existing goal
          categoryGoal = await prisma.categoryGoal.create({
            data: {
              ...goal,
              categoryId: id,
              userId: user.id,
              color: goal.color || updatedCategory.color,
            },
          })
        }
      } else {

        // create goal
        categoryGoal = await prisma.categoryGoal.create({
          data: {
            name: goal.name || `${goal.name} Goal`,
            description: goal.description,
            amount: goal.targetAmount || 0,
            period: goal.period || Period.NONE as any,
            startDate: goal.startDate || new Date(),
            endDate: goal.endDate || undefined,
            color: goal.color || '#45B7D1',
            isDefault: false, // Default to false unless specified
            isActive: true,
            categoryId: id,
            userId: user.id,
          },
        })
      }

      // 4. Return the updated category with its goal
      return {
        ...updatedCategory,
        categoryGoal,
      }
    })
  } catch (error) {
    console.error('Failed to update category with goal:', error)
    throw new GraphQLError(error instanceof Error ? error.message : 'Failed to update category with goal')
  }
}

/**
 * Get a category with its associated goal
 */
export const getCategoryWithGoal = async (
  _: any,
  { id }: { id: string },
  context: GraphQLContext
): Promise<CategoryWithGoal | null> => {
  requireAuth(context)

  try {
    const category = await context.prisma.category.findUnique({
      where: { id },
      include: {
        categoryGoal: true,
      },
    })

    if (!category) {
      return null
    }

    requireOwnershipOrAdmin(context, category.userId)

    return category
  } catch (error) {
    console.error('Failed to fetch category with goal:', error)
    throw new GraphQLError('Failed to fetch category with goal')
  }
}

/**
 * Get paginated categories with their associated goals
 */
export const getCategoriesWithGoals = async (
  _: any,
  args: {
    first?: number
    after?: string
    search?: string
    type?: 'INCOME' | 'EXPENSE'
    parentId?: string
    isActive?: boolean
  },
  context: GraphQLContext
) => {
  const user = requireAuth(context)
  const { first = 10, after, search, type, parentId, isActive } = args

  try {
    // Build the where clause
    const where: any = { userId: user.id }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (type) where.type = type
    if (parentId !== undefined) where.parentId = parentId
    if (isActive !== undefined) where.isActive = isActive

    // Get the total count
    const totalCount = await context.prisma.category.count({ where })

    // Get the categories with their goals
    const categories = await context.prisma.category.findMany({
      where,
      include: {
        categoryGoal: true,
      },
      take: first,
      ...(after && { skip: 1, cursor: { id: after } }),
      orderBy: { name: 'asc' },
    })

    // Format the edges and page info for pagination
    const edges = categories.map((category) => ({
      node: category,
      cursor: category.id,
    }))

    const hasNextPage = edges.length === first
    const endCursor = edges.length > 0 ? edges[edges.length - 1].cursor : null

    return {
      edges,
      pageInfo: {
        hasNextPage,
        hasPreviousPage: !!after,
        startCursor: edges[0]?.cursor || null,
        endCursor,
      },
      totalCount,
    }
  } catch (error) {
    console.error('Failed to fetch categories with goals:', error)
    throw new GraphQLError('Failed to fetch categories with goals')
  }
}
