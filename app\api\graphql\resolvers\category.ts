/**
 * Category GraphQL Resolvers
 *
 * Resolvers for Category queries and mutations using Prisma directly
 */

import { GraphQLContext, requireAuth, requireOwnershipOrAdmin } from '../context'
import { prisma } from '@/lib/database'
import { GraphQLError } from 'graphql'

interface CategoryArgs {
  id: string
}

interface CategoriesArgs {
  first?: number
  after?: string
  search?: string
  type?: 'income' | 'expense'
  parentId?: string
  isActive?: boolean
}

interface CreateCategoryArgs {
  input: {
    name: string
    description?: string
    type: 'income' | 'expense'
    color?: string
    icon?: string
    parentId?: string
  }
}

interface UpdateCategoryArgs {
  id: string
  input: {
    name?: string
    description?: string
    type?: 'income' | 'expense'
    color?: string
    icon?: string
    parentId?: string
    isActive?: boolean
  }
}

// Helper function to build connection response
function buildConnection<T>(
  items: T[],
  totalCount: number,
  first?: number,
  after?: string
) {
  const edges = items.map((item: any, index) => ({
    node: item,
    cursor: Buffer.from(`${after ? parseInt(Buffer.from(after, 'base64').toString()) + index + 1 : index}`).toString('base64')
  }))

  const hasNextPage = first ? items.length === first : false
  const hasPreviousPage = !!after

  return {
    edges,
    pageInfo: {
      hasNextPage,
      hasPreviousPage,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount
  }
}

export const categoryResolvers = {
  Query: {
    categories: async (_: any, args: CategoriesArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const { first = 25, after, search, type, parentId, isActive } = args

      // Build where clause
      const where: any = {
        userId: user.id
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (type) where.type = type
      if (parentId) where.parentId = parentId
      if (isActive !== undefined) where.isActive = isActive

      // Convert cursor to skip value
      const skip = after ? parseInt(Buffer.from(after, 'base64').toString()) : 0

      const [categories, totalCount] = await Promise.all([
        prisma.category.findMany({
          where,
          include: {
            parent: true,
            children: true
          },
          skip,
          take: first,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.category.count({ where })
      ])

      return buildConnection(categories, totalCount, first, after)
    },

    category: async (_: any, args: CategoryArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const category = await prisma.category.findFirst({
        where: {
          id: args.id,
          userId: user.id
        },
        include: {
          parent: true,
          children: true
        }
      })

      if (!category) {
        throw new GraphQLError('Category not found')
      }

      // Check ownership
      requireOwnershipOrAdmin(context, category.userId)

      return category
    }
  },

  Mutation: {
    createCategory: async (_: any, args: CreateCategoryArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // Verify parent category exists if provided
        if (input.parentId) {
          const parentCategory = await prisma.category.findFirst({
            where: {
              id: input.parentId,
              userId: user.id
            }
          })

          if (!parentCategory) {
            throw new GraphQLError('Parent category not found')
          }
        }

        const category = await prisma.category.create({
          data: {
            name: input.name,
            description: input.description,
            type: input.type,
            color: input.color || '#45B7D1',
            icon: input.icon,
            parentId: input.parentId,
            userId: user.id
          },
          include: {
            parent: true,
            children: true
          }
        })

        return category
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to create category')
      }
    },

    updateCategory: async (_: any, args: UpdateCategoryArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // First check if category exists and user has permission
        const existingCategory = await prisma.category.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingCategory) {
          throw new GraphQLError('Category not found')
        }

        requireOwnershipOrAdmin(context, existingCategory.userId)

        // Verify parent category exists if provided
        if (input.parentId && input.parentId !== existingCategory.parentId) {
          const parentCategory = await prisma.category.findFirst({
            where: {
              id: input.parentId,
              userId: user.id
            }
          })

          if (!parentCategory) {
            throw new GraphQLError('Parent category not found')
          }
        }

        const category = await prisma.category.update({
          where: { id: args.id },
          data: {
            name: input.name,
            description: input.description,
            type: input.type,
            color: input.color,
            icon: input.icon,
            parentId: input.parentId,
            isActive: input.isActive
          },
          include: {
            parent: true,
            children: true
          }
        })

        return category
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update category')
      }
    },

    deleteCategory: async (_: any, args: CategoryArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        // First check if category exists and user has permission
        const existingCategory = await prisma.category.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingCategory) {
          throw new GraphQLError('Category not found')
        }

        requireOwnershipOrAdmin(context, existingCategory.userId)

        // Check if category has children
        const childrenCount = await prisma.category.count({
          where: { parentId: args.id }
        })

        if (childrenCount > 0) {
          throw new GraphQLError('Cannot delete category with subcategories')
        }

        // Soft delete by setting isActive to false
        await prisma.category.update({
          where: { id: args.id },
          data: { isActive: false }
        })

        return true
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to delete category')
      }
    }
  },

  Category: {
    user: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.user) {
        return parent.user
      }

      return await context.prisma.user.findUnique({
        where: { id: parent.userId }
      })
    },

    parent: async (parent: any, _: any, context: GraphQLContext) => {
      if (!parent.parentId) {
        return null
      }

      if (parent.parent) {
        return parent.parent
      }

      return await context.prisma.category.findUnique({
        where: { id: parent.parentId }
      })
    },

    children: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.category.findMany({
        where: { 
          parentId: parent.id,
          isActive: true 
        },
        orderBy: { name: 'asc' }
      })
    },

    transactions: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.transaction.findMany({
        where: { 
          categoryId: parent.id,
          isActive: true 
        },
        include: {
          account: { include: { accountType: true } },
          category: true,
          fromAccount: { include: { accountType: true } },
          toAccount: { include: { accountType: true } }
        },
        orderBy: { date: 'desc' },
        take: 50 // Limit to recent transactions
      })
    },
    categoryGoal: async (parent: any, _: any, context: GraphQLContext) => {
      return await context.prisma.categoryGoal.findFirst({
        where: { 
          categoryId: parent.id,
          isActive: true,
          isDefault: true
        },
        orderBy: { createdAt: 'desc' }
      })
    },
    categoryGoals: async (parent: any, { filter }: { filter?: { isActive?: boolean, isDefault?: boolean } } = {}, context: GraphQLContext) => {
      const where: any = { categoryId: parent.id };
      
      // Apply filters if provided
      if (filter) {
        if (filter.isActive !== undefined) where.isActive = filter.isActive;
        if (filter.isDefault !== undefined) where.isDefault = filter.isDefault;
      }
      
      // Default to only active goals if no filters are provided
      if (Object.keys(where).length === 1) {
        where.isActive = true;
      }
      
      // Get the most recent active goal by default
      const goals = await context.prisma.categoryGoal.findMany({
        where,
        orderBy: [
          { isDefault: 'desc' },  // Default goals first
          { startDate: 'desc' },  // Then most recent first
        ],
      });
      
      // If no specific filters, return only the most relevant goal (default or most recent active)
      if (!filter) {
        return goals.length > 0 ? [goals[0]] : [];
      }
      
      return goals;
    },

    recurringTransactions: async (parent: any) => {
      return await prisma.recurringTransaction.findMany({
        where: {
          categoryId: parent.id,
          isActive: true
        },
        orderBy: { createdAt: 'desc' }
      })
    }
  }
}
