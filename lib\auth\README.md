# Authentication System

Hệ thống authentication hoàn chỉnh với JWT tokens, password hashing, và role-based access control.

## 🔧 C<PERSON>u hình

### Environment Variables

Thêm vào `.env.local`:

```env
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d
```

### Dependencies

```bash
pnpm install jsonwebtoken bcryptjs
pnpm install -D @types/jsonwebtoken
```

## 📁 Cấu trúc Files

```
lib/auth/
├── config.ts          # Auth configuration
├── jwt.ts             # JWT utilities
├── password.ts        # Password hashing & validation
├── middleware.ts      # Auth middleware
└── README.md          # Documentation
```

## 🔐 Features

### 1. JWT Token Management
- Access tokens (7 days default)
- Refresh tokens (30 days default)
- Token verification & validation
- Automatic expiration handling

### 2. Password Security
- bcrypt hashing (12 rounds)
- Password strength validation
- Secure password generation

### 3. Authentication Middleware
- `withAuth()` - Require authentication
- `withRole()` - Role-based access control
- `withAdminRole()` - Admin-only access
- `withOptionalAuth()` - Optional authentication

### 4. Account Security
- Login attempt tracking
- Account lockout (5 failed attempts)
- Temporary lockout (15 minutes)
- Active user validation

## 🚀 Usage

### API Routes

#### Login
```typescript
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Register
```typescript
POST /api/auth/register
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

#### Get Current User
```typescript
GET /api/auth/me
Headers: Authorization: Bearer <token>
```

#### Refresh Token
```typescript
POST /api/auth/refresh
Headers: Authorization: Bearer <refresh_token>
```

#### Change Password
```typescript
POST /api/auth/change-password
Headers: Authorization: Bearer <token>
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

### Middleware Usage

```typescript
// Require authentication
export const GET = withAuth(request, async (req) => {
  // req.user contains JWT payload
  const userId = req.user.userId
  // ... handler logic
})

// Admin only
export const POST = withAdminRole(request, async (req) => {
  // Only admin users can access
  // ... handler logic
})

// Role-based access
export const PUT = withRole(['admin', 'user'], request, async (req) => {
  // Both admin and user can access
  // ... handler logic
})
```

### Frontend Integration

```typescript
// Login
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
})

// Authenticated requests
const response = await fetch('/api/protected-route', {
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

## 🔒 Security Features

### Password Requirements
- Minimum 8 characters
- At least 1 lowercase letter
- At least 1 uppercase letter
- At least 1 number
- At least 1 special character
- No common patterns

### Account Protection
- Maximum 5 login attempts
- 15-minute lockout after failed attempts
- Account activation status check
- Last login tracking

### Token Security
- Secure JWT signing
- Proper token expiration
- Refresh token rotation
- Bearer token extraction

## 📊 Default Users

Hệ thống tạo sẵn 2 users:

```typescript
// Admin user
{
  email: "<EMAIL>",
  password: "password123", // Hashed in database
  role: "admin"
}

// Regular user
{
  email: "<EMAIL>", 
  password: "password123", // Hashed in database
  role: "user"
}
```

## 🛠️ Development

### Testing Authentication

```bash
# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Use token
curl -X GET http://localhost:3000/api/auth/me \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Error Handling

Tất cả auth endpoints trả về consistent error format:

```json
{
  "success": false,
  "error": {
    "message": "Invalid email or password",
    "code": "INVALID_CREDENTIALS"
  }
}
```

## 🔄 Migration từ Mock Auth

Hệ thống mới tương thích với frontend hiện tại. Chỉ cần:

1. Update login calls để handle refresh tokens
2. Store both access & refresh tokens
3. Implement token refresh logic
4. Update error handling

## 🚀 Production Deployment

1. Set strong JWT_SECRET in production
2. Configure proper CORS headers
3. Enable HTTPS only
4. Set secure cookie flags
5. Implement rate limiting
6. Add request logging
7. Monitor failed login attempts
