/**
 * useOnClickOutside Hook
 * 
 * Detects clicks outside of a specified element and triggers a callback
 */

import { useEffect, useRef } from 'react'

type Handler = (event: MouseEvent | TouchEvent) => void

/**
 * Hook that alerts clicks outside of the passed ref
 */
export function useOnClickOutside<T extends HTMLElement = HTMLElement>(
  handler: Hand<PERSON>,
  mouseEvent: 'mousedown' | 'mouseup' = 'mousedown',
  touchEvent: 'touchstart' | 'touchend' = 'touchstart'
) {
  const ref = useRef<T>(null)

  useEffect(() => {
    const listener = (event: MouseEvent | TouchEvent) => {
      const el = ref?.current
      if (!el || el.contains((event?.target as Node) || null)) {
        return
      }

      handler(event) // Call the handler only if the click is outside of the element passed.
    }

    document.addEventListener(mouseEvent, listener)
    document.addEventListener(touchEvent, listener)

    return () => {
      document.removeEventListener(mouseEvent, listener)
      document.removeEventListener(touchEvent, listener)
    }
  }, [ref, handler, mouseEvent, touchEvent])

  return ref
}

/**
 * Alternative version that accepts a ref as parameter
 */
export function useOnClickOutsideRef<T extends HTMLElement = HTMLElement>(
  ref: React.RefObject<T>,
  handler: Handler,
  mouseEvent: 'mousedown' | 'mouseup' = 'mousedown',
  touchEvent: 'touchstart' | 'touchend' = 'touchstart'
) {
  useEffect(() => {
    const listener = (event: MouseEvent | TouchEvent) => {
      const el = ref?.current
      if (!el || el.contains((event?.target as Node) || null)) {
        return
      }

      handler(event)
    }

    document.addEventListener(mouseEvent, listener)
    document.addEventListener(touchEvent, listener)

    return () => {
      document.removeEventListener(mouseEvent, listener)
      document.removeEventListener(touchEvent, listener)
    }
  }, [ref, handler, mouseEvent, touchEvent])
}

export default useOnClickOutside
