export interface TransactionColumn {
  key: string
  label: string
  required: boolean // Columns that should never be hidden
  priority: number // Higher numbers = higher priority (1 = lowest, 5 = highest)
  minWidth: number // Minimum width in pixels
  flexGrow?: number // Flex grow factor (default 1)
  align?: "left" | "center" | "right"
  mobileOnly?: boolean // Only show on mobile
  desktopOnly?: boolean // Only show on desktop
}

// Configuration for transaction table columns
export const TRANSACTION_COLUMNS: TransactionColumn[] = [
  {
    key: "date",
    label: "Ngày",
    required: true, // Always visible
    priority: 5, // Highest priority
    minWidth: 140, // Increased for date + time
    flexGrow: 1,
    align: "left"
  },
  {
    key: "description",
    label: "<PERSON><PERSON> tả",
    required: true, // Always visible
    priority: 5, // Highest priority
    minWidth: 150,
    flexGrow: 2,
    align: "left"
  },
  {
    key: "category",
    label: "<PERSON>h mục",
    required: false,
    priority: 2, // Low priority - hide 3rd
    minWidth: 120,
    flexGrow: 1,
    align: "left"
  },
  {
    key: "transferFrom",
    label: "<PERSON><PERSON><PERSON><PERSON> từ",
    required: false,
    priority: 3, // Medium priority - hide 2nd
    minWidth: 140,
    flexGrow: 1,
    align: "left"
  },
  {
    key: "transferTo",
    label: "Chuyển đến",
    required: false,
    priority: 1, // Lowest priority - hide 1st
    minWidth: 140,
    flexGrow: 1,
    align: "left"
  },
  {
    key: "amount",
    label: "Số tiền",
    required: true, // Always visible
    priority: 5, // Highest priority
    minWidth: 100,
    flexGrow: 1,
    align: "right"
  },
  {
    key: "actions",
    label: "",
    required: true, // Always visible
    priority: 5, // Highest priority
    minWidth: 80,
    flexGrow: 0,
    align: "right",
    desktopOnly: true
  }
]

// Breakpoints for responsive behavior
export const RESPONSIVE_BREAKPOINTS = {
  mobile: 640,    // sm
  tablet: 768,    // md
  desktop: 1024,  // lg
  wide: 1280      // xl
}

// Get visible columns based on available width
export function getVisibleColumns(availableWidth: number): TransactionColumn[] {
  let visibleColumns = [...TRANSACTION_COLUMNS]

  // Filter out mobile/desktop only columns based on screen size
  if (availableWidth >= RESPONSIVE_BREAKPOINTS.tablet) {
    // Desktop view - remove mobile-only columns
    visibleColumns = visibleColumns.filter(col => !col.mobileOnly)
  } else {
    // Mobile view - remove desktop-only columns
    visibleColumns = visibleColumns.filter(col => !col.desktopOnly)
  }

  // Calculate minimum width needed for all columns
  let totalMinWidth = visibleColumns.reduce((sum, col) => sum + col.minWidth, 0)

  // If total min width exceeds available width, start hiding columns by priority
  while (totalMinWidth > availableWidth && visibleColumns.some(col => !col.required)) {
    // Find the lowest priority optional column
    const optionalColumns = visibleColumns.filter(col => !col.required)
    if (optionalColumns.length === 0) break

    const lowestPriorityCol = optionalColumns.reduce((lowest, col) =>
      col.priority < lowest.priority ? col : lowest
    )

    // Remove the lowest priority column
    visibleColumns = visibleColumns.filter(col => col.key !== lowestPriorityCol.key)
    totalMinWidth = visibleColumns.reduce((sum, col) => sum + col.minWidth, 0)
  }

  return visibleColumns
}

// Helper function to get column configuration by key
export function getColumnConfig(key: string): TransactionColumn | undefined {
  return TRANSACTION_COLUMNS.find(col => col.key === key)
}

// Helper function to check if column should be visible at given width
export function isColumnVisible(columnKey: string, availableWidth: number): boolean {
  const visibleColumns = getVisibleColumns(availableWidth)
  return visibleColumns.some(col => col.key === columnKey)
}

// CSS Grid template generator for responsive columns with flex-based widths
export function generateGridTemplate(visibleColumns: TransactionColumn[]): string {
  return visibleColumns
    .map(col => {
      if (col.flexGrow === 0) {
        return `${col.minWidth}px`
      }
      return `minmax(${col.minWidth}px, ${col.flexGrow}fr)`
    })
    .join(" ")
}

// Calculate total minimum width needed for columns
export function calculateMinWidth(columns: TransactionColumn[]): number {
  return columns.reduce((sum, col) => sum + col.minWidth, 0)
}

// Get columns that fit within available width
export function getColumnsForWidth(availableWidth: number): TransactionColumn[] {
  return getVisibleColumns(availableWidth)
}
