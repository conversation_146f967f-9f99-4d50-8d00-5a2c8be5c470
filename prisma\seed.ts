/**
 * Prisma Database Seed Script
 * 
 * Populates the database with initial data for development and testing
 */

import { PrismaClient } from '../lib/generated/prisma'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Clear existing data (in development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('🧹 Clearing existing data...')
    await prisma.auditLog.deleteMany()
    await prisma.categoryGoal.deleteMany()
    await prisma.recurringTransaction.deleteMany()
    await prisma.transaction.deleteMany()
    await prisma.category.deleteMany()
    await prisma.bankInfo.deleteMany()
    await prisma.account.deleteMany()
    await prisma.contact.deleteMany()
    await prisma.contactType.deleteMany()
    await prisma.accountType.deleteMany()
    await prisma.userPreferences.deleteMany()
    await prisma.user.deleteMany()
  }

  // Define default account types
  const DEFAULT_ACCOUNT_TYPES = [
    { 
      name: 'Tiền mặt',
      color: '#10B981',
      isBankAccount: false,
      isDefault: true
    },
    { 
      name: 'Ngân hàng',
      color: '#3B82F6',
      isBankAccount: true,
      isDefault: true
    },
    { 
      name: 'Tiết kiệm',
      color: '#8B5CF6',
      isBankAccount: true,
      isDefault: true
    },
    { 
      name: 'Thẻ tín dụng',
      color: '#EF4444',
      isBankAccount: false,
      isDefault: true
    },
    { 
      name: 'Đầu tư',
      color: '#F59E0B',
      isBankAccount: false,
      isDefault: true
    },
    { 
      name: 'Tiền vay',
      color: '#EC4899',
      isBankAccount: false,
      isDefault: true
    },
    { 
      name: 'Ví điện tử',
      color: '#8B5CF6',
      isBankAccount: false,
      isDefault: true
    }
  ]

  // Create Account Types
  console.log('💳 Creating account types...')
  const accountTypes = await Promise.all(
    DEFAULT_ACCOUNT_TYPES.map(type =>
      prisma.accountType.create({
        data: type
      })
    )
  )

  // Create Contact Types
  console.log('👥 Creating contact types...')
  const contactTypes = await Promise.all([
    prisma.contactType.create({
      data: {
        name: 'Cá nhân',
        description: 'Liên hệ cá nhân'
      }
    }),
    prisma.contactType.create({
      data: {
        name: 'Công ty',
        description: 'Liên hệ công ty/doanh nghiệp'
      }
    })
  ])

  // Create Admin User
  console.log('👤 Creating admin user...')
  const hashedPassword = await bcrypt.hash('password123', 10)
  
  const adminUser = await prisma.user.create({
    data: {
      name: 'Admin User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      preferences: {
        create: {
          name: 'User Preferences',
          currency: 'VND',
          language: 'vi',
          theme: 'light'
        }
      }
    },
    include: {
      preferences: true
    }
  })

  // Create Categories
  console.log('📂 Creating categories...')
  const categories = await Promise.all([
    prisma.category.create({
      data: {
        name: 'Ăn uống',
        type: 'expense',
        color: '#f59e0b',
        userId: adminUser.id,
      }
    }),
    prisma.category.create({
      data: {
        name: 'Di chuyển',
        type: 'expense',
        color: '#3b82f6',
        userId: adminUser.id,
      }
    }),
    prisma.category.create({
      data: {
        name: 'Mua sắm',
        type: 'expense',
        color: '#ef4444',
        userId: adminUser.id,
      }
    }),
    prisma.category.create({
      data: {
        name: 'Lương',
        type: 'income',
        color: '#10b981',
        userId: adminUser.id,
      }
    }),
    prisma.category.create({
      data: {
        name: 'Đầu tư',
        type: 'income',
        color: '#8b5cf6',
        userId: adminUser.id,
      }
    })
  ])

  // Create Contacts
  console.log('👥 Creating contacts...')
  const contacts = await Promise.all([
    prisma.contact.create({
      data: {
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        phone: '**********',
        contactTypeId: contactTypes[0].id,
        userId: adminUser.id,
      }
    }),
    prisma.contact.create({
      data: {
        name: 'Công ty ABC',
        email: '<EMAIL>',
        phone: '**********',
        contactTypeId: contactTypes[1].id,
        userId: adminUser.id,
      }
    })
  ])

  // Create Accounts with BankInfo
  console.log('💰 Creating accounts...')
  const cashAccount = await prisma.account.create({
    data: {
      name: 'Tiền mặt',
      description: 'Tiền mặt trong ví',
      accountTypeId: accountTypes[0].id, // Tiền mặt
      balance: 5000000,
      currency: 'VND',
      color: '#f59e0b',
      userId: adminUser.id,
    }
  })

  const bankAccount = await prisma.account.create({
    data: {
      name: 'Tài khoản Vietcombank',
      description: 'Tài khoản thanh toán chính',
      accountTypeId: accountTypes[1].id, // Ngân hàng
      balance: ********,
      currency: 'VND',
      color: '#3b82f6',
      userId: adminUser.id,
      bankInfo: {
        create: {
          bankName: 'Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)',
          accountNumber: '**********',
          userId: adminUser.id,
        }
      }
    }
  })

  const creditAccount = await prisma.account.create({
    data: {
      name: 'Thẻ tín dụng Techcombank',
      description: 'Thẻ tín dụng chính',
      accountTypeId: accountTypes[3].id, // Thẻ tín dụng
      balance: -2000000,
      currency: 'VND',
      color: '#ef4444',
      userId: adminUser.id
    }
  })

  const savingsAccount = await prisma.account.create({
    data: {
      name: 'Tài khoản tiết kiệm BIDV',
      description: 'Tài khoản tiết kiệm lãi suất cao',
      accountTypeId: accountTypes[2].id, // Tài khoản tiết kiệm
      balance: *********,
      currency: 'VND',
      color: '#06b6d4',
      userId: adminUser.id,
      bankInfo: {
        create: {
          bankName: 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam (BIDV)',
          accountNumber: '**********',
          userId: adminUser.id,
        }
      }
    }
  })

  const investmentAccount = await prisma.account.create({
    data: {
      name: 'Tài khoản đầu tư',
      description: 'Tài khoản đầu tư chứng khoán',
      accountTypeId: accountTypes[3].id, // Đầu tư
      balance: ********,
      currency: 'VND',
      color: '#10b981',
      userId: adminUser.id,
    }
  })

  // Create Payment App Accounts
  console.log('📱 Creating payment app accounts...')
  // Find the e-wallet account type (Ví điện tử)
  const eWalletAccountType = accountTypes.find(t => t.name === 'Ví điện tử');
  
  if (!eWalletAccountType) {
    throw new Error('E-wallet account type not found');
  }

  const momoAccount = await prisma.account.create({
    data: {
      name: 'Ví MoMo',
      description: 'Ví điện tử MoMo chính',
      accountTypeId: eWalletAccountType.id,
      balance: 5000000,
      currency: 'VND',
      color: '#d946ef',
      userId: adminUser.id,
    }
  })

  const zaloPayAccount = await prisma.account.create({
    data: {
      name: 'Ví ZaloPay',
      description: 'Ví điện tử ZaloPay',
      accountTypeId: eWalletAccountType.id,
      balance: 2000000,
      currency: 'VND',
      color: '#0ea5e9',
      userId: adminUser.id,
    }
  })

  const accounts = [cashAccount, bankAccount, creditAccount, savingsAccount, investmentAccount, momoAccount, zaloPayAccount]

  // Create sample transactions
  console.log('💸 Creating sample transactions...')
  const transactions = await Promise.all([
    // Income transaction
    prisma.transaction.create({
      data: {
        kind: 'income',
        amount: ********,
        description: 'Lương tháng 12',
        date: new Date('2024-12-01'),
        accountId: bankAccount.id,
        categoryId: categories[3].id, // Lương
        userId: adminUser.id,
      }
    }),
    // Expense transactions
    prisma.transaction.create({
      data: {
        kind: 'expense',
        amount: 500000,
        description: 'Ăn trưa',
        date: new Date('2024-12-02'),
        accountId: cashAccount.id,
        categoryId: categories[0].id, // Ăn uống
        userId: adminUser.id,
      }
    }),
    prisma.transaction.create({
      data: {
        kind: 'expense',
        amount: 200000,
        description: 'Grab xe',
        date: new Date('2024-12-02'),
        accountId: cashAccount.id,
        categoryId: categories[1].id, // Di chuyển
        userId: adminUser.id,
      }
    }),
    prisma.transaction.create({
      data: {
        kind: 'expense',
        amount: 2000000,
        description: 'Mua quần áo',
        date: new Date('2024-12-03'),
        accountId: creditAccount.id,
        categoryId: categories[2].id, // Mua sắm
        userId: adminUser.id,
      }
    }),
    // Transfer transaction
    prisma.transaction.create({
      data: {
        kind: 'transfer',
        amount: 3000000,
        description: 'Chuyển tiền từ ngân hàng sang tiền mặt',
        date: new Date('2024-12-04'),
        fromAccountId: bankAccount.id,
        toAccountId: cashAccount.id,
        userId: adminUser.id,
      }
    })
  ])

  console.log('✅ Database seed completed!')
  console.log(`👤 Admin user created: ${adminUser.email}`)
  console.log(`💳 Account types created: ${accountTypes.length}`)
  console.log(`👥 Contact types created: ${contactTypes.length}`)
  console.log(`📂 Categories created: ${categories.length}`)
  console.log(`👥 Contacts created: ${contacts.length}`)
  console.log(`💰 Accounts created: ${accounts.length}`)
  console.log(`🏦 Bank info records created: 2`)
  console.log(`💸 Transactions created: ${transactions.length}`)
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
