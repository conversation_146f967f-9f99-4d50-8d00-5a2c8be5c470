/**
 * GraphQL Error Handling
 * 
 * Custom error classes and error formatting for GraphQL
 */

import { GraphQLError, GraphQLFormattedError } from 'graphql'
import { 
  ValidationError, 
  NotFoundError, 
  ConflictError, 
  ForbiddenError,
  UnauthorizedError 
} from '@/lib/api/utils'

// Custom GraphQL Error Classes
export class GraphQLValidationError extends GraphQLError {
  constructor(message: string, field?: string) {
    super(message, {
      extensions: {
        code: 'VALIDATION_ERROR',
        field
      }
    })
  }
}

export class GraphQLNotFoundError extends GraphQLError {
  constructor(resource: string) {
    super(`${resource} not found`, {
      extensions: {
        code: 'NOT_FOUND',
        resource
      }
    })
  }
}

export class GraphQLUnauthorizedError extends GraphQLError {
  constructor(message: string = 'Authentication required') {
    super(message, {
      extensions: {
        code: 'UNAUTHENTICATED'
      }
    })
  }
}

export class GraphQLForbiddenError extends GraphQLError {
  constructor(message: string = 'Access denied: insufficient permissions') {
    super(message, {
      extensions: {
        code: 'FORBIDDEN'
      }
    })
  }
}

export class GraphQLConflictError extends GraphQLError {
  constructor(message: string, field?: string) {
    super(message, {
      extensions: {
        code: 'CONFLICT',
        field
      }
    })
  }
}

export class GraphQLInternalError extends GraphQLError {
  constructor(message: string = 'Internal server error') {
    super(message, {
      extensions: {
        code: 'INTERNAL_ERROR'
      }
    })
  }
}

/**
 * Maps service errors to GraphQL errors
 */
export function mapServiceErrorToGraphQL(error: any): GraphQLError {
  if (error instanceof ValidationError) {
    return new GraphQLValidationError(error.message, error.field)
  }
  
  if (error instanceof NotFoundError) {
    return new GraphQLNotFoundError(error.resource || 'Resource')
  }
  
  if (error instanceof UnauthorizedError) {
    return new GraphQLUnauthorizedError(error.message)
  }
  
  if (error instanceof ForbiddenError) {
    return new GraphQLForbiddenError(error.message)
  }
  
  if (error instanceof ConflictError) {
    return new GraphQLConflictError(error.message, error.field)
  }
  
  // For unknown errors, return internal error in production
  if (process.env.NODE_ENV === 'production') {
    console.error('Unexpected error in GraphQL resolver:', error)
    return new GraphQLInternalError()
  }
  
  // In development, return the original error for debugging
  return new GraphQLError(error.message || 'Unknown error occurred')
}

/**
 * Formats GraphQL errors for consistent response structure
 */
export function formatError(formattedError: GraphQLFormattedError, error: any): GraphQLFormattedError {
  // In production, don't expose internal error details
  if (process.env.NODE_ENV === 'production') {
    // Only expose safe error codes
    const safeCodes = [
      'VALIDATION_ERROR',
      'NOT_FOUND',
      'UNAUTHENTICATED',
      'FORBIDDEN',
      'CONFLICT'
    ]
    
    const code = formattedError.extensions?.code
    if (!safeCodes.includes(code as string)) {
      return {
        message: 'Internal server error',
        extensions: {
          code: 'INTERNAL_ERROR'
        }
      }
    }
  }
  
  return formattedError
}

/**
 * Error codes for client-side error handling
 */
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHENTICATED: 'UNAUTHENTICATED',
  FORBIDDEN: 'FORBIDDEN',
  CONFLICT: 'CONFLICT',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
} as const

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]
