import { useState, useEffect, useCallback } from 'react'
import { CategoryApiService } from '@/services/client'
import type { Category } from '@/lib/generated/prisma'
import type { CategoryQueryParams, CreateCategoryRequest, UpdateCategoryRequest } from '@/types/api'

interface UseCategoriesReturn {
  categories: Category[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createCategory: (data: CreateCategoryRequest) => Promise<boolean>
  updateCategory: (id: string, data: UpdateCategoryRequest) => Promise<boolean>
  deleteCategory: (id: string) => Promise<boolean>
}

export function useCategories(params?: CategoryQueryParams): UseCategoriesReturn {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await CategoryApiService.getCategories(params)
      
      if (response.success) {
        setCategories(response.data)
      } else {
        setError('Failed to fetch categories')
      }
    } catch (err) {
      setError('Error fetching categories')
      console.error('Error fetching categories:', err)
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  const createCategory = useCallback(async (data: CreateCategoryRequest): Promise<boolean> => {
    try {
      const response = await CategoryApiService.createCategory(data)
      if (response.success) {
        await fetchCategories() // Refresh list
        return true
      } else {
        setError('Failed to create category')
        return false
      }
    } catch (err) {
      setError('Error creating category')
      console.error('Error creating category:', err)
      return false
    }
  }, [fetchCategories])

  const updateCategory = useCallback(async (id: string, data: UpdateCategoryRequest): Promise<boolean> => {
    try {
      const response = await CategoryApiService.updateCategory(id, data)
      if (response.success) {
        // Update local state optimistically
        setCategories(prev => prev.map(cat => 
          cat.id === id ? { ...cat, ...data, updatedAt: new Date().toISOString() } : cat
        ))
        return true
      } else {
        setError('Failed to update category')
        return false
      }
    } catch (err) {
      setError('Error updating category')
      console.error('Error updating category:', err)
      return false
    }
  }, [])

  const deleteCategory = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await CategoryApiService.deleteCategory(id)
      if (response.success) {
        // Remove from local state
        setCategories(prev => prev.filter(cat => cat.id !== id))
        return true
      } else {
        setError('Failed to delete category')
        return false
      }
    } catch (err) {
      setError('Error deleting category')
      console.error('Error deleting category:', err)
      return false
    }
  }, [])

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory
  }
}

export default useCategories
