import { useState, useEffect, useCallback } from 'react'
import { ContactApiService } from '@/services/client'
import type { Contact } from '@/lib/generated/prisma'
import type { ContactQueryParams, CreateContactRequest, UpdateContactRequest } from '@/types/api'

interface UseContactsReturn {
  contacts: Contact[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createContact: (data: CreateContactRequest) => Promise<boolean>
  updateContact: (id: number, data: UpdateContactRequest) => Promise<boolean>
  deleteContact: (id: number) => Promise<boolean>
}

export function useContacts(params?: ContactQueryParams): UseContactsReturn {
  const [contacts, setContacts] = useState<Contact[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchContacts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await ContactApiService.getContacts(params)
      
      if (response.success) {
        setContacts(response.data)
      } else {
        setError('Failed to fetch contacts')
      }
    } catch (err) {
      setError('Error fetching contacts')
      console.error('Error fetching contacts:', err)
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    fetchContacts()
  }, [fetchContacts])

  const createContact = useCallback(async (data: CreateContactRequest): Promise<boolean> => {
    try {
      const response = await ContactApiService.createContact(data)
      if (response.success) {
        await fetchContacts() // Refresh list
        return true
      } else {
        setError('Failed to create contact')
        return false
      }
    } catch (err) {
      setError('Error creating contact')
      console.error('Error creating contact:', err)
      return false
    }
  }, [fetchContacts])

  const updateContact = useCallback(async (id: number, data: UpdateContactRequest): Promise<boolean> => {
    try {
      const response = await ContactApiService.updateContact(String(id), data)
      if (response.success) {
        // Update local state optimistically
        setContacts(prev => prev.map(contact => 
          contact.id === id ? { ...contact, ...data, updatedAt: new Date() } : contact
        ))
        return true
      } else {
        setError('Failed to update contact')
        return false
      }
    } catch (err) {
      setError('Error updating contact')
      console.error('Error updating contact:', err)
      return false
    }
  }, [])

  const deleteContact = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await ContactApiService.deleteContact(String(id))
      if (response.success) {
        // Remove from local state
        setContacts(prev => prev.filter(contact => contact.id !== id))
        return true
      } else {
        setError('Failed to delete contact')
        return false
      }
    } catch (err) {
      setError('Error deleting contact')
      console.error('Error deleting contact:', err)
      return false
    }
  }, [])

  return {
    contacts,
    loading,
    error,
    refetch: fetchContacts,
    createContact,
    updateContact,
    deleteContact
  }
}

export default useContacts
