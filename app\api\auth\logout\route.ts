import { NextRequest } from 'next/server'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse
} from '@/lib/api/utils'
import { AuthService } from '@/services/server'

// ==================== POST /api/auth/logout ====================

export const POST = withErrorHandler(async (request: NextRequest) => {
  // For development, just return success
  // In production, this would need proper token handling

  return successResponse({
    message: 'Logged out successfully'
  })
})
