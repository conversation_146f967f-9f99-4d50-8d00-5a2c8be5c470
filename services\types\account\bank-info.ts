import { BaseTableActiveable } from '../base';
import { UUID } from '../common';

export type {
  BankInfo,
  BankInfoBase,
  BankInfoWithRelations,
  CreateBankInfoInput,
  UpdateBankInfoInput,
  BankInfoFilterInput
};

/**
 * Base interface for BankInfo
 */
interface BankInfoBase extends BaseTableActiveable {
  userId: UUID;
  accountId: UUID;
  bankName: string | null;
  bankCode: string | null;
  accountNumber: string | null;
  accountHolderName: string | null;
  branchName: string | null;
  swiftCode: string | null;
  routingNumber: string | null;
  note: string | null;
}

/**
 * BankInfo with all possible relations
 */
interface BankInfoWithRelations extends BankInfoBase {
  user: UserBase;
  account: AccountBase;
}

/**
 * Union type for BankInfo
 */
type BankInfo = BankInfoBase | BankInfoWithRelations;

/**
 * Input for creating a new bank info
 */
interface CreateBankInfoInput {
  userId: UUID;
  accountId: UUID;
  bankName?: string | null;
  bankCode?: string | null;
  accountNumber?: string | null;
  accountHolderName?: string | null;
  branchName?: string | null;
  swiftCode?: string | null;
  routingNumber?: string | null;
  note?: string | null;
  isActive?: boolean;
}

/**
 * Input for updating an existing bank info
 */
interface UpdateBankInfoInput {
  id: UUID;
  bankName?: string | null;
  bankCode?: string | null;
  accountNumber?: string | null;
  accountHolderName?: string | null;
  branchName?: string | null;
  swiftCode?: string | null;
  routingNumber?: string | null;
  note?: string | null;
  isActive?: boolean;
}

/**
 * Input for filtering bank info
 */
interface BankInfoFilterInput {
  search?: string;
  userId?: UUID;
  accountId?: UUID;
  bankName?: string;
  bankCode?: string;
  accountNumber?: string;
  accountHolderName?: string;
  branchName?: string;
  swiftCode?: string;
  routingNumber?: string;
  isActive?: boolean;
}

// Import related types
import { UserBase } from '../user';
import { AccountBase } from './account';
