/**
 * PageHeader Component
 * 
 * Consistent header component for all pages with title, description, and actions
 */

 import React from 'react'
import { Button } from '@/components/ui/button'
import { Plus, ArrowLeft } from 'lucide-react'

// ==================== TYPES ====================

export interface PageHeaderAction {
  label: string
  onClick: () => void
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  icon?: React.ReactNode
  disabled?: boolean
}

export interface PageHeaderProps {
  title: string
  description?: string
  showBackButton?: boolean
  onBack?: () => void
  actions?: PageHeaderAction[]
  children?: React.ReactNode
  className?: string
}

// ==================== COMPONENT ====================

export function PageHeader({
  title,
  description,
  showBackButton = false,
  onBack,
  actions = [],
  children,
  className = ''
}: PageHeaderProps) {
  return (
    <div className={`flex flex-col gap-4 pb-6 border-b ${className}`}>
      {/* Header Row */}
      <div className="flex items-center justify-between">
        {/* Left Side - Title and Back Button */}
        <div className="flex items-center gap-4">
          {showBackButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="p-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {title}
            </h1>
            {description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {description}
              </p>
            )}
          </div>
        </div>

        {/* Right Side - Actions */}
        {actions.length > 0 && (
          <div className="flex items-center gap-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'default'}
                onClick={action.onClick}
                disabled={action.disabled}
                className="flex items-center gap-2"
              >
                {action.icon}
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Custom Content */}
      {children && (
        <div className="flex items-center justify-between">
          {children}
        </div>
      )}
    </div>
  )
}

// ==================== SPECIALIZED VARIANTS ====================

/**
 * PageHeader with Add Button
 */
export function PageHeaderWithAdd({
  title,
  description,
  onAdd,
  addLabel = 'Thêm mới',
  addDisabled = false,
  ...props
}: Omit<PageHeaderProps, 'actions'> & {
  onAdd: () => void
  addLabel?: string
  addDisabled?: boolean
}) {
  const actions: PageHeaderAction[] = [
    {
      label: addLabel,
      onClick: onAdd,
      icon: <Plus className="h-4 w-4" />,
      disabled: addDisabled
    }
  ]

  return (
    <PageHeader
      title={title}
      description={description}
      actions={actions}
      {...props}
    />
  )
}

/**
 * PageHeader with Multiple Actions
 */
export function PageHeaderWithActions({
  title,
  description,
  primaryAction,
  secondaryActions = [],
  ...props
}: Omit<PageHeaderProps, 'actions'> & {
  primaryAction?: PageHeaderAction
  secondaryActions?: PageHeaderAction[]
}) {
  const actions: PageHeaderAction[] = [
    ...secondaryActions,
    ...(primaryAction ? [primaryAction] : [])
  ]

  return (
    <PageHeader
      title={title}
      description={description}
      actions={actions}
      {...props}
    />
  )
}

/**
 * Simple PageHeader for detail pages
 */
export function DetailPageHeader({
  title,
  description,
  onBack,
  onEdit,
  onDelete,
  editDisabled = false,
  deleteDisabled = false,
  ...props
}: Omit<PageHeaderProps, 'actions' | 'showBackButton'> & {
  onBack?: () => void
  onEdit?: () => void
  onDelete?: () => void
  editDisabled?: boolean
  deleteDisabled?: boolean
}) {
  const actions: PageHeaderAction[] = []

  if (onEdit) {
    actions.push({
      label: 'Chỉnh sửa',
      onClick: onEdit,
      variant: 'outline',
      disabled: editDisabled
    })
  }

  if (onDelete) {
    actions.push({
      label: 'Xóa',
      onClick: onDelete,
      variant: 'destructive',
      disabled: deleteDisabled
    })
  }

  return (
    <PageHeader
      title={title}
      description={description}
      showBackButton={!!onBack}
      onBack={onBack}
      actions={actions}
      {...props}
    />
  )
}

export default PageHeader
