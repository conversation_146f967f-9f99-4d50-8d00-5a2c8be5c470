
export type { CategoryGoal, CategoryGoalBase, CategoryGoalWithRelations, CreateCategoryGoalInput, UpdateCategoryGoalInput, CategoryGoalFilterInput, UpdateCategoryGoalProgressInput, CategoryGoalResponse }
import { CategoryBase } from './category';
import { DateTime, Period, UUID } from '../common';
import { UserBase } from '../user';
import { BaseTableActiveable } from '../base';

/**
 * Base interface for CategoryGoal
 */
interface CategoryGoalBase extends BaseTableActiveable {
  name?: string;
  description?: string;
  targetAmount?: number;
  period?: Period;
  startDate?: DateTime;
  endDate?: DateTime;
  isRecurring?: boolean;
  color?: string;
  isDefault?: boolean;
  categoryId?: UUID;
  userId?: UUID; // relations
}

/**
 * CategoryGoal with all possible relations
 */
interface CategoryGoalWithRelations extends CategoryGoalBase {
  user: UserBase;
  category: CategoryBase;
}

/**
 * Union type for CategoryGoal
 */
type CategoryGoal = CategoryGoalBase | CategoryGoalWithRelations;

/**
 * Input type for creating a new category goal
 */
interface CreateCategoryGoalInput extends Omit<CategoryGoalBase, 'id' | 'userId' | 'user' | 'createdAt' | 'updatedAt' | 'deletedAt'> {

}

/**
 * Input type for updating a category goal
 */
interface UpdateCategoryGoalInput extends Omit<CategoryGoalBase, 'userId' | 'user' | 'createdAt' | 'updatedAt' | 'deletedAt'> {

}

/**
 * Input type for category goal filters
 */
interface CategoryGoalFilterInput {
  search?: string;
  categoryId?: UUID;
  period?: Period;
  isActive?: boolean;
  isDefault?: boolean;
  startDateBefore?: DateTime;
  startDateAfter?: DateTime;
  endDateBefore?: DateTime;
  endDateAfter?: DateTime;
}

/**
 * Input type for updating category goal progress
 */
interface UpdateCategoryGoalProgressInput {
  id: UUID;
  amount: number;
  isIncrement?: boolean;
}

/**
 * Response type for category goal operations
 */
interface CategoryGoalResponse {
  success: boolean;
  message?: string;
  categoryGoal?: CategoryGoal;
  errors?: Array<{ message: string; field?: string }>;
}


