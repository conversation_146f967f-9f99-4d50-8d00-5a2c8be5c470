"use client"

import React, { useState, useEffect } from "react"
import { Building2, <PERSON><PERSON>, Eye, CreditCard, Smartphone } from "lucide-react"
import { BaseSidebar } from "./BaseSidebar"
import { SidebarForm, SidebarInputField, SidebarTextareaField } from "./SidebarForm"
import { SidebarActionFooter } from "./SidebarFooter"
import { AccountType } from "@/lib/generated/prisma"

interface AccountTypeSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: Partial<AccountType>) => void
  accountType?: AccountType | null
  isEditing: boolean
}

interface FormData {
  name: string
  description: string
  color: string
  isBankAccount: boolean
  isPaymentApp: boolean
  isActive: boolean
}

interface FormErrors {
  name?: string
  description?: string
  color?: string
}

const PRESET_COLORS = [
  "#10b981", // green
  "#3b82f6", // blue
  "#ef4444", // red
  "#8b5cf6", // purple
  "#f59e0b", // amber
  "#06b6d4", // cyan
  "#84cc16", // lime
  "#f97316", // orange
  "#ec4899", // pink
  "#6b7280", // gray
]

export function AccountTypeSidebar({
  isOpen,
  onClose,
  onSave,
  accountType,
  isEditing
}: AccountTypeSidebarProps) {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    color: PRESET_COLORS[0],
    isBankAccount: false,
    isPaymentApp: false,
    isActive: true
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)

  // Reset form when sidebar opens/closes or accountType changes
  useEffect(() => {
    if (isOpen) {
      if (isEditing && accountType) {
        setFormData({
          name: accountType.name,
          description: accountType.description || "",
          color: accountType.color,
          isBankAccount: accountType.isBankAccount,
          isPaymentApp: accountType.isPaymentApp,
          isActive: accountType.isActive
        })
      } else {
        setFormData({
          name: "",
          description: "",
          color: PRESET_COLORS[0],
          isBankAccount: false,
          isPaymentApp: false,
          isActive: true
        })
      }
      setErrors({})
    }
  }, [isOpen, isEditing, accountType])

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = "Tên loại tài khoản là bắt buộc"
    }

    if (formData.name.trim().length < 2) {
      newErrors.name = "Tên loại tài khoản phải có ít nhất 2 ký tự"
    }

    if (!formData.color) {
      newErrors.color = "Vui lòng chọn màu"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      const data: Partial<AccountType> = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        color: formData.color,
        isBankAccount: formData.isBankAccount,
        isPaymentApp: formData.isPaymentApp,
        isActive: formData.isActive,
        isDefault: false // Custom account types are never default
      }

      await onSave(data)
    } catch (error) {
      console.error('Error saving account type:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      onClose()
    }
  }

  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa loại tài khoản" : "Thêm loại tài khoản mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${accountType?.name}` : "Tạo loại tài khoản mới"}
      storageKey="accountTypeSidebarWidth"
      config={{
        DEFAULT_WIDTH: 400,
        MIN_WIDTH: 350,
        MAX_WIDTH_PERCENTAGE: 0.6
      }}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={() => {
            const fakeEvent = { preventDefault: () => {} } as React.FormEvent
            handleSubmit(fakeEvent)
          }}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={!formData.name.trim() || !formData.color}
          sidebarWidth={400}
        />
      }
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={400}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* Account Type Name */}
        <SidebarInputField
          label="Tên loại tài khoản"
          value={formData.name}
          onChange={(value) => handleInputChange('name', value)}
          placeholder="VD: Tài khoản tiết kiệm"
          required
          error={errors.name}
          icon={<Building2 size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />

        {/* Description */}
        <SidebarTextareaField
          label="Mô tả"
          value={formData.description}
          onChange={(value) => handleInputChange('description', value)}
          placeholder="Mô tả chi tiết về loại tài khoản này..."
          error={errors.description}
          disabled={loading}
          span="full"
          sidebarWidth={400}
          rows={3}
        />

        {/* Color Picker */}
        <div className="col-span-full space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
            <Palette size={14} className="mr-2" />
            Màu sắc *
          </label>
          <div className="grid grid-cols-5 gap-2">
            {PRESET_COLORS.map((color) => (
              <button
                key={color}
                type="button"
                onClick={() => handleInputChange('color', color)}
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  formData.color === color 
                    ? 'border-gray-900 dark:border-gray-100 scale-110' 
                    : 'border-gray-300 dark:border-gray-600 hover:scale-105'
                }`}
                style={{ backgroundColor: color }}
                disabled={loading}
              />
            ))}
          </div>
          {errors.color && (
            <p className="text-xs text-red-500">{errors.color}</p>
          )}
        </div>

        {/* Account Type Properties */}
        <div className="col-span-full space-y-3">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Thuộc tính
          </label>
          
          <div className="space-y-2">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.isBankAccount}
                onChange={(e) => handleInputChange('isBankAccount', e.target.checked)}
                disabled={loading}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <CreditCard size={14} className="text-gray-500" />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Là tài khoản ngân hàng
              </span>
            </label>

            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.isPaymentApp}
                onChange={(e) => handleInputChange('isPaymentApp', e.target.checked)}
                disabled={loading}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <Smartphone size={14} className="text-gray-500" />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Là ví điện tử
              </span>
            </label>

            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.isActive}
                onChange={(e) => handleInputChange('isActive', e.target.checked)}
                disabled={loading}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <Eye size={14} className="text-gray-500" />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Hiển thị trong danh sách
              </span>
            </label>
          </div>
        </div>
      </SidebarForm>
    </BaseSidebar>
  )
}
