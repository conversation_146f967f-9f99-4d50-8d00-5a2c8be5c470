"use client"

import { useState, useEffect, useMemo } from "react"

interface SidebarLayoutConfig {
  isOpen: boolean
  sidebarWidth: number
  isResizing: boolean
}

interface SidebarLayoutReturn {
  layoutClass: string
  getFieldSpan: (fieldType: 'single' | 'double' | 'full') => string
  getFieldClass: (fieldType?: 'normal' | 'full-width') => string
  shouldShowSections: boolean
  columnCount: number
}

export function useSidebarLayout({
  isOpen,
  sidebarWidth,
  isResizing
}: SidebarLayoutConfig): SidebarLayoutReturn {
  const [mounted, setMounted] = useState(false)

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Determine layout based on sidebar width
  const layoutInfo = useMemo(() => {
    if (!mounted) {
      return {
        layoutClass: 'sidebar-medium',
        columnCount: 1,
        shouldShowSections: false
      }
    }

    if (sidebarWidth < 400) {
      return {
        layoutClass: 'sidebar-narrow',
        columnCount: 1,
        shouldShowSections: false
      }
    } else if (sidebarWidth < 600) {
      return {
        layoutClass: 'sidebar-medium',
        columnCount: 1,
        shouldShowSections: true
      }
    } else if (sidebarWidth < 800) {
      return {
        layoutClass: 'sidebar-wide',
        columnCount: 2,
        shouldShowSections: true
      }
    } else {
      return {
        layoutClass: 'sidebar-extra-wide',
        columnCount: 3,
        shouldShowSections: true
      }
    }
  }, [sidebarWidth, mounted])

  // No animation classes needed - sidebar handles its own animation

  // Field spanning logic
  const getFieldSpan = (fieldType: 'single' | 'double' | 'full'): string => {
    const { columnCount } = layoutInfo

    switch (fieldType) {
      case 'full':
        return 'field-span-full'
      case 'double':
        if (columnCount >= 2) {
          return 'field-span-2'
        }
        return 'field-span-1'
      case 'single':
      default:
        return 'field-span-1'
    }
  }

  // Field class logic
  const getFieldClass = (fieldType: 'normal' | 'full-width' = 'normal'): string => {
    const baseClass = 'sidebar-field'
    
    if (fieldType === 'full-width') {
      return `${baseClass} field-full-width`
    }
    
    return baseClass
  }

  return {
    layoutClass: layoutInfo.layoutClass,
    getFieldSpan,
    getFieldClass,
    shouldShowSections: layoutInfo.shouldShowSections,
    columnCount: layoutInfo.columnCount
  }
}

// Utility function to get responsive field layout
export function getResponsiveFieldLayout(
  sidebarWidth: number,
  formType: 'account' | 'transaction' | 'contact' | 'category' | 'budget'
) {
  const columnCount = sidebarWidth < 400 ? 1 : sidebarWidth < 600 ? 1 : sidebarWidth < 800 ? 2 : 3

  const layouts = {
    account: {
      1: [
        { section: 'Thông tin cơ bản', fields: [
          { name: 'name', span: 'full' },
          { name: 'type', span: 'full' }
        ]},
        { section: 'Tài chính', fields: [
          { name: 'balance', span: 'full' },
          { name: 'currency', span: 'full' }
        ]},
        { section: 'Mô tả', fields: [
          { name: 'description', span: 'full' }
        ]}
      ],
      2: [
        { section: 'Thông tin cơ bản', fields: [
          { name: 'name', span: 'single' },
          { name: 'type', span: 'single' }
        ]},
        { section: 'Tài chính', fields: [
          { name: 'balance', span: 'single' },
          { name: 'currency', span: 'single' }
        ]},
        { section: 'Mô tả', fields: [
          { name: 'description', span: 'full' }
        ]}
      ],
      3: [
        { section: 'Thông tin cơ bản', fields: [
          { name: 'name', span: 'single' },
          { name: 'type', span: 'single' },
          { name: 'balance', span: 'single' }
        ]},
        { section: 'Chi tiết', fields: [
          { name: 'currency', span: 'single' },
          { name: 'description', span: 'double' }
        ]}
      ]
    },
    transaction: {
      1: [
        { section: 'Cơ bản', fields: [
          { name: 'account', span: 'full' },
          { name: 'category', span: 'full' },
          { name: 'amount', span: 'full' },
          { name: 'type', span: 'full' }
        ]},
        { section: 'Chi tiết', fields: [
          { name: 'description', span: 'full' },
          { name: 'date', span: 'full' },
          { name: 'time', span: 'full' }
        ]}
      ],
      2: [
        { section: 'Cơ bản', fields: [
          { name: 'account', span: 'single' },
          { name: 'category', span: 'single' },
          { name: 'amount', span: 'single' },
          { name: 'type', span: 'single' }
        ]},
        { section: 'Chi tiết', fields: [
          { name: 'description', span: 'full' },
          { name: 'date', span: 'single' },
          { name: 'time', span: 'single' }
        ]}
      ],
      3: [
        { section: 'Cơ bản', fields: [
          { name: 'account', span: 'single' },
          { name: 'category', span: 'single' },
          { name: 'amount', span: 'single' }
        ]},
        { section: 'Chi tiết', fields: [
          { name: 'type', span: 'single' },
          { name: 'date', span: 'single' },
          { name: 'time', span: 'single' }
        ]},
        { section: 'Mô tả', fields: [
          { name: 'description', span: 'full' }
        ]}
      ]
    }
  }

  return layouts[formType]?.[columnCount] || layouts[formType][1]
}

// Simplified content animation - no staggered effects
export function useContentAnimation(isOpen: boolean, itemCount: number) {
  const getItemClass = (index: number) => {
    return 'content-item' // Simple static class, no animations
  }

  return { getItemClass }
}
