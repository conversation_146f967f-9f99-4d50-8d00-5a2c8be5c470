"use client"

import React, { useState, useRef } from "react"
import { Upload, X, Image as ImageIcon, AlertCircle } from "lucide-react"

interface ImageUploadProps {
  value?: string
  onChange: (value: string) => void
  onClear: () => void
  maxSize?: number // in MB
  acceptedFormats?: string[]
  className?: string
}

export function ImageUpload({
  value,
  onChange,
  onClear,
  maxSize = 2,
  acceptedFormats = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'],
  className = ""
}: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [error, setError] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (file: File) => {
    setError("")
    setIsLoading(true)

    // Validate file type
    if (!acceptedFormats.includes(file.type)) {
      setError(`Định dạng không được hỗ trợ. Chỉ chấp nhận: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`)
      setIsLoading(false)
      return
    }

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`Kích thước file quá lớn. Tối đa ${maxSize}MB`)
      setIsLoading(false)
      return
    }

    try {
      // Convert to base64 for preview (in real app, upload to server)
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result
        onChange(result)
        setIsLoading(false)
      }
      reader.onerror = () => {
        setError("Lỗi khi đọc file")
        setIsLoading(false)
      }
      reader.readAsDataURL(file)
    } catch (err) {
      setError("Lỗi khi xử lý file")
      setIsLoading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleClear = () => {
    setError("")
    onClear()
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Upload Area */}
      {!value && (
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => fileInputRef.current?.click()}
          className={`
            relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
            ${isDragging 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-800'
            }
            ${isLoading ? 'pointer-events-none opacity-50' : ''}
          `}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={acceptedFormats.join(',')}
            onChange={handleFileInputChange}
            className="hidden"
          />
          
          <div className="space-y-2">
            <div className="mx-auto w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              {isLoading ? (
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              ) : (
                <Upload className="w-6 h-6 text-gray-400" />
              )}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {isLoading ? 'Đang tải lên...' : 'Tải lên hình ảnh'}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Kéo thả hoặc click để chọn file
              </p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                PNG, JPG, SVG tối đa {maxSize}MB
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Preview */}
      {value && (
        <div className="relative">
          <div className="w-full h-32 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden flex items-center justify-center">
            <img
              src={value}
              alt="Preview"
              className="max-w-full max-h-full object-contain"
              onError={() => setError("Không thể hiển thị hình ảnh")}
            />
          </div>
          <button
            onClick={handleClear}
            className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
            title="Xóa hình ảnh"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
        </div>
      )}
    </div>
  )
}
