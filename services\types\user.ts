import { BaseTable, BaseTableActiveable } from './base';
import { DateTime, Language, Theme, UUID } from './common';

// Import related types
import type { AccountBase } from './account/account';
import type { AuditLogBase } from './audit-log';
import type { BankInfoBase } from './account/bank-info';
import type { CategoryBase } from './category/category';
import type { CategoryGoalBase } from './category/category-goal';
import type { ContactBase } from './contact/contact';
import type { CategoryTransactionExecutionInfoBase } from './category/category-transaction-execution-info';
import type { CategoryPeriodRecordBase } from './category/category-period-record';
import type { TransactionBase } from './transaction/transaction';
import type { DebitCardBase } from './account/debit-card';
import type { PaymentAppBase } from './account/payment-app';

/**
 * User role enum
 */
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user'
}

/**
 * Base interface for UserInformation
 */
export interface UserInformationBase extends BaseTable {
  userId: UUID;
  lastLogin: DateTime | null;
  loginCount: number;
  ipAddress: string | null;
  userAgent: string | null;
}

/**
 * UserInformation with all relations
 */
interface UserInformationWithRelations extends UserInformationBase {
  user: UserBase;
}

/**
 * Union type for UserInformation
 */
export type UserInformation = UserInformationBase | UserInformationWithRelations;

/**
 * Base interface for User
 */
export interface UserBase extends BaseTableActiveable {
  email: string;
  password: string; // hashed
  role: UserRole;
  isActive: boolean;
  isVerified: boolean;
  lastLoginAt: DateTime | null;
}

/**
 * User with all relations
 */
interface UserWithRelations extends UserBase {
  // User information
  userInformation: UserInformation | null;
  preferences: UserPreferences | null;
  
  // Related entities
  accounts: AccountBase[];
  auditLogs: AuditLogBase[];
  bankInfos: BankInfoBase[];
  categories: CategoryBase[];
  categoryGoals: CategoryGoalBase[];
  contacts: ContactBase[];
  categoryTransactionExecutionInfos: CategoryTransactionExecutionInfoBase[];
  categoryPeriodRecords: CategoryPeriodRecordBase[];
  transactions: TransactionBase[];
  debitCards: DebitCardBase[];
  paymentApps: PaymentAppBase[];
}

/**
 * Union type for User
 */
export type User = UserBase | UserWithRelations;

/**
 * Base interface for UserPreferences
 */
export interface UserPreferencesBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  isActive: boolean;
  userId: UUID;
  currency: string;
  language: Language;
  theme: Theme;
  dateFormat: string | null;
  numberFormat: string | null;
  timezone: string | null;
}

/**
 * UserPreferences with all relations
 */
interface UserPreferencesWithRelations extends UserPreferencesBase {
  user: UserBase;
}

/**
 * Union type for UserPreferences
 */
export type UserPreferences = UserPreferencesBase | UserPreferencesWithRelations;

// Input types for User mutations

/**
 * Input for creating a new user
 */
export interface CreateUserInput {
  email: string;
  password: string;
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
  preferences?: CreateUserPreferencesInput;
}

/**
 * Input for updating an existing user
 */
export interface UpdateUserInput {
  id: UUID;
  email?: string;
  password?: string; // Hashed on the server
  role?: UserRole;
  isActive?: boolean;
  isVerified?: boolean;
  lastLoginAt?: DateTime | null;
}

/**
 * Input for creating user preferences
 */
export interface CreateUserPreferencesInput {
  name: string;
  description?: string | null;
  currency?: string;
  language?: Language;
  theme?: Theme;
  dateFormat?: string | null;
  numberFormat?: string | null;
  timezone?: string | null;
  isActive?: boolean;
}

/**
 * Input for updating user preferences
 */
export interface UpdateUserPreferencesInput {
  id: UUID;
  name?: string;
  description?: string | null;
  currency?: string;
  language?: Language;
  theme?: Theme;
  dateFormat?: string | null;
  numberFormat?: string | null;
  timezone?: string | null;
  isActive?: boolean;
}

/**
 * Input for user login
 */
export interface LoginInput {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Input for changing password
 */
export interface ChangePasswordInput {
  currentPassword: string;
  newPassword: string;
}

/**
 * Input for resetting password
 */
export interface ResetPasswordInput {
  token: string;
  newPassword: string;
}

/**
 * Input for requesting password reset
 */
export interface ForgotPasswordInput {
  email: string;
}

// Response types
/**
 * Authentication response type
 */
export interface AuthResponse {
  token: string;
  user: User;
  expiresIn: number;
}
