"use client"

import React from "react"

interface FormData {
  amount: string
  date: string
  time: string
}

interface FormErrors {
  amount?: string
  date?: string
  time?: string
}

interface AmountDateTimeFieldsProps {
  formData: FormData
  errors: FormErrors
  loading: boolean
  onInputChange: (field: keyof FormData, value: string | boolean) => void
}

export function AmountDateTimeFields({
  formData,
  errors,
  loading,
  onInputChange
}: AmountDateTimeFieldsProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
        Amount & Timing
      </h3>
      
      {/* Amount */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Amount (VND) *
        </label>
        <div className="relative">
          <input
            type="text"
            value={formData.amount}
            onChange={(e) => {
              // Allow only numbers and commas for formatting
              const value = e.target.value.replace(/[^\d]/g, '')
              onInputChange('amount', value)
            }}
            placeholder="0"
            className={`w-full px-3 py-2 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
              errors.amount
                ? 'border-red-500 dark:border-red-400'
                : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm">
            VND
          </div>
        </div>
        {formData.amount && (
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {parseInt(formData.amount || '0').toLocaleString('vi-VN')} VND
          </p>
        )}
        {errors.amount && (
          <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.amount}</p>
        )}

        {/* Quick Amount Buttons */}
        <div className="mt-2 flex flex-wrap gap-2">
          {[50000, 100000, 200000, 500000, 1000000, 2000000].map((amount) => (
            <button
              key={amount}
              type="button"
              onClick={() => onInputChange('amount', amount.toString())}
              className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              disabled={loading}
            >
              {(amount / 1000).toLocaleString()}K
            </button>
          ))}
        </div>
      </div>

      {/* Date & Time Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date *
          </label>
          <input
            type="date"
            value={formData.date}
            onChange={(e) => onInputChange('date', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
              errors.date
                ? 'border-red-500 dark:border-red-400'
                : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
          />
          {errors.date && (
            <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.date}</p>
          )}
        </div>

        {/* Time */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Time *
          </label>
          <input
            type="time"
            value={formData.time}
            onChange={(e) => onInputChange('time', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
              errors.time
                ? 'border-red-500 dark:border-red-400'
                : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
          />
          {errors.time && (
            <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.time}</p>
          )}
        </div>
      </div>
    </div>
  )
}
