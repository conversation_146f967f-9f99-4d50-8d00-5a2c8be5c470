/**
 * Comprehensive GraphQL Connection Hooks
 * 
 * This file provides a unified interface for all GraphQL connection hooks,
 * combining both Apollo Client hooks and ApiService-based hooks
 */

// Re-export all connection utilities
export * from '@/lib/utils/connection-helpers'

// Re-export core connection hooks
export * from './useConnection'
export * from './useEntityConnections'

// Re-export updated GraphQL hooks with connection support
export * from '@/lib/graphql/hooks/useUser'
export * from '@/lib/graphql/hooks/useAccount'
export * from '@/lib/graphql/hooks/useCategoryGoal'
export * from '@/lib/graphql/hooks/useRecurringTransaction'

// Re-export GraphQL types
export type {
  Connection,
  Edge,
  PageInfo,
  ConnectionResponse,
  UserConnection,
  AccountConnection,
  TransactionConnection,
  CategoryConnection,
  ContactConnection,
  CategoryGoalConnection,
  RecurringTransactionConnection,
  AccountTypeConnection,
  ContactTypeConnection,
  UserConnectionParams,
  AccountConnectionParams,
  TransactionConnectionParams,
  CategoryConnectionParams,
  ContactConnectionParams,
  CategoryGoalConnectionParams,
  RecurringTransactionConnectionParams,
  AccountTypeConnectionParams,
  ContactTypeConnectionParams
} from '@/types/graphql-connections'

import { useMemo } from 'react'
import type { Connection } from '@/types/graphql-connections'

// ==================== UNIFIED CONNECTION HOOK ====================

/**
 * Universal hook that can work with any GraphQL connection
 * Provides consistent interface regardless of data source
 */
export function useUniversalConnection<T>(
  connectionData: Connection<T> | null | undefined,
  loading: boolean = false,
  error: any = null
) {
  return useMemo(() => {
    const items = connectionData?.edges?.map(edge => edge.node) || []
    const totalCount = connectionData?.totalCount || 0
    const pageInfo = connectionData?.pageInfo || {
      hasNextPage: false,
      hasPreviousPage: false,
      startCursor: null,
      endCursor: null
    }

    return {
      // Data
      items,
      connection: connectionData,
      totalCount,
      
      // Pagination
      hasNextPage: pageInfo.hasNextPage,
      hasPreviousPage: pageInfo.hasPreviousPage,
      startCursor: pageInfo.startCursor,
      endCursor: pageInfo.endCursor,
      
      // State
      loading,
      error,
      
      // Computed
      isEmpty: items.length === 0,
      isNotEmpty: items.length > 0,
      currentCount: items.length
    }
  }, [connectionData, loading, error])
}

// ==================== CONNECTION HOOK FACTORY ====================

/**
 * Factory function to create connection hooks with consistent interface
 */
export function createConnectionHook<T, TVariables = any>(
  useQueryHook: (variables?: TVariables) => any,
  dataPath: string
) {
  return function useConnectionHook(variables?: TVariables) {
    const queryResult = useQueryHook(variables)
    
    // Extract connection data from nested path
    const connectionData = dataPath.split('.').reduce((obj, key) => obj?.[key], queryResult.data)
    
    return useUniversalConnection<T>(
      connectionData,
      queryResult.loading,
      queryResult.error
    )
  }
}

// ==================== HOOK COMPOSITION UTILITIES ====================

/**
 * Combine multiple connection hooks into a single result
 */
export function useCombinedConnections<T>(
  hooks: Array<() => ReturnType<typeof useUniversalConnection<T>>>
) {
  const results = hooks.map(hook => hook())
  
  return useMemo(() => {
    const allItems = results.flatMap(result => result.items)
    const totalCount = results.reduce((sum, result) => sum + result.totalCount, 0)
    const loading = results.some(result => result.loading)
    const error = results.find(result => result.error)?.error
    
    return {
      items: allItems,
      totalCount,
      loading,
      error,
      isEmpty: allItems.length === 0,
      isNotEmpty: allItems.length > 0,
      currentCount: allItems.length,
      results // Individual results for detailed access
    }
  }, [results])
}

/**
 * Hook to merge multiple connections with deduplication
 */
export function useMergedConnections<T extends { id: string }>(
  connections: Array<Connection<T> | null | undefined>,
  loading: boolean = false,
  error: any = null
) {
  return useMemo(() => {
    const allItems = connections
      .filter(Boolean)
      .flatMap(conn => conn!.edges.map(edge => edge.node))
    
    // Deduplicate by id
    const uniqueItems = allItems.reduce((acc, item) => {
      if (!acc.find(existing => existing.id === item.id)) {
        acc.push(item)
      }
      return acc
    }, [] as T[])
    
    const totalCount = connections.reduce((sum, conn) => sum + (conn?.totalCount || 0), 0)
    
    return {
      items: uniqueItems,
      totalCount,
      loading,
      error,
      isEmpty: uniqueItems.length === 0,
      isNotEmpty: uniqueItems.length > 0,
      currentCount: uniqueItems.length
    }
  }, [connections, loading, error])
}

// ==================== SEARCH AND FILTER UTILITIES ====================

/**
 * Hook to filter connection items locally
 */
export function useFilteredConnection<T>(
  connection: ReturnType<typeof useUniversalConnection<T>>,
  filterFn: (item: T) => boolean
) {
  return useMemo(() => {
    const filteredItems = connection.items.filter(filterFn)
    
    return {
      ...connection,
      items: filteredItems,
      currentCount: filteredItems.length,
      isEmpty: filteredItems.length === 0,
      isNotEmpty: filteredItems.length > 0
    }
  }, [connection, filterFn])
}

/**
 * Hook to sort connection items locally
 */
export function useSortedConnection<T>(
  connection: ReturnType<typeof useUniversalConnection<T>>,
  sortFn: (a: T, b: T) => number
) {
  return useMemo(() => {
    const sortedItems = [...connection.items].sort(sortFn)
    
    return {
      ...connection,
      items: sortedItems
    }
  }, [connection, sortFn])
}

// ==================== PAGINATION UTILITIES ====================

/**
 * Hook to handle cursor-based pagination
 */
export function useCursorPagination<T>(
  connection: ReturnType<typeof useUniversalConnection<T>>,
  fetchMore: (cursor: string) => Promise<void>
) {
  const loadNext = async () => {
    if (connection.hasNextPage && connection.endCursor) {
      await fetchMore(connection.endCursor)
    }
  }
  
  const loadPrevious = async () => {
    if (connection.hasPreviousPage && connection.startCursor) {
      await fetchMore(connection.startCursor)
    }
  }
  
  return {
    loadNext,
    loadPrevious,
    canLoadNext: connection.hasNextPage,
    canLoadPrevious: connection.hasPreviousPage
  }
}

// ==================== DEBUGGING UTILITIES ====================

/**
 * Hook to debug connection data
 */
export function useConnectionDebug<T>(
  connection: ReturnType<typeof useUniversalConnection<T>>,
  label: string = 'Connection'
) {
  return useMemo(() => {
    const debug = {
      label,
      itemCount: connection.items.length,
      totalCount: connection.totalCount,
      hasNextPage: connection.hasNextPage,
      hasPreviousPage: connection.hasPreviousPage,
      loading: connection.loading,
      error: connection.error,
      isEmpty: connection.isEmpty
    }
    
    console.log(`[${label}] Connection Debug:`, debug)
    return debug
  }, [connection, label])
}
