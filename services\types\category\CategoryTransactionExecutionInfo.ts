import { BaseTableActiveable } from '../base';
import { UUID } from '../common';

// When a Category execute a transaction, it will create a record of configuration of this transaction
/**
 * Base interface for CategoryTransaction
 */
interface CategoryTransactionExecutionInfo extends BaseTableActiveable {
  transactionId: UUID;
  categoryId: UUID;
  /**
   * this will crate a new categoryGoal from snapshot of categoryGoal original for cache 
   * and make sure this info will not change in future and set isDefault to false
   * 
   */
  categoryGoalId?: UUID;
}

export type { CategoryTransactionExecutionInfo };

