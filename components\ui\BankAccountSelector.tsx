"use client"

import React, { useState, useRef, useEffect } from "react"
import { ChevronDown, Building2, CreditCard, Check } from "lucide-react"
import { Contact } from "@/lib/generated/prisma"

interface AccountSelectorProps {
  contact: Contact
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  placeholder?: string
  className?: string
}

export function BankAccountSelector({
  contact,
  value,
  onChange,
  disabled = false,
  placeholder = "Chọn tài khoản",
  className = ""
}: AccountSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle click outside to close dropdown
  useEffect(() => {
    if (!mounted) return

    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [mounted])

  const selectedAccount = contact?.accounts?.find(acc => acc.id.toString() === value)
  const accounts = contact?.accounts || []

  const handleAccountSelect = (account: any) => {
    onChange(account.id.toString())
    setIsOpen(false)
  }

  if (!mounted) {
    return (
      <div className="w-full px-3 py-2.5 text-sm border border-gray-200 dark:border-gray-700 rounded-xl bg-gray-100 dark:bg-gray-800 animate-pulse">
        <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded"></div>
      </div>
    )
  }

  if (accounts.length === 0) {
    return (
      <div className="w-full px-3 py-2.5 text-sm border border-gray-200 dark:border-gray-700 rounded-xl bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400">
        Không có tài khoản ngân hàng
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <button
        ref={buttonRef}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className="w-full px-3 py-2.5 pr-10 text-sm border border-gray-200 dark:border-gray-700 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white dark:focus:bg-gray-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-300 dark:hover:border-gray-600 text-left"
      >
        <div className="flex items-center gap-2">
          {selectedAccount ? (
            <>
              <Building2 size={16} className="text-gray-400 dark:text-gray-500 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">{selectedAccount.bankName || selectedAccount.name}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {selectedAccount.accountNumber ? `${selectedAccount.accountNumber} • ${selectedAccount.type}` : selectedAccount.type}
                </div>
              </div>
            </>
          ) : (
            <>
              <CreditCard size={16} className="text-gray-400 dark:text-gray-500 flex-shrink-0" />
              <span className="text-gray-500 dark:text-gray-400">{placeholder}</span>
            </>
          )}
        </div>
        <ChevronDown 
          size={16} 
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {/* Dropdown */}
      {isOpen && !disabled && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-2 bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-2xl max-h-60 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent"
        >
          <div className="py-1">
            {accounts.map((account) => (
              <button
                key={account.id}
                type="button"
                onClick={() => handleAccountSelect(account)}
                className="w-full px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-3 transition-colors"
              >
                <Building2 size={16} className="text-gray-400 dark:text-gray-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
                    {account.bankName || account.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                    {account.accountNumber ? `${account.accountNumber} • ${account.type}` : account.type}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {value === account.id.toString() && (
                    <Check size={16} className="text-blue-500 dark:text-blue-400" />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}



// Helper function to get default account
export function getDefaultAccount(contact: Contact): any | undefined {
  return contact.accounts?.[0]
}
