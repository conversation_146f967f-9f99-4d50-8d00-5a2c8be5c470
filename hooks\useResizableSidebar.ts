import { useState, useEffect, useCallback, useRef } from 'react'

interface SidebarConfig {
  DEFAULT_WIDTH: number
  MIN_WIDTH: number
  MAX_WIDTH_PERCENTAGE: number
  TRANSITION_DURATION: number
  TRANSITION_EASING: string
  Z_INDEX: {
    OVERLAY: number
    SIDEBAR: number
    RESIZE_HANDLE: number
  }
}

const DEFAULT_SIDEBAR_CONFIG: SidebarConfig = {
  DEFAULT_WIDTH: 400,
  MIN_WIDTH: 300,
  MAX_WIDTH_PERCENTAGE: 0.6,
  TRANSITION_DURATION: 300,
  TRANSITION_EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  Z_INDEX: {
    OVERLAY: 55,
    SIDEBAR: 56,
    RESIZE_HANDLE: 57
  }
}

interface UseResizableSidebarOptions {
  storageKey: string
  config?: Partial<SidebarConfig>
  isOpen: boolean
}

interface UseResizableSidebarReturn {
  // State
  sidebarWidth: number
  isResizing: boolean
  isMobile: boolean
  isMinimized: boolean

  // Actions
  setSidebarWidth: (width: number) => void
  toggleMinimize: () => void

  // Resize handlers
  handleResizeStart: (e: React.MouseEvent) => void
  handleResizeEnd: () => void

  // Computed values
  sidebarStyle: React.CSSProperties
  config: SidebarConfig
}

function isMobile(): boolean {
  if (typeof window === 'undefined') return false
  return window.innerWidth < 1024
}

function useIsMobile(): boolean {
  const [mobile, setMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => setMobile(isMobile())
    checkMobile()

    const debouncedCheck = debounce(checkMobile, 100)
    window.addEventListener('resize', debouncedCheck)
    return () => window.removeEventListener('resize', debouncedCheck)
  }, [])

  return mobile
}

function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

function clampWidth(width: number, windowWidth: number, config: SidebarConfig): number {
  const maxWidth = windowWidth * config.MAX_WIDTH_PERCENTAGE
  return Math.max(config.MIN_WIDTH, Math.min(width, maxWidth))
}

function saveWidth(storageKey: string, width: number): void {
  if (typeof window === 'undefined') return
  try {
    localStorage.setItem(storageKey, width.toString())
  } catch (error) {
    console.warn('Failed to save sidebar width to localStorage:', error)
  }
}

function loadWidth(storageKey: string, defaultWidth: number): number {
  if (typeof window === 'undefined') return defaultWidth
  try {
    const saved = localStorage.getItem(storageKey)
    return saved ? parseInt(saved, 10) : defaultWidth
  } catch (error) {
    console.warn('Failed to load sidebar width from localStorage:', error)
    return defaultWidth
  }
}

export function useResizableSidebar({
  storageKey,
  config: userConfig = {},
  isOpen
}: UseResizableSidebarOptions): UseResizableSidebarReturn {
  const config = { ...DEFAULT_SIDEBAR_CONFIG, ...userConfig }

  const [sidebarWidth, setSidebarWidthState] = useState(config.DEFAULT_WIDTH)
  const [isResizing, setIsResizing] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const mobile = useIsMobile()
  
  const startXRef = useRef<number>(0)
  const startWidthRef = useRef<number>(0)
  const isMouseDownRef = useRef<boolean>(false)

  // Initialize width from localStorage
  useEffect(() => {
    const savedWidth = loadWidth(storageKey, config.DEFAULT_WIDTH)
    setSidebarWidthState(savedWidth)
  }, [storageKey, config.DEFAULT_WIDTH])

  // Handle window resize to maintain constraints
  useEffect(() => {
    const handleWindowResize = () => {
      if (typeof window === 'undefined') return
      
      const clampedWidth = clampWidth(sidebarWidth, window.innerWidth, config)
      if (clampedWidth !== sidebarWidth) {
        setSidebarWidthState(clampedWidth)
        saveWidth(storageKey, clampedWidth)
      }
    }

    window.addEventListener('resize', handleWindowResize)
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [sidebarWidth, storageKey, config])

  // Mouse move handler for resizing
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isMouseDownRef.current) return
    
    const deltaX = startXRef.current - e.clientX
    const newWidth = startWidthRef.current + deltaX
    const clampedWidth = clampWidth(newWidth, window.innerWidth, config)
    
    setSidebarWidthState(clampedWidth)
  }, [config])

  // Mouse up handler for resizing
  const handleMouseUp = useCallback(() => {
    if (!isMouseDownRef.current) return
    
    isMouseDownRef.current = false
    setIsResizing(false)
    saveWidth(storageKey, sidebarWidth)
    
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }, [handleMouseMove, sidebarWidth, storageKey])

  // Start resize handler
  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    if (isMobile()) return
    
    e.preventDefault()
    e.stopPropagation()
    
    startXRef.current = e.clientX
    startWidthRef.current = sidebarWidth
    isMouseDownRef.current = true
    setIsResizing(true)
    
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'col-resize'
    document.body.style.userSelect = 'none'
  }, [sidebarWidth, handleMouseMove, handleMouseUp])

  // End resize handler
  const handleResizeEnd = useCallback(() => {
    handleMouseUp()
  }, [handleMouseUp])

  const setSidebarWidth = useCallback((width: number) => {
    const clampedWidth = clampWidth(width, window.innerWidth, config)
    setSidebarWidthState(clampedWidth)
    saveWidth(storageKey, clampedWidth)
  }, [storageKey, config])

  const toggleMinimize = useCallback(() => {
    setIsMinimized(prev => !prev)
  }, [])

  // Computed styles
  const effectiveWidth = isMinimized ? config.MIN_WIDTH : sidebarWidth
  const sidebarStyle: React.CSSProperties = {
    width: mobile ? '100%' : `${effectiveWidth}px`,
    transform: isOpen ? 'translateX(0)' : 'translateX(100%)',
    transition: isResizing ? 'none' : `all ${config.TRANSITION_DURATION}ms ${config.TRANSITION_EASING}`
  }

  return {
    sidebarWidth: effectiveWidth,
    isResizing,
    isMobile: mobile,
    isMinimized,
    setSidebarWidth,
    toggleMinimize,
    handleResizeStart,
    handleResizeEnd,
    sidebarStyle,
    config
  }
}
