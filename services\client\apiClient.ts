/**
 * API Client for Money Management App
 * 
 * Static class implementation with full CRUD operations,
 * error handling, and TypeScript support for all entities.
 */

import type {
  ApiError,
  ApiResponse
} from '@/types/api'

import type {
  Account,
  Category,
  Contact,
  Transaction,
  User
} from '@/lib/generated/prisma'

// Type aliases for response types
type UserResponse = User
type AccountResponse = Account
type ContactResponse = Contact
type CategoryResponse = Category
type TransactionResponse = Transaction

// ==================== API CLIENT CONFIGURATION ====================

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api'
const DEFAULT_TIMEOUT = 30000 // 30 seconds

// ==================== API CLIENT ERROR CLASSES ====================

export class ApiClientError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'ApiClientError'
  }

}

export class NetworkError extends ApiClientError {
  constructor(message: string = 'Network error occurred') {
    super(message)
    this.name = 'NetworkError'
  }
}

export class ValidationError extends ApiClientError {
  constructor(message: string, public errors: any[] = []) {
    super(message)
    this.name = 'ValidationError'
  }
}

// ==================== API CLIENT STATIC CLASS ====================

export class ApiClient {
  private static baseURL: string = API_BASE_URL.replace(/\/$/, '') // Remove trailing slash
  private static timeout: number = DEFAULT_TIMEOUT
  private static defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }

  // ==================== CONFIGURATION METHODS ====================

  /**
   * Set base URL
   */
  static setBaseURL(url: string): void {
    this.baseURL = url.replace(/\/$/, '')
  }

  /**
   * Get base URL
   */
  static getBaseURL(): string {
    return this.baseURL
  }

  /**
   * Set timeout
   */
  static setTimeout(timeout: number): void {
    this.timeout = timeout
  }

  /**
   * Get timeout
   */
  static getTimeout(): number {
    return this.timeout
  }

  /**
   * Set authentication token
   */
  static setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`
  }

  /**
   * Remove authentication token
   */
  static removeAuthToken(): void {
    delete this.defaultHeaders['Authorization']
  }

  /**
   * Set custom header
   */
  static setHeader(key: string, value: string): void {
    this.defaultHeaders[key] = value
  }

  /**
   * Get all headers
   */
  static getHeaders(): Record<string, string> {
    return { ...this.defaultHeaders }
  }

  /**
   * Check if authenticated
   */
  static isAuthenticated(): boolean {
    return !!this.defaultHeaders['Authorization']
  }

  // ==================== CORE HTTP METHODS ====================

  /**
   * Generic request method with error handling
   */
  private static async request<T extends ApiResponse<unknown>>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    // Create AbortController for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)

    const config: RequestInit = {
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
      signal: controller.signal,
      ...options,
    }

    try {
      const response = await fetch(url, config)
      clearTimeout(timeoutId)
      
      // Handle different response types
      const contentType = response.headers.get('content-type')
      let data: any

      if (contentType?.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text()
      }

      if (!response.ok) {
        const error = data as ApiError
        throw new ApiClientError(
          error.message || `HTTP ${response.status}`,
          response.status,
          error.code,
          error.details
        )
      }

      // Check if response has success field (API response structure)
      if (data && typeof data === 'object' && 'success' in data) {
        if (!data.success) {
          throw new ApiClientError(
            data.error?.message || 'API request failed',
            response.status,
            data.error?.code,
            data.error?.details
          )
        }
        // Return the full API response structure for proper typing
        return data as T
      }

      return data as T
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof ApiClientError) {
        throw error
      }
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new NetworkError('Request timeout')
        }
        throw new NetworkError(error.message)
      }
      
      throw new NetworkError('Unknown error occurred')
    }
  }

  /**
   * GET request
   */
  static async get<T extends ApiResponse<unknown> >(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = params ? `${endpoint}?${this.buildQueryString(params)}` : endpoint
    return this.request<T>(url, { method: 'GET' })
  }

  /**
   * POST request
   */
  static async post<T extends ApiResponse<unknown>>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PUT request
   */
  static async put<T extends ApiResponse<unknown>>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PATCH request
   */
  static async patch<T extends ApiResponse<unknown>>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * DELETE request
   */
  static async delete<T extends ApiResponse<unknown>>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  /**
   * Build query string from parameters
   */
  private static buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, String(item)))
        } else {
          searchParams.append(key, String(value))
        }
      }
    })
    
    return searchParams.toString()
  }

}
