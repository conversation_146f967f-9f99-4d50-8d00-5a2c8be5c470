import { useEffect, useRef, useCallback } from 'react'

// Performance metrics interface
interface PerformanceMetrics {
  renderTime: number
  componentName: string
  timestamp: number
  props?: any
}

interface WebVitals {
  FCP?: number // First Contentful Paint
  LCP?: number // Largest Contentful Paint
  FID?: number // First Input Delay
  CLS?: number // Cumulative Layout Shift
  TTFB?: number // Time to First Byte
}

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string, enabled = process.env.NODE_ENV === 'development') {
  const renderStartTime = useRef<number>(0)
  const renderCount = useRef<number>(0)
  const metricsRef = useRef<PerformanceMetrics[]>([])

  // Start performance measurement
  const startMeasurement = useCallback(() => {
    if (!enabled) return
    renderStartTime.current = performance.now()
  }, [enabled])

  // End performance measurement
  const endMeasurement = useCallback((props?: any) => {
    if (!enabled || renderStartTime.current === 0) return

    const renderTime = performance.now() - renderStartTime.current
    renderCount.current += 1

    const metrics: PerformanceMetrics = {
      renderTime,
      componentName,
      timestamp: Date.now(),
      props: process.env.NODE_ENV === 'development' ? props : undefined
    }

    metricsRef.current.push(metrics)

    // Log slow renders (>16ms for 60fps)
    if (renderTime > 16) {
      console.warn(`🐌 Slow render detected in ${componentName}:`, {
        renderTime: `${renderTime.toFixed(2)}ms`,
        renderCount: renderCount.current,
        props
      })
    }

    // Keep only last 100 measurements
    if (metricsRef.current.length > 100) {
      metricsRef.current = metricsRef.current.slice(-100)
    }

    renderStartTime.current = 0
  }, [enabled, componentName])

  // Get performance statistics
  const getStats = useCallback(() => {
    if (!enabled || metricsRef.current.length === 0) return null

    const renderTimes = metricsRef.current.map(m => m.renderTime)
    const avgRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length
    const maxRenderTime = Math.max(...renderTimes)
    const minRenderTime = Math.min(...renderTimes)

    return {
      componentName,
      totalRenders: renderCount.current,
      avgRenderTime: Number(avgRenderTime.toFixed(2)),
      maxRenderTime: Number(maxRenderTime.toFixed(2)),
      minRenderTime: Number(minRenderTime.toFixed(2)),
      slowRenders: renderTimes.filter(t => t > 16).length,
      recentMetrics: metricsRef.current.slice(-10)
    }
  }, [enabled, componentName])

  // Log stats on unmount (development only)
  useEffect(() => {
    if (!enabled) return

    return () => {
      const stats = getStats()
      if (stats && stats.totalRenders > 0) {
        console.log(`📊 Performance stats for ${componentName}:`, stats)
      }
    }
  }, [enabled, componentName, getStats])

  return {
    startMeasurement,
    endMeasurement,
    getStats,
    renderCount: renderCount.current
  }
}

// Hook for measuring Web Vitals
export function useWebVitals() {
  const vitalsRef = useRef<WebVitals>({})

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Measure Web Vitals using Performance Observer
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcp = entries.find(entry => entry.name === 'first-contentful-paint')
        if (fcp) {
          vitalsRef.current.FCP = fcp.startTime
        }
      })

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        if (lastEntry) {
          vitalsRef.current.LCP = lastEntry.startTime
        }
      })

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.processingStart && entry.startTime) {
            vitalsRef.current.FID = entry.processingStart - entry.startTime
          }
        })
      })

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        vitalsRef.current.CLS = clsValue
      })

      try {
        fcpObserver.observe({ entryTypes: ['paint'] })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        fidObserver.observe({ entryTypes: ['first-input'] })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (error) {
        console.warn('Performance Observer not supported:', error)
      }

      // TTFB from Navigation Timing
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        vitalsRef.current.TTFB = navigationEntry.responseStart - navigationEntry.requestStart
      }
    }

    measureWebVitals()

    // Log vitals after page load
    const logVitals = () => {
      setTimeout(() => {
        const vitals = vitalsRef.current
        if (Object.keys(vitals).length > 0) {
          console.log('📈 Web Vitals:', {
            FCP: vitals.FCP ? `${vitals.FCP.toFixed(2)}ms` : 'N/A',
            LCP: vitals.LCP ? `${vitals.LCP.toFixed(2)}ms` : 'N/A',
            FID: vitals.FID ? `${vitals.FID.toFixed(2)}ms` : 'N/A',
            CLS: vitals.CLS ? vitals.CLS.toFixed(4) : 'N/A',
            TTFB: vitals.TTFB ? `${vitals.TTFB.toFixed(2)}ms` : 'N/A'
          })

          // Check against thresholds
          const warnings = []
          if (vitals.FCP && vitals.FCP > 1800) warnings.push('FCP > 1.8s')
          if (vitals.LCP && vitals.LCP > 2500) warnings.push('LCP > 2.5s')
          if (vitals.FID && vitals.FID > 100) warnings.push('FID > 100ms')
          if (vitals.CLS && vitals.CLS > 0.1) warnings.push('CLS > 0.1')
          if (vitals.TTFB && vitals.TTFB > 800) warnings.push('TTFB > 800ms')

          if (warnings.length > 0) {
            console.warn('⚠️ Performance warnings:', warnings)
          }
        }
      }, 3000) // Wait 3 seconds for metrics to stabilize
    }

    if (document.readyState === 'complete') {
      logVitals()
    } else {
      window.addEventListener('load', logVitals)
    }

    return () => {
      window.removeEventListener('load', logVitals)
    }
  }, [])

  const getVitals = useCallback(() => vitalsRef.current, [])

  return { getVitals }
}

// Hook for memory usage monitoring
export function useMemoryMonitor(interval = 5000) {
  const memoryRef = useRef<any>({})

  useEffect(() => {
    if (typeof window === 'undefined' || !('memory' in performance)) return

    const checkMemory = () => {
      const memory = (performance as any).memory
      if (memory) {
        memoryRef.current = {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          timestamp: Date.now()
        }

        // Warn if memory usage is high
        const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
        if (usagePercent > 80) {
          console.warn('🧠 High memory usage detected:', {
            usage: `${usagePercent.toFixed(1)}%`,
            used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
            limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
          })
        }
      }
    }

    checkMemory()
    const intervalId = setInterval(checkMemory, interval)

    return () => clearInterval(intervalId)
  }, [interval])

  const getMemoryUsage = useCallback(() => memoryRef.current, [])

  return { getMemoryUsage }
}

// Combined performance hook
export function usePerformanceAnalytics(componentName: string) {
  const componentPerf = usePerformanceMonitor(componentName)
  const webVitals = useWebVitals()
  const memoryMonitor = useMemoryMonitor()

  const getFullReport = useCallback(() => {
    return {
      component: componentPerf.getStats(),
      webVitals: webVitals.getVitals(),
      memory: memoryMonitor.getMemoryUsage(),
      timestamp: Date.now()
    }
  }, [componentPerf, webVitals, memoryMonitor])

  return {
    ...componentPerf,
    getWebVitals: webVitals.getVitals,
    getMemoryUsage: memoryMonitor.getMemoryUsage,
    getFullReport
  }
}
