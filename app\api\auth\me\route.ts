import { NextRequest } from 'next/server'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse
} from '@/lib/api/utils'
import { AuthService } from '@/services/server'
import prisma from '@/lib/prisma'

// ==================== GET /api/auth/me ====================

export const GET = withErrorHandler(async (request: NextRequest) => {
  const { userId } = await AuthService.verifyRequest(request)

  // use graphql
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      preferences: true
    }
  })


  return successResponse(user)
})
