/**
 * API Utilities
 * 
 * Common utilities for API routes including validation, error handling, and response formatting
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// ==================== RESPONSE TYPES ====================

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    code: string
    details?: any
  }
  timestamp: string
}

export interface PaginatedApiResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// ==================== ERROR CLASSES ====================

export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code: string = 'INTERNAL_ERROR',
    public details?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details)
    this.name = 'ValidationError'
  }
}

export class NotFoundError extends ApiError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND')
    this.name = 'NotFoundError'
  }
}

export class UnauthorizedError extends ApiError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED')
    this.name = 'UnauthorizedError'
  }
}

export class ForbiddenError extends ApiError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN')
    this.name = 'ForbiddenError'
  }
}

export class ConflictError extends ApiError {
  constructor(message: string = 'Resource already exists') {
    super(message, 409, 'CONFLICT')
    this.name = 'ConflictError'
  }
}

// ==================== RESPONSE HELPERS ====================

export function successResponse<T>(data: T, status: number = 200): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    timestamp: new Date().toISOString()
  }, { status })
}

export function paginatedResponse<T>(
  data: T[],
  pagination: {
    page: number
    limit: number
    total: number
  },
  status: number = 200
): NextResponse {
  const totalPages = Math.ceil(pagination.total / pagination.limit)
  
  return NextResponse.json({
    success: true,
    data,
    pagination: {
      ...pagination,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1
    },
    timestamp: new Date().toISOString()
  }, { status })
}

export function errorResponse(
  error: ApiError | Error,
  status?: number
): NextResponse {
  if (error instanceof ApiError) {
    return NextResponse.json({
      success: false,
      error: {
        message: error.message,
        code: error.code,
        details: error.details
      },
      timestamp: new Date().toISOString()
    }, { status: error.statusCode })
  }

  // Generic error
  return NextResponse.json({
    success: false,
    error: {
      message: error.message || 'Internal server error',
      code: 'INTERNAL_ERROR'
    },
    timestamp: new Date().toISOString()
  }, { status: status || 500 })
}

// ==================== VALIDATION HELPERS ====================

export async function validateRequest<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json()
    return schema.parse(body)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('Validation failed', error.errors)
    }
    throw new ValidationError('Invalid request body')
  }
}

export function validateQueryParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): T {
  try {
    const params = Object.fromEntries(searchParams.entries())
    return schema.parse(params)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError('Invalid query parameters', error.errors)
    }
    throw new ValidationError('Invalid query parameters')
  }
}

// ==================== PAGINATION HELPERS ====================

export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder: 'asc' | 'desc'
}

export const paginationSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 25),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

export function getPaginationParams(searchParams: URLSearchParams): PaginationParams {
  const params = Object.fromEntries(searchParams.entries())
  return paginationSchema.parse(params)
}

// ==================== AUTHENTICATION HELPERS ====================

export function extractBearerToken(request: NextRequest): string | null {
  const authorization = request.headers.get('authorization')
  if (!authorization || !authorization.startsWith('Bearer ')) {
    return null
  }
  return authorization.substring(7)
}

export function requireAuth(request: NextRequest): string {
  const token = extractBearerToken(request)
  if (!token) {
    throw new UnauthorizedError('Authentication token required')
  }
  return token
}

// Real JWT verification using auth utilities
export function verifyToken(token: string): { userId: string; role: string; email: string } {
  try {
    // Import JWT utilities dynamically to avoid circular imports
    const { verifyToken: jwtVerify } = require('@/lib/auth/jwt')
    const payload = jwtVerify(token)

    return {
      userId: payload.userId,
      role: payload.role,
      email: payload.email
    }
  } catch (error) {
    throw new UnauthorizedError('Invalid or expired token')
  }
}

// ==================== CORS HELPERS ====================

export function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  }
}

export function handleCors(request: NextRequest): NextResponse | null {
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 200,
      headers: corsHeaders()
    })
  }
  return null
}

// ==================== REQUEST LOGGING ====================

export function logRequest(request: NextRequest, startTime: number) {
  // const duration = Date.now() - startTime
  // const method = request.method
  // const url = request.url
  
  // console.log(`${method} ${url} - ${duration}ms`)
}

// ==================== ERROR HANDLER WRAPPER ====================
export function withErrorHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    const startTime = Date.now()
    
    try {
      // Handle CORS preflight
      const corsResponse = handleCors(request)
      if (corsResponse) return corsResponse
      
      const response = await handler(request, context)
      
      // Add CORS headers to response
      Object.entries(corsHeaders()).forEach(([key, value]) => {
        response.headers.set(key, value)
      })
      
      logRequest(request, startTime)
      return response
      
    } catch (error) {
      console.error('API Error:', error)
      logRequest(request, startTime)
      
      const response = errorResponse(error as Error)
      
      // Add CORS headers to error response
      Object.entries(corsHeaders()).forEach(([key, value]) => {
        response.headers.set(key, value)
      })
      
      return response
    }
  }
}


