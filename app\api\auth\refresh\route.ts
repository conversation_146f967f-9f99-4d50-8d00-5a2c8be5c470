import { NextRequest } from 'next/server'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse
} from '@/lib/api/utils'

// ==================== POST /api/auth/refresh ====================

export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await request.json()
  const { refreshToken } = body

  if (!refreshToken) {
    throw new Error('Refresh token is required')
  }

  // Use AuthService to refresh token
  const { AuthService } = await import('@/services/server/auth-service')
  const result = await AuthService.refreshToken(refreshToken)

  return successResponse(result)
})
