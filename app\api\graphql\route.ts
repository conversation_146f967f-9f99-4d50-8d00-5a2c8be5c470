/**
 * GraphQL API Route
 *
 * Apollo GraphQL server endpoint for Next.js App Router
 */

import { NextRequest } from 'next/server'
import { ApolloServer } from '@apollo/server'
import { startServerAndCreateNextHandler } from '@as-integrations/next'
import { typeDefs } from './schema'
import { resolvers } from './resolvers'
import { formatError } from './errors'
import { createContext } from './context'

// Create Apollo Server
const server = new ApolloServer({
  typeDefs,
  resolvers,
  introspection: process.env.NODE_ENV !== 'production',
  includeStacktraceInErrorResponses: process.env.NODE_ENV !== 'production',
  formatError,
})

// Create Next.js handler
const handler = startServerAndCreateNextHandler<NextRequest>(server, {
  context: async (req) => createContext({ req })
  
})

export { handler as GET, handler as POST }
