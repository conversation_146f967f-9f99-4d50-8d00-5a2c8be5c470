/**
 * Entity-Specific Connection Hooks
 * 
 * Convenient hooks for working with specific entities using GraphQL connections
 */

import { useConnection, usePaginatedConnection, useInfiniteConnection } from './useConnection'
import {
  UserApiService,
  AccountApiService,
  TransactionApiService,
  CategoryApiService,
  ContactApiService,
  CategoryGoalApiService,
  RecurringTransactionApiService,
  AccountTypeApiService
} from '@/services/client'

import type {
  UserConnectionParams,
  AccountConnectionParams,
  TransactionConnectionParams,
  CategoryConnectionParams,
  ContactConnectionParams,
  CategoryGoalConnectionParams,
  RecurringTransactionConnectionParams,
  AccountTypeConnectionParams
} from '@/types/graphql-connections'

import type {
  User,
  Account,
  Transaction,
  Category,
  Contact,
  CategoryGoal,
  RecurringTransaction,
  AccountType
} from '@/lib/generated/prisma'

// ==================== USER HOOKS ====================

export function useUsers(params?: UserConnectionParams) {
  return useConnection<User>({
    fetchFunction: (p) => UserApiService.getUsers(p),
    initialParams: params
  })
}

export function usePaginatedUsers(params?: UserConnectionParams, pageSize?: number) {
  return usePaginatedConnection<User>({
    fetchFunction: (p) => UserApiService.getUsers(p),
    initialParams: params,
    pageSize
  })
}

export function useInfiniteUsers(params?: UserConnectionParams) {
  return useInfiniteConnection<User>({
    fetchFunction: (p) => UserApiService.getUsers(p),
    initialParams: params
  })
}

export function useUserSearch(query: string, first?: number) {
  return useConnection<User>({
    fetchFunction: () => UserApiService.searchUsers(query, first),
    initialParams: { query, first },
    autoFetch: !!query
  })
}

// ==================== ACCOUNT HOOKS ====================

export function useAccounts(params?: AccountConnectionParams) {
  return useConnection<Account>({
    fetchFunction: (p) => AccountApiService.getAccounts(p),
    initialParams: params
  })
}

export function usePaginatedAccounts(params?: AccountConnectionParams, pageSize?: number) {
  return usePaginatedConnection<Account>({
    fetchFunction: (p) => AccountApiService.getAccounts(p),
    initialParams: params,
    pageSize
  })
}

export function useActiveAccounts(params?: Omit<AccountConnectionParams, 'isActive'>) {
  return useConnection<Account>({
    fetchFunction: (p) => AccountApiService.getActiveAccounts(p),
    initialParams: params
  })
}

export function useAccountSearch(query: string, first?: number) {
  return useConnection<Account>({
    fetchFunction: () => AccountApiService.searchAccounts(query, first),
    initialParams: { query, first },
    autoFetch: !!query
  })
}

// ==================== TRANSACTION HOOKS ====================

export function useTransactions(params?: TransactionConnectionParams) {
  return useConnection<Transaction>({
    fetchFunction: (p) => TransactionApiService.getTransactions(p),
    initialParams: params
  })
}

export function usePaginatedTransactions(params?: TransactionConnectionParams, pageSize?: number) {
  return usePaginatedConnection<Transaction>({
    fetchFunction: (p) => TransactionApiService.getTransactions(p),
    initialParams: params,
    pageSize
  })
}

export function useInfiniteTransactions(params?: TransactionConnectionParams) {
  return useInfiniteConnection<Transaction>({
    fetchFunction: (p) => TransactionApiService.getTransactions(p),
    initialParams: params
  })
}

export function useTransactionSearch(query: string, first?: number) {
  return useConnection<Transaction>({
    fetchFunction: () => TransactionApiService.searchTransactions(query, first),
    initialParams: { query, first },
    autoFetch: !!query
  })
}

// ==================== CATEGORY HOOKS ====================

export function useCategories(params?: CategoryConnectionParams) {
  return useConnection<Category>({
    fetchFunction: (p) => CategoryApiService.getCategories(p),
    initialParams: params
  })
}

export function usePaginatedCategories(params?: CategoryConnectionParams, pageSize?: number) {
  return usePaginatedConnection<Category>({
    fetchFunction: (p) => CategoryApiService.getCategories(p),
    initialParams: params,
    pageSize
  })
}

export function useActiveCategories(params?: Omit<CategoryConnectionParams, 'isActive'>) {
  return useConnection<Category>({
    fetchFunction: (p) => CategoryApiService.getActiveCategories(p),
    initialParams: params
  })
}

export function useCategorySearch(query: string, first?: number) {
  return useConnection<Category>({
    fetchFunction: () => CategoryApiService.searchCategories(query, first),
    initialParams: { query, first },
    autoFetch: !!query
  })
}

// ==================== CONTACT HOOKS ====================

export function useContacts(params?: ContactConnectionParams) {
  return useConnection<Contact>({
    fetchFunction: (p) => ContactApiService.getContacts(p),
    initialParams: params
  })
}

export function usePaginatedContacts(params?: ContactConnectionParams, pageSize?: number) {
  return usePaginatedConnection<Contact>({
    fetchFunction: (p) => ContactApiService.getContacts(p),
    initialParams: params,
    pageSize
  })
}

export function useActiveContacts(params?: Omit<ContactConnectionParams, 'isActive'>) {
  return useConnection<Contact>({
    fetchFunction: (p) => ContactApiService.getActiveContacts(p),
    initialParams: params
  })
}

export function useContactSearch(query: string, first?: number) {
  return useConnection<Contact>({
    fetchFunction: () => ContactApiService.searchContacts(query, first),
    initialParams: { query, first },
    autoFetch: !!query
  })
}

// ==================== CATEGORY GOAL HOOKS ====================

export function useCategoryGoals(params?: CategoryGoalConnectionParams) {
  return useConnection<CategoryGoal>({
    fetchFunction: (p) => CategoryGoalApiService.getCategoryGoals(p),
    initialParams: params
  })
}

export function usePaginatedCategoryGoals(params?: CategoryGoalConnectionParams, pageSize?: number) {
  return usePaginatedConnection<CategoryGoal>({
    fetchFunction: (p) => CategoryGoalApiService.getCategoryGoals(p),
    initialParams: params,
    pageSize
  })
}

export function useActiveCategoryGoals(params?: Omit<CategoryGoalConnectionParams, 'isActive'>) {
  return useConnection<CategoryGoal>({
    fetchFunction: (p) => CategoryGoalApiService.getActiveCategoryGoals(p),
    initialParams: params
  })
}

// ==================== RECURRING TRANSACTION HOOKS ====================

export function useRecurringTransactions(params?: RecurringTransactionConnectionParams) {
  return useConnection<RecurringTransaction>({
    fetchFunction: (p) => RecurringTransactionApiService.getRecurringTransactions(p),
    initialParams: params
  })
}

export function usePaginatedRecurringTransactions(params?: RecurringTransactionConnectionParams, pageSize?: number) {
  return usePaginatedConnection<RecurringTransaction>({
    fetchFunction: (p) => RecurringTransactionApiService.getRecurringTransactions(p),
    initialParams: params,
    pageSize
  })
}

export function useActiveRecurringTransactions(params?: Omit<RecurringTransactionConnectionParams, 'isActive'>) {
  return useConnection<RecurringTransaction>({
    fetchFunction: (p) => RecurringTransactionApiService.getActiveRecurringTransactions(p),
    initialParams: params
  })
}

// ==================== ACCOUNT TYPE HOOKS ====================

export function useAccountTypes(params?: AccountTypeConnectionParams) {
  return useConnection<AccountType>({
    fetchFunction: (p) => AccountTypeApiService.getAccountTypes(p),
    initialParams: params
  })
}

export function usePaginatedAccountTypes(params?: AccountTypeConnectionParams, pageSize?: number) {
  return usePaginatedConnection<AccountType>({
    fetchFunction: (p) => AccountTypeApiService.getAccountTypes(p),
    initialParams: params,
    pageSize
  })
}

// ==================== COMBINED HOOKS ====================

/**
 * Hook to fetch all data needed for transaction forms
 */
export function useTransactionFormData() {
  const accounts = useActiveAccounts({ first: 100 })
  const categories = useActiveCategories({ first: 100 })
  const contacts = useContacts({ first: 100 })

  return {
    accounts: accounts.items,
    categories: categories.items,
    contacts: contacts.items,
    loading: accounts.loading || categories.loading || contacts.loading,
    error: accounts.error || categories.error || contacts.error,
    refetch: () => {
      accounts.refetch()
      categories.refetch()
      contacts.refetch()
    }
  }
}

/**
 * Hook to fetch all data needed for account forms
 */
export function useAccountFormData() {
  const accountTypes = useAccountTypes({ first: 50 })

  return {
    accountTypes: accountTypes.items,
    loading: accountTypes.loading,
    error: accountTypes.error,
    refetch: accountTypes.refetch
  }
}

/**
 * Hook to fetch dashboard data
 */
export function useDashboardData() {
  const accounts = useActiveAccounts({ first: 10 })
  const recentTransactions = useTransactions({ first: 10 })
  const categories = useActiveCategories({ first: 20 })

  return {
    accounts: accounts.items,
    recentTransactions: recentTransactions.items,
    categories: categories.items,
    loading: accounts.loading || recentTransactions.loading || categories.loading,
    error: accounts.error || recentTransactions.error || categories.error,
    refetch: () => {
      accounts.refetch()
      recentTransactions.refetch()
      categories.refetch()
    }
  }
}
