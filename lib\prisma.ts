/**
 * Prisma Client Singleton
 * 
 * Ensures single instance of Prisma Client across the application
 * Handles connection pooling and prevents multiple instances in development
 */

import { PrismaClient } from './generated/prisma'

// Prevent multiple instances of Prisma Client in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: [],
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

export default prisma
