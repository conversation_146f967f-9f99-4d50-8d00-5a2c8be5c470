import { AccountTypeType } from '../common';
import type { BaseTableActiveable } from '../base';

export type {
  AccountType,
  AccountTypeBase,
  AccountTypeWithRelations,
  CreateAccountTypeInput,
  UpdateAccountTypeInput,
  AccountTypeFilterInput
};

/**
 * Base account type interface
 */
interface AccountTypeBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  color: string;
  type: AccountTypeType;
  icon: string | null;
  isActive: boolean;
  userId: string;
}

/**
 * Account type with all relations
 */
interface AccountTypeWithRelations extends AccountTypeBase {
  accounts: AccountBase[];
  user: UserBase;
}

/**
 * Union type for account type
 */
type AccountType = AccountTypeBase | AccountTypeWithRelations;

/**
 * Input for creating a new account type
 */
interface CreateAccountTypeInput {
  name: string;
  description?: string | null;
  color: string;
  type: AccountTypeType;
  icon?: string | null;
  isActive?: boolean;
  userId: string;
}

/**
 * Input for updating an existing account type
 */
interface UpdateAccountTypeInput {
  id: string;
  name?: string;
  description?: string | null;
  color?: string;
  type?: AccountTypeType;
  icon?: string | null;
  isActive?: boolean;
}

/**
 * Input for filtering account types
 */
interface AccountTypeFilterInput {
  search?: string;
  type?: AccountTypeType;
  isActive?: boolean;
  userId?: string;
}

// Import related types
import type { AccountBase } from './account';
import type { UserBase } from '../user';