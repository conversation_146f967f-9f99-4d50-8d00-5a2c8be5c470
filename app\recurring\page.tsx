"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { ListPageLayout } from "@/components/layout/ResponsiveLayout"
import { GenericList } from "@/components/ui/GenericList"
import { PortalDropdown } from "@/components/ui/PortalDropdown"
import { RecurringSidebar } from "@/components/ui/RecurringSidebar"
import { Button } from "@/components/ui/button"
import { RecurringTransactionApiService } from "../../services/client/recurringTransactionApi"
import { CategoryApiService } from "@/services/client/categoryApi"
import { useAuth } from "@/contexts/AuthContext"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { RefreshCw, Plus, Edit, Play, Pause, Calendar, TrendingUp, TrendingDown, Clock, Search, SortAsc, SortDesc, ChevronUp, ChevronDown } from "lucide-react"

// RecurringTransactionCard component
function RecurringTransactionCard({
  transaction,
  categories,
  onEdit,
  onToggleStatus
}: {
  transaction: any;
  categories: any[];
  onEdit: () => void;
  onToggleStatus: () => void;
}) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount)
  }

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case "daily": return "Hàng ngày"
      case "weekly": return "Hàng tuần"
      case "monthly": return "Hàng tháng"
      case "yearly": return "Hàng năm"
      default: return frequency
    }
  }

  const category = categories.find(c => c.id === transaction.categoryId)

  return (
    <div className="group bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/50 dark:hover:border-gray-600/50 transition-all duration-200 h-full flex flex-col">
      
      {/* Transaction Info */}
      <div className="flex-1 space-y-2">
        {/* Description and Status */}
        <div className="flex items-start justify-between gap-2">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {/* Category Color */}
            <div
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: category?.color || '#6B7280' }}
            />
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 flex-1">
              {transaction.description}
            </h3>
          </div>

          {/* Status Badge */}
          <span className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
            transaction.isActive
              ? "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
              : "bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400"
          }`}>
            {transaction.isActive ? (
              <div className="flex items-center gap-1">
                <Play size={10} />
                Hoạt động
              </div>
            ) : (
              <div className="flex items-center gap-1">
                <Pause size={10} />
                Tạm dừng
              </div>
            )}
          </span>
        </div>

        {/* Category and Type */}
        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
          {transaction.kind === "income" ? (
            <TrendingUp size={12} className="text-green-600" />
          ) : (
            <TrendingDown size={12} className="text-red-600" />
          )}
          <span>{category?.name || "Không có danh mục"}</span>
        </div>

        {/* Amount and Frequency */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
            <Clock size={12} />
            <span>{getFrequencyText(transaction.frequency)}</span>
          </div>
          <div className={`font-semibold ${
            transaction.kind === "income"
              ? "text-green-600 dark:text-green-400"
              : "text-red-600 dark:text-red-400"
          }`}>
            {formatCurrency(transaction.amount)}
          </div>
        </div>

        {/* Start Date */}
        <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
          <Calendar size={12} />
          <span>Bắt đầu: {new Date(transaction.startDate).toLocaleDateString('vi-VN')}</span>
        </div>
      </div>

      {/* Footer - Action Buttons */}
      <div className="flex items-center justify-between gap-2 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        {/* Status Indicator */}
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${
            transaction.isActive ? "bg-green-400" : "bg-gray-400"
          }`} />
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {transaction.isActive ? "Đang chạy" : "Đã dừng"}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="flex items-center gap-1">
            <button
              onClick={onToggleStatus}
              className={`p-1.5 rounded-lg transition-colors ${
                transaction.isActive
                  ? "text-gray-400 hover:text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20"
                  : "text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20"
              }`}
              title={transaction.isActive ? "Tạm dừng" : "Kích hoạt"}
            >
              {transaction.isActive ? <Pause size={14} /> : <Play size={14} />}
            </button>

            <button
              onClick={onEdit}
              className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
              title="Chỉnh sửa"
            >
              <Edit size={14} />
            </button>


          </div>
        </div>
      </div>
    </div>
  )
}

function RecurringPageContent() {
  const { user } = useAuth()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [recurringTransactions, setRecurringTransactions] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Search and Sort states
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState<"all" | "income" | "expense">("all")
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "paused">("all")
  const [sortField, setSortField] = useState<"name" | "amount" | "frequency" | "startDate" | "isActive">("startDate")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [showSortOptions, setShowSortOptions] = useState(false)
  const sortButtonRef = useRef<HTMLButtonElement>(null)

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        console.log('Fetching recurring transactions...')
        const [recurringResponse, categoriesResponse] = await Promise.all([
          RecurringTransactionApiService.getRecurringTransactions({}),
          CategoryApiService.getCategories({})
        ])

        console.log('Recurring response:', recurringResponse)
        console.log('Categories response:', categoriesResponse)

        if (recurringResponse.success) {
          console.log('Setting recurring transactions:', recurringResponse.data)
          // Handle both paginated and direct response
          const transactions = Array.isArray(recurringResponse.data)
            ? recurringResponse.data
            : (recurringResponse.data as any)?.data || []
          setRecurringTransactions(transactions)
        } else {
          console.error('Failed to load recurring transactions:', recurringResponse.error)
          setError('Failed to load recurring transactions')
        }

        if (categoriesResponse.success) {
          setCategories(categoriesResponse.data || [])
        } else {
          setError('Failed to load categories')
        }
      } catch (err) {
        setError('Failed to load data')
        console.error('Recurring page error:', err)
      } finally {
        setLoading(false)
      }
    }

    if (user?.id) {
      fetchData()
    }
  }, [user?.id])

  // Test function to manually trigger API call
  const testApiCall = async () => {
    console.log('Manual test API call triggered')
    console.log('Current user:', user)
    try {
      // Test with direct fetch first
      const token = localStorage.getItem('accessToken')
      console.log('Token:', token)

      const response = await fetch('/api/recurring', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('Direct fetch response status:', response.status)
      const data = await response.json()
      console.log('Direct fetch response data:', data)

      // Also test with API service
      const serviceResponse = await RecurringTransactionApiService.getRecurringTransactions({})
      console.log('API service response:', serviceResponse)
    } catch (error) {
      console.error('Manual test API error:', error)
    }
  }

  // Statistics
  const totalTransactions = recurringTransactions.length
  const activeTransactions = recurringTransactions.filter(t => t.isActive).length
  const incomeTransactions = recurringTransactions.filter(t => t.kind === "income").length
  const expenseTransactions = recurringTransactions.filter(t => t.kind === "expense").length

  // Filter and sort data
  const filteredAndSortedTransactions = recurringTransactions
    .filter(transaction => {
      const matchesType = typeFilter === "all" || transaction.kind === typeFilter
      const matchesStatus = statusFilter === "all" ||
        (statusFilter === "active" && transaction.isActive) ||
        (statusFilter === "paused" && !transaction.isActive)
      const matchesSearch = !searchTerm ||
        transaction.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.description?.toLowerCase().includes(searchTerm.toLowerCase())
      return matchesType && matchesStatus && matchesSearch
    })
    .sort((a, b) => {
      let aValue: any = a[sortField]
      let bValue: any = b[sortField]

      if (sortField === "startDate") {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      } else if (sortField === "amount") {
        aValue = Number(aValue)
        bValue = Number(bValue)
      } else if (sortField === "isActive") {
        aValue = aValue ? 1 : 0
        bValue = bValue ? 1 : 0
      }

      if (sortDirection === "asc") {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

  // Search and Sort handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value)
  }

  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
    setShowSortOptions(false)
  }

  const getSortLabel = (field: typeof sortField) => {
    switch (field) {
      case "name": return "Tên"
      case "amount": return "Số tiền"
      case "frequency": return "Tần suất"
      case "startDate": return "Ngày bắt đầu"
      case "isActive": return "Trạng thái"
      default: return field
    }
  }

  const handleAddRecurring = () => {
    setSelectedTransaction(null)
    setIsEditing(false)
    setSidebarOpen(true)
  }

  const handleEditRecurring = (transaction: any) => {
    setSelectedTransaction(transaction)
    setIsEditing(true)
    setSidebarOpen(true)
  }

  const handleCloseSidebar = () => {
    setSidebarOpen(false)
    setSelectedTransaction(null)
    setIsEditing(false)
  }

  const handleToggleStatus = async (transaction: any) => {
    try {
      setLoading(true)
      const response = await RecurringTransactionApiService.toggleRecurringTransactionStatus(
        transaction.id,
        !transaction.isActive
      )
      if (response.success) {
        // Refresh recurring transactions list
        const recurringResponse = await RecurringTransactionApiService.getRecurringTransactions({})
        if (recurringResponse.success) {
          setRecurringTransactions(recurringResponse.data || [])
        }
        setError(null)
        console.log(`Giao dịch định kỳ đã được ${!transaction.isActive ? 'kích hoạt' : 'tạm dừng'} thành công`)
      } else {
        setError(typeof response.error === 'string' ? response.error : 'Không thể cập nhật trạng thái giao dịch định kỳ. Vui lòng thử lại.')
      }
    } catch (err: any) {
      setError(err?.message || 'Không thể cập nhật trạng thái giao dịch định kỳ. Vui lòng thử lại.')
      console.error('Toggle recurring transaction status error:', err)
    } finally {
      setLoading(false)
    }
  }



  const handleSaveRecurring = async (transaction: any) => {
    try {
      // Validate required fields
      if (!transaction.name?.trim()) {
        setError('Vui lòng nhập tên giao dịch định kỳ')
        return
      }

      if (!transaction.amount || isNaN(Number(transaction.amount)) || Number(transaction.amount) <= 0) {
        setError('Vui lòng nhập số tiền hợp lệ')
        return
      }

      if (!transaction.frequency) {
        setError('Vui lòng chọn tần suất')
        return
      }

      if (!transaction.startDate) {
        setError('Vui lòng chọn ngày bắt đầu')
        return
      }

      if (transaction.kind !== 'transfer' && !transaction.accountId) {
        setError('Vui lòng chọn tài khoản')
        return
      }

      if (transaction.kind === 'transfer' && (!transaction.fromAccountId || !transaction.toAccountId)) {
        setError('Vui lòng chọn tài khoản nguồn và tài khoản đích')
        return
      }

      setLoading(true)

      // Prepare transaction data
      const transactionData = {
        name: transaction.name.trim(),
        amount: Number(transaction.amount),
        kind: transaction.kind,
        frequency: transaction.frequency,
        startDate: transaction.startDate,
        endDate: transaction.endDate || undefined,
        description: transaction.description?.trim() || '',
        isActive: Boolean(transaction.isActive !== false)
      }

      // Add specific fields based on transaction type
      if (transaction.kind === 'transfer') {
        Object.assign(transactionData, {
          fromAccountId: transaction.fromAccountId,
          toAccountId: transaction.toAccountId
        })
      } else {
        Object.assign(transactionData, {
          accountId: transaction.accountId,
          categoryId: transaction.categoryId || null,
          contactId: transaction.contactId || null
        })
      }

      let response
      if (isEditing) {
        response = await RecurringTransactionApiService.updateRecurringTransaction(transaction.id, transactionData)
      } else {
        response = await RecurringTransactionApiService.createRecurringTransaction(transactionData as any)
      }

      if (response.success) {
        // Refresh recurring transactions list
        const recurringResponse = await RecurringTransactionApiService.getRecurringTransactions({})
        if (recurringResponse.success) {
          setRecurringTransactions(recurringResponse.data || [])
        }
        setSidebarOpen(false)
        setError(null)
        console.log(`Giao dịch định kỳ đã được ${isEditing ? 'cập nhật' : 'tạo'} thành công`)
      } else {
        setError(typeof response.error === 'string' ? response.error : 'Không thể lưu giao dịch định kỳ. Vui lòng thử lại.')
      }
    } catch (error: any) {
      console.error('Error saving recurring transaction:', error)
      setError(error?.message || 'Không thể lưu giao dịch định kỳ. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <ListPageLayout
        title="Giao dịch định kỳ"
        subtitle="Quản lý các giao dịch tự động định kỳ"
        stats={[
          {
            label: "Tổng số",
            value: totalTransactions.toString(),
            icon: <RefreshCw size={14} className="text-blue-500" />
          },
          {
            label: "Đang hoạt động",
            value: activeTransactions.toString(),
            icon: <Play size={14} className="text-green-500" />
          },
          {
            label: "Tạm dừng",
            value: (totalTransactions - activeTransactions).toString(),
            icon: <Pause size={14} className="text-gray-500" />
          }
        ]}
        actions={
          <div className="flex gap-2">
           
            <Button onClick={handleAddRecurring} className="flex items-center gap-2">
              <Plus size={16} />
              Thêm mới
            </Button>
          </div>
        }
      >
        {/* Search, Filter and Sort Row */}
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3 mb-4">
          <div className="flex flex-wrap items-center gap-2">
            {/* Type Filter Tabs */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-0.5 h-8">
              {[
                { value: "all", label: "Tất cả", count: totalTransactions },
                { value: "income", label: "Thu nhập", count: incomeTransactions },
                { value: "expense", label: "Chi tiêu", count: expenseTransactions }
              ].map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => setTypeFilter(filter.value as "all" | "income" | "expense")}
                  className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 h-full flex items-center ${
                    typeFilter === filter.value
                      ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as typeof statusFilter)}
              className="px-3 py-1 h-8 text-xs border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-gray-100 transition-all duration-200"
            >
              <option value="all">Tất cả trạng thái</option>
              <option value="active">Đang hoạt động</option>
              <option value="paused">Tạm dừng</option>
            </select>

            {/* Search */}
            <div className="flex-1 min-w-[200px] relative">
              <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm giao dịch định kỳ..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full h-8 pl-9 pr-3 bg-white/80 dark:bg-gray-700/80 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 dark:text-gray-100 text-xs transition-all duration-200"
              />
            </div>

            {/* Transaction Count */}
            <div className="flex items-center justify-center px-3 py-1 h-8 bg-gray-50 dark:bg-gray-700/50 rounded-lg text-xs text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
              {filteredAndSortedTransactions.length} giao dịch
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                ref={sortButtonRef}
                onClick={() => setShowSortOptions(!showSortOptions)}
                className="flex items-center gap-1 px-3 py-1 h-8 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 bg-white/80 dark:bg-gray-700/80 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg transition-all duration-200"
              >
                {sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />}
                <span className="whitespace-nowrap">Sắp xếp</span>
                {showSortOptions ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
              </button>

              <PortalDropdown
                isOpen={showSortOptions}
                onClose={() => setShowSortOptions(false)}
                triggerRef={sortButtonRef}
              >
                {(["name", "amount", "frequency", "startDate", "isActive"] as const).map((field) => (
                  <button
                    key={field}
                    onClick={() => handleSort(field)}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                      sortField === field
                        ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
                        : "text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      {getSortLabel(field)}
                      {sortField === field && (
                        sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                      )}
                    </div>
                  </button>
                ))}
              </PortalDropdown>
            </div>
          </div>
        </div>

        {/* Recurring Transactions List */}
        <GenericList
          data={filteredAndSortedTransactions}
          renderCard={(transaction) => (
            <RecurringTransactionCard
              transaction={transaction}
              categories={categories}
              onEdit={() => handleEditRecurring(transaction)}

              onToggleStatus={() => handleToggleStatus(transaction)}
            />
          )}
          gridCols={{ default: 1, sm: 2, lg: 3, xl: 4 }}
          emptyMessage={
            filteredAndSortedTransactions.length === 0 && recurringTransactions.length > 0
              ? `Không tìm thấy giao dịch định kỳ nào phù hợp với bộ lọc hiện tại`
              : typeFilter === "all"
              ? "Chưa có giao dịch định kỳ nào. Hãy tạo giao dịch định kỳ đầu tiên!"
              : `Chưa có giao dịch định kỳ ${typeFilter === "income" ? "thu nhập" : "chi tiêu"} nào. Hãy tạo giao dịch mới!`
          }
          searchable={false}
          sortable={false}
          paginated={true}
          defaultItemsPerPage={25}
          itemsPerPageOptions={[10, 25, 50, 100]}
        />
      </ListPageLayout>

      {/* Sidebar */}
      <RecurringSidebar
        isOpen={sidebarOpen}
        onClose={handleCloseSidebar}
        onSave={handleSaveRecurring}

        recurring={selectedTransaction}
        isEditing={isEditing}
        categories={categories.map(cat => ({
          ...cat,
          description: cat.description || cat.name,
          logo: cat.icon || ''
        }))}
      />

      {/* Toast Notifications */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 animate-fade-in">
          <div className="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="flex-1">{error}</span>
            <button
              onClick={() => setError(null)}
              className="text-white hover:text-gray-200 p-1"
              aria-label="Đóng"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Global Loading Indicator */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 flex items-center gap-3 shadow-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-700 dark:text-gray-200 text-sm font-medium">Đang xử lý...</span>
          </div>
        </div>
      )}
    </>
  )
}

export default function RecurringPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <RecurringPageContent />
    </ProtectedRoute>
  )
}