"use client"

import React, { useState, useMemo, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import { FilterSystemProps, FilterDefinition, SortOption } from '@/types/ui'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { PortalDropdown } from '@/components/ui/PortalDropdown'
import { 
  Search, Filter, SortAsc, SortDesc, ChevronDown, ChevronUp, X, 
  Calendar, DollarSign, Tag, Building2 
} from 'lucide-react'

/**
 * FilterSystem - Unified filter, search, and sort system for all list pages
 */
export function FilterSystem<T>({
  data,
  onFilteredData,
  config,
  compact = false,
  className
}: FilterSystemProps<T>) {
  
  // State management
  const [searchTerm, setSearchTerm] = useState('')
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({})
  const [sortBy, setSortBy] = useState(config.defaultSort)
  const [showSortOptions, setShowSortOptions] = useState(false)
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  
  // Refs for dropdowns
  const sortButtonRef = useRef<HTMLButtonElement>(null)
  const filterButtonRef = useRef<HTMLButtonElement>(null)

  // Debounced search
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    
    return () => clearTimeout(timer)
  }, [searchTerm])

  // Filter and sort data
  const filteredData = useMemo(() => {
    let result = [...data]
    
    // Apply search
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase()
      result = result.filter(item => 
        config.searchFields.some(field => {
          const value = item[field]
          if (value == null) return false
          return String(value).toLowerCase().includes(searchLower)
        })
      )
    }
    
    // Apply filters
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== '') {
        const filter = config.filters.find(f => f.key === key)
        if (filter) {
          result = result.filter(item => filter.predicate(item, value))
        }
      }
    })
    
    // Apply sorting
    if (sortBy) {
      result.sort(sortBy.compareFn)
    }
    
    return result
  }, [data, debouncedSearchTerm, activeFilters, sortBy, config])

  // Update parent component with filtered data
  useEffect(() => {
    onFilteredData(filteredData)
  }, [filteredData, onFilteredData])

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('')
    setActiveFilters({})
    setSortBy(config.defaultSort)
  }

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return searchTerm !== '' || 
           Object.values(activeFilters).some(value => value && value !== 'all' && value !== '') ||
           sortBy !== config.defaultSort
  }, [searchTerm, activeFilters, sortBy, config.defaultSort])

  // Render filter control based on type
  const renderFilterControl = (filter: FilterDefinition<T>) => {
    const value = activeFilters[filter.key] || filter.defaultValue || ''

    switch (filter.type) {
      case 'select':
        return (
          <select
            value={value}
            onChange={(e) => setActiveFilters(prev => ({ ...prev, [filter.key]: e.target.value }))}
            className={cn(
              'px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-lg',
              'focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500',
              'bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-gray-100',
              'transition-all duration-200',
              compact ? 'h-7' : 'h-8'
            )}
          >
            <option value="">Tất cả {filter.label.toLowerCase()}</option>
            {filter.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label} {option.count !== undefined ? `(${option.count})` : ''}
              </option>
            ))}
          </select>
        )

      case 'multiselect':
        // Simplified multiselect - would need more complex implementation
        return (
          <select
            value={Array.isArray(value) ? value[0] || '' : value}
            onChange={(e) => setActiveFilters(prev => ({ ...prev, [filter.key]: [e.target.value] }))}
            className={cn(
              'px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-lg',
              'focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500',
              'bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-gray-100',
              'transition-all duration-200',
              compact ? 'h-7' : 'h-8'
            )}
          >
            <option value="">Chọn {filter.label.toLowerCase()}</option>
            {filter.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )

      case 'date':
        return (
          <input
            type="date"
            value={value}
            onChange={(e) => setActiveFilters(prev => ({ ...prev, [filter.key]: e.target.value }))}
            className={cn(
              'px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-lg',
              'focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500',
              'bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-gray-100',
              'transition-all duration-200',
              compact ? 'h-7' : 'h-8'
            )}
          />
        )

      case 'number':
        return (
          <input
            type="number"
            placeholder={`${filter.label}...`}
            value={value}
            onChange={(e) => setActiveFilters(prev => ({ ...prev, [filter.key]: e.target.value }))}
            className={cn(
              'px-3 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-lg',
              'focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500',
              'bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-gray-100',
              'transition-all duration-200',
              compact ? 'h-7 w-24' : 'h-8 w-28'
            )}
          />
        )

      default:
        return null
    }
  }

  if (compact) {
    return (
      <div className={cn(
        'bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-2',
        className
      )}>
        <div className="flex items-center gap-2 flex-wrap">
          {/* Search */}
          <div className="flex-1 min-w-[200px] relative">
            <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Tìm kiếm..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="h-7 pl-9 pr-3 text-xs"
            />
          </div>

          {/* Quick filters */}
          {config.filters.slice(0, 2).map(filter => (
            <div key={filter.key}>
              {renderFilterControl(filter)}
            </div>
          ))}

          {/* Sort */}
          <div className="relative">
            <Button
              ref={sortButtonRef}
              variant="outline"
              size="sm"
              onClick={() => setShowSortOptions(!showSortOptions)}
              className="h-7 px-2 text-xs"
            >
              {sortBy.key.includes('asc') ? <SortAsc size={12} /> : <SortDesc size={12} />}
              <ChevronDown size={12} className="ml-1" />
            </Button>

            <PortalDropdown
              isOpen={showSortOptions}
              onClose={() => setShowSortOptions(false)}
              triggerRef={sortButtonRef}
            >
              {config.sortOptions.map(option => (
                <button
                  key={option.key}
                  onClick={() => {
                    setSortBy(option)
                    setShowSortOptions(false)
                  }}
                  className={cn(
                    'w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700',
                    sortBy.key === option.key ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''
                  )}
                >
                  {option.label}
                </button>
              ))}
            </PortalDropdown>
          </div>

          {/* Clear filters */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="h-7 px-2 text-xs text-gray-500 hover:text-gray-700"
            >
              <X size={12} />
            </Button>
          )}

          {/* Results count */}
          <div className="text-xs text-gray-500 px-2">
            {filteredData.length} kết quả
          </div>
        </div>
      </div>
    )
  }

  // Full filter system (non-compact)
  return (
    <div className={cn(
      'bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3',
      className
    )}>
      {/* Search bar */}
      <div className="flex items-center gap-2 mb-3">
        <div className="flex-1 relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            type="text"
            placeholder="Tìm kiếm..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4"
          />
        </div>
        
        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={clearAllFilters}
            className="flex items-center gap-2"
          >
            <X size={16} />
            Xóa bộ lọc
          </Button>
        )}
      </div>

      {/* Filters and sort */}
      <div className="flex flex-wrap items-center gap-2">
        {config.filters.map(filter => (
          <div key={filter.key} className="flex items-center gap-1">
            <label className="text-xs text-gray-600 dark:text-gray-400 whitespace-nowrap">
              {filter.label}:
            </label>
            {renderFilterControl(filter)}
          </div>
        ))}

        {/* Sort control */}
        <div className="relative ml-auto">
          <Button
            ref={sortButtonRef}
            variant="outline"
            onClick={() => setShowSortOptions(!showSortOptions)}
            className="flex items-center gap-2"
          >
            {sortBy.key.includes('desc') ? <SortDesc size={16} /> : <SortAsc size={16} />}
            Sắp xếp: {sortBy.label}
            {showSortOptions ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </Button>

          <PortalDropdown
            isOpen={showSortOptions}
            onClose={() => setShowSortOptions(false)}
            triggerRef={sortButtonRef}
          >
            {config.sortOptions.map(option => (
              <button
                key={option.key}
                onClick={() => {
                  setSortBy(option)
                  setShowSortOptions(false)
                }}
                className={cn(
                  'w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700',
                  sortBy.key === option.key ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''
                )}
              >
                {option.label}
              </button>
            ))}
          </PortalDropdown>
        </div>

        {/* Results count */}
        <div className="text-sm text-gray-600 dark:text-gray-400 px-3 py-1 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          {filteredData.length} / {data.length} kết quả
        </div>
      </div>
    </div>
  )
}

export default FilterSystem
