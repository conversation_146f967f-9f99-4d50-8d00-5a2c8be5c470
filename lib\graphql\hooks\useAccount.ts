/**
 * Account GraphQL Hooks
 * 
 * React hooks for account-related GraphQL operations
 */

import { useQuery, useMutation, useApolloClient } from '@apollo/client'
import {
  GET_ACCOUNTS,
  GET_ACCOUNT_BY_ID,
  GET_ACCOUNT_TYPES,
  GET_ACCOUNT_SUMMARY,
  CREATE_ACCOUNT,
  UPDATE_ACCOUNT,
  DELETE_ACCOUNT
} from '../operations'
import { convertToClientError } from '../errors'
import { extractNodes, extractPaginationInfo, useConnectionData } from '@/lib/utils/connection-helpers'
import type {
  Account,
  AccountConnection,
  CreateAccountInput,
  UpdateAccountInput
} from '../types'

// ==================== QUERY HOOKS ====================

/**
 * Hook to get accounts with pagination and connection helpers
 */
export function useAccounts(variables?: {
  first?: number
  after?: string
  search?: string
  accountTypeId?: string
  isActive?: boolean
}) {
  const queryResult = useQuery(GET_ACCOUNTS, {
    variables,
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.accounts,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.accounts,
    pageInfo: queryResult.data?.accounts?.pageInfo
  }
}

/**
 * Hook to get account by ID
 */
export function useAccount(id: string) {
  return useQuery(GET_ACCOUNT_BY_ID, {
    variables: { id },
    errorPolicy: 'all',
    skip: !id
  })
}

/**
 * Hook to get account types
 */
export function useAccountTypes() {
  return useQuery(GET_ACCOUNT_TYPES, {
    errorPolicy: 'all'
  })
}

/**
 * Hook to get account summary
 */
export function useAccountSummary() {
  return useQuery(GET_ACCOUNT_SUMMARY, {
    errorPolicy: 'all'
  })
}

// ==================== MUTATION HOOKS ====================

/**
 * Hook to create account
 */
export function useCreateAccount() {
  return useMutation(CREATE_ACCOUNT, {
    refetchQueries: ['GetAccounts', 'GetAccountSummary'],
    onError: (error) => {
      console.error('Create account error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to update account
 */
export function useUpdateAccount() {
  return useMutation(UPDATE_ACCOUNT, {
    refetchQueries: ['GetAccounts', 'GetAccountById', 'GetAccountSummary'],
    onError: (error) => {
      console.error('Update account error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to delete account
 */
export function useDeleteAccount() {
  return useMutation(DELETE_ACCOUNT, {
    refetchQueries: ['GetAccounts', 'GetAccountSummary'],
    onError: (error) => {
      console.error('Delete account error:', convertToClientError(error))
    }
  })
}

// ==================== UTILITY HOOKS ====================

/**
 * Hook to refresh account data
 */
export function useRefreshAccounts() {
  const client = useApolloClient()
  
  return () => {
    client.refetchQueries({
      include: ['GetAccounts', 'GetAccountSummary', 'GetAccountTypes']
    })
  }
}

/**
 * Hook to clear account cache
 */
export function useClearAccountCache() {
  const client = useApolloClient()
  
  return () => {
    client.cache.evict({ fieldName: 'accounts' })
    client.cache.evict({ fieldName: 'accountTypes' })
    client.cache.gc()
  }
}
