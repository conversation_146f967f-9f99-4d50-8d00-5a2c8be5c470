import { useState, useEffect, useCallback } from 'react'
import { TransactionApiService } from '@/services/client'
import type { Transaction } from '@/lib/generated/prisma'
import type { TransactionQueryParams, CreateTransactionRequest, UpdateTransactionRequest } from '@/types/api'

interface UseTransactionsReturn {
  transactions: Transaction[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createTransaction: (data: CreateTransactionRequest) => Promise<boolean>
  updateTransaction: (id: number, data: UpdateTransactionRequest) => Promise<boolean>
  deleteTransaction: (id: number) => Promise<boolean>
}

export function useTransactions(params?: TransactionQueryParams): UseTransactionsReturn {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchTransactions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await TransactionApiService.getTransactions(params)
      
      if (response.success) {
        setTransactions(response.data)
      } else {
        setError('Failed to fetch transactions')
      }
    } catch (err) {
      setError('Error fetching transactions')
      console.error('Error fetching transactions:', err)
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    fetchTransactions()
  }, [fetchTransactions])

  const createTransaction = useCallback(async (data: CreateTransactionRequest): Promise<boolean> => {
    try {
      const response = await TransactionApiService.createTransaction(data)
      if (response.success) {
        await fetchTransactions() // Refresh list
        return true
      } else {
        setError('Failed to create transaction')
        return false
      }
    } catch (err) {
      setError('Error creating transaction')
      console.error('Error creating transaction:', err)
      return false
    }
  }, [fetchTransactions])

  const updateTransaction = useCallback(async (id: number, data: UpdateTransactionRequest): Promise<boolean> => {
    try {
      const response = await TransactionApiService.updateTransaction(String(id), data)
      if (response.success) {
        // Update local state optimistically
        setTransactions(prev => prev.map(txn => 
          txn.id === id ? { ...txn, ...data, updatedAt: new Date() } : txn
        ))
        return true
      } else {
        setError('Failed to update transaction')
        return false
      }
    } catch (err) {
      setError('Error updating transaction')
      console.error('Error updating transaction:', err)
      return false
    }
  }, [])

  const deleteTransaction = useCallback(async (id: number): Promise<boolean> => {
    try {
      const response = await TransactionApiService.deleteTransaction(String(id))
      if (response.success) {
        // Remove from local state
        setTransactions(prev => prev.filter(txn => txn.id !== id))
        return true
      } else {
        setError('Failed to delete transaction')
        return false
      }
    } catch (err) {
      setError('Error deleting transaction')
      console.error('Error deleting transaction:', err)
      return false
    }
  }, [])

  return {
    transactions,
    loading,
    error,
    refetch: fetchTransactions,
    createTransaction,
    updateTransaction,
    deleteTransaction
  }
}

export default useTransactions
