"use client"

import { getSidebarConfig } from "@/config/sidebarConfig"
import { CategoryBase, CategoryGoalBase, CategoryGoalWithRelations, CategoryType, CategoryWithGoal, Period } from "@/lib/graphql/types"
import { Calendar, CalendarDays, DollarSign, FileText, Palette, Repeat, Tag, TrendingUp, Type } from "lucide-react"
import React, { useEffect, useState } from "react"
import { BaseSidebar } from "./BaseSidebar"
import { SearchableCombobox } from "./SearchableCombobox"
import { SidebarActionFooter } from "./SidebarFooter"
import { SidebarForm, SidebarInputField, SidebarTextareaField } from "./SidebarForm"
import { Omit } from "@/lib/generated/prisma/runtime/library"
import { FormCategory } from "./form-types"

// Define form data type that includes both category and goal fields for easier form handling


interface CategorySidebarProps {
  isOpen: boolean
  onClose: () => any
  onSave: (category: FormCategory) => void
  category?: CategoryWithGoal | null
  isEditing: boolean
}




interface FormErrors {
  name?: string
  type?: string
  color?: string
  targetAmount?: string
  startDate?: string
  endDate?: string
}

const CATEGORY_TYPES = [
  { value: "expense", label: "Chi tiêu" },
  { value: "income", label: "Thu nhập" }
]

const PREDEFINED_COLORS = [
  "#3B82F6", "#EF4444", "#10B981", "#F59E0B", "#8B5CF6",
  "#EC4899", "#06B6D4", "#84CC16", "#F97316", "#6366F1",
  "#64748B", "#0F172A", "#7C2D12", "#166534", "#7C3AED"
]

 // Create default form data with proper structure
  const createDefaultFormData = (cat?: CategoryWithGoal | null): FormCategory => {
    if (!cat) {
      return {
        id: '',
        name: '',
        description: '',
        type: CategoryType.EXPENSE ,
        color: '#3B82F6',
        icon: '',
        parentId: '',
        isActive: true,

        categoryGoal: {
          isRecurring: false,
          targetAmount: 0,
          period: Period.NONE,
          startDate: '',
          endDate: '',
          isActive: true,
          
        }
      }
    }

    return {
      ...cat,
    }
  }

export function CategorySidebar({ isOpen, onClose, onSave, category, isEditing }: CategorySidebarProps) {
  const config = getSidebarConfig('CATEGORY')

 

  const [formData, setFormData] = useState<FormCategory>(createDefaultFormData(category))
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen) {
      if (category && isEditing) {
        setFormData(createDefaultFormData(category))
      } else {
        setFormData(createDefaultFormData(null))
      }
      setErrors({})
    }
  }, [isOpen, category, isEditing])

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}
    if (!formData.name.trim()) newErrors.name = "Tên danh mục là bắt buộc"
    if (!formData.type) newErrors.type = "Loại danh mục là bắt buộc"
    if (!formData.color) newErrors.color = "Màu sắc là bắt buộc"

    if (formData.categoryGoal?.targetAmount && formData.categoryGoal.targetAmount > 0) {
      if (formData.categoryGoal.startDate && formData.categoryGoal.endDate) {
        const s = new Date(formData.categoryGoal.startDate)
        const e = new Date(formData.categoryGoal.endDate)
        if (s > e) newErrors.endDate = "Ngày kết thúc phải sau ngày bắt đầu"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  type CategoryGoalKey = keyof NonNullable<FormCategory['categoryGoal']>;
  type NestedField = `categoryGoal.${CategoryGoalKey}`; // "categoryGoal.isRecurring" | ...
  type Field = keyof Omit<FormCategory, "id" | "categoryGoal"> | NestedField;
  const handleInputChange = (field: Field, value: string | boolean) => {
    let processed: any = value

      // Handle specific field types
   
    if (field.startsWith('categoryGoal.')) {
      const goalField = field.replace('categoryGoal.', '') as CategoryGoalKey
      if (goalField === 'isRecurring') {
        processed = Boolean(value)
      } else if (goalField === 'targetAmount') {
        processed = value === '' ? 0 : Number(value)
      } 
      setFormData(prev => ({
        ...prev,
        categoryGoal: {
          ...prev.categoryGoal as CategoryGoalBase,
          [goalField]: processed,
        },
      }))
    }else 
    setFormData(prev => ({
      ...prev,
      [field]: processed,  
    }))

    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field as keyof FormErrors]: undefined }))
    }
  }

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault()
    if (!validateForm()) return
    setLoading(true)
    try {
     

      onSave(formData)
      handleClose()
    } catch (err) {
      console.error('Error saving category:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData(createDefaultFormData(null))
    setErrors({})
    onClose()
  }

  const isAmountEntered = Boolean(formData.categoryGoal?.targetAmount && formData.categoryGoal.targetAmount !== 0)

  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa danh mục" : "Thêm danh mục mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${category?.name}` : "Tạo danh mục mới cho giao dịch"}
      storageKey="categorySidebarWidth"
      config={config}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={handleSubmit}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={!formData.name.trim()}
          sidebarWidth={400}
        />
      }
    >
      <SidebarForm onSubmit={handleSubmit} sidebarWidth={400} isOpen={isOpen} isResizing={false}>
        <SidebarInputField
          label="Tên danh mục"
          value={formData.name}
          onChange={val => handleInputChange('name', val)}
          placeholder="Nhập tên danh mục"
          required
          error={errors.name}
          icon={<Tag size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />

        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <Type size={14} className="mr-2" /> Loại danh mục *
          </label>
          <SearchableCombobox
            value={formData.type}
            onChange={val => handleInputChange('type', val as any)}
            options={CATEGORY_TYPES}
            placeholder="Chọn loại danh mục..."
            disabled={loading}
          />
          {errors.type && <p className="text-xs text-red-500">{errors.type}</p>}
        </div>

        <div className="col-span-full">
          <label className="block text-xs font-medium text-gray-700 mb-2">
            <Palette size={14} className="inline mr-1" /> Màu sắc
          </label>
          <div className="grid grid-cols-5 gap-2">
            {PREDEFINED_COLORS.map(c => (
              <button
                key={c}
                type="button"
                onClick={() => handleInputChange('color', c)}
                className={`w-8 h-8 rounded-lg border-2 transition-all ${
                  formData.color === c
                    ? 'border-gray-900 ring-2 ring-blue-500/20'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                style={{ backgroundColor: c }}
                disabled={loading}
              />
            ))}
          </div>
          {errors.color && <p className="text-xs text-red-500 mt-1">{errors.color}</p>}
        </div>

        <SidebarInputField
          label={formData.type === 'expense' ? 'Ngân sách (tùy chọn)' : 'Mục tiêu thu nhập (tùy chọn)'}
          value={formData.categoryGoal?.targetAmount?.toString() || ''}
          onChange={val => handleInputChange('categoryGoal.targetAmount', val)}
          placeholder="0"
          type="number"
          error={errors.targetAmount || ""}
          icon={formData.type === 'expense' ? <DollarSign size={14} /> : <TrendingUp size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />

        {isAmountEntered && (
          <>
            <SidebarInputField
              label="Ngày bắt đầu"
              type="date"
              value={formData.categoryGoal?.startDate || ""} 
              onChange={val => handleInputChange('categoryGoal.startDate', val)}
              error={errors.startDate}
              icon={<CalendarDays size={14} />}
              disabled={loading}
              span="full"
              sidebarWidth={400}
            />
            <SidebarInputField
              label="Ngày kết thúc"
              type="date"
              value={formData.categoryGoal?.endDate || ""}
              onChange={val => handleInputChange('categoryGoal.endDate', val)}
              error={errors.endDate}
              icon={<CalendarDays size={14} />}
              disabled={loading}
              span="full"
              sidebarWidth={400}
            />

            <div className="col-span-full">
              <label className="flex items-center gap-2 text-xs font-medium text-gray-700 mb-2">
                <input
                  type="checkbox"
                  checked={formData.categoryGoal?.isRecurring}
                  onChange={e => handleInputChange('categoryGoal.isRecurring', e.target.checked)}
                  className="rounded border-gray-300"
                  disabled={loading}
                />
                <Repeat size={14} /> Lặp lại {formData.type === 'expense' ? 'ngân sách' : 'mục tiêu'}
              </label>
            </div>

            {formData.categoryGoal?.isRecurring && (
              <>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <Calendar size={14} className="mr-2" /> Kiểu lặp lại
                  </label>
                  <SearchableCombobox
                    value={formData.categoryGoal?.period || ''}
                    onChange={val => handleInputChange('categoryGoal.period', val)}
                    options={[
                      { value: 'monthly', label: 'Hàng tháng' },
                      { value: 'yearly', label: 'Hàng năm' },
                      { value: 'custom', label: 'Tùy chỉnh' }
                    ]}
                    placeholder="Chọn kiểu lặp lại..."
                    disabled={loading}
                  />
                </div>
              </>
            )}
          </>
        )}

        <SidebarTextareaField
          label="Mô tả"
          value={formData.description || ''}
          onChange={val => handleInputChange('description', val)}
          placeholder="Mô tả chi tiết về danh mục (tùy chọn)"
          rows={3}
          icon={<FileText size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />
      </SidebarForm>
    </BaseSidebar>
  )
}
