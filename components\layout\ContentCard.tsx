"use client"

import { ReactNode } from "react"

interface ContentCardProps {
  children: ReactNode
  title?: string
  subtitle?: string
  headerActions?: ReactNode
  className?: string
  variant?: "default" | "glass" | "solid"
  padding?: "none" | "sm" | "md" | "lg"
}

export function ContentCard({ 
  children, 
  title, 
  subtitle,
  headerActions,
  className = "",
  variant = "glass",
  padding = "md"
}: ContentCardProps) {
  const baseClasses = "rounded-3xl border shadow-xl overflow-hidden"
  
  const variantClasses = {
    default: "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700",
    glass: "bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50",
    solid: "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
  }

  const paddingClasses = {
    none: "",
    sm: "p-3",
    md: "p-4", 
    lg: "p-6"
  }

  const headerPaddingClasses = {
    none: "",
    sm: "p-3",
    md: "p-4",
    lg: "p-6"
  }

  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {(title || subtitle || headerActions) && (
        <div className={`border-b border-gray-200/50 dark:border-gray-700/50 ${headerPaddingClasses[padding]} flex items-center justify-between`}>
          <div>
            {title && (
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{title}</h2>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{subtitle}</p>
            )}
          </div>
          {headerActions && (
            <div className="flex items-center gap-2">
              {headerActions}
            </div>
          )}
        </div>
      )}
      <div className={title || subtitle || headerActions ? "" : paddingClasses[padding]}>
        {children}
      </div>
    </div>
  )
}
