"use client"

import React from "react"
import { Edit, Trash2, ArrowUpRight, ArrowDownRight, Calendar, CreditCard, Tag } from "lucide-react"
import { TransactionColumn, isColumnVisible, generateGridTemplate } from "../../config/transactionTableConfig"
import { TruncatedText } from "./TruncatedText"

interface Transaction {
  id: number
  date: string
  description: string
  category: string
  categoryColor?: string
  categoryIcon?: string
  account: string
  transferFrom: string
  type: "income" | "expense" | "transfer"
  amount: string
  rawAmount: number
  transferTo?: string
}

interface TransactionCardProps {
  transaction: Transaction
  onEdit?: (transaction: Transaction) => void
  visibleColumns?: TransactionColumn[]
  screenWidth?: number
}

export function TransactionCard({
  transaction,
  onEdit,
  visibleColumns,
  screenWidth = 1024
}: TransactionCardProps) {
  // Generate grid template from visible columns
  const gridTemplate = visibleColumns ? generateGridTemplate(visibleColumns) : "2fr 2fr 2fr 2fr 2fr 1fr 1fr"
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    // Format time part (hours:minutes)
    const timeString = date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false // Use 24-hour format
    })

    if (date.toDateString() === today.toDateString()) {
      return `Hôm nay ${timeString}`
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `Hôm qua ${timeString}`
    } else {
      const dateString = date.toLocaleDateString("vi-VN", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      })
      return `${dateString} ${timeString}`
    }
  }

  return (
    <div className="group bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/50 dark:hover:border-gray-600/50 transition-all duration-200">
      {/* Mobile Layout */}
      <div className="block md:hidden p-4 space-y-3">
        {/* Header Row */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <TruncatedText
              text={transaction.description}
              className="font-medium text-gray-900 dark:text-gray-100 text-xs"
              as="h3"
            />
            <div className="flex items-center gap-2 mt-1">
              <Calendar size={12} className="text-gray-400" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formatDate(transaction.date)}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2 ml-3">
            <span className={`text-sm font-semibold ${
              transaction.kind === "income"
                ? "text-green-600 dark:text-green-400"
                : transaction.kind === "expense"
                ? "text-red-600 dark:text-red-400"
                : "text-blue-600 dark:text-blue-400"
            }`}>
              {transaction.kind === "income" ? "+" : transaction.kind === "expense" ? "-" : ""}{transaction.amount}
            </span>
            {transaction.kind === "income" ? (
              <ArrowUpRight size={16} className="text-green-500" />
            ) : transaction.kind === "expense" ? (
              <ArrowDownRight size={16} className="text-red-500" />
            ) : (
              <ArrowUpRight size={16} className="text-blue-500" />
            )}
          </div>
        </div>

        {/* Details Row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Category */}
            {transaction.category && isColumnVisible("category", screenWidth) && (
              <div className="flex items-center gap-1.5">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: transaction.categoryColor || "#6B7280" }}
                />
                <TruncatedText
                  text={transaction.category}
                  className="text-xs text-gray-600 dark:text-gray-400"
                />
              </div>
            )}

            {/* Transfer From */}
            {transaction.transferFrom && isColumnVisible("transferFrom", screenWidth) && (
              <div className="flex items-center gap-1.5">
                <CreditCard size={12} className="text-gray-400" />
                <TruncatedText
                  text={transaction.transferFrom}
                  className="text-xs text-gray-600 dark:text-gray-400"
                />
              </div>
            )}
          </div>

          {/* Transfer To Row */}
          {transaction.transferTo && isColumnVisible("transferTo", screenWidth) && (
            <div className="flex items-center gap-1.5">
              <Tag size={12} className="text-gray-400" />
              <TruncatedText
                text={transaction.transferTo}
                className="text-xs text-gray-600 dark:text-gray-400"
              />
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {onEdit && (
              <button
                onClick={() => onEdit(transaction)}
                className="p-1.5 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                title="Edit transaction"
              >
                <Edit size={14} />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Desktop Layout - Table Style */}
      <div
        className="hidden md:grid gap-3 items-center p-4"
        style={{
          gridTemplateColumns: gridTemplate
        }}
      >
        {/* Render columns dynamically based on visible columns */}
        {visibleColumns?.map((column) => {
          switch (column.key) {
            case "date":
              return (
                <div key="date" className="flex items-center gap-2">
                  <Calendar size={14} className="text-gray-400 flex-shrink-0" />
                  <TruncatedText
                    text={formatDate(transaction.date)}
                    className="text-sm text-gray-600 dark:text-gray-400"
                  />
                </div>
              )

            case "description":
              return (
                <div key="description">
                  <TruncatedText
                    text={transaction.description}
                    className="font-medium text-gray-900 dark:text-gray-100 text-sm"
                    as="h3"
                  />
                </div>
              )

            case "category":
              return (
                <div key="category" className="flex items-center gap-2">
                  {transaction.category ? (
                    <>
                      <div
                        className="w-3 h-3 rounded-full flex-shrink-0"
                        style={{ backgroundColor: transaction.categoryColor || "#6B7280" }}
                      />
                      <TruncatedText
                        text={transaction.category}
                        className="text-sm text-gray-600 dark:text-gray-400"
                      />
                    </>
                  ) : (
                    <span className="text-sm text-gray-400 dark:text-gray-500 italic">
                      -
                    </span>
                  )}
                </div>
              )

            case "transferFrom":
              return (
                <div key="transferFrom" className="flex items-center gap-2">
                  <CreditCard size={14} className="text-gray-400 flex-shrink-0" />
                  <TruncatedText
                    text={transaction.transferFrom || "-"}
                    className="text-sm text-gray-600 dark:text-gray-400"
                  />
                </div>
              )

            case "transferTo":
              return (
                <div key="transferTo">
                  <TruncatedText
                    text={transaction.transferTo || "-"}
                    className="text-sm text-gray-600 dark:text-gray-400"
                  />
                </div>
              )

            case "amount":
              return (
                <div key="amount" className="flex items-center justify-end gap-1">
                  <span className={`text-sm font-semibold ${
                    transaction.kind === "income"
                      ? "text-green-600 dark:text-green-400"
                      : transaction.kind === "expense"
                      ? "text-red-600 dark:text-red-400"
                      : "text-blue-600 dark:text-blue-400"
                  }`}>
                    {transaction.kind === "income" ? "+" : transaction.kind === "expense" ? "-" : ""}{transaction.amount}
                  </span>
                  {transaction.kind === "income" ? (
                    <ArrowUpRight size={14} className="text-green-500 flex-shrink-0" />
                  ) : transaction.kind === "expense" ? (
                    <ArrowDownRight size={14} className="text-red-500 flex-shrink-0" />
                  ) : (
                    <ArrowUpRight size={14} className="text-blue-500 flex-shrink-0" />
                  )}
                </div>
              )

            case "actions":
              return (
                <div key="actions" className="flex items-center justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  {onEdit && (
                    <button
                      onClick={() => onEdit(transaction)}
                      className="p-1.5 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                      title="Edit transaction"
                    >
                      <Edit size={14} />
                    </button>
                  )}

                </div>
              )

            default:
              return null
          }
        })}
      </div>
    </div>
  )
}
