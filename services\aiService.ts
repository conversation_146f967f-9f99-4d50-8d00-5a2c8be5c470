interface AIResponse {
  content: string
  suggestions?: string[]
  quickActions?: Array<{
    label: string
    action: string
  }>
}

export class AIService {
  private static responses = {
    greeting: [
      "Xin chào! Tô<PERSON> là AI Assistant của bạn. Tôi có thể giúp bạn quản lý tài chính hiệu quả hơn.",
      "Chào bạn! Tôi sẵn sàng hỗ trợ bạn với mọi câu hỏi về quản lý tài chính cá nhân.",
      "Hello! Tôi có thể giúp bạn phân tích chi tiêu, lập ngân sách và sử dụng các tính năng của ứng dụng."
    ],
    
    expense: [
      "Để quản lý chi tiêu hiệu quả:\n\n• **Ghi nhận chi tiêu ngay**: Vào Transactions → Add Transaction\n• **Phân loại chi tiêu**: Sử dụng Categories để nhóm các kho<PERSON>n chi\n• **Đặt giới hạn**: Tạo Budget cho từng category\n• **Theo dõi xu hướng**: Xem Dashboard để phân tích\n\n💡 **Mẹo**: Ghi nhận chi tiêu ngay sau khi mua để không quên!",
      "Quản lý chi tiêu thông minh:\n\n🎯 **Nguyên tắc 50/30/20**:\n• 50% cho nhu cầu thiết yếu\n• 30% cho giải trí\n• 20% cho tiết kiệm\n\n📊 **Sử dụng ứng dụng**:\n• Categories để phân loại\n• CategoryGoal để kiểm soát\n• Reports để phân tích"
    ],
    
    income: [
      "Quản lý thu nhập hiệu quả:\n\n💰 **Đa dạng nguồn thu**:\n• Lương chính\n• Thu nhập phụ\n• Đầu tư, lãi suất\n\n📱 **Trong ứng dụng**:\n• Thêm các Accounts cho từng nguồn\n• Ghi nhận Income transactions\n• Thiết lập Recurring cho lương\n• Theo dõi xu hướng trong Dashboard",
      "Tối ưu hóa thu nhập:\n\n🔄 **Tự động hóa**:\n• Thiết lập Recurring transactions cho lương\n• Tự động phân bổ vào các mục tiết kiệm\n\n📈 **Theo dõi tăng trưởng**:\n• So sánh thu nhập theo tháng\n• Phân tích hiệu quả các nguồn thu"
    ],
    
    budget: [
      "Lập ngân sách thông minh:\n\n📋 **Các bước cơ bản**:\n1. Tính tổng thu nhập\n2. Liệt kê chi tiêu cố định\n3. Đặt giới hạn cho từng category\n4. Để dành 20% cho tiết kiệm\n\n🎯 **Trong ứng dụng**:\n• Vào CategoryGoal → Create Budget\n• Đặt limit cho từng category\n• Theo dõi % sử dụng\n• Nhận cảnh báo khi vượt ngân sách",
      "Ngân sách hiệu quả:\n\n⚖️ **Cân bằng thu chi**:\n• Thu nhập > Chi tiêu\n• Dự phòng khẩn cấp 3-6 tháng\n• Đầu tư dài hạn\n\n📊 **Theo dõi thường xuyên**:\n• Review hàng tuần\n• Điều chỉnh khi cần\n• Phân tích xu hướng"
    ]
  }

  private static quickActions = {
    expense: [
      { label: "➕ Thêm chi tiêu", action: "add_expense" },
      { label: "📊 Xem báo cáo", action: "view_reports" },
      { label: "🎯 Đặt ngân sách", action: "set_budget" }
    ],
    income: [
      { label: "💰 Thêm thu nhập", action: "add_income" },
      { label: "🔄 Thiết lập định kỳ", action: "setup_recurring" },
      { label: "📈 Xem xu hướng", action: "view_trends" }
    ],
    budget: [
      { label: "📋 Tạo ngân sách", action: "create_budget" },
      { label: "⚙️ Cài đặt cảnh báo", action: "setup_alerts" },
      { label: "📊 Phân tích chi tiêu", action: "analyze_spending" }
    ],
    contact: [
      { label: "👥 Thêm contact", action: "add_contact" },
      { label: "🏦 Quản lý tài khoản", action: "manage_accounts" },
      { label: "💸 Tạo chuyển khoản", action: "create_transfer" }
    ]
  }

  static generateResponse(userInput: string): AIResponse {
    const input = userInput.toLowerCase()
    
    // Greeting responses
    if (this.isGreeting(input)) {
      return {
        content: this.getRandomResponse(this.responses.greeting),
        suggestions: [
          "Hướng dẫn sử dụng ứng dụng",
          "Tư vấn quản lý chi tiêu",
          "Cách lập ngân sách hiệu quả",
          "Phân tích dữ liệu tài chính"
        ]
      }
    }

    // Expense management
    if (this.isAboutExpense(input)) {
      return {
        content: this.getRandomResponse(this.responses.expense),
        quickActions: this.quickActions.expense,
        suggestions: [
          "Cách phân loại chi tiêu",
          "Thiết lập cảnh báo ngân sách",
          "Phân tích xu hướng chi tiêu"
        ]
      }
    }

    // Income management
    if (this.isAboutIncome(input)) {
      return {
        content: this.getRandomResponse(this.responses.income),
        quickActions: this.quickActions.income,
        suggestions: [
          "Đa dạng hóa nguồn thu",
          "Tự động hóa thu nhập",
          "Theo dõi tăng trưởng thu nhập"
        ]
      }
    }

    // Budget management
    if (this.isAboutBudget(input)) {
      return {
        content: this.getRandomResponse(this.responses.budget),
        quickActions: this.quickActions.budget,
        suggestions: [
          "Nguyên tắc 50/30/20",
          "Cách đặt mục tiêu tiết kiệm",
          "Review và điều chỉnh ngân sách"
        ]
      }
    }

    // Contact management
    if (this.isAboutContact(input)) {
      return {
        content: "Quản lý danh bạ giao dịch:\n\n👥 **Tính năng Contacts**:\n• Lưu thông tin người nhận/gửi tiền\n• Quản lý nhiều tài khoản ngân hàng\n• Tự động điền khi tạo transaction\n• Theo dõi lịch sử giao dịch\n\n🏦 **Multiple Bank Accounts**:\n• Mỗi contact có thể có nhiều tài khoản\n• Đặt tài khoản mặc định\n• Chọn tài khoản cụ thể khi chuyển tiền",
        quickActions: this.quickActions.contact,
        suggestions: [
          "Thêm contact mới",
          "Quản lý tài khoản ngân hàng",
          "Tạo giao dịch với contact"
        ]
      }
    }

    // App usage guide
    if (this.isAboutUsage(input)) {
      return {
        content: "🚀 **Hướng dẫn sử dụng ứng dụng**:\n\n📊 **Dashboard**: Tổng quan tài chính, biểu đồ, thống kê\n💰 **Transactions**: Ghi nhận thu chi hàng ngày\n🏦 **Accounts**: Quản lý tài khoản ngân hàng\n📂 **Categories**: Phân loại thu chi\n🎯 **CategoryGoal**: Đặt và theo dõi ngân sách\n👥 **Contacts**: Quản lý danh bạ giao dịch\n\n💡 **Tips**: Bắt đầu bằng việc thêm accounts và categories!",
        suggestions: [
          "Thiết lập ban đầu",
          "Ghi nhận giao dịch đầu tiên",
          "Tạo ngân sách cơ bản",
          "Phân tích dữ liệu"
        ]
      }
    }

    // Financial analysis
    if (this.isAboutAnalysis(input)) {
      return {
        content: "📈 **Phân tích tài chính cá nhân**:\n\n🔍 **Các chỉ số quan trọng**:\n• Tỷ lệ tiết kiệm (>20%)\n• Tỷ lệ nợ/thu nhập (<30%)\n• Quỹ khẩn cấp (3-6 tháng chi tiêu)\n\n📊 **Sử dụng Dashboard**:\n• Xem xu hướng thu chi\n• So sánh theo tháng/quý\n• Phân tích theo category\n• Theo dõi mục tiêu",
        suggestions: [
          "Cách đọc biểu đồ",
          "Thiết lập mục tiêu tài chính",
          "Tối ưu hóa chi tiêu",
          "Lập kế hoạch đầu tư"
        ]
      }
    }

    // Default response
    return {
      content: "🤖 Tôi có thể giúp bạn với:\n\n💰 **Quản lý tài chính**:\n• Phân tích chi tiêu và thu nhập\n• Lập ngân sách cá nhân\n• Tư vấn tiết kiệm\n\n📱 **Sử dụng ứng dụng**:\n• Hướng dẫn các tính năng\n• Thiết lập ban đầu\n• Mẹo sử dụng hiệu quả\n\nBạn muốn tìm hiểu về vấn đề gì cụ thể?",
      suggestions: [
        "Quản lý chi tiêu",
        "Lập ngân sách",
        "Hướng dẫn sử dụng",
        "Phân tích tài chính"
      ]
    }
  }

  private static isGreeting(input: string): boolean {
    const greetings = ['xin chào', 'chào', 'hello', 'hi', 'hey', 'chào bạn']
    return greetings.some(greeting => input.includes(greeting))
  }

  private static isAboutExpense(input: string): boolean {
    const keywords = ['chi tiêu', 'expense', 'chi phí', 'tiêu tiền', 'mua sắm', 'thanh toán']
    return keywords.some(keyword => input.includes(keyword))
  }

  private static isAboutIncome(input: string): boolean {
    const keywords = ['thu nhập', 'income', 'lương', 'tiền lương', 'kiếm tiền', 'thu tiền']
    return keywords.some(keyword => input.includes(keyword))
  }

  private static isAboutBudget(input: string): boolean {
    const keywords = ['ngân sách', 'budget', 'kế hoạch chi tiêu', 'lập ngân sách', 'giới hạn chi tiêu']
    return keywords.some(keyword => input.includes(keyword))
  }

  private static isAboutContact(input: string): boolean {
    const keywords = ['contact', 'danh bạ', 'người nhận', 'chuyển khoản', 'tài khoản ngân hàng']
    return keywords.some(keyword => input.includes(keyword))
  }

  private static isAboutUsage(input: string): boolean {
    const keywords = ['cách sử dụng', 'hướng dẫn', 'how to', 'làm sao', 'thiết lập', 'bắt đầu']
    return keywords.some(keyword => input.includes(keyword))
  }

  private static isAboutAnalysis(input: string): boolean {
    const keywords = ['phân tích', 'analysis', 'báo cáo', 'thống kê', 'xu hướng', 'biểu đồ']
    return keywords.some(keyword => input.includes(keyword))
  }

  private static getRandomResponse(responses: string[]): string {
    return responses[Math.floor(Math.random() * responses.length)]
  }
}
