/**
 * GraphQL Client Error Handling
 * 
 * Error handling utilities for GraphQL operations
 */

import { ApolloError } from '@apollo/client'

// ==================== ERROR TYPES ====================

export interface GraphQLErrorExtensions {
  code?: string
  field?: string
  resource?: string
}

export interface GraphQLFormattedError {
  message: string
  locations?: Array<{
    line: number
    column: number
  }>
  path?: Array<string | number>
  extensions?: GraphQLErrorExtensions
}

export interface GraphQLErrorInfo {
  message: string
  code?: string
  field?: string
  resource?: string
}

// ==================== ERROR CODES ====================

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHENTICATED: 'UNAUTHENTICATED',
  FORBIDDEN: 'FORBIDDEN',
  CONFLICT: 'CONFLICT',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR'
} as const

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

// ==================== ERROR CLASSES ====================

export class GraphQLClientError extends Error {
  public code: ErrorCode
  public field?: string
  public resource?: string

  constructor(message: string, code: ErrorCode, field?: string, resource?: string) {
    super(message)
    this.name = 'GraphQLClientError'
    this.code = code
    this.field = field
    this.resource = resource
  }
}

export class ValidationError extends GraphQLClientError {
  constructor(message: string, field?: string) {
    super(message, ERROR_CODES.VALIDATION_ERROR, field)
    this.name = 'ValidationError'
  }
}

export class NotFoundError extends GraphQLClientError {
  constructor(resource: string) {
    super(`${resource} not found`, ERROR_CODES.NOT_FOUND, undefined, resource)
    this.name = 'NotFoundError'
  }
}

export class UnauthorizedError extends GraphQLClientError {
  constructor(message: string = 'Authentication required') {
    super(message, ERROR_CODES.UNAUTHENTICATED)
    this.name = 'UnauthorizedError'
  }
}

export class ForbiddenError extends GraphQLClientError {
  constructor(message: string = 'Access denied: insufficient permissions') {
    super(message, ERROR_CODES.FORBIDDEN)
    this.name = 'ForbiddenError'
  }
}

export class ConflictError extends GraphQLClientError {
  constructor(message: string, field?: string) {
    super(message, ERROR_CODES.CONFLICT, field)
    this.name = 'ConflictError'
  }
}

export class NetworkError extends GraphQLClientError {
  constructor(message: string = 'Network error occurred') {
    super(message, ERROR_CODES.NETWORK_ERROR)
    this.name = 'NetworkError'
  }
}

// ==================== ERROR PARSING ====================

/**
 * Parses Apollo GraphQL errors and returns structured error information
 */
export function parseGraphQLError(error: ApolloError): GraphQLErrorInfo[] {
  const errors: GraphQLErrorInfo[] = []

  // Handle GraphQL errors
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    error.graphQLErrors.forEach((gqlError) => {
      const errorInfo: GraphQLErrorInfo = {
        message: gqlError.message,
        code: gqlError.extensions?.code as string,
        field: gqlError.extensions?.field as string,
        resource: gqlError.extensions?.resource as string
      }
      errors.push(errorInfo)
    })
  }

  // Handle network errors
  if (error.networkError) {
    errors.push({
      message: error.networkError.message || 'Network error occurred',
      code: ERROR_CODES.NETWORK_ERROR
    })
  }

  // If no specific errors, return generic error
  if (errors.length === 0) {
    errors.push({
      message: error.message || 'An unknown error occurred',
      code: ERROR_CODES.INTERNAL_ERROR
    })
  }

  return errors
}

/**
 * Converts Apollo error to appropriate client error class
 */
export function convertToClientError(error: ApolloError): GraphQLClientError {
  const errorInfos = parseGraphQLError(error)
  const firstError = errorInfos[0]

  switch (firstError.code) {
    case ERROR_CODES.VALIDATION_ERROR:
      return new ValidationError(firstError.message, firstError.field)
    
    case ERROR_CODES.NOT_FOUND:
      return new NotFoundError(firstError.resource || 'Resource')
    
    case ERROR_CODES.UNAUTHENTICATED:
      return new UnauthorizedError(firstError.message)
    
    case ERROR_CODES.FORBIDDEN:
      return new ForbiddenError(firstError.message)
    
    case ERROR_CODES.CONFLICT:
      return new ConflictError(firstError.message, firstError.field)
    
    case ERROR_CODES.NETWORK_ERROR:
      return new NetworkError(firstError.message)
    
    default:
      return new GraphQLClientError(
        firstError.message,
        ERROR_CODES.INTERNAL_ERROR
      )
  }
}

/**
 * Gets user-friendly error message for display
 */
export function getUserFriendlyErrorMessage(error: ApolloError | GraphQLClientError): string {
  if (error instanceof GraphQLClientError) {
    switch (error.code) {
      case ERROR_CODES.VALIDATION_ERROR:
        return error.field 
          ? `Validation error in ${error.field}: ${error.message}`
          : `Validation error: ${error.message}`
      
      case ERROR_CODES.NOT_FOUND:
        return error.resource 
          ? `${error.resource} not found`
          : 'Resource not found'
      
      case ERROR_CODES.UNAUTHENTICATED:
        return 'Please log in to continue'
      
      case ERROR_CODES.FORBIDDEN:
        return 'You do not have permission to perform this action'
      
      case ERROR_CODES.CONFLICT:
        return error.field
          ? `Conflict in ${error.field}: ${error.message}`
          : `Conflict: ${error.message}`
      
      case ERROR_CODES.NETWORK_ERROR:
        return 'Network error. Please check your connection and try again'
      
      default:
        return 'An unexpected error occurred. Please try again'
    }
  }

  // Handle Apollo errors
  const errorInfos = parseGraphQLError(error as ApolloError)
  const firstError = errorInfos[0]
  
  return getUserFriendlyErrorMessage(convertToClientError(error as ApolloError))
}

/**
 * Checks if error is a specific type
 */
export function isErrorType(error: ApolloError | GraphQLClientError, code: ErrorCode): boolean {
  if (error instanceof GraphQLClientError) {
    return error.code === code
  }

  const errorInfos = parseGraphQLError(error as ApolloError)
  return errorInfos.some(info => info.code === code)
}

/**
 * Checks if error is authentication related
 */
export function isAuthError(error: ApolloError | GraphQLClientError): boolean {
  return isErrorType(error, ERROR_CODES.UNAUTHENTICATED) || 
         isErrorType(error, ERROR_CODES.FORBIDDEN)
}

/**
 * Checks if error is validation related
 */
export function isValidationError(error: ApolloError | GraphQLClientError): boolean {
  return isErrorType(error, ERROR_CODES.VALIDATION_ERROR)
}

/**
 * Checks if error is network related
 */
export function isNetworkError(error: ApolloError | GraphQLClientError): boolean {
  return isErrorType(error, ERROR_CODES.NETWORK_ERROR)
}
