/**
 * Minimal GraphQL Resolvers for Testing
 */

import { GraphQLScalarType } from 'graphql'
import { Kind } from 'graphql/language'

// Scalar resolvers
const scalarResolvers = {
  DateTime: new GraphQLScalarType({
    name: 'DateTime',
    description: 'Date custom scalar type',
    serialize(value: any) {
      return value instanceof Date ? value.toISOString() : value
    },
    parseValue(value: any) {
      return new Date(value)
    },
    parseLiteral(ast) {
      if (ast.kind === Kind.STRING) {
        return new Date(ast.value)
      }
      return null
    }
  }),
  
  UUID: new GraphQLScalarType({
    name: 'UUID',
    description: 'UUID custom scalar type',
    serialize(value: any) {
      return value
    },
    parseValue(value: any) {
      return value
    },
    parseLiteral(ast) {
      if (ast.kind === Kind.STRING) {
        return ast.value
      }
      return null
    }
  }),
  
  JSON: new GraphQLScalarType({
    name: 'J<PERSON><PERSON>',
    description: 'JSON custom scalar type',
    serialize(value: any) {
      return value
    },
    parseValue(value: any) {
      return value
    },
    parseLiteral(ast) {
      return ast
    }
  })
}

export const resolvers = {
  ...scalarResolvers,
  Query: {
    hello: () => 'Hello from GraphQL!',
    health: () => 'OK',
    currentUser: () => null,
    users: () => ({
      edges: [],
      pageInfo: {
        hasNextPage: false,
        hasPreviousPage: false,
        startCursor: null,
        endCursor: null
      },
      totalCount: 0
    })
  },
  Mutation: {
    createUser: () => {
      throw new Error('Not implemented')
    },
    updateUser: () => {
      throw new Error('Not implemented')
    },
    deleteUser: () => {
      throw new Error('Not implemented')
    }
  }
}
