import { NextRequest } from 'next/server'
import { z } from 'zod'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  validateRequest
} from '@/lib/api/utils'
import { AuthService } from '@/services/server'

// ==================== VALIDATION SCHEMAS ====================

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required')
})

// ==================== POST /api/auth/login ====================

export const POST = with<PERSON>rror<PERSON><PERSON>ler(async (request: NextRequest) => {
  const data = await validateRequest(request, loginSchema)

  // Call AuthService to handle login
  const result = await AuthService.login(data)

  return successResponse(result)
})
