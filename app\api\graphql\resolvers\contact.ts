/**
 * Contact GraphQL Resolvers
 *
 * Resolvers for Contact queries and mutations using Prisma directly
 */

import { GraphQLContext, requireAuth, requireOwnershipOrAdmin } from '../context'
import { prisma } from '@/lib/database'
import { GraphQLError } from 'graphql'

interface ContactArgs {
  id: string
}

interface ContactsArgs {
  first?: number
  after?: string
  search?: string
  contactTypeId?: string
  isActive?: boolean
}

interface CreateContactArgs {
  input: {
    name: string
    email?: string
    phone?: string
    address?: string
    note?: string
    avatar?: string
    contactTypeId: string
  }
}

interface UpdateContactArgs {
  id: string
  input: {
    name?: string
    email?: string
    phone?: string
    address?: string
    note?: string
    avatar?: string
    contactTypeId?: string
    isActive?: boolean
  }
}

// Helper function to build connection response
function buildConnection<T>(
  items: T[],
  totalCount: number,
  first?: number,
  after?: string
) {
  const edges = items.map((item: any, index) => ({
    node: item,
    cursor: Buffer.from(`${after ? parseInt(Buffer.from(after, 'base64').toString()) + index + 1 : index}`).toString('base64')
  }))

  const hasNextPage = first ? items.length === first : false
  const hasPreviousPage = !!after

  return {
    edges,
    pageInfo: {
      hasNextPage,
      hasPreviousPage,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount
  }
}

export const contactResolvers = {
  Query: {
    contacts: async (_: any, args: ContactsArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const { first = 25, after, search, contactTypeId, isActive } = args
      
      // Convert cursor to page number
      const page = after ? Math.floor(parseInt(Buffer.from(after, 'base64').toString()) / first) + 1 : 1

      // Build where clause
      const where: any = {
        userId: user.id
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (contactTypeId) where.contactTypeId = contactTypeId
      if (isActive !== undefined) where.isActive = isActive

      // Convert cursor to skip value
      const skip = after ? parseInt(Buffer.from(after, 'base64').toString()) : 0

      const [contacts, totalCount] = await Promise.all([
        prisma.contact.findMany({
          where,
          include: {
            contactType: true
          },
          skip,
          take: first,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.contact.count({ where })
      ])

      return buildConnection(contacts, totalCount, first, after)
    },

    contact: async (_: any, args: ContactArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const contact = await prisma.contact.findFirst({
        where: {
          id: args.id,
          userId: user.id
        },
        include: {
          contactType: true
        }
      })

      if (!contact) {
        throw new GraphQLError('Contact not found')
      }

      // Check ownership
      requireOwnershipOrAdmin(context, contact.userId)

      return contact
    },

    contactTypes: async (_: any, __: any, context: GraphQLContext) => {
      requireAuth(context)

      const contactTypes = await prisma.contactType.findMany({
        where: { isActive: true },
        orderBy: { name: 'asc' }
      })

      return contactTypes
    }
  },

  Mutation: {
    createContact: async (_: any, args: CreateContactArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // Verify contact type exists if provided
        if (input.contactTypeId) {
          const contactType = await prisma.contactType.findUnique({
            where: { id: input.contactTypeId }
          })

          if (!contactType) {
            throw new GraphQLError('Contact type not found')
          }
        }

        const contact = await prisma.contact.create({
          data: {
            name: input.name,
            email: input.email,
            phone: input.phone,
            address: input.address,
            description: input.description,
            userId: user.id,
            contactTypeId: input.contactTypeId
          },
          include: {
            contactType: true
          }
        })

        return contact
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to create contact')
      }
    },

    updateContact: async (_: any, args: UpdateContactArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // First check if contact exists and user has permission
        const existingContact = await prisma.contact.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingContact) {
          throw new GraphQLError('Contact not found')
        }

        requireOwnershipOrAdmin(context, existingContact.userId)

        // Verify contact type exists if being updated
        if (input.contactTypeId) {
          const contactType = await prisma.contactType.findUnique({
            where: { id: input.contactTypeId }
          })

          if (!contactType) {
            throw new GraphQLError('Contact type not found')
          }
        }

        const contact = await prisma.contact.update({
          where: { id: args.id },
          data: {
            name: input.name,
            email: input.email,
            phone: input.phone,
            address: input.address,
            description: input.description,
            contactTypeId: input.contactTypeId,
            isActive: input.isActive
          },
          include: {
            contactType: true
          }
        })

        return contact
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update contact')
      }
    },

    deleteContact: async (_: any, args: ContactArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        // First check if contact exists and user has permission
        const existingContact = await prisma.contact.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingContact) {
          throw new GraphQLError('Contact not found')
        }

        requireOwnershipOrAdmin(context, existingContact.userId)

        // Soft delete by setting isActive to false
        await prisma.contact.update({
          where: { id: args.id },
          data: { isActive: false }
        })

        return true
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to delete contact')
      }
    }
  },

  Contact: {
    user: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.user) {
        return parent.user
      }

      return await context.prisma.user.findUnique({
        where: { id: parent.userId }
      })
    },

    contactType: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.contactType) {
        return parent.contactType
      }

      return await context.prisma.contactType.findUnique({
        where: { id: parent.contactTypeId }
      })
    }
  }
}
