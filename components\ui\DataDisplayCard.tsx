"use client"

import React from "react"
import { Edit, Trash2, MoreH<PERSON>zon<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "./button"
import { TruncatedText } from "./TruncatedText"

interface DataDisplayCardProps {
  // Core data
  id: string | number
  title: string
  subtitle?: string
  description?: string
  
  // Visual elements
  icon?: React.ReactNode
  color?: string
  badge?: {
    text: string
    color?: string
    variant?: "default" | "success" | "warning" | "error" | "info"
  }
  
  // Actions
  onEdit?: (id: string | number) => void
  onView?: (id: string | number) => void
  customActions?: Array<{
    label: string
    icon?: React.ReactNode
    onClick: (id: string | number) => void
    variant?: "default" | "destructive" | "outline"
  }>
  
  // Layout & styling
  className?: string
  compact?: boolean
  showActions?: boolean
  
  // Additional content
  children?: React.ReactNode
  footer?: React.ReactNode
  
  // Metadata
  metadata?: Array<{
    label: string
    value: string
    icon?: React.ReactNode
  }>
}

const badgeVariants = {
  default: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
  success: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
  warning: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
  error: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
  info: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
}

export function DataDisplayCard({
  id,
  title,
  subtitle,
  description,
  icon,
  color,
  badge,
  onEdit,
  onView,
  customActions = [],
  className = "",
  compact = false,
  showActions = true,
  children,
  footer,
  metadata = []
}: DataDisplayCardProps) {
  const hasActions = showActions && (onEdit || onView || customActions.length > 0)

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit?.(id)
  }
  
  const handleView = () => {
    onView?.(id)
  }
  
  return (
    <div
      className={`
        group relative
        bg-white/80 dark:bg-gray-800/80 
        backdrop-blur-sm
        border border-gray-200/50 dark:border-gray-700/50
        rounded-xl
        transition-all duration-200
        hover:bg-white dark:hover:bg-gray-800
        hover:border-gray-300 dark:hover:border-gray-600
        hover:shadow-md
        ${onView ? 'cursor-pointer' : ''}
        ${compact ? 'p-3' : 'p-4'}
        ${className}
      `}
      onClick={onView ? handleView : undefined}
    >
      {/* Header */}
      <div className="flex items-start justify-between gap-3">
        <div className="flex items-start gap-3 flex-1 min-w-0">
          {/* Icon */}
          {icon && (
            <div 
              className={`
                flex-shrink-0 p-2 rounded-lg
                ${color || 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'}
              `}
            >
              {icon}
            </div>
          )}
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className={`font-semibold text-gray-900 dark:text-gray-100 truncate ${compact ? 'text-sm' : 'text-base'}`}>
                {title}
              </h3>
              {badge && (
                <span className={`
                  px-2 py-1 text-xs font-medium rounded-full
                  ${badgeVariants[badge.variant || 'default']}
                  ${badge.color || ''}
                `}>
                  {badge.text}
                </span>
              )}
            </div>
            
            {subtitle && (
              <p className={`text-gray-600 dark:text-gray-400 mb-1 ${compact ? 'text-xs' : 'text-sm'}`}>
                {subtitle}
              </p>
            )}
            
            {description && (
              <div className={compact ? 'text-xs' : 'text-sm'}>
                <TruncatedText 
                  text={description}
                  maxLength={compact ? 60 : 100}
                  className="text-gray-500 dark:text-gray-500"
                />
              </div>
            )}
          </div>
        </div>
        
        {/* Actions */}
        {hasActions && (
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                <Edit size={14} className="text-blue-600 dark:text-blue-400" />
              </Button>
            )}
            
            {customActions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || "ghost"}
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  action.onClick(id)
                }}
                className="h-8 w-8 p-0"
              >
                {action.icon || <MoreHorizontal size={14} />}
              </Button>
            ))}
          </div>
        )}
      </div>
      
      {/* Metadata */}
      {metadata.length > 0 && (
        <div className={`flex flex-wrap gap-4 ${compact ? 'mt-2' : 'mt-3'}`}>
          {metadata.map((item, index) => (
            <div key={index} className="flex items-center gap-1">
              {item.icon}
              <span className={`text-gray-500 dark:text-gray-500 ${compact ? 'text-xs' : 'text-sm'}`}>
                {item.label}: {item.value}
              </span>
            </div>
          ))}
        </div>
      )}
      
      {/* Custom content */}
      {children && (
        <div className={compact ? 'mt-2' : 'mt-3'}>
          {children}
        </div>
      )}
      
      {/* Footer */}
      {footer && (
        <div className={`border-t border-gray-200 dark:border-gray-700 ${compact ? 'mt-2 pt-2' : 'mt-3 pt-3'}`}>
          {footer}
        </div>
      )}
    </div>
  )
}

// Specialized variants for common use cases
export function AccountCard({ account, onEdit, onDelete, onView }: {
  account: {
    id: number
    name: string
    type: string
    balance: number
    currency: string
    description?: string
    bankName?: string
    accountNumber?: string
    accountType?: {
      id: string
      name: string
      color: string
      icon?: string
      description?: string
    }
  }
  onEdit?: (id: number) => void
  onDelete?: (id: number) => void
  onView?: (id: number) => void
}) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount)
  }

  const getTypeLabel = () => {
    const typeLabels: Record<string, string> = {
      cash: "Tiền mặt",
      'type-1': "Tiền mặt",
      bank: "Ngân hàng",
      'type-2': "Ngân hàng",
      credit: "Thẻ tín dụng",
      'type-3': "Thẻ tín dụng",
      investment: "Đầu tư",
      savings: "Tiết kiệm",
      digital: "Ví điện tử",
      custom: "Tùy chỉnh"
    }
    return typeLabels[account.accountTypeId] || account.accountTypeId
  }

  const getTypeColor = () => {
    const typeColors: Record<string, string> = {
      cash: "bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400",
      'type-1': "bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400",
      bank: "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400",
      'type-2': "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400",
      credit: "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400",
      'type-3': "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400",
      investment: "bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400",
      savings: "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400",
      digital: "bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400",
      custom: "bg-pink-50 text-pink-700 dark:bg-pink-900/20 dark:text-pink-400"
    }
    return typeColors[account.accountTypeId] || "bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400"
  }



  return (
    <div className="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-200/60 dark:border-gray-700/60 p-4 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/60 dark:hover:border-gray-600/60 transition-all duration-200 h-full flex flex-col">

      {/* Account Info */}
      <div className="flex-1 space-y-2">
        {/* Name and Type Badge */}
        <div className="flex items-start justify-between gap-2">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 flex-1">
            {account.name}
          </h3>
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${getTypeColor()}`}
            style={{
              backgroundColor: account.accountType?.color,
              color: account.accountType ? 'white' : undefined
            }}
          >
            {getTypeLabel()}
          </span>
        </div>

        {/* Bank/Description with Action Buttons */}
        <div className="flex items-center justify-between gap-2">
          {(account.bankName || account.description) ? (
            <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1 flex-1">
              {account.bankName || account.description}
            </p>
          ) : (
            <div className="flex-1"></div>
          )}

          {/* Action Buttons - Show on hover */}
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="flex items-center gap-1">
              {onEdit && (
                <button
                  onClick={() => onEdit(account.id)}
                  className="p-1.5 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                  title="Chỉnh sửa"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
              )}
              {onView && (
                <button
                  onClick={() => onView(account.id)}
                  className="p-1.5 text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                  title="Xem chi tiết"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </button>
              )}

            </div>
          </div>
        </div>

        {/* Account Number */}
        {account.accountNumber && (
          <p className="text-xs text-gray-400 dark:text-gray-500 font-mono">
            {account.accountNumber}
          </p>
        )}
      </div>

      {/* Balance */}
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500 dark:text-gray-400">Số dư</span>
          <span className={`text-sm font-semibold ${
            account.balance >= 0
              ? "text-green-600 dark:text-green-400"
              : "text-red-600 dark:text-red-400"
          }`}>
            {formatCurrency(account.balance)}
          </span>
        </div>
      </div>
    </div>
  )
}

export function CategoryCard({ category, onEdit }: {
  category: {
    id: number
    name: string
    type: "income" | "expense"
    description?: string
    color?: string
  }
  onEdit?: (id: number) => void
}) {
  return (
    <DataDisplayCard
      id={category.id}
      title={category.name}
      description={category.description}
      badge={{
        text: category.type === "income" ? "Thu nhập" : "Chi tiêu",
        variant: category.type === "income" ? "success" : "error"
      }}
      icon={<div className="w-5 h-5 rounded bg-current" style={{ backgroundColor: category.color }} />}
      onEdit={onEdit}
    />
  )
}

import { ContactUtils } from "@/lib/generated/prisma"

export function ContactCard({ contact, onEdit, onView }: {
  contact: {
    id: number
    name: string
    phone?: string
    email?: string
    accounts?: any[]
    description?: string
    isActive?: boolean
    contactType?: {
      id: string
      name: string
      color: string
      description?: string
    }
  }
  onEdit?: (id: number) => void
  onView?: (id: number) => void
}) {
  const getContactTypeColor = () => {
    if (contact.contactType) {
      return `text-white`
    }
    return "bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400"
  }

  const getContactTypeLabel = () => {
    return contact.contactType?.name || "Chưa phân loại"
  }

  // Use utility to check if contact has accounts
  const hasAccounts = ContactUtils.hasAccounts(contact as any)
  const totalAccountCount = ContactUtils.getTotalAccountCount(contact as any)

  return (
    <div className="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-200/60 dark:border-gray-700/60 p-4 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/60 dark:hover:border-gray-600/60 transition-all duration-200 h-full flex flex-col min-h-[180px]">
      {/* Contact Info */}
      <div className="flex-1 space-y-2">
        {/* Name and Type Badge */}
        <div className="flex items-start justify-between gap-2">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 flex-1">
            {contact.name}
          </h3>
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${getContactTypeColor()}`}
            style={{
              backgroundColor: contact.contactType?.color || '#6b7280',
              color: 'white'
            }}
          >
            {getContactTypeLabel()}
          </span>
        </div>

        {/* Contact Info with Action Buttons */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex-1">
            {(contact.email || contact.phone) && (
              <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1">
                {contact.email || contact.phone}
              </p>
            )}
          </div>

          {/* Action Buttons - Show on hover */}
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="flex items-center gap-1">
              {onEdit && (
                <button
                  onClick={() => onEdit(contact.id)}
                  className="p-1.5 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                  title="Chỉnh sửa"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
              )}
              {onView && (
                <button
                  onClick={() => onView(contact.id)}
                  className="p-1.5 text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                  title="Xem chi tiết"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </button>
              )}

            </div>
          </div>
        </div>

        {/* Description */}
        {contact.description && (
          <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
            {contact.description}
          </p>
        )}
      </div>

      {/* Contact Status */}
      <div className="mt-auto pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>Trạng thái:</span>
          <span className={`font-medium ${
            contact.isActive !== false
              ? "text-green-600 dark:text-green-400"
              : "text-gray-500 dark:text-gray-400"
          }`}>
            {contact.isActive !== false ? "Hoạt động" : "Không hoạt động"}
          </span>
        </div>
      </div>
    </div>
  )
}
