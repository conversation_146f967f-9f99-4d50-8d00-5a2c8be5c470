/**
 * Loading Provider Component
 * 
 * Provides loading state management for the entire application
 */

"use client"

import React, { createContext, useContext, useState, useCallback } from 'react'
import { LoadingOverlay } from './Loading'

interface LoadingContextType {
  isLoading: boolean
  startLoading: (message?: string) => void
  stopLoading: () => void
  setLoadingMessage: (message: string) => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function useLoading() {
  const context = useContext(LoadingContext)
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

interface LoadingProviderProps {
  children: React.ReactNode
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('Đang xử lý...')

  const startLoading = useCallback((message?: string) => {
    if (message) {
      setLoadingMessage(message)
    }
    setIsLoading(true)
  }, [])

  const stopLoading = useCallback(() => {
    setIsLoading(false)
    // Reset to default message after a delay to avoid flickering
    setTimeout(() => {
      setLoadingMessage('Đang xử lý...')
    }, 300)
  }, [])

  const value = {
    isLoading,
    startLoading,
    stopLoading,
    setLoadingMessage
  }

  return (
    <LoadingContext.Provider value={value}>
      {children}
      <LoadingOverlay isVisible={isLoading} message={loadingMessage} />
    </LoadingContext.Provider>
  )
}
