"use client"

import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { DebitCardCard } from "@/components/ui/DebitCardCard"
import { ListPageLayout } from "@/components/layout/ResponsiveLayout"
import { GenericList } from "@/components/ui/GenericList"
import { useAuth } from "@/contexts/AuthContext"
import { useEffect, useRef, useState } from "react"

import { useGlobalTaskbarRestore } from "@/components/layout/GlobalLayout"
import { DebitCardSidebar } from "@/components/ui/DebitCardSidebar"
import { Button } from "@/components/ui/button"
import { PortalDropdown } from "@/components/ui/PortalDropdown"
import { useSidebarManager } from "@/hooks/useSidebarManager"
import { Account } from "@/lib/generated/prisma"
import { AccountApiService, DebitCardApiService } from "@/services/client"
import { extractNodes } from "@/lib/utils/connection-helpers"
import { DebitCardWithRelations } from "@/types/entities"
import { CreditCard, Plus, Search, SortAsc, SortDesc, ChevronDown, ChevronUp, Building2, Wallet } from "lucide-react"
import { toast } from "sonner"

function DebitCardsPageContent() {
  const { user } = useAuth()
  const {
    openSidebar,
    closeSidebar,
    isSidebarOpen
  } = useSidebarManager()

  const [selectedDebitCard, setSelectedDebitCard] = useState<DebitCardWithRelations | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [debitCards, setDebitCards] = useState<DebitCardWithRelations[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [showSortOptions, setShowSortOptions] = useState(false)
  const sortButtonRef = useRef<HTMLButtonElement>(null)

  // Global taskbar restore
  const { restoredItem, restoreType, clearRestored } = useGlobalTaskbarRestore()

  // Fetch debit cards
  useEffect(() => {
    const fetchDebitCards = async () => {
      if (!user?.id) {
        setError('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const response = await DebitCardApiService.getDebitCards({})
        if (response.success) {
          setDebitCards(response.data || [])
        } else {
          setError(response.error || 'Không thể tải danh sách thẻ ghi nợ')
        }
      } catch (error: any) {
        console.error('Error fetching debit cards:', error)
        setError(error?.message || 'Không thể tải danh sách thẻ ghi nợ')
      } finally {
        setLoading(false)
      }
    }

    fetchDebitCards()
  }, [user?.id])

  // Fetch accounts for dropdown
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const response = await AccountApiService.getAccounts({})
        if (response.success && response.data) {
          // Filter only bank accounts
          const allAccounts = extractNodes(response.data)
          const bankAccounts = allAccounts.filter((account: Account) =>
            account.accountType?.isBankAccount
          )
          setAccounts(bankAccounts)
        }
      } catch (error) {
        console.error('Error fetching accounts:', error)
      }
    }

    fetchAccounts()
  }, [])

  // Statistics
  const totalCards = debitCards.length
  const activeCards = debitCards.filter(card => card.isActive).length
  const inactiveCards = totalCards - activeCards
  const cardsByType = debitCards.reduce((acc, card) => {
    acc[card.cardType] = (acc[card.cardType] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Filter tabs
  const [activeFilter, setActiveFilter] = useState('all')
  const filteredCards = debitCards.filter(card => {
    switch (activeFilter) {
      case 'active':
        return card.isActive
      case 'inactive':
        return !card.isActive
      default:
        return true
    }
  })

  const filterTabs = [
    { value: 'all', label: 'Tất cả', count: totalCards },
    { value: 'active', label: 'Hoạt động', count: activeCards },
    { value: 'inactive', label: 'Không hoạt động', count: inactiveCards }
  ]

  // Sort options
  const sortOptions = [
    { value: 'cardName', label: 'Tên thẻ' },
    { value: 'cardType', label: 'Loại thẻ' },
    { value: 'account.name', label: 'Tài khoản' },
    { value: 'createdAt', label: 'Ngày tạo' }
  ]

  // Debit card handlers
  const handleAddDebitCard = () => {
    if (!user?.id) {
      setError('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.')
      return
    }

    setSelectedDebitCard(null)
    setIsEditing(false)
    openSidebar('debitCard')
  }

  const handleEditDebitCard = (debitCard: DebitCardWithRelations) => {
    setSelectedDebitCard(debitCard)
    setIsEditing(true)
    openSidebar('debitCard')
  }

  const handleCloseDebitCardSidebar = () => {
    closeSidebar('debitCard')
    setSelectedDebitCard(null)
    setIsEditing(false)
  }

  const handleSaveDebitCard = async (data: any) => {
    try {
      setLoading(true)
      let response

      if (isEditing && selectedDebitCard) {
        response = await DebitCardApiService.updateDebitCard(selectedDebitCard.id, data)
      } else {
        response = await DebitCardApiService.createDebitCard(data)
      }

      if (response.success) {
        // Refresh debit cards list
        const debitCardsResponse = await DebitCardApiService.getDebitCards({})
        if (debitCardsResponse.success) {
          setDebitCards(debitCardsResponse.data || [])
        }

        handleCloseDebitCardSidebar()
        toast.success(`Thẻ ghi nợ đã được ${isEditing ? 'cập nhật' : 'tạo'} thành công`)
      } else {
        setError(typeof response.error === 'string' ? response.error : 'Không thể lưu thẻ ghi nợ. Vui lòng thử lại.')
      }
    } catch (error: any) {
      console.error('Error saving debit card:', error)
      setError(error?.message || 'Không thể lưu thẻ ghi nợ. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }

  // Handle global taskbar restore
  useEffect(() => {
    if (restoredItem && restoreType === 'debitCard') {
      if (restoredItem.data) {
        setSelectedDebitCard(restoredItem.data)
        setIsEditing(restoredItem.isEditing || false)
      }
      openSidebar('debitCard')
      clearRestored()
    }
  }, [restoredItem, restoreType, clearRestored, openSidebar])

  return (
    <>
      <ListPageLayout
        title="Thẻ ghi nợ"
        subtitle="Quản lý các thẻ ghi nợ của bạn"
        stats={[
          {
            label: "Tổng thẻ",
            value: totalCards.toString(),
            icon: <CreditCard size={14} className="text-blue-500" />
          },
          {
            label: "Đang hoạt động",
            value: activeCards.toString(),
            icon: <Building2 size={14} className="text-green-500" />
          },
          {
            label: "Loại thẻ phổ biến",
            value: Object.keys(cardsByType)[0] || "N/A",
            icon: <Wallet size={14} className="text-gray-500" />
          }
        ]}
        actions={
          <Button onClick={handleAddDebitCard} className="flex items-center gap-2">
            <Plus size={16} />
            Thêm mới
          </Button>
        }
      >
        {/* Search, Filter and Sort Row */}
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3 mb-4">
          <div className="flex flex-wrap items-center gap-2">
            {/* Type Filter Tabs */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-0.5 h-8">
              {filterTabs.map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => setActiveFilter(filter.value)}
                  className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 h-full flex items-center ${
                    activeFilter === filter.value
                      ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>

            {/* Sort Dropdown */}
            <div className="relative ml-auto">
              <PortalDropdown
                trigger={
                  <Button
                    ref={sortButtonRef}
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSortOptions(!showSortOptions)}
                    className="h-8 px-3 text-xs"
                  >
                    <SortAsc size={12} className="mr-1" />
                    Sắp xếp
                    {showSortOptions ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
                  </Button>
                }
                isOpen={showSortOptions}
                onClose={() => setShowSortOptions(false)}
                align="right"
              >
                <div className="py-1">
                  {sortOptions.map((option) => (
                    <button
                      key={option.value}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                      onClick={() => {
                        // Handle sort logic here
                        setShowSortOptions(false)
                      }}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </PortalDropdown>
            </div>
          </div>
        </div>

        {/* Debit Cards List */}
        <GenericList
          data={filteredCards}
          renderCard={(debitCard) => (
            <DebitCardCard
              key={debitCard.id}
              debitCard={debitCard}
              onEdit={handleEditDebitCard}
              onDelete={async (card) => {
                if (!confirm(`Bạn có chắc chắn muốn xóa thẻ "${card.cardName}"?`)) {
                  return
                }
                try {
                  await DebitCardApiService.deleteDebitCard(card.id)
                  // Refresh list
                  const response = await DebitCardApiService.getDebitCards({})
                  if (response.success) {
                    setDebitCards(response.data || [])
                  }
                  toast.success('Đã xóa thẻ ghi nợ')
                } catch (error) {
                  toast.error('Không thể xóa thẻ ghi nợ')
                }
              }}
              onToggleVisibility={async (card) => {
                try {
                  await DebitCardApiService.updateDebitCard(card.id, {
                    isActive: !card.isActive
                  })
                  // Refresh list
                  const response = await DebitCardApiService.getDebitCards({})
                  if (response.success) {
                    setDebitCards(response.data || [])
                  }
                  toast.success(card.isActive ? 'Đã ẩn thẻ' : 'Đã hiển thị thẻ')
                } catch (error) {
                  toast.error('Không thể cập nhật trạng thái thẻ')
                }
              }}
            />
          )}
          gridCols={{ default: 1, sm: 2, lg: 3, xl: 4 }}
          emptyMessage={
            filteredCards.length === 0 && debitCards.length > 0
              ? `Không tìm thấy thẻ ghi nợ nào phù hợp với bộ lọc hiện tại`
              : "Chưa có thẻ ghi nợ nào. Hãy tạo thẻ đầu tiên!"
          }
          loading={loading}
          searchable={false}
          sortable={false}
          paginated={true}
          defaultItemsPerPage={25}
          itemsPerPageOptions={[10, 25, 50, 100]}
          sidebarOpen={isSidebarOpen('debitCard')}
          sidebarWidth={400}
        />
      </ListPageLayout>

      {/* Debit Card Sidebar */}
      <DebitCardSidebar
        isOpen={isSidebarOpen('debitCard')}
        onClose={handleCloseDebitCardSidebar}
        onSave={handleSaveDebitCard}
        debitCard={selectedDebitCard as any}
        isEditing={isEditing}
        accounts={accounts}
      />

      {/* Toast Notifications */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 animate-fade-in">
          <div className="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="flex-1">{error}</span>
            <button
              onClick={() => setError(null)}
              className="text-white hover:text-gray-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </>
  )
}

export default function DebitCardsPage() {
  return (
    <ProtectedRoute>
      <DebitCardsPageContent />
    </ProtectedRoute>
  )
}
