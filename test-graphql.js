/**
 * Simple GraphQL endpoint test
 */

async function testGraphQL() {
  try {
    console.log('Testing GraphQL endpoint...');
    
    const response = await fetch('http://localhost:3000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: '{ __typename }'
      })
    });

    const result = await response.json();
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ GraphQL endpoint is working!');
    } else {
      console.log('❌ GraphQL endpoint has issues');
    }
  } catch (error) {
    console.error('❌ Error testing GraphQL:', error.message);
  }
}

testGraphQL();
