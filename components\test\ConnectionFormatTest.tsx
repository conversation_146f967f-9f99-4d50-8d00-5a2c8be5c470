/**
 * Test Component for GraphQL Connection Format
 * 
 * This component tests the new GraphQL connection format implementation
 */

'use client'

import React, { useState } from 'react'
import { useUsers, useAccounts, useTransactions } from '@/hooks/useEntityConnections'
import { extractNodes, extractPaginationInfo } from '@/lib/utils/connection-helpers'
import { UserApiService, AccountApiService, TransactionApiService } from '@/services/client'

export default function ConnectionFormatTest() {
  const [testResults, setTestResults] = useState<{
    apiService: boolean
    hooks: boolean
    helpers: boolean
  }>({
    apiService: false,
    hooks: false,
    helpers: false
  })

  const [testOutput, setTestOutput] = useState<string[]>([])

  const addOutput = (message: string) => {
    setTestOutput(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  // Test API Services
  const testApiServices = async () => {
    addOutput('🧪 Testing API Services...')
    
    try {
      // Test UserApiService
      const usersResponse = await UserApiService.getUsers({ first: 5 })
      addOutput(`✅ UserApiService.getUsers: ${usersResponse.success ? 'SUCCESS' : 'FAILED'}`)
      
      if (usersResponse.success && usersResponse.data) {
        const users = extractNodes(usersResponse.data)
        const pagination = extractPaginationInfo(usersResponse.data)
        addOutput(`   - Users count: ${users.length}`)
        addOutput(`   - Total count: ${pagination.totalCount}`)
        addOutput(`   - Has next: ${pagination.hasNextPage}`)
      }

      // Test AccountApiService
      const accountsResponse = await AccountApiService.getAccounts({ first: 3 })
      addOutput(`✅ AccountApiService.getAccounts: ${accountsResponse.success ? 'SUCCESS' : 'FAILED'}`)
      
      if (accountsResponse.success && accountsResponse.data) {
        const accounts = extractNodes(accountsResponse.data)
        addOutput(`   - Accounts count: ${accounts.length}`)
      }

      // Test TransactionApiService
      const transactionsResponse = await TransactionApiService.getTransactions({ first: 10 })
      addOutput(`✅ TransactionApiService.getTransactions: ${transactionsResponse.success ? 'SUCCESS' : 'FAILED'}`)
      
      if (transactionsResponse.success && transactionsResponse.data) {
        const transactions = extractNodes(transactionsResponse.data)
        addOutput(`   - Transactions count: ${transactions.length}`)
      }

      setTestResults(prev => ({ ...prev, apiService: true }))
      addOutput('🎉 API Services test completed successfully!')
      
    } catch (error: any) {
      addOutput(`❌ API Services test failed: ${error.message}`)
      setTestResults(prev => ({ ...prev, apiService: false }))
    }
  }

  // Test Hooks
  const testHooks = () => {
    addOutput('🧪 Testing Connection Hooks...')
    
    try {
      // This will be tested by the hook components below
      setTestResults(prev => ({ ...prev, hooks: true }))
      addOutput('✅ Connection Hooks initialized successfully!')
    } catch (error: any) {
      addOutput(`❌ Connection Hooks test failed: ${error.message}`)
      setTestResults(prev => ({ ...prev, hooks: false }))
    }
  }

  // Test Helper Functions
  const testHelpers = () => {
    addOutput('🧪 Testing Helper Functions...')
    
    try {
      // Create mock connection data
      const mockConnection = {
        edges: [
          { node: { id: '1', name: 'Test 1' }, cursor: 'cursor1' },
          { node: { id: '2', name: 'Test 2' }, cursor: 'cursor2' }
        ],
        pageInfo: {
          hasNextPage: true,
          hasPreviousPage: false,
          startCursor: 'cursor1',
          endCursor: 'cursor2'
        },
        totalCount: 10
      }

      // Test extractNodes
      const nodes = extractNodes(mockConnection)
      addOutput(`✅ extractNodes: ${nodes.length === 2 ? 'SUCCESS' : 'FAILED'}`)
      
      // Test extractPaginationInfo
      const paginationInfo = extractPaginationInfo(mockConnection)
      addOutput(`✅ extractPaginationInfo: ${paginationInfo.totalCount === 10 ? 'SUCCESS' : 'FAILED'}`)
      addOutput(`   - Total: ${paginationInfo.totalCount}`)
      addOutput(`   - Current: ${paginationInfo.currentCount}`)
      addOutput(`   - Has next: ${paginationInfo.hasNextPage}`)

      setTestResults(prev => ({ ...prev, helpers: true }))
      addOutput('🎉 Helper Functions test completed successfully!')
      
    } catch (error: any) {
      addOutput(`❌ Helper Functions test failed: ${error.message}`)
      setTestResults(prev => ({ ...prev, helpers: false }))
    }
  }

  const runAllTests = async () => {
    setTestOutput([])
    addOutput('🚀 Starting Connection Format Tests...')
    
    testHelpers()
    testHooks()
    await testApiServices()
    
    addOutput('📊 Test Summary:')
    addOutput(`   - API Services: ${testResults.apiService ? '✅' : '❌'}`)
    addOutput(`   - Hooks: ${testResults.hooks ? '✅' : '❌'}`)
    addOutput(`   - Helpers: ${testResults.helpers ? '✅' : '❌'}`)
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">GraphQL Connection Format Test</h1>
        <p className="text-gray-600">Testing the new GraphQL connection format implementation</p>
      </div>

      {/* Test Controls */}
      <div className="flex gap-4 justify-center">
        <button
          onClick={runAllTests}
          className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Run All Tests
        </button>
        
        <button
          onClick={testApiServices}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Test API Services
        </button>
        
        <button
          onClick={testHooks}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Test Hooks
        </button>
        
        <button
          onClick={testHelpers}
          className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
        >
          Test Helpers
        </button>
      </div>

      {/* Test Results */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <TestResultCard
          title="API Services"
          status={testResults.apiService}
          description="Testing ApiService classes with connection format"
        />
        
        <TestResultCard
          title="Connection Hooks"
          status={testResults.hooks}
          description="Testing custom hooks for GraphQL connections"
        />
        
        <TestResultCard
          title="Helper Functions"
          status={testResults.helpers}
          description="Testing utility functions for connection data"
        />
      </div>

      {/* Live Hook Tests */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <HookTestCard title="Users Hook" />
        <HookTestCard title="Accounts Hook" />
        <HookTestCard title="Transactions Hook" />
      </div>

      {/* Test Output */}
      <div className="bg-gray-900 text-green-400 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Test Output</h3>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {testOutput.map((line, index) => (
            <div key={index} className="text-sm font-mono">
              {line}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Test Result Card Component
function TestResultCard({ 
  title, 
  status, 
  description 
}: { 
  title: string
  status: boolean
  description: string 
}) {
  return (
    <div className={`p-4 rounded-lg border-2 ${
      status 
        ? 'border-green-500 bg-green-50' 
        : 'border-gray-300 bg-gray-50'
    }`}>
      <div className="flex items-center gap-2 mb-2">
        <div className={`w-3 h-3 rounded-full ${
          status ? 'bg-green-500' : 'bg-gray-400'
        }`} />
        <h3 className="font-semibold">{title}</h3>
      </div>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  )
}

// Hook Test Card Component
function HookTestCard({ title }: { title: string }) {
  const usersHook = useUsers({ first: 3 })
  const accountsHook = useAccounts({ first: 3 })
  const transactionsHook = useTransactions({ first: 3 })

  const getHookData = () => {
    switch (title) {
      case 'Users Hook':
        return usersHook
      case 'Accounts Hook':
        return accountsHook
      case 'Transactions Hook':
        return transactionsHook
      default:
        return usersHook
    }
  }

  const hookData = getHookData()

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="font-semibold mb-2">{title}</h3>
      
      {hookData.loading && (
        <div className="text-blue-600">Loading...</div>
      )}
      
      {hookData.error && (
        <div className="text-red-600">Error: {hookData.error.message}</div>
      )}
      
      {!hookData.loading && !hookData.error && (
        <div className="space-y-1 text-sm">
          <div>Items: {hookData.items.length}</div>
          <div>Total: {hookData.totalCount}</div>
          <div>Has Next: {hookData.hasNextPage ? 'Yes' : 'No'}</div>
          <div>Has Prev: {hookData.hasPreviousPage ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  )
}
