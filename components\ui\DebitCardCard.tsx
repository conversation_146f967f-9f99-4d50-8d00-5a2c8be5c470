"use client"

import React from "react"
import { Credit<PERSON><PERSON>, Edit, Trash2, <PERSON>, <PERSON>Off, Unlink } from "lucide-react"
import { DebitCard } from "@/lib/generated/prisma"
import { DebitCardWithRelations } from "@/types/entities"

interface DebitCardCardProps {
  debitCard: DebitCardWithRelations
  onEdit?: (debitCard: DebitCardWithRelations) => void
  onDelete?: (debitCard: DebitCardWithRelations) => void
  onUnlink?: (debitCard: DebitCardWithRelations) => void
  onToggleVisibility?: (debitCard: DebitCardWithRelations) => void
  showActions?: boolean
  compact?: boolean
}

export function DebitCardCard({
  debitCard,
  onEdit,
  onDelete,
  onUnlink,
  onToggleVisibility,
  showActions = true,
  compact = false
}: DebitCardCardProps) {
  const [showFullNumber, setShowFullNumber] = React.useState(false)

  const getCardTypeColor = (cardType: string) => {
    switch (cardType.toUpperCase()) {
      case 'VISA':
        return 'bg-blue-500'
      case 'MASTERCARD':
        return 'bg-red-500'
      case 'AMEX':
        return 'bg-green-500'
      case 'JCB':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getCardTypeIcon = (cardType: string) => {
    return <CreditCard size={16} />
  }

  const formatCardNumber = (cardNumber: string) => {
    if (showFullNumber) {
      // Format as XXXX XXXX XXXX XXXX
      return cardNumber.replace(/(.{4})/g, '$1 ').trim()
    }
    // Show only last 4 digits
    return `**** **** **** ${cardNumber.slice(-4)}`
  }

  const handleToggleVisibility = () => {
    setShowFullNumber(!showFullNumber)
    if (onToggleVisibility) {
      onToggleVisibility(debitCard)
    }
  }

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
        <div className="flex items-center space-x-3">
          <div className={`w-8 h-8 rounded-lg ${getCardTypeColor(debitCard.cardType)} flex items-center justify-center text-white`}>
            {getCardTypeIcon(debitCard.cardType)}
          </div>
          <div>
            <p className="font-medium text-gray-900">{debitCard.cardName}</p>
            <p className="text-sm text-gray-500">{formatCardNumber(debitCard.cardNumber)}</p>
          </div>
        </div>
        
        {showActions && (
          <div className="flex items-center space-x-2">
            <button
              onClick={handleToggleVisibility}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title={showFullNumber ? "Ẩn số thẻ" : "Hiện số thẻ"}
            >
              {showFullNumber ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
            {onEdit && (
              <button
                onClick={() => onEdit(debitCard)}
                className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
                title="Chỉnh sửa"
              >
                <Edit size={16} />
              </button>
            )}
            {onDelete && (
              <button
                onClick={() => onDelete(debitCard)}
                className="p-1 text-red-600 hover:text-red-800 transition-colors"
                title="Xóa"
              >
                <Trash2 size={16} />
              </button>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 hover:border-gray-300 transition-all duration-200 hover:shadow-md">
      {/* Card Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-12 h-12 rounded-xl ${getCardTypeColor(debitCard.cardType)} flex items-center justify-center text-white`}>
              {getCardTypeIcon(debitCard.cardType)}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{debitCard.cardName}</h3>
              <p className="text-sm text-gray-500">{debitCard.cardType}</p>
            </div>
          </div>
          
          {showActions && (
            <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={handleToggleVisibility}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                title={showFullNumber ? "Ẩn số thẻ" : "Hiện số thẻ"}
              >
                {showFullNumber ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
              {onEdit && (
                <button
                  onClick={() => onEdit(debitCard)}
                  className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                  title="Chỉnh sửa"
                >
                  <Edit size={18} />
                </button>
              )}
              {onUnlink && (
                <button
                  onClick={() => onUnlink(debitCard)}
                  className="p-2 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-lg transition-colors"
                  title="Hủy liên kết"
                >
                  <Unlink size={18} />
                </button>
              )}
              {onDelete && (
                <button
                  onClick={() => onDelete(debitCard)}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                  title="Xóa"
                >
                  <Trash2 size={18} />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Card Number */}
      <div className="px-6 pb-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-500 mb-1">Số thẻ</p>
          <p className="font-mono text-lg font-medium text-gray-900 tracking-wider">
            {formatCardNumber(debitCard.cardNumber)}
          </p>
        </div>
      </div>

      {/* Account Info */}
      {debitCard.account && (
        <div className="px-6 pb-4">
          <p className="text-sm text-gray-500 mb-1">Tài khoản liên kết</p>
          <p className="font-medium text-gray-900">{debitCard.account.name}</p>
          {debitCard.account.description && (
            <p className="text-sm text-gray-500">{debitCard.account.description}</p>
          )}
        </div>
      )}

      {/* Status */}
      <div className="px-6 pb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${debitCard.isActive ? 'bg-green-500' : 'bg-gray-400'}`} />
            <span className={`text-sm font-medium ${debitCard.isActive ? 'text-green-700' : 'text-gray-500'}`}>
              {debitCard.isActive ? 'Đang hoạt động' : 'Không hoạt động'}
            </span>
          </div>
          
          <p className="text-xs text-gray-400">
            Tạo: {new Date(debitCard.createdAt).toLocaleDateString('vi-VN')}
          </p>
        </div>
      </div>
    </div>
  )
}
