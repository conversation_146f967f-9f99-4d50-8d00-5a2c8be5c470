/**
 * API Types
 *
 * Request/Response types for API endpoints using Omit and extend from database types
 */

import {
  User,
  UserPreferences,
  Account,
  Category,
  Contact,
  Transaction,
  CategoryType,
  TransactionKind,
  Period
} from '@/lib/generated/prisma'
type EntityId = string
// ==================== COMMON API TYPES ====================

export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  timestamp: string
  message?: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ==================== AUTH API TYPES ====================

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  role?: 'admin' | 'user'
  preferences?: Partial<UserPreferences>
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface LoginResponse {
  user: Omit<User, 'password'>
  tokens: TokenPair
}

export interface RefreshTokenResponse {
  tokens: TokenPair
}

// ==================== USER API TYPES ====================

export interface CreateUserRequest {
  name: string
  email: string
  password: string
  role?: 'admin' | 'user'
  avatar?: string
  preferences?: Partial<UserPreferences>
}

export interface UpdateUserRequest {
  name?: string
  email?: string
  avatar?: string
  role?: 'admin' | 'user'
  isActive?: boolean
}

export interface UpdateUserPreferencesRequest {
  currency?: string
  language?: 'vi' | 'en'
  theme?: 'light' | 'dark' | 'system'
  dateFormat?: string
  numberFormat?: string
}

export interface UserQueryParams extends PaginationParams {
  search?: string
  role?: 'admin' | 'user'
  isActive?: boolean
}

export interface UserListResponse {
  users: Omit<User, 'password'>[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// User Response Types - Direct data, no wrapping
export type CreateUserResponse = Omit<User, 'password'>

export type UpdateUserResponse = Omit<User, 'password'>

export type GetUsersResponse = UserListResponse

// ==================== ACCOUNT API TYPES ====================

export interface CreateAccountRequest {
  name: string
  description?: string
  accountTypeId: EntityId
  balance?: number
  currency?: string
  color?: string
  contactId?: EntityId
  bankInfo?: {
    bankName: string
    branchName?: string
    accountNumber: string
  }
  isActive?: boolean
}

export interface UpdateAccountRequest {
  name?: string
  description?: string
  accountTypeId?: EntityId
  balance?: number
  currency?: string
  color?: string
  contactId?: EntityId
  bankInfo?: {
    bankName: string
    branchName?: string
    accountNumber: string
  }
  isActive?: boolean
}

export interface AccountQueryParams extends PaginationParams {
  search?: string
  accountTypeId?: EntityId
  currency?: string
  isActive?: boolean
  balanceMin?: number
  balanceMax?: number
}

export interface AccountListResponse {
  accounts: Account[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface AccountStatsResponse {
  totalBalance: number
  totalAccounts: number
  activeAccounts: number
  accountsByType: {
    accountTypeId: EntityId
    count: number
    totalBalance: number
  }[]
  recentTransactions: number
  monthlyStats: {
    month: string
    balance: number
    transactions: number
  }[]
}

// Account Response Types - Direct data, no wrapping
export type CreateAccountResponse = Account

export type UpdateAccountResponse = Account

export type GetAccountsResponse = AccountListResponse

// ==================== TRANSACTION API TYPES ====================

export interface CreateIncomeExpenseTransactionRequest {
  kind: 'income' | 'expense'
  amount: number
  date: string
  accountId: EntityId
  categoryId?: EntityId
  description?: string
  location?: string
  note?: string
  attachments?: string[]
  isCleared?: boolean
  isReconciled?: boolean
}

export interface CreateTransferTransactionRequest {
  kind: 'transfer'
  amount: number
  date: string
  fromAccountId: EntityId
  toAccountId: EntityId
  description?: string
  location?: string
  note?: string
  attachments?: string[]
  isCleared?: boolean
  isReconciled?: boolean
}

export type CreateTransactionRequest =
  | CreateIncomeExpenseTransactionRequest
  | CreateTransferTransactionRequest

export interface UpdateTransactionRequest {
  amount?: number
  date?: string
  description?: string
  location?: string
  note?: string
  attachments?: string[]
  isCleared?: boolean
  isReconciled?: boolean
  isActive?: boolean
  // For income/expense transactions
  accountId?: EntityId
  categoryId?: EntityId
  // For transfer transactions
  fromAccountId?: EntityId
  toAccountId?: EntityId
}

export interface TransactionQueryParams extends PaginationParams {
  search?: string
  kind?: TransactionKind
  accountId?: EntityId
  categoryId?: EntityId
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
  isCleared?: boolean
  isReconciled?: boolean
  isActive?: boolean
}

export interface TransactionListResponse {
  transactions: Transaction[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface TransactionStatsResponse {
  totalTransactions: number
  totalIncome: number
  totalExpenses: number
  totalTransfers: number
  netIncome: number
  averageTransaction: number
  transactionsByCategory: {
    categoryId: EntityId
    categoryName: string
    count: number
    totalAmount: number
  }[]
  transactionsByAccount: {
    accountId: EntityId
    accountName: string
    count: number
    totalAmount: number
  }[]
  monthlyStats: {
    month: string
    income: number
    expenses: number
    transfers: number
    netIncome: number
    transactionCount: number
  }[]
}

// Transaction Response Types - Direct data, no wrapping
export type CreateTransactionResponse = Transaction

export type UpdateTransactionResponse = Transaction

export type GetTransactionsResponse = TransactionListResponse

// ==================== CATEGORY API TYPES ====================

export interface CreateCategoryRequest {
  name: string
  description?: string
  type: CategoryType
  color?: string
  parentId?: EntityId
  budgetAmount?: number
  budgetPeriod?: Period
}

export interface UpdateCategoryRequest {
  name?: string
  description?: string
  type?: CategoryType
  color?: string
  parentId?: EntityId
  isActive?: boolean
}

export type CreateOrUpdateCategoryRequest = Partial<CreateCategoryRequest> & Partial<UpdateCategoryRequest>;


export interface CategoryQueryParams extends PaginationParams {
  search?: string
  type?: CategoryType
  parentId?: EntityId
  isActive?: boolean
}

export interface CategoryListResponse {
  categories: Category[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface CategoryStatsResponse {
  totalCategories: number
  incomeCategories: number
  expenseCategories: number
  categoriesWithCategoryGoal: number
  totalBudgetAmount: number
  categorySpending: {
    categoryId: EntityId
    categoryName: string
    spent: number
    budgetAmount?: number
    budgetUsagePercent?: number
  }[]
  monthlySpending: {
    month: string
    categories: {
      categoryId: EntityId
      categoryName: string
      amount: number
    }[]
  }[]
}

export interface CreateBudgetRequest {
  categoryId: EntityId
  amount: number
  period: Period
  startDate: string
  endDate?: string
  alertThreshold?: number
}

export interface UpdateBudgetRequest {
  amount?: number
  period?: Period
  startDate?: string
  endDate?: string
  alertThreshold?: number
  isActive?: boolean
}

// Category Response Types - Direct data, no wrapping
export type CategoryResponseData = Category & {
  startDate: string
  endDate: string
}
export type CreateCategoryResponse = CategoryResponseData

export type UpdateCategoryResponse = CategoryResponseData

export type GetCategoriesResponse = CategoryListResponse

// ==================== CONTACT API TYPES ====================

export interface CreateContactRequest {
  name: string
  description?: string
  contactTypeId: EntityId
  phone?: string
  email?: string
  address?: string
  isActive?: boolean
}

export interface UpdateContactRequest {
  name?: string
  description?: string
  contactTypeId?: EntityId
  phone?: string
  email?: string
  address?: string
  isActive?: boolean
}

export interface ContactQueryParams extends PaginationParams {
  search?: string
  contactTypeId?: EntityId
  isActive?: boolean
}

export interface ContactListResponse {
  contacts: Contact[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ContactStatsResponse {
  totalContacts: number
  activeContacts: number
  contactsByType: {
    contactTypeId: EntityId
    count: number
  }[]
  recentContacts: Contact[]
  contactsWithTransactions: number
}

// Contact Response Types - Direct data, no wrapping
export type CreateContactResponse = Contact

export type UpdateContactResponse = Contact

export type GetContactsResponse = ContactListResponse

// ==================== ACCOUNT TYPE API TYPES ====================

export interface CreateAccountTypeRequest {
  name: string
  color?: string
  isBankAccount?: boolean
  isDefault?: boolean
  isActive?: boolean
}

export interface UpdateAccountTypeRequest {
  name?: string
  color?: string
  isBankAccount?: boolean
  isDefault?: boolean
  isActive?: boolean
}

export interface AccountTypeQueryParams extends PaginationParams {
  search?: string
  name?: string
  category?: string
  isBankAccount?: boolean
  isDefault?: boolean
  isActive?: boolean
}

// Account Type Response Types
export interface AccountTypeListResponse {
  items: any[]
  total: number
  page: number
  limit: number
}

export type CreateAccountTypeResponse = any
export type UpdateAccountTypeResponse = any
export type GetAccountTypesResponse = AccountTypeListResponse

// ==================== RECURRING TRANSACTION API TYPES ====================

export interface CreateRecurringTransactionRequest {
  name: string
  description?: string
  amount: number
  kind: 'income' | 'expense' | 'transfer'
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
  startDate: string
  endDate?: string
  accountId?: EntityId
  categoryId?: EntityId
  contactId?: EntityId
  fromAccountId?: EntityId
  toAccountId?: EntityId
  isActive?: boolean
}

export interface UpdateRecurringTransactionRequest {
  name?: string
  description?: string
  amount?: number
  kind?: 'income' | 'expense' | 'transfer'
  frequency?: 'daily' | 'weekly' | 'monthly' | 'yearly'
  startDate?: string
  endDate?: string
  accountId?: EntityId
  categoryId?: EntityId
  contactId?: EntityId
  fromAccountId?: EntityId
  toAccountId?: EntityId
  isActive?: boolean
}

export interface RecurringTransactionQueryParams extends PaginationParams {
  search?: string
  kind?: 'income' | 'expense' | 'transfer'
  accountId?: EntityId
  categoryId?: EntityId
  frequency?: 'daily' | 'weekly' | 'monthly' | 'yearly'
  isActive?: boolean
}

// Recurring Transaction Response Types
export interface RecurringTransactionListResponse {
  items: any[]
  total: number
  page: number
  limit: number
}

export type CreateRecurringTransactionResponse = any
export type UpdateRecurringTransactionResponse = any
export type GetRecurringTransactionsResponse = RecurringTransactionListResponse

// ==================== DASHBOARD API TYPES ====================

export interface DashboardStatsResponse {
  user: {
    id: EntityId
    name: string
    email: string
    role: string
  }
  overview: {
    totalBalance: number
    totalAccounts: number
    totalTransactions: number
    totalCategories: number
    totalContacts: number
    netIncome: number
    monthlyIncome: number
    monthlyExpenses: number
  }
  recentTransactions: {
    id: EntityId
    kind: TransactionKind
    amount: number
    date: string
    name: string
    accountName?: string
    categoryName?: string
  }[]
  accountSummary: {
    accountId: EntityId
    accountName: string
    balance: number
    currency: string
    recentTransactionCount: number
  }[]
  categorySpending: {
    categoryId: EntityId
    categoryName: string
    type: 'income' | 'expense'
    amount: number
    budgetAmount?: number
    budgetUsagePercent?: number
  }[]
  monthlyTrends: {
    month: string
    income: number
    expenses: number
    netIncome: number
    transactionCount: number
  }[]
  upcomingBudgetAlerts: {
    categoryId: EntityId
    categoryName: string
    budgetAmount: number
    spentAmount: number
    usagePercent: number
    alertThreshold: number
  }[]
}

export interface FinancialSummaryResponse {
  totalAssets: number
  totalLiabilities: number
  netWorth: number
  liquidAssets: number
  investments: number
  monthlyIncome: number
  monthlyExpenses: number
  savingsRate: number
  expensesByCategory: {
    categoryId: EntityId
    categoryName: string
    amount: number
    percentage: number
  }[]
  incomeByCategory: {
    categoryId: EntityId
    categoryName: string
    amount: number
    percentage: number
  }[]
  accountBreakdown: {
    accountTypeId: EntityId
    accountTypeName: string
    totalBalance: number
    accountCount: number
    percentage: number
  }[]
}

export interface PeriodComparisonResponse {
  currentPeriod: {
    startDate: string
    endDate: string
    income: number
    expenses: number
    netIncome: number
    transactionCount: number
  }
  previousPeriod: {
    startDate: string
    endDate: string
    income: number
    expenses: number
    netIncome: number
    transactionCount: number
  }
  comparison: {
    incomeChange: number
    incomeChangePercent: number
    expenseChange: number
    expenseChangePercent: number
    netIncomeChange: number
    netIncomeChangePercent: number
    transactionCountChange: number
  }
}

// Legacy types for backward compatibility
export interface DashboardStats {
  totalAccounts: number
  totalTransactions: number
  totalIncome: number
  totalExpenses: number
  totalBalance: number
  recentTransactions: Transaction[]
  monthlyStats: {
    month: string
    income: number
    expenses: number
    balance: number
  }[]
  categoryStats: {
    categoryId: EntityId
    categoryName: string
    amount: number
    percentage: number
  }[]
}

export interface DashboardResponse {
  stats: DashboardStats
  user: User
  preferences: UserPreferences
}

// ==================== ERROR TYPES ====================

export interface ApiError {
  code: string
  message: string
  details?: any
}

export interface ValidationError extends ApiError {
  field: string
  value: any
}




