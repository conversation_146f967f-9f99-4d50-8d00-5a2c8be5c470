"use client"

import React, { useState, useCallback } from "react"
import { Taskbar } from "../ui/Taskbar"
import { useTaskbar, TaskbarItem } from "@/contexts/TaskbarContext"

interface TaskbarManagerProps {
  onRestoreAccount?: (item: TaskbarItem) => void
  onRestoreTransaction?: (item: TaskbarItem) => void
  onRestoreContact?: (item: TaskbarItem) => void
  onRestoreCategory?: (item: TaskbarItem) => void
  onRestoreBudget?: (item: TaskbarItem) => void
}

export function TaskbarManager({
  onRestoreAccount,
  onRestoreTransaction,
  onRestoreContact,
  onRestoreCategory,
  onRestoreBudget
}: TaskbarManagerProps) {
  const { items, restoreItem } = useTaskbar()

  const handleItemClick = useCallback((item: TaskbarItem) => {
    // Restore the item (update timestamp)
    restoreItem(item.id)

    // Call the appropriate restore handler
    switch (item.type) {
      case 'account':
        onRestoreAccount?.(item)
        break
      case 'transaction':
        onRestoreTransaction?.(item)
        break
      case 'contact':
        onRestoreContact?.(item)
        break
      case 'category':
        onRestoreCategory?.(item)
        break
      case 'budget':
        onRestoreBudget?.(item)
        break
      default:
        console.warn('Unknown taskbar item type:', item.type)
    }
  }, [restoreItem, onRestoreAccount, onRestoreTransaction, onRestoreContact, onRestoreCategory, onRestoreBudget])

  // Don't render if no items
  if (items.length === 0) {
    return null
  }

  return (
    <Taskbar
      onItemClick={handleItemClick}
      className="taskbar-manager"
    />
  )
}

// Hook for pages to easily integrate with taskbar
export function useTaskbarManager() {
  const [activeItems, setActiveItems] = useState<{
    account?: TaskbarItem
    transaction?: TaskbarItem
    contact?: TaskbarItem
    category?: TaskbarItem
    budget?: TaskbarItem
  }>({})

  const handleRestoreAccount = useCallback((item: TaskbarItem) => {
    setActiveItems(prev => ({ ...prev, account: item }))
  }, [])

  const handleRestoreTransaction = useCallback((item: TaskbarItem) => {
    setActiveItems(prev => ({ ...prev, transaction: item }))
  }, [])

  const handleRestoreContact = useCallback((item: TaskbarItem) => {
    setActiveItems(prev => ({ ...prev, contact: item }))
  }, [])

  const handleRestoreCategory = useCallback((item: TaskbarItem) => {
    setActiveItems(prev => ({ ...prev, category: item }))
  }, [])

  const handleRestoreBudget = useCallback((item: TaskbarItem) => {
    setActiveItems(prev => ({ ...prev, budget: item }))
  }, [])

  const clearActiveItem = useCallback((type: TaskbarItem['type']) => {
    setActiveItems(prev => ({ ...prev, [type]: undefined }))
  }, [])

  return {
    activeItems,
    handleRestoreAccount,
    handleRestoreTransaction,
    handleRestoreContact,
    handleRestoreCategory,
    handleRestoreBudget,
    clearActiveItem,
    TaskbarManager: (
      <TaskbarManager
        onRestoreAccount={handleRestoreAccount}
        onRestoreTransaction={handleRestoreTransaction}
        onRestoreContact={handleRestoreContact}
        onRestoreCategory={handleRestoreCategory}
        onRestoreBudget={handleRestoreBudget}
      />
    )
  }
}

// Global taskbar state management
interface GlobalTaskbarState {
  accountSidebarOpen: boolean
  transactionSidebarOpen: boolean
  contactSidebarOpen: boolean
  categorySidebarOpen: boolean
  CategoryGoalidebarOpen: boolean
  accountData?: any
  transactionData?: any
  contactData?: any
  categoryData?: any
  budgetData?: any
}

export function useGlobalTaskbarState() {
  const [state, setState] = useState<GlobalTaskbarState>({
    accountSidebarOpen: false,
    transactionSidebarOpen: false,
    contactSidebarOpen: false,
    categorySidebarOpen: false,
    CategoryGoalidebarOpen: false
  })

  const openAccountSidebar = useCallback((data?: any) => {
    setState(prev => ({
      ...prev,
      accountSidebarOpen: true,
      accountData: data
    }))
  }, [])

  const closeAccountSidebar = useCallback(() => {
    setState(prev => ({
      ...prev,
      accountSidebarOpen: false,
      accountData: undefined
    }))
  }, [])

  const openTransactionSidebar = useCallback((data?: any) => {
    setState(prev => ({
      ...prev,
      transactionSidebarOpen: true,
      transactionData: data
    }))
  }, [])

  const closeTransactionSidebar = useCallback(() => {
    setState(prev => ({
      ...prev,
      transactionSidebarOpen: false,
      transactionData: undefined
    }))
  }, [])

  const openContactSidebar = useCallback((data?: any) => {
    setState(prev => ({
      ...prev,
      contactSidebarOpen: true,
      contactData: data
    }))
  }, [])

  const closeContactSidebar = useCallback(() => {
    setState(prev => ({
      ...prev,
      contactSidebarOpen: false,
      contactData: undefined
    }))
  }, [])

  const openCategorySidebar = useCallback((data?: any) => {
    setState(prev => ({
      ...prev,
      categorySidebarOpen: true,
      categoryData: data
    }))
  }, [])

  const closeCategorySidebar = useCallback(() => {
    setState(prev => ({
      ...prev,
      categorySidebarOpen: false,
      categoryData: undefined
    }))
  }, [])

  const openCategoryGoalidebar = useCallback((data?: any) => {
    setState(prev => ({
      ...prev,
      CategoryGoalidebarOpen: true,
      budgetData: data
    }))
  }, [])

  const closeCategoryGoalidebar = useCallback(() => {
    setState(prev => ({
      ...prev,
      CategoryGoalidebarOpen: false,
      budgetData: undefined
    }))
  }, [])

  return {
    state,
    openAccountSidebar,
    closeAccountSidebar,
    openTransactionSidebar,
    closeTransactionSidebar,
    openContactSidebar,
    closeContactSidebar,
    openCategorySidebar,
    closeCategorySidebar,
    openCategoryGoalidebar,
    closeCategoryGoalidebar
  }
}
