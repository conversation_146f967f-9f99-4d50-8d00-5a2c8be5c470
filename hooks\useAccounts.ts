import { useState, useEffect, useCallback } from 'react'
import { AccountApiService } from '@/services/client'
import type { Account } from '@/lib/generated/prisma'
import type { AccountQueryParams, CreateAccountRequest, UpdateAccountRequest } from '@/types/api'

interface UseAccountsReturn {
  accounts: Account[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createAccount: (data: CreateAccountRequest) => Promise<boolean>
  updateAccount: (id: string, data: UpdateAccountRequest) => Promise<boolean>
  deleteAccount: (id: string ) => Promise<boolean>
}

export function useAccounts(params?: AccountQueryParams): UseAccountsReturn {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAccounts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await AccountApiService.getAccounts(params)
      
      if (response.success) {
        setAccounts(response.data)
      } else {
        setError('Failed to fetch accounts')
      }
    } catch (err) {
      setError('Error fetching accounts')
      console.error('Error fetching accounts:', err)
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    fetchAccounts()
  }, [fetchAccounts])

  const createAccount = useCallback(async (data: CreateAccountRequest): Promise<boolean> => {
    try {
      const response = await AccountApiService.createAccount(data)
      if (response.success) {
        await fetchAccounts() // Refresh list
        return true
      } else {
        setError('Failed to create account')
        return false
      }
    } catch (err) {
      setError('Error creating account')
      console.error('Error creating account:', err)
      return false
    }
  }, [fetchAccounts])

  const updateAccount = useCallback(async (id: string, data: UpdateAccountRequest): Promise<boolean> => {
    try {
      const response = await AccountApiService.updateAccount(id, data)
      if (response.success) {
        // Update local state optimistically
        setAccounts(prev => prev.map(account => 
          account.id === String(id) ? { ...account, ...data, updatedAt: new Date() } : account
        ))
        return true
      } else {
        setError('Failed to update account')
        return false
      }
    } catch (err) {
      setError('Error updating account')
      console.error('Error updating account:', err)
      return false
    }
  }, [])

  const deleteAccount = useCallback(async (id: string | number): Promise<boolean> => {
    try {
      const response = await AccountApiService.deleteAccount(String(id))
      if (response.success) {
        // Remove from local state
        setAccounts(prev => prev.filter(account => account.id !== String(id)))
        return true
      } else {
        setError('Failed to delete account')
        return false
      }
    } catch (err) {
      setError('Error deleting account')
      console.error('Error deleting account:', err)
      return false
    }
  }, [])

  return {
    accounts,
    loading,
    error,
    refetch: fetchAccounts,
    createAccount,
    updateAccount,
    deleteAccount
  }
}

export default useAccounts
