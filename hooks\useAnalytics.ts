import { useState, useEffect, useCallback } from 'react'
import { DashboardApiService } from '@/services/client'

interface DashboardStats {
  totalBalance: number
  monthlyIncome: number
  monthlyExpense: number
  transactionsCount: number
  accountsCount: number
  categoriesCount: number
  recentTransactions: any[]
  topCategories: any[]
  balanceHistory: any[]
}

interface MonthlyReport {
  income: number
  expense: number
  balance: number
  transactionsByCategory: any[]
  transactionsByAccount: any[]
  dailyTransactions: any[]
}

interface UseAnalyticsReturn {
  dashboardStats: DashboardStats | null
  monthlyReport: MonthlyReport | null
  categoryAnalytics: any | null
  accountAnalytics: any | null
  loading: boolean
  error: string | null
  fetchDashboard: (userId?: number) => Promise<void>
  fetchMonthlyReport: (userId: number, year: number, month: number) => Promise<void>
  fetchCategoryAnalytics: (userId: number, categoryId?: number) => Promise<void>
  fetchAccountAnalytics: (userId: number, accountId?: number) => Promise<void>
}

export function useAnalytics(): UseAnalyticsReturn {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null)
  const [monthlyReport, setMonthlyReport] = useState<MonthlyReport | null>(null)
  const [categoryAnalytics, setCategoryAnalytics] = useState<any | null>(null)
  const [accountAnalytics, setAccountAnalytics] = useState<any | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchDashboard = useCallback(async (userId?: number) => {
    try {
      setLoading(true)
      setError(null)
      const response = await analyticsService.getDashboard(userId)
      
      if (response.success) {
        setDashboardStats(response.data)
      } else {
        setError('Failed to fetch dashboard stats')
      }
    } catch (err) {
      setError('Error fetching dashboard stats')
      console.error('Error fetching dashboard stats:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchMonthlyReport = useCallback(async (userId: number, year: number, month: number) => {
    try {
      setLoading(true)
      setError(null)
      const response = await analyticsService.getMonthlyReport(userId, year, month)
      
      if (response.success) {
        setMonthlyReport(response.data)
      } else {
        setError('Failed to fetch monthly report')
      }
    } catch (err) {
      setError('Error fetching monthly report')
      console.error('Error fetching monthly report:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchCategoryAnalytics = useCallback(async (userId: number, categoryId?: number) => {
    try {
      setLoading(true)
      setError(null)
      const response = await analyticsService.getCategoryAnalytics(userId, categoryId)
      
      if (response.success) {
        setCategoryAnalytics(response.data)
      } else {
        setError('Failed to fetch category analytics')
      }
    } catch (err) {
      setError('Error fetching category analytics')
      console.error('Error fetching category analytics:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const fetchAccountAnalytics = useCallback(async (userId: number, accountId?: number) => {
    try {
      setLoading(true)
      setError(null)
      const response = await analyticsService.getAccountAnalytics(userId, accountId)
      
      if (response.success) {
        setAccountAnalytics(response.data)
      } else {
        setError('Failed to fetch account analytics')
      }
    } catch (err) {
      setError('Error fetching account analytics')
      console.error('Error fetching account analytics:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Auto-fetch dashboard on mount
  useEffect(() => {
    fetchDashboard(1) // TODO: Get from auth context
  }, [fetchDashboard])

  return {
    dashboardStats,
    monthlyReport,
    categoryAnalytics,
    accountAnalytics,
    loading,
    error,
    fetchDashboard,
    fetchMonthlyReport,
    fetchCategoryAnalytics,
    fetchAccountAnalytics
  }
}

export default useAnalytics
