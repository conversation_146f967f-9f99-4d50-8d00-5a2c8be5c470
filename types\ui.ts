// UI Component Types for Money Management App

import { <PERSON>actNode, MouseE<PERSON>, ChangeEvent } from 'react'
import { LucideIcon } from 'lucide-react'

// ==================== GENERIC COMPONENT TYPES ====================

export interface BaseComponentProps {
  className?: string
  children?: ReactNode
}

export interface LoadingState {
  loading?: boolean
  error?: string | null
}

// ==================== CARD COMPONENT TYPES ====================

export interface CardAction<T = any> {
  label: string
  icon?: LucideIcon
  onClick: (item: T) => void
  variant?: 'default' | 'destructive' | 'outline' | 'ghost'
  disabled?: boolean
  hidden?: boolean
}

export interface GenericCardProps<T> extends BaseComponentProps {
  item: T
  renderContent: (item: T) => ReactNode
  renderActions?: (item: T) => ReactNode
  actions?: CardAction<T>[]
  onClick?: (item: T) => void
  variant?: 'default' | 'compact' | 'detailed'
  hoverable?: boolean
  selectable?: boolean
  selected?: boolean
}

// ==================== LIST COMPONENT TYPES ====================

export interface ResponsiveGridCols {
  default: number
  sm?: number
  md?: number
  lg?: number
  xl?: number
  '2xl'?: number
}

export interface GenericListProps<T> extends BaseComponentProps, LoadingState {
  data: T[]
  renderCard: (item: T) => ReactNode
  emptyMessage?: string
  searchable?: boolean
  sortable?: boolean
  filterable?: boolean
  paginated?: boolean
  defaultItemsPerPage?: number
  itemsPerPageOptions?: number[]
  gridCols?: ResponsiveGridCols
  sidebarOpen?: boolean
  sidebarWidth?: number
  onItemClick?: (item: T) => void
}

// ==================== FILTER SYSTEM TYPES ====================

export interface FilterDefinition<T> {
  key: string
  label: string
  type: 'select' | 'multiselect' | 'date' | 'daterange' | 'number' | 'numberrange'
  options?: FilterOption[]
  predicate: (item: T, value: any) => boolean
  defaultValue?: any
}

export interface FilterOption {
  value: string | number
  label: string
  count?: number
  icon?: LucideIcon
  color?: string
}

export interface SortOption<T> {
  key: string
  label: string
  compareFn: (a: T, b: T) => number
}

export interface FilterConfig<T> {
  searchFields: (keyof T)[]
  filters: FilterDefinition<T>[]
  sortOptions: SortOption<T>[]
  defaultSort: SortOption<T>
}

export interface FilterSystemProps<T> extends BaseComponentProps {
  data: T[]
  onFilteredData: (filtered: T[]) => void
  config: FilterConfig<T>
  compact?: boolean
}

// ==================== SIDEBAR COMPONENT TYPES ====================

export interface SidebarConfig {
  minWidth: number
  maxWidth: number
  defaultWidth: number
  storageKey: string
}

export interface BaseSidebarProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title: string
  subtitle?: string
  width?: number
  onWidthChange?: (width: number) => void
  config?: SidebarConfig
  showCloseButton?: boolean
  closeOnEscape?: boolean
  closeOnOutsideClick?: boolean
}

// ==================== FORM COMPONENT TYPES ====================

export interface FormFieldProps extends BaseComponentProps {
  label: string
  name: string
  required?: boolean
  error?: string
  helpText?: string
  disabled?: boolean
}

export interface InputFieldProps extends FormFieldProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  placeholder?: string
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  autoComplete?: string
  maxLength?: number
  minLength?: number
}

export interface SelectFieldProps extends FormFieldProps {
  value: string
  onChange: (value: string) => void
  options: SelectOption[]
  placeholder?: string
  searchable?: boolean
  clearable?: boolean
  multiple?: boolean
}

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
  icon?: LucideIcon
  color?: string
  description?: string
}

export interface TextareaFieldProps extends FormFieldProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  rows?: number
  maxLength?: number
  resize?: boolean
}

export interface CheckboxFieldProps extends FormFieldProps {
  checked: boolean
  onChange: (checked: boolean) => void
  indeterminate?: boolean
}

export interface DateFieldProps extends FormFieldProps {
  value: Date | null
  onChange: (date: Date | null) => void
  placeholder?: string
  minDate?: Date
  maxDate?: Date
  showTime?: boolean
  format?: string
}

// ==================== MODAL & DIALOG TYPES ====================

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnEscape?: boolean
  closeOnOutsideClick?: boolean
  showCloseButton?: boolean
}

export interface ConfirmDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive'
}

// ==================== DATA VISUALIZATION TYPES ====================

export interface StatCard {
  label: string
  value: string | number
  icon?: LucideIcon
  color?: string
  trend?: {
    value: number
    isPositive: boolean
  }
  onClick?: () => void
}

export interface ChartDataPoint {
  label: string
  value: number
  color?: string
  metadata?: any
}

export interface ChartProps extends BaseComponentProps {
  data: ChartDataPoint[]
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'area'
  title?: string
  height?: number
  showLegend?: boolean
  showTooltip?: boolean
  colors?: string[]
}

// ==================== PAGINATION TYPES ====================

export interface PaginationProps extends BaseComponentProps {
  currentPage: number
  totalPages: number
  itemsPerPage: number
  totalItems: number
  onPageChange: (page: number) => void
  onItemsPerPageChange: (itemsPerPage: number) => void
  itemsPerPageOptions?: number[]
  showItemsPerPage?: boolean
  showPageInfo?: boolean
  compact?: boolean
}

// ==================== SEARCH & FILTER UI TYPES ====================

export interface SearchInputProps extends BaseComponentProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  debounceMs?: number
  showClearButton?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export interface FilterControlProps<T> extends BaseComponentProps {
  filter: FilterDefinition<T>
  value: any
  onChange: (value: any) => void
  size?: 'sm' | 'md' | 'lg'
}

export interface SortControlProps<T> extends BaseComponentProps {
  options: SortOption<T>[]
  value: SortOption<T>
  onChange: (option: SortOption<T>) => void
  size?: 'sm' | 'md' | 'lg'
}

// ==================== LAYOUT TYPES ====================

export interface PageHeaderProps extends BaseComponentProps {
  title: string
  subtitle?: string
  breadcrumbs?: Breadcrumb[]
  actions?: ReactNode
  stats?: StatCard[]
}

export interface Breadcrumb {
  label: string
  href?: string
  onClick?: () => void
  isActive?: boolean
}

export interface ResponsiveLayoutProps extends BaseComponentProps {
  sidebar?: ReactNode
  sidebarWidth?: number
  sidebarOpen?: boolean
  header?: ReactNode
  footer?: ReactNode
  maxWidth?: string
}

// ==================== UTILITY TYPES ====================

export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
export type Variant = 'default' | 'primary' | 'secondary' | 'destructive' | 'outline' | 'ghost'
export type Color = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'

export interface ThemeColors {
  primary: string
  secondary: string
  success: string
  warning: string
  error: string
  info: string
  background: string
  foreground: string
  muted: string
  border: string
}

// ==================== EVENT HANDLER TYPES ====================

export type ClickHandler<T = any> = (event: MouseEvent<HTMLElement>, data?: T) => void
export type ChangeHandler<T = any> = (value: T, event?: ChangeEvent<HTMLInputElement>) => void
export type SubmitHandler<T = any> = (data: T) => void | Promise<void>
