/**
 * CategoryGoal GraphQL Hooks
 *
 * React hooks for category goal-related GraphQL operations
 */

import React, { useMemo } from 'react'
import { useQuery, useMutation, useApolloClient } from '@apollo/client'
import {
  GET_CATEGORY_GOALS,
  GET_CATEGORY_GOAL_BY_ID,
  GET_CATEGORY_GOALS_BY_CATEGORY,
  GET_ACTIVE_CATEGORY_GOALS,
  SEARCH_CATEGORY_GOALS,
  CREATE_CATEGORY_GOAL,
  UPDATE_CATEGORY_GOAL,
  DELETE_CATEGORY_GOAL
} from '../operations'
import { convertToClientError } from '../errors'
import { extractNodes, extractPaginationInfo, useConnectionData } from '@/lib/utils/connection-helpers'
import type {
  CategoryGoal,
  CategoryGoalConnection,
  CreateCategoryGoalInput,
  UpdateCategoryGoalInput,
  Period
} from '../types'

// ==================== QUERY HOOKS ====================

/**
 * Hook to get category goals with pagination and connection helpers
 */
export function useCategoryGoals(variables?: {
  first?: number
  after?: string
  search?: string
  categoryId?: string
  period?: Period
  isActive?: boolean
}) {
  const queryResult = useQuery(GET_CATEGORY_GOALS, {
    variables,
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.categoryGoals,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.categoryGoals,
    pageInfo: queryResult.data?.categoryGoals?.pageInfo
  }
}

/**
 * Hook to get category goal by ID
 */
export function useCategoryGoal(id: string) {
  return useQuery(GET_CATEGORY_GOAL_BY_ID, {
    variables: { id },
    errorPolicy: 'all',
    skip: !id
  })
}

/**
 * Hook to get category goals by category with connection helpers
 */
export function useCategoryGoalsByCategory(categoryId: string) {
  const queryResult = useQuery(GET_CATEGORY_GOALS_BY_CATEGORY, {
    variables: { categoryId },
    errorPolicy: 'all',
    skip: !categoryId
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.categoryGoalsByCategory,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.categoryGoalsByCategory
  }
}

/**
 * Hook to get active category goals with connection helpers
 */
export function useActiveCategoryGoals() {
  const queryResult = useQuery(GET_ACTIVE_CATEGORY_GOALS, {
    errorPolicy: 'all'
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.activeCategoryGoals,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.activeCategoryGoals
  }
}

/**
 * Hook to search category goals with connection helpers
 */
export function useSearchCategoryGoals(search: string, first: number = 20) {
  const queryResult = useQuery(SEARCH_CATEGORY_GOALS, {
    variables: { search, first },
    errorPolicy: 'all',
    skip: !search
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.searchCategoryGoals,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.searchCategoryGoals
  }
}

// ==================== MUTATION HOOKS ====================

/**
 * Hook to create category goal
 */
export function useCreateCategoryGoal() {
  return useMutation(CREATE_CATEGORY_GOAL, {
    refetchQueries: ['GetCategoryGoals', 'GetActiveCategoryGoals'],
    onError: (error) => {
      console.error('Create category goal error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to update category goal
 */
export function useUpdateCategoryGoal() {
  return useMutation(UPDATE_CATEGORY_GOAL, {
    refetchQueries: ['GetCategoryGoals', 'GetCategoryGoalById', 'GetActiveCategoryGoals'],
    onError: (error) => {
      console.error('Update category goal error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to delete category goal
 */
export function useDeleteCategoryGoal() {
  return useMutation(DELETE_CATEGORY_GOAL, {
    refetchQueries: ['GetCategoryGoals', 'GetActiveCategoryGoals'],
    onError: (error) => {
      console.error('Delete category goal error:', convertToClientError(error))
    }
  })
}

// ==================== UTILITY HOOKS ====================

/**
 * Hook to refresh category goal data
 */
export function useRefreshCategoryGoals() {
  const client = useApolloClient()
  
  return () => {
    client.refetchQueries({
      include: ['GetCategoryGoals', 'GetActiveCategoryGoals']
    })
  }
}

/**
 * Hook to clear category goal cache
 */
export function useClearCategoryGoalCache() {
  const client = useApolloClient()
  
  return () => {
    client.cache.evict({ fieldName: 'categoryGoals' })
    client.cache.gc()
  }
}

/**
 * Hook to get category goal statistics
 */
export function useCategoryGoalStats() {
  const { data: categoryGoals, loading } = useActiveCategoryGoals()
  
  const stats = React.useMemo(() => {
    if (!categoryGoals?.categoryGoals?.edges) {
      return {
        totalGoals: 0,
        totalTargetAmount: 0,
        totalCurrentAmount: 0,
        averageProgress: 0,
        completedGoals: 0
      }
    }

    const goals = categoryGoals.categoryGoals.edges.map(edge => edge.node)
    const totalGoals = goals.length
    const totalTargetAmount = goals.reduce((sum, goal) => sum + goal.amount, 0)
    const totalCurrentAmount = goals.reduce((sum, goal) => sum + goal.currentAmount, 0)
    const averageProgress = totalGoals > 0 ? goals.reduce((sum, goal) => sum + goal.progress, 0) / totalGoals : 0
    const completedGoals = goals.filter(goal => goal.progress >= 100).length

    return {
      totalGoals,
      totalTargetAmount,
      totalCurrentAmount,
      averageProgress,
      completedGoals
    }
  }, [categoryGoals])

  return { stats, loading }
}
