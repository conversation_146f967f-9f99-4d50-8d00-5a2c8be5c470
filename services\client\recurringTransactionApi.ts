/**
 * Recurring Transaction API Service
 *
 * Handles recurring transaction-related API operations using GraphQL client
 */
import { apolloClient } from '@/lib/graphql/client'
import {
  GET_RECURRING_TRANSACTIONS,
  GET_RECURRING_TRANSACTION_BY_ID,
  GET_ACTIVE_RECURRING_TRANSACTIONS,
  GET_RECURRING_TRANSACTIONS_BY_KIND,
  GET_RECURRING_TRANSACTIONS_BY_FREQUENCY,
  SEARCH_RECURRING_TRANSACTIONS,
  CREATE_RECURRING_TRANSACTION,
  UPDATE_RECURRING_TRANSACTION,
  DELETE_RECURRING_TRANSACTION
} from '@/lib/graphql/operations'
import {
  convertToClientError,
  getUserFriendlyErrorMessage
} from '@/lib/graphql/errors'
import type {
  ApiResponse
} from '@/types/api'
import type {
  RecurringTransaction,
  RecurringTransactionConnection,
  CreateRecurringTransactionInput,
  UpdateRecurringTransactionInput,
  TransactionKind
} from '@/lib/graphql/types'
import type {
  RecurringTransactionConnectionResponse,
  RecurringTransactionConnectionParams
} from '@/types/graphql-connections'
export interface CreateRecurringTransactionRequest {
  name: string
  description?: string
  amount: number
  kind: TransactionKind
  frequency: string
  startDate: string
  endDate?: string
  accountId?: string
  categoryId?: string
  contactId?: string
  fromAccountId?: string
  toAccountId?: string
}
export interface UpdateRecurringTransactionRequest {
  name?: string
  description?: string
  amount?: number
  kind?: TransactionKind
  frequency?: string
  startDate?: string
  endDate?: string
  accountId?: string
  categoryId?: string
  contactId?: string
  fromAccountId?: string
  toAccountId?: string
  isActive?: boolean
}
export class RecurringTransactionApiService {
  // ==================== RECURRING TRANSACTION CRUD OPERATIONS ====================
  /**
   * Get all recurring transactions with filtering and pagination
   */
  static async getRecurringTransactions(params?: RecurringTransactionConnectionParams): Promise<RecurringTransactionConnectionResponse> {
    try {
      const { first = 10, after, ...filters } = params || {}
      const { data } = await apolloClient.query({
        query: GET_RECURRING_TRANSACTIONS,
        variables: {
          first,
          after,
          ...filters
        },
        fetchPolicy: 'network-only'
      })
      return data.recurringTransactions
    } catch (error: any) {
      console.error('Failed to fetch recurring transactions:', error);
      throw convertToClientError(error);
    }
  }
  /**
   * Get recurring transaction by ID
   */
  static async getRecurringTransactionById(id: string): Promise<ApiResponse<RecurringTransaction>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_RECURRING_TRANSACTION_BY_ID,
        variables: { id },
        fetchPolicy: 'cache-first'
      })
      if (!data.recurringTransaction) {
        throw new Error('Recurring transaction not found')
      }
      return {
        data: data.recurringTransaction,
        success: true,
        message: 'Recurring transaction retrieved successfully'
      }
    } catch (error: any) {
      console.error(`Failed to fetch recurring transaction ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Create new recurring transaction
   */
  static async createRecurringTransaction(data: CreateRecurringTransactionRequest): Promise<ApiResponse<RecurringTransaction>> {
    try {
      const input: CreateRecurringTransactionInput = {
        name: data.name,
        description: data.description,
        amount: data.amount,
        kind: data.kind,
        frequency: data.frequency,
        startDate: data.startDate,
        endDate: data.endDate,
        accountId: data.accountId,
        categoryId: data.categoryId,
        contactId: data.contactId,
        fromAccountId: data.fromAccountId,
        toAccountId: data.toAccountId
      }
      const { data: result } = await apolloClient.mutate({
        mutation: CREATE_RECURRING_TRANSACTION,
        variables: { input },
        refetchQueries: ['GetRecurringTransactions', 'GetActiveRecurringTransactions']
      })
      return {
        data: result.createRecurringTransaction,
        success: true,
        message: 'Recurring transaction created successfully'
      }
    } catch (error: any) {
      console.error('Failed to create recurring transaction:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Update recurring transaction
   */
  static async updateRecurringTransaction(id: string, data: UpdateRecurringTransactionRequest): Promise<ApiResponse<RecurringTransaction>> {
    try {
      const input: UpdateRecurringTransactionInput = {
        name: data.name,
        description: data.description,
        amount: data.amount,
        kind: data.kind,
        frequency: data.frequency,
        startDate: data.startDate,
        endDate: data.endDate,
        accountId: data.accountId,
        categoryId: data.categoryId,
        contactId: data.contactId,
        fromAccountId: data.fromAccountId,
        toAccountId: data.toAccountId,
        isActive: data.isActive
      }
      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_RECURRING_TRANSACTION,
        variables: { id, input },
        refetchQueries: ['GetRecurringTransactions', 'GetRecurringTransactionById', 'GetActiveRecurringTransactions']
      })
      return {
        data: result.updateRecurringTransaction,
        success: true,
        message: 'Recurring transaction updated successfully'
      }
    } catch (error: any) {
      console.error(`Failed to update recurring transaction ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Delete recurring transaction
   */
  static async deleteRecurringTransaction(id: string): Promise<ApiResponse<void>> {
    try {
      const { data: result } = await apolloClient.mutate({
        mutation: DELETE_RECURRING_TRANSACTION,
        variables: { id },
        refetchQueries: ['GetRecurringTransactions', 'GetActiveRecurringTransactions']
      })
      return {
        data: undefined,
        success: result.deleteRecurringTransaction,
        message: 'Recurring transaction deleted successfully'
      }
    } catch (error: any) {
      console.error(`Failed to delete recurring transaction ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  // ==================== CONVENIENCE METHODS ====================
  /**
   * Get active recurring transactions
   */
  static async getActiveRecurringTransactions(): Promise<ApiResponse<RecurringTransaction[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_ACTIVE_RECURRING_TRANSACTIONS,
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.recurringTransactions.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Active recurring transactions retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch active recurring transactions:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get recurring transactions by kind
   */
  static async getRecurringTransactionsByKind(kind: TransactionKind): Promise<ApiResponse<RecurringTransaction[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_RECURRING_TRANSACTIONS_BY_KIND,
        variables: { kind },
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.recurringTransactions.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Recurring transactions retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch recurring transactions by kind:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Search recurring transactions by name
   */
  static async searchRecurringTransactions(query: string, first: number = 10): Promise<RecurringTransactionConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: SEARCH_RECURRING_TRANSACTIONS,
        variables: { search: query, first },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.recurringTransactions,
        message: 'recurringTransactions retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to search recurring transactions:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Toggle active status of a recurring transaction
   */
  static async toggleRecurringTransactionStatus(id: string, isActive: boolean): Promise<ApiResponse<RecurringTransaction>> {
    return this.updateRecurringTransaction(id, { isActive })
  }
}
