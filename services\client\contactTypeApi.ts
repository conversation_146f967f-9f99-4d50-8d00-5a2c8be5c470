/**
 * Contact Type API Service
 * 
 * Static class for contact type-related operations
 */

import { ApiClient } from './apiClient'
import type {
  ApiResponse,
  PaginatedResponse
} from '@/types/api'
import type { ContactType } from '@/lib/generated/prisma'

export class ContactTypeApiService {

  // ==================== CONTACT TYPE CRUD OPERATIONS ====================

  /**
   * Get all contact types
   */
  static async getContactTypes(): Promise<ApiResponse<ContactType[]>> {
    try {
      return await ApiClient.get<ApiResponse<ContactType[]>>('/contact-types')
    } catch (error) {
      console.error('Failed to fetch contact types:', error)
      throw error
    }
  }

  /**
   * Get contact type by ID
   */
  static async getContactType(id: string): Promise<ApiResponse<ContactType>> {
    try {
      return await ApiClient.get<ApiResponse<ContactType>>(`/contact-types/${id}`)
    } catch (error) {
      console.error(`Failed to fetch contact type ${id}:`, error)
      throw error
    }
  }

  /**
   * Create new contact type
   */
  static async createContactType(data: Partial<ContactType>): Promise<ApiResponse<ContactType>> {
    try {
      return await ApiClient.post<ApiResponse<ContactType>>('/contact-types', data)
    } catch (error) {
      console.error('Failed to create contact type:', error)
      throw error
    }
  }

  /**
   * Update contact type
   */
  static async updateContactType(id: string, data: Partial<ContactType>): Promise<ApiResponse<ContactType>> {
    try {
      return await ApiClient.put<ApiResponse<ContactType>>(`/contact-types/${id}`, data)
    } catch (error) {
      console.error(`Failed to update contact type ${id}:`, error)
      throw error
    }
  }

  /**
   * Delete contact type
   */
  static async deleteContactType(id: string): Promise<ApiResponse<void>> {
    try {
      return await ApiClient.delete<ApiResponse<void>>(`/contact-types/${id}`)
    } catch (error) {
      console.error(`Failed to delete contact type ${id}:`, error)
      throw error
    }
  }
}
