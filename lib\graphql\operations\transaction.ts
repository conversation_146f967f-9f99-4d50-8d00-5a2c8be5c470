/**
 * GraphQL Operations for Transaction Management
 * 
 * Queries and mutations for transaction operations
 */

import { gql } from '@apollo/client'

// ==================== FRAGMENTS ====================

export const TRANSACTION_FRAGMENT = gql`
  fragment TransactionFragment on Transaction {
    id
    kind
    amount
    date
    description
    location
    note
    attachments
    isCleared
    isReconciled
    userId
    isActive
    accountId
    categoryId
    fromAccountId
    toAccountId
    createdAt
    updatedAt
  }
`

export const TRANSACTION_WITH_RELATIONS_FRAGMENT = gql`
  fragment TransactionWithRelationsFragment on Transaction {
    ...TransactionFragment
    account {
      id
      name
      currency
      accountType {
        name
        category
        color
      }
    }
    category {
      id
      name
      type
      color
      icon
    }
    fromAccount {
      id
      name
      currency
      accountType {
        name
        category
        color
      }
    }
    toAccount {
      id
      name
      currency
      accountType {
        name
        category
        color
      }
    }
  }
  ${TRANSACTION_FRAGMENT}
`

// ==================== QUERIES ====================

export const GET_TRANSACTIONS = gql`
  query GetTransactions(
    $first: Int
    $after: String
    $search: String
    $kind: TransactionKind
    $accountId: UUID
    $categoryId: UUID
    $startDate: DateTime
    $endDate: DateTime
    $isActive: Boolean
  ) {
    transactions(
      first: $first
      after: $after
      search: $search
      kind: $kind
      accountId: $accountId
      categoryId: $categoryId
      startDate: $startDate
      endDate: $endDate
      isActive: $isActive
    ) {
      edges {
        node {
          ...TransactionWithRelationsFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_TRANSACTIONS_BY_KIND = gql`
  query GetTransactionsByKind(
    $kind: TransactionKind!
    $first: Int
    $after: String
  ) {
    transactionsByKind(
      kind: $kind
      first: $first
      after: $after
    ) {
      edges {
        node {
          ...TransactionWithRelationsFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_TRANSACTION_BY_ID = gql`
  query GetTransactionById($id: UUID!) {
    transaction(id: $id) {
      ...TransactionWithRelationsFragment
      user {
        id
        name
        email
      }
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_RECENT_TRANSACTIONS = gql`
  query GetRecentTransactions($first: Int = 20) {
    transactions(first: $first, isActive: true) {
      edges {
        node {
          ...TransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_TRANSACTIONS_BY_ACCOUNT = gql`
  query GetTransactionsByAccount($accountId: UUID!, $first: Int = 50) {
    transactions(accountId: $accountId, first: $first, isActive: true) {
      edges {
        node {
          ...TransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_TRANSACTIONS_BY_CATEGORY = gql`
  query GetTransactionsByCategory($categoryId: UUID!, $first: Int = 50) {
    transactions(categoryId: $categoryId, first: $first, isActive: true) {
      edges {
        node {
          ...TransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const GET_TRANSACTIONS_BY_DATE_RANGE = gql`
  query GetTransactionsByDateRange(
    $startDate: DateTime!
    $endDate: DateTime!
    $first: Int = 100
  ) {
    transactions(
      startDate: $startDate
      endDate: $endDate
      first: $first
      isActive: true
    ) {
      edges {
        node {
          ...TransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const SEARCH_TRANSACTIONS = gql`
  query SearchTransactions($search: String!, $first: Int = 20) {
    transactions(search: $search, first: $first) {
      edges {
        node {
          ...TransactionWithRelationsFragment
        }
      }
      totalCount
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

// ==================== MUTATIONS ====================

export const CREATE_TRANSACTION = gql`
  mutation CreateTransaction($input: CreateTransactionInput!) {
    createTransaction(input: $input) {
      ...TransactionWithRelationsFragment
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const UPDATE_TRANSACTION = gql`
  mutation UpdateTransaction($id: UUID!, $input: UpdateTransactionInput!) {
    updateTransaction(id: $id, input: $input) {
      ...TransactionWithRelationsFragment
    }
  }
  ${TRANSACTION_WITH_RELATIONS_FRAGMENT}
`

export const DELETE_TRANSACTION = gql`
  mutation DeleteTransaction($id: UUID!) {
    deleteTransaction(id: $id)
  }
`
