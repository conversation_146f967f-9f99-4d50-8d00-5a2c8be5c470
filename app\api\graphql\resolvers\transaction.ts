/**
 * Transaction GraphQL Resolvers
 *
 * Resolvers for Transaction queries and mutations using Prisma directly
 */

import { GraphQLContext, requireAuth, requireOwnershipOrAdmin } from '../context'
import { prisma } from '@/lib/database'
import { GraphQLError } from 'graphql'

interface TransactionArgs {
  id: string
}

interface TransactionsArgs {
  first?: number
  after?: string
  search?: string
  kind?: 'income' | 'expense' | 'transfer'
  accountId?: string
  categoryId?: string
  startDate?: Date
  endDate?: Date
  isActive?: boolean
}

interface CreateTransactionArgs {
  input: {
    kind: 'income' | 'expense' | 'transfer'
    amount: number
    date: Date
    description?: string
    location?: string
    note?: string
    attachments?: string[]
    accountId?: string
    categoryId?: string
    fromAccountId?: string
    toAccountId?: string
  }
}

interface UpdateTransactionArgs {
  id: string
  input: {
    kind?: 'income' | 'expense' | 'transfer'
    amount?: number
    date?: Date
    description?: string
    location?: string
    note?: string
    attachments?: string[]
    isCleared?: boolean
    isReconciled?: boolean
    accountId?: string
    categoryId?: string
    fromAccountId?: string
    toAccountId?: string
    isActive?: boolean
  }
}

// Helper function to build connection response
function buildConnection<T>(
  items: T[],
  totalCount: number,
  first?: number,
  after?: string
) {
  const edges = items.map((item: any, index) => ({
    node: item,
    cursor: Buffer.from(`${after ? parseInt(Buffer.from(after, 'base64').toString()) + index + 1 : index}`).toString('base64')
  }))

  const hasNextPage = first ? items.length === first : false
  const hasPreviousPage = !!after

  return {
    edges,
    pageInfo: {
      hasNextPage,
      hasPreviousPage,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount
  }
}

export const transactionResolvers = {
  Query: {
    transactions: async (_: any, args: TransactionsArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const { first = 25, after, search, kind, accountId, categoryId, startDate, endDate, isActive } = args

      // Build where clause
      const where: any = {
        userId: user.id
      }

      if (search) {
        where.OR = [
          { description: { contains: search, mode: 'insensitive' } },
          { note: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (kind) where.kind = kind
      if (accountId) where.accountId = accountId
      if (categoryId) where.categoryId = categoryId
      if (isActive !== undefined) where.isActive = isActive

      if (startDate || endDate) {
        where.date = {}
        if (startDate) where.date.gte = startDate
        if (endDate) where.date.lte = endDate
      }

      // Convert cursor to skip value
      const skip = after ? parseInt(Buffer.from(after, 'base64').toString()) : 0

      const [transactions, totalCount] = await Promise.all([
        prisma.transaction.findMany({
          where,
          include: {
            account: true,
            category: true,
            contact: true
          },
          skip,
          take: first,
          orderBy: { date: 'desc' }
        }),
        prisma.transaction.count({ where })
      ])

      return buildConnection(transactions, totalCount, first, after)
    },

    transaction: async (_: any, args: TransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const transaction = await prisma.transaction.findFirst({
        where: {
          id: args.id,
          userId: user.id
        },
        include: {
          account: true,
          category: true,
          contact: true
        }
      })

      if (!transaction) {
        throw new GraphQLError('Transaction not found')
      }

      // Check ownership
      requireOwnershipOrAdmin(context, transaction.userId)

      return transaction
    }
  },

  Mutation: {
    createTransaction: async (_: any, args: CreateTransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // Verify account exists and belongs to user
        const account = await prisma.account.findFirst({
          where: {
            id: input.accountId,
            userId: user.id
          }
        })

        if (!account) {
          throw new GraphQLError('Account not found')
        }

        // Verify category exists and belongs to user (if provided)
        if (input.categoryId) {
          const category = await prisma.category.findFirst({
            where: {
              id: input.categoryId,
              userId: user.id
            }
          })

          if (!category) {
            throw new GraphQLError('Category not found')
          }
        }

        const transaction = await prisma.transaction.create({
          data: {
            kind: input.kind,
            amount: input.amount,
            description: input.description,
            note: input.note,
            date: input.date || new Date(),
            userId: user.id,
            accountId: input.accountId,
            categoryId: input.categoryId,
            contactId: input.contactId
          },
          include: {
            account: true,
            category: true,
            contact: true
          }
        })

        return transaction
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to create transaction')
      }
    },

    updateTransaction: async (_: any, args: UpdateTransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // First check if transaction exists and user has permission
        const existingTransaction = await prisma.transaction.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingTransaction) {
          throw new GraphQLError('Transaction not found')
        }

        requireOwnershipOrAdmin(context, existingTransaction.userId)

        // Verify account exists and belongs to user (if being updated)
        if (input.accountId) {
          const account = await prisma.account.findFirst({
            where: {
              id: input.accountId,
              userId: user.id
            }
          })

          if (!account) {
            throw new GraphQLError('Account not found')
          }
        }

        // Verify category exists and belongs to user (if being updated)
        if (input.categoryId) {
          const category = await prisma.category.findFirst({
            where: {
              id: input.categoryId,
              userId: user.id
            }
          })

          if (!category) {
            throw new GraphQLError('Category not found')
          }
        }

        const transaction = await prisma.transaction.update({
          where: { id: args.id },
          data: {
            kind: input.kind,
            amount: input.amount,
            description: input.description,
            note: input.note,
            date: input.date,
            accountId: input.accountId,
            categoryId: input.categoryId,
            contactId: input.contactId,
            isActive: input.isActive
          },
          include: {
            account: true,
            category: true,
            contact: true
          }
        })

        return transaction
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update transaction')
      }
    },

    deleteTransaction: async (_: any, args: TransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        // First check if transaction exists and user has permission
        const existingTransaction = await prisma.transaction.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingTransaction) {
          throw new GraphQLError('Transaction not found')
        }

        requireOwnershipOrAdmin(context, existingTransaction.userId)

        // Soft delete by setting isActive to false
        await prisma.transaction.update({
          where: { id: args.id },
          data: { isActive: false }
        })

        return true
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to delete transaction')
      }
    }
  },

  Transaction: {
    user: async (parent: any) => {
      if (parent.user) {
        return parent.user
      }

      return await prisma.user.findUnique({
        where: { id: parent.userId }
      })
    },

    account: async (parent: any) => {
      if (!parent.accountId) {
        return null
      }

      if (parent.account) {
        return parent.account
      }

      return await prisma.account.findUnique({
        where: { id: parent.accountId },
        include: { accountType: true }
      })
    },

    category: async (parent: any) => {
      if (!parent.categoryId) {
        return null
      }

      if (parent.category) {
        return parent.category
      }

      return await prisma.category.findUnique({
        where: { id: parent.categoryId }
      })
    },

    fromAccount: async (parent: any) => {
      if (!parent.fromAccountId) {
        return null
      }

      if (parent.fromAccount) {
        return parent.fromAccount
      }

      return await prisma.account.findUnique({
        where: { id: parent.fromAccountId },
        include: { accountType: true }
      })
    },

    toAccount: async (parent: any) => {
      if (!parent.toAccountId) {
        return null
      }

      if (parent.toAccount) {
        return parent.toAccount
      }

      return await prisma.account.findUnique({
        where: { id: parent.toAccountId },
        include: { accountType: true }
      })
    }
  }
}
