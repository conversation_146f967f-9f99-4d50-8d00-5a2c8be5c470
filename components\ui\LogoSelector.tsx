"use client"

import type React from "react"
import { useState } from "react"
import { Upload, X } from "lucide-react"

interface LogoSelectorProps {
  selectedLogo?: string
  selectedEmoji?: string
  onLogoChange: (logo: string, emoji?: string) => void
  onClose: () => void
}

const emojiOptions = [
  "🏦",
  "💳",
  "💰",
  "💵",
  "🏧",
  "💎",
  "📊",
  "📈",
  "💼",
  "🎯",
  "🔥",
  "⭐",
  "🚀",
  "💡",
  "🎨",
  "🎪",
  "🎭",
  "🎨",
  "🎯",
  "🎲",
]

export function LogoSelector({ selectedLogo, selectedEmoji, onLogoChange, onClose }: LogoSelectorProps) {
  const [activeTab, setActiveTab] = useState<"emoji" | "upload">("emoji")

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        const result = event.target?.result
        onLogoChange(result)
        onClose()
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/50">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Select Logo</h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-all duration-200"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6">
          <div className="flex gap-2 mb-4">
            <button
              onClick={() => setActiveTab("emoji")}
              className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === "emoji"
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
            >
              Emoji
            </button>
            <button
              onClick={() => setActiveTab("upload")}
              className={`flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === "upload"
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
            >
              Upload
            </button>
          </div>

          {activeTab === "emoji" && (
            <div className="grid grid-cols-5 gap-3">
              {emojiOptions.map((emoji) => (
                <button
                  key={emoji}
                  onClick={() => {
                    onLogoChange("", emoji)
                    onClose()
                  }}
                  className={`w-12 h-12 text-2xl rounded-lg border-2 transition-all duration-200 ${
                    selectedEmoji === emoji
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-500/20"
                      : "border-gray-300 dark:border-gray-600 hover:border-blue-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                  }`}
                >
                  {emoji}
                </button>
              ))}
            </div>
          )}

          {activeTab === "upload" && (
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">Upload your logo image</p>
                <input type="file" accept="image/*" onChange={handleFileUpload} className="hidden" id="logo-upload" />
                <label
                  htmlFor="logo-upload"
                  className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 cursor-pointer transition-colors"
                >
                  Choose File
                </label>
              </div>

              {selectedLogo && selectedLogo.startsWith("data:") && (
                <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <img
                    src={selectedLogo || "/placeholder.svg"}
                    alt="Selected logo"
                    className="w-10 h-10 rounded object-cover"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">Current logo</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
