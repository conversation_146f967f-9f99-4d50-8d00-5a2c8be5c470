"use client"

import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { TransactionCard } from "@/components/features/transactions/TransactionCard"
import { ListPageLayout } from "@/components/layout/ResponsiveLayout"
import { UnifiedFilterControls, useUnifiedFilters } from "@/components/shared/controls/UnifiedFilterControls"
import { EnhancedGenericList } from "@/components/shared/lists/EnhancedGenericList"
import { TransactionSidebar } from "@/components/ui/TransactionSidebar"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import { useEffect, useMemo, useState } from "react"

import { extractNodes } from "@/lib/utils/connection-helpers"
import { AccountApiService, CategoryApiService, ContactApiService, TransactionApiService } from "@/services/client"
// Using any for now due to type conflicts with filter system
import { ArrowUpDown, FileText, Plus, TrendingDown, TrendingUp } from "lucide-react"

function TransactionsPageContent() {
  const router = useRouter()
  const { user } = useAuth()

  // State management
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [transactions, setTransactions] = useState<any[]>([])
  const [accounts, setAccounts] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [contacts, setContacts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)

        // Fetch all required data in parallel
        const [transactionsRes, accountsRes, categoriesRes, contactsRes] = await Promise.all([
          TransactionApiService.getTransactions({}),
          AccountApiService.getAccounts({}),
          CategoryApiService.getCategories({}),
          ContactApiService.getContacts({})
        ])

        if (transactionsRes.success && transactionsRes.data) {
          setTransactions(extractNodes(transactionsRes.data))
        }

        if (accountsRes.success && accountsRes.data) {
          setAccounts(extractNodes(accountsRes.data))
        }

        if (categoriesRes.success && categoriesRes.data) {
          setCategories(extractNodes(categoriesRes.data))
        }

        if (contactsRes.success && contactsRes.data) {
          setContacts(extractNodes(contactsRes.data))
        }

        if (!transactionsRes.success) {
          setError('Failed to load transactions')
        }
      } catch (err) {
        setError('Failed to load data')
        console.error('Data fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [user?.id])



  // Statistics
  const stats = useMemo(() => {
    const total = transactions.length
    const income = transactions.filter(t => t.kind === "income")
    const expense = transactions.filter(t => t.kind === "expense")
    const transfer = transactions.filter(t => t.kind === "transfer")

    const totalIncome = income.reduce((sum, t) => sum + t.amount, 0)
    const totalExpense = expense.reduce((sum, t) => sum + t.amount, 0)

    return [
      {
        label: "Tổng giao dịch",
        value: total.toString(),
        icon: <FileText size={14} className="text-blue-500" />
      },
      {
        label: "Thu nhập",
        value: `${income.length} (${new Intl.NumberFormat("vi-VN", {
          style: "currency",
          currency: "VND",
          minimumFractionDigits: 0,
        }).format(totalIncome)})`,
        icon: <TrendingUp size={14} className="text-green-500" />
      },
      {
        label: "Chi tiêu",
        value: `${expense.length} (${new Intl.NumberFormat("vi-VN", {
          style: "currency",
          currency: "VND",
          minimumFractionDigits: 0,
        }).format(totalExpense)})`,
        icon: <TrendingDown size={14} className="text-red-500" />
      },
      {
        label: "Chuyển khoản",
        value: transfer.length.toString(),
        icon: <ArrowUpDown size={14} className="text-blue-500" />
      }
    ]
  }, [transactions])

  // Unified filters
  const {
    filteredData: filteredTransactions,
    activeFilter,
    setActiveFilter,
    searchTerm,
    setSearchTerm,
    activeSortField,
    currentSortDirection,
    handleSortChange
  } = useUnifiedFilters({
    data: transactions,
    filterField: 'type',
    searchFields: ['description', 'amount'],
    sortField: 'date',
    sortDirection: 'desc'
  })

  // Filter tabs for transaction types
  const filterTabs = [
    { value: 'all', label: 'Tất cả', count: transactions.length },
    { value: 'income', label: 'Thu nhập', count: transactions.filter(t => t.type === 'income').length },
    { value: 'expense', label: 'Chi tiêu', count: transactions.filter(t => t.type === 'expense').length },
    { value: 'transfer', label: 'Chuyển khoản', count: transactions.filter(t => t.type === 'transfer').length }
  ]

  // Sort options
  const sortOptions = [
    { value: 'date', label: 'Ngày giao dịch' },
    { value: 'amount', label: 'Số tiền' },
    { value: 'description', label: 'Mô tả' },
    { value: 'type', label: 'Loại giao dịch' }
  ]

  // Event handlers
  const handleAddTransaction = (type?: 'income' | 'expense' | 'transfer') => {
    setSelectedTransaction(null)
    setIsEditing(false)
    setSidebarOpen(true)
    // If type is specified, we could pass it to the sidebar
  }

  const handleEditTransaction = (transaction: any) => {
    setSelectedTransaction(transaction)
    setIsEditing(true)
    setSidebarOpen(true)
  }



  const handleViewTransaction = (transaction: any) => {
    console.log('TransactionsPage: handleViewTransaction called for transaction:', transaction.id)
    router.push(`/transactions/${transaction.id}`)
  }

  const handleSaveTransaction = async (transaction: any) => {
    try {


      // Validate required fields
      if (!transaction.amount || isNaN(Number(transaction.amount)) || Number(transaction.amount) <= 0) {
        setError('Vui lòng nhập số tiền hợp lệ')
        return
      }

      if (!transaction.date) {
        setError('Vui lòng chọn ngày giao dịch')
        return
      }

      // Validate account selection based on transaction kind
      // if (transaction.kind === 'transfer') {
        // if (!transaction.fromAccountId || !transaction.toAccountId) {
        //   setError('Vui lòng chọn tài khoản nguồn và tài khoản đích')
        //   return
        // }
        // if (transaction.fromAccountId === transaction.toAccountId) {
        //   setError('Tài khoản nguồn và đích không thể giống nhau')
        //   return
        // }
      // } else {
      //   // For income/expense, need accountId
      //   if (!transaction.accountId) {
      //     setError('Vui lòng chọn tài khoản')
      //     return
      //   }
      // }

      // if (transaction.kind === 'transfer' && (!transaction.fromAccountId || !transaction.toAccountId)) {
      //   setError('Vui lòng chọn tài khoản nguồn và tài khoản đích')
      //   return
      // }

      setLoading(true)
      let response

      // Prepare transaction data based on transaction kind - match API format exactly
      let transactionData: any

      if (transaction.kind === 'transfer') {
        transactionData = {
          kind: 'transfer',
          amount: Number(transaction.amount),
          date: transaction.date,
          fromAccountId: String(transaction.fromAccountId),
          toAccountId: String(transaction.toAccountId),
          description: transaction.description?.trim() || '',
          location: transaction.location?.trim() || '',
          note: transaction.note?.trim() || '',
          attachments: transaction.attachments || [],
          isCleared: Boolean(transaction.isCleared),
          isReconciled: Boolean(transaction.isReconciled)
        }
      } else {
        // For income/expense transactions - get accountId from appropriate field
        let accountId = transaction.accountId
        if (!accountId) {
          if (transaction.kind === 'expense' && transaction.fromAccountId) {
            accountId = transaction.fromAccountId
          } else if (transaction.kind === 'income' && transaction.toAccountId) {
            accountId = transaction.toAccountId
          }
        }

        transactionData = {
          kind: transaction.kind,
          amount: Number(transaction.amount),
          date: transaction.date,
          accountId: String(accountId),
          categoryId: transaction.categoryId ? String(transaction.categoryId) : undefined,
          description: transaction.description?.trim() || '',
          location: transaction.location?.trim() || '',
          note: transaction.note?.trim() || '',
          attachments: transaction.attachments || [],
          isCleared: Boolean(transaction.isCleared),
          isReconciled: Boolean(transaction.isReconciled)
        }
      }

      if (isEditing) {
        response = await TransactionApiService.updateTransaction(transaction.id, transactionData)
      } else {
        response = await TransactionApiService.createTransaction(transactionData)
      }

      if (response.success) {
        // Refresh transactions list
        const transactionsResponse = await TransactionApiService.getTransactions({})
        if (transactionsResponse.success) {
          setTransactions(transactionsResponse.data || [])
        }
        setSidebarOpen(false)
        setError(null)
        console.log(`Giao dịch đã được ${isEditing ? 'cập nhật' : 'tạo'} thành công`)
      } else {
        setError(response.error || 'Không thể lưu giao dịch. Vui lòng thử lại.')
      }
    } catch (error: any) {
      console.error('Error saving transaction:', error)
      setError(error?.message || 'Không thể lưu giao dịch. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }

  const handleCloseSidebar = () => {
    setSidebarOpen(false)
    setSelectedTransaction(null)
    setIsEditing(false)
  }

  // Render transaction card
  const renderTransactionCard = (transaction: any) => (
    <TransactionCard
      transaction={transaction}
      onEdit={handleEditTransaction}

      onView={handleViewTransaction}
      showAccountInfo={true}
      showCategoryInfo={true}
      variant="default"
      // TODO: Get account and category names from related data
      accountName={`Account ${transaction.accountId}`}
      categoryName={`Category ${transaction.categoryId}`}
    />
  )

  // Empty message based on filters
  const getEmptyMessage = () => {
    if (filteredTransactions.length === 0 && transactions.length > 0) {
      return "Không tìm thấy giao dịch nào phù hợp với bộ lọc hiện tại"
    }
    return "Chưa có giao dịch nào. Hãy tạo giao dịch đầu tiên để bắt đầu theo dõi tài chính!"
  }

  return (
    <>
      <ListPageLayout
        title="Giao dịch"
        subtitle="Theo dõi tất cả các giao dịch thu chi của bạn"
        stats={stats}
        actions={
          <Button
            onClick={() => handleAddTransaction()}
            className="flex items-center gap-2"
          >
            <Plus size={16} />
            Thêm giao dịch
          </Button>
        }
      >
        {/* Unified Filter Controls */}
        <UnifiedFilterControls
          filterTabs={filterTabs}
          activeFilter={activeFilter}
          onFilterChange={setActiveFilter}
          searchValue={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Tìm kiếm giao dịch..."
          sortOptions={sortOptions}
          activeSortField={activeSortField}
          sortDirection={currentSortDirection}
          onSortChange={handleSortChange}
          itemCount={filteredTransactions.length}
          itemLabel="giao dịch"
        />

        {/* Enhanced Generic List */}
        <EnhancedGenericList
          data={filteredTransactions}
          renderCard={renderTransactionCard}
          loading={loading}
          error={error}
          emptyMessage={getEmptyMessage()}
          searchable={false}
          sortable={false}
          filterable={false}
          paginated={true}
          defaultItemsPerPage={25}
          itemsPerPageOptions={[10, 25, 50, 100]}
          gridCols={{ default: 1, sm: 1, lg: 2, xl: 3, '2xl': 4 }}
          sidebarOpen={sidebarOpen}
          sidebarWidth={500}
          onItemClick={handleViewTransaction}
        />
      </ListPageLayout>

      {/* Transaction Sidebar */}
      <TransactionSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onSave={handleSaveTransaction}

        transaction={selectedTransaction}
        isEditing={isEditing}
        accounts={accounts}
        categories={categories}
        contacts={contacts}
      />

      {/* Toast Notifications */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 animate-fade-in">
          <div className="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="flex-1">{error}</span>
            <button
              onClick={() => setError(null)}
              className="text-white hover:text-gray-200 p-1"
              aria-label="Đóng"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Global Loading Indicator */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 flex items-center gap-3 shadow-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-700 dark:text-gray-200 text-sm font-medium">Đang xử lý...</span>
          </div>
        </div>
      )}
    </>
  )
}

export default function TransactionsPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <TransactionsPageContent />
    </ProtectedRoute>
  )
}

