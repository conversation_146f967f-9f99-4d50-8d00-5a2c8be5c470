"use client"

import { useState, useEffect, useCallback } from "react"

export type AnimationState = 'entering' | 'entered' | 'exiting' | 'exited'

interface UseSidebarAnimationOptions {
  isOpen: boolean
  duration?: number
  onEntered?: () => void
  onExited?: () => void
}

interface UseSidebarAnimationReturn {
  animationState: AnimationState
  animationClass: string
  isAnimating: boolean
}

export function useSidebarAnimation({
  isOpen,
  duration = 300,
  onEntered,
  onExited
}: UseSidebarAnimationOptions): UseSidebarAnimationReturn {
  const [animationState, setAnimationState] = useState<AnimationState>('exited')
  const [isAnimating, setIsAnimating] = useState(false)
  
  // Check if user prefers reduced motion
  const prefersReducedMotion = useCallback(() => {
    if (typeof window === 'undefined') return false
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }, [])
  
  useEffect(() => {
    if (isOpen) {
      // Opening animation
      setAnimationState('entering')
      setIsAnimating(true)
      
      const timer = setTimeout(() => {
        setAnimationState('entered')
        setIsAnimating(false)
        onEntered?.()
      }, prefersReducedMotion() ? 0 : duration)
      
      return () => clearTimeout(timer)
    } else {
      // Closing animation
      if (animationState === 'entered' || animationState === 'entering') {
        setAnimationState('exiting')
        setIsAnimating(true)
        
        const timer = setTimeout(() => {
          setAnimationState('exited')
          setIsAnimating(false)
          onExited?.()
        }, prefersReducedMotion() ? 0 : duration)
        
        return () => clearTimeout(timer)
      }
    }
  }, [isOpen, duration, animationState, onEntered, onExited, prefersReducedMotion])
  
  // Generate animation class based on state
  const getAnimationClass = useCallback((state: AnimationState): string => {
    if (prefersReducedMotion()) {
      return state === 'entered' || state === 'entering' ? 'opacity-100' : 'opacity-0'
    }
    
    switch (state) {
      case 'entering':
        return 'sidebar-enter'
      case 'entered':
        return 'sidebar-entered'
      case 'exiting':
        return 'sidebar-exit'
      case 'exited':
        return 'sidebar-exited'
      default:
        return ''
    }
  }, [prefersReducedMotion])
  
  return {
    animationState,
    animationClass: getAnimationClass(animationState),
    isAnimating
  }
}

// Utility hook for content animations
export function useSidebarContentAnimation({
  isOpen,
  delay = 100,
  duration = 400
}: {
  isOpen: boolean
  delay?: number
  duration?: number
}) {
  const [contentVisible, setContentVisible] = useState(false)
  
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        setContentVisible(true)
      }, delay)
      
      return () => clearTimeout(timer)
    } else {
      setContentVisible(false)
    }
  }, [isOpen, delay])
  
  return {
    contentVisible,
    contentClass: contentVisible ? 'sidebar-content-enter' : 'opacity-0'
  }
}
