"use client"

import React, { create<PERSON>ontext, use<PERSON>ontext, ReactNode } from "react"
import { useGlobalSidebarState, UseGlobalSidebarStateReturn } from "../hooks/useGlobalSidebarState"

interface GlobalSidebarContextType extends UseGlobalSidebarStateReturn {}

const GlobalSidebarContext = createContext<GlobalSidebarContextType | undefined>(undefined)

interface GlobalSidebarProviderProps {
  children: ReactNode
}

export function GlobalSidebarProvider({ children }: GlobalSidebarProviderProps) {
  const sidebarState = useGlobalSidebarState()

  return (
    <GlobalSidebarContext.Provider value={sidebarState}>
      {children}
    </GlobalSidebarContext.Provider>
  )
}

export function useGlobalSidebar(): GlobalSidebarContextType {
  const context = useContext(GlobalSidebarContext)
  if (context === undefined) {
    throw new Error('useGlobalSidebar must be used within a GlobalSidebarProvider')
  }
  return context
}

// Hook for form sidebars to register themselves with real-time updates
export function useFormSidebarIntegration(
  isOpen: boolean,
  width: number,
  sidebarType: string,
  isResizing?: boolean
) {
  const { setFormSidebarOpen, setFormSidebarWidth, setFormSidebarResizing } = useGlobalSidebar()

  React.useEffect(() => {
    if (isOpen) {
      setFormSidebarOpen(true, width, sidebarType)
    } else {
      setFormSidebarOpen(false)
    }
  }, [isOpen, width, sidebarType, setFormSidebarOpen])

  // Real-time width updates during resize
  React.useEffect(() => {
    if (isOpen) {
      setFormSidebarWidth(width)
    }
  }, [width, isOpen, setFormSidebarWidth])

  // Track resize state
  React.useEffect(() => {
    if (isOpen && isResizing !== undefined) {
      setFormSidebarResizing(isResizing)
    }
  }, [isResizing, isOpen, setFormSidebarResizing])
}

// Hook for AI sidebar integration
export function useAISidebarIntegration(
  isOpen: boolean,
  width: number
) {
  const { setAISidebarOpen } = useGlobalSidebar()

  React.useEffect(() => {
    setAISidebarOpen(isOpen, width)
  }, [isOpen, width, setAISidebarOpen])
}
