/**
 * Shared Service Utilities
 * 
 * Common utilities used across all services to reduce code duplication
 */

// EntityId is just a string UUID
type EntityId = string
import { ForbiddenError } from '@/lib/api/utils'

// ==================== TYPES ====================

export interface PaginationParams {
  page: number
  limit: number 
}

export interface PaginationResult<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface SortParams {
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

export interface SearchParams {
  search: string
  searchFields: string[]
}

// ==================== AUTHORIZATION UTILITIES ====================

/**
 * Validate user ownership of an entity
 */
export function validateOwnership(entity: { userId: EntityId }, userId: EntityId, entityType: string = 'resource'): void {
  if (entity.userId !== userId) {
    throw new ForbiddenError(`You can only access your own ${entityType}`)
  }
}

/**
 * Validate admin role access
 */
export function validateAdminAccess(userRole: string): void {
  if (userRole !== 'admin') {
    throw new ForbiddenError('Admin access required')
  }
}

// ==================== PAGINATION UTILITIES ====================

/**
 * Apply pagination to an array of items
 */
export function paginate<T>(items: T[], params: PaginationParams): PaginationResult<T> {
  const { page, limit } = params
  const total = items.length
  const totalPages = Math.ceil(total / limit)
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit

  const paginatedItems = items.slice(startIndex, endIndex)

  return {
    items: paginatedItems,
    total,
    page,
    limit,
    totalPages
  }
}

/**
 * Create pagination metadata
 */
export function createPaginationMeta(total: number, page: number, limit: number) {
  const totalPages = Math.ceil(total / limit)
  return {
    total,
    page,
    limit,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  }
}

// ==================== SEARCH UTILITIES ====================

/**
 * Apply search filter to items
 */
export function applySearch<T>(items: T[], params: SearchParams): T[] {
  const { search, searchFields } = params
  
  if (!search.trim()) {
    return items
  }

  const searchLower = search.toLowerCase()
  
  return items.filter(item => {
    return searchFields.some(field => {
      const value = (item as any)[field]
      if (value == null) return false
      
      return String(value).toLowerCase().includes(searchLower)
    })
  })
}

/**
 * Advanced search with multiple criteria
 */
export function applyAdvancedSearch<T>(
  items: T[], 
  searchTerms: string[], 
  searchFields: string[]
): T[] {
  if (searchTerms.length === 0) return items

  return items.filter(item => {
    return searchTerms.every(term => {
      const termLower = term.toLowerCase()
      return searchFields.some(field => {
        const value = (item as any)[field]
        if (value == null) return false
        return String(value).toLowerCase().includes(termLower)
      })
    })
  })
}

// ==================== SORTING UTILITIES ====================

/**
 * Apply sorting to items
 */
export function applySort<T>(items: T[], params: SortParams): T[] {
  const { sortBy, sortOrder } = params
  const order = sortOrder === 'asc' ? 1 : -1

  return [...items].sort((a, b) => {
    const aValue = (a as any)[sortBy]
    const bValue = (b as any)[sortBy]

    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0
    if (aValue == null) return order
    if (bValue == null) return -order

    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return aValue.localeCompare(bValue) * order
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return (aValue - bValue) * order
    }

    // Handle dates
    if (aValue instanceof Date && bValue instanceof Date) {
      return (aValue.getTime() - bValue.getTime()) * order
    }

    // Handle date strings
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const aDate = new Date(aValue)
      const bDate = new Date(bValue)
      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
        return (aDate.getTime() - bDate.getTime()) * order
      }
    }

    // Fallback to string comparison
    return String(aValue).localeCompare(String(bValue)) * order
  })
}

/**
 * Multi-field sorting
 */
export function applyMultiSort<T>(items: T[], sortFields: Array<{ field: string; order: 'asc' | 'desc' }>): T[] {
  return [...items].sort((a, b) => {
    for (const { field, order } of sortFields) {
      const aValue = (a as any)[field]
      const bValue = (b as any)[field]
      const orderMultiplier = order === 'asc' ? 1 : -1

      if (aValue == null && bValue == null) continue
      if (aValue == null) return orderMultiplier
      if (bValue == null) return -orderMultiplier

      let comparison = 0
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue)
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue
      } else {
        comparison = String(aValue).localeCompare(String(bValue))
      }

      if (comparison !== 0) {
        return comparison * orderMultiplier
      }
    }
    return 0
  })
}

// ==================== FILTERING UTILITIES ====================

/**
 * Apply filters to items
 */
export function applyFilters<T>(items: T[], filters: Record<string, any>): T[] {
  return items.filter(item => {
    return Object.entries(filters).every(([key, value]) => {
      if (value == null || value === '') return true
      
      const itemValue = (item as any)[key]
      
      // Handle boolean filters
      if (typeof value === 'boolean') {
        return itemValue === value
      }
      
      // Handle array filters (multiple selection)
      if (Array.isArray(value)) {
        return value.length === 0 || value.includes(itemValue)
      }
      
      // Handle range filters
      if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
        const numValue = Number(itemValue)
        return numValue >= value.min && numValue <= value.max
      }
      
      // Handle exact match
      return itemValue === value
    })
  })
}

// ==================== QUERY PROCESSING ====================

/**
 * Process complete query with search, filter, sort, and pagination
 */
export function processQuery<T>(
  items: T[],
  params: {
    search?: string
    searchFields?: string[]
    filters?: Record<string, any>
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    page?: number
    limit?: number
  }
): PaginationResult<T> {
  let processedItems = [...items]

  // Apply search
  if (params.search && params.searchFields) {
    processedItems = applySearch(processedItems, {
      search: params.search,
      searchFields: params.searchFields
    })
  }

  // Apply filters
  if (params.filters) {
    processedItems = applyFilters(processedItems, params.filters)
  }

  // Apply sorting
  if (params.sortBy) {
    processedItems = applySort(processedItems, {
      sortBy: params.sortBy,
      sortOrder: params.sortOrder || 'asc'
    })
  }

  // Apply pagination
  return paginate(processedItems, {
    page: params.page || 1,
    limit: params.limit || 25
  })
}

// ==================== STATISTICS UTILITIES ====================

/**
 * Calculate basic statistics for numeric fields
 */
export function calculateStats(items: any[], field: string) {
  const values = items
    .map(item => Number(item[field]))
    .filter(value => !isNaN(value))

  if (values.length === 0) {
    return { count: 0, sum: 0, average: 0, min: 0, max: 0 }
  }

  const sum = values.reduce((acc, val) => acc + val, 0)
  const average = sum / values.length
  const min = Math.min(...values)
  const max = Math.max(...values)

  return {
    count: values.length,
    sum,
    average,
    min,
    max
  }
}

/**
 * Group items by field value
 */
export function groupBy<T>(items: T[], field: string): Record<string, T[]> {
  return items.reduce((groups, item) => {
    const key = String((item as any)[field])
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(item)
    return groups
  }, {} as Record<string, T[]>)
}
