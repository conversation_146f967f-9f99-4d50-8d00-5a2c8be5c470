/**
 * Loading Components
 * 
 * Reusable loading indicators for consistent user feedback
 */

import React from 'react'
import { Loader2 } from 'lucide-react'

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

const spinnerSizes = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  return (
    <Loader2 
      className={`animate-spin ${spinnerSizes[size]} ${className}`}
      aria-label="Đang tải..."
    />
  )
}

export interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
  backdrop?: boolean
  size?: 'sm' | 'md' | 'lg'
}

const overlayMessageSizes = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg'
}

export function LoadingOverlay({ 
  isVisible, 
  message = 'Đang xử lý...', 
  backdrop = true,
  size = 'md'
}: LoadingOverlayProps) {
  if (!isVisible) return null

  return (
    <div 
      className={`fixed inset-0 z-50 flex items-center justify-center ${
        backdrop ? 'bg-black/20 backdrop-blur-sm' : ''
      }`}
      role="status"
      aria-live="polite"
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center gap-3 shadow-lg max-w-sm">
        <LoadingSpinner size={size === 'sm' ? 'md' : size === 'lg' ? 'xl' : 'lg'} className="text-blue-500" />
        <span className={`text-gray-700 dark:text-gray-200 font-medium ${overlayMessageSizes[size]}`}>
          {message}
        </span>
      </div>
    </div>
  )
}

export interface LoadingButtonProps {
  isLoading: boolean
  children: React.ReactNode
  loadingText?: string
  disabled?: boolean
  className?: string
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

export function LoadingButton({
  isLoading,
  children,
  loadingText,
  disabled,
  className = '',
  onClick,
  type = 'button'
}: LoadingButtonProps) {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`inline-flex items-center justify-center gap-2 ${className} ${
        isLoading || disabled ? 'opacity-50 cursor-not-allowed' : ''
      }`}
    >
      {isLoading && <LoadingSpinner size="sm" />}
      {isLoading && loadingText ? loadingText : children}
    </button>
  )
}

export interface LoadingCardProps {
  isLoading: boolean
  children: React.ReactNode
  loadingMessage?: string
  className?: string
}

export function LoadingCard({ 
  isLoading, 
  children, 
  loadingMessage = 'Đang tải...', 
  className = '' 
}: LoadingCardProps) {
  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="flex items-center justify-center gap-3">
          <LoadingSpinner size="md" className="text-blue-500" />
          <span className="text-gray-600 dark:text-gray-400">{loadingMessage}</span>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

export interface LoadingSkeletonProps {
  lines?: number
  className?: string
}

export function LoadingSkeleton({ lines = 3, className = '' }: LoadingSkeletonProps) {
  return (
    <div className={`animate-pulse space-y-3 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`h-4 bg-gray-200 dark:bg-gray-700 rounded ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  )
}

export interface LoadingTableProps {
  rows?: number
  columns?: number
  className?: string
}

export function LoadingTable({ rows = 5, columns = 4, className = '' }: LoadingTableProps) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="space-y-3">
        {/* Header */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <div key={`header-${index}`} className="h-4 bg-gray-300 dark:bg-gray-600 rounded" />
          ))}
        </div>
        
        {/* Rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={`row-${rowIndex}`} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div key={`cell-${rowIndex}-${colIndex}`} className="h-4 bg-gray-200 dark:bg-gray-700 rounded" />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

// Loading states for specific use cases
export function PageLoading({ message = 'Đang tải trang...' }: { message?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="xl" className="text-blue-500 mx-auto mb-4" />
        <p className="text-gray-600 dark:text-gray-400 text-lg">{message}</p>
      </div>
    </div>
  )
}

export function SectionLoading({ message = 'Đang tải...' }: { message?: string }) {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <LoadingSpinner size="lg" className="text-blue-500 mx-auto mb-3" />
        <p className="text-gray-600 dark:text-gray-400">{message}</p>
      </div>
    </div>
  )
}
