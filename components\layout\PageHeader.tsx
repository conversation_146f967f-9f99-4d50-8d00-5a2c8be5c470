"use client"

import React from "react"
import { Menu } from "lucide-react"

interface PageHeaderProps {
  title: string
  subtitle?: string
  stats?: Array<{
    label: string
    value: string
    icon?: React.ReactNode
    color?: string
  }>
  actions?: React.ReactNode
  onMenuClick: () => void
  className?: string
}

export function PageHeader({
  title,
  subtitle,
  stats = [],
  actions,
  onMenuClick,
  className = ""
}: PageHeaderProps) {
  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={onMenuClick}
        className="lg:hidden fixed top-4 left-4 z-40 p-2 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg shadow-lg transition-colors"
      >
        <Menu size={20} />
      </button>

      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-2 ${className}`}>
        <div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>
          {subtitle && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
          {stats.length > 0 && (
            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
              {stats.map((stat, index) => (
                <span
                  key={index}
                  className={`flex items-center gap-1 ${stat.color || ''}`}
                >
                  {stat.icon}
                  {stat.label}: {stat.value}
                </span>
              ))}
            </div>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
    </>
  )
}
