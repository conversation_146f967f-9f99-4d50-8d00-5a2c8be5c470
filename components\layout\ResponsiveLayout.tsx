"use client"

import React from "react"
import { PageHeader } from "./PageHeader"
import { cn } from "@/lib/utils"

interface ResponsiveLayoutProps {
  children: React.ReactNode
  className?: string
  
  // Page header props
  title?: string
  subtitle?: string
  stats?: Array<{
    label: string
    value: string
    icon?: React.ReactNode
    color?: string
  }>
  actions?: React.ReactNode
  onMenuClick?: () => void
  
  // Layout configuration
  maxWidth?: "none" | "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  spacing?: "tight" | "normal" | "loose"
  background?: "default" | "minimal" | "gradient"
  
  // Content sections
  headerContent?: React.ReactNode
  sidebarContent?: React.ReactNode
  footerContent?: React.ReactNode
  
  // Responsive behavior
  mobileFullWidth?: boolean
  hideHeaderOnMobile?: boolean
}

const maxWidthClasses = {
  none: "",
  sm: "max-w-sm",
  md: "max-w-md", 
  lg: "max-w-lg",
  xl: "max-w-xl",
  "2xl": "max-w-2xl",
  full: "w-full"
}

const spacingClasses = {
  tight: "p-2 lg:p-3 space-y-2",
  normal: "p-3 lg:p-4 space-y-3", 
  loose: "p-4 lg:p-6 space-y-4"
}

const backgroundClasses = {
  default: "min-h-screen",
  minimal: "min-h-screen bg-gray-50/50 dark:bg-gray-900/50",
  gradient: "min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800"
}

export function ResponsiveLayout({
  children,
  className = "",
  title,
  subtitle,
  stats = [],
  actions,
  onMenuClick = () => {},
  maxWidth = "full",
  spacing = "normal",
  background = "default",
  headerContent,
  sidebarContent,
  footerContent,
  mobileFullWidth = true,
  hideHeaderOnMobile = false
}: ResponsiveLayoutProps) {
  const containerClasses = `
    ${backgroundClasses[background]}
    ${className}
  `
  
  const mainClasses = `
    ${spacingClasses[spacing]}
    ${maxWidth !== "full" ? `mx-auto ${maxWidthClasses[maxWidth]}` : ""}
    ${mobileFullWidth ? "w-full" : ""}
  `
  
  return (
    <div className={containerClasses}>
      <main className={mainClasses}>
        {/* Page Header */}
        {(title || headerContent) && (
          <div className={hideHeaderOnMobile ? "hidden sm:block" : ""}>
            {title ? (
              <PageHeader
                title={title}
                subtitle={subtitle}
                stats={stats}
                actions={actions}
                onMenuClick={onMenuClick}
              />
            ) : (
              headerContent
            )}
          </div>
        )}
        
        {/* Main Content */}
        <div className="flex gap-6">
          {/* Primary Content */}
          <div className={`flex-1 ${sidebarContent ? "min-w-0" : ""}`}>
            {children}
          </div>
          
          {/* Sidebar Content */}
          {sidebarContent && (
            <div className="hidden lg:block w-80 flex-shrink-0">
              {sidebarContent}
            </div>
          )}
        </div>
        
        {/* Footer Content */}
        {footerContent && (
          <div className="mt-6">
            {footerContent}
          </div>
        )}
      </main>
    </div>
  )
}

// Specialized layout variants
export function DashboardLayout({ 
  children, 
  stats = [],
  actions,
  className = ""
}: {
  children: React.ReactNode
  stats?: Array<{
    label: string
    value: string
    icon?: React.ReactNode
    color?: string
  }>
  actions?: React.ReactNode
  className?: string
}) {
  return (
    <ResponsiveLayout
      title="Dashboard"
      subtitle="Tổng quan tài chính cá nhân"
      stats={stats}
      actions={actions}
      background="gradient"
      spacing="normal"
      className={className}
    >
      {children}
    </ResponsiveLayout>
  )
}

export function ListPageLayout({ 
  children,
  title,
  subtitle,
  stats = [],
  actions,
  searchBar,
  filters,
  className = ""
}: {
  children: React.ReactNode
  title: string
  subtitle?: string
  stats?: Array<{
    label: string
    value: string
    icon?: React.ReactNode
    color?: string
  }>
  actions?: React.ReactNode
  searchBar?: React.ReactNode
  filters?: React.ReactNode
  className?: string
}) {
  return (
    <ResponsiveLayout
      title={title}
      subtitle={subtitle}
      stats={stats}
      actions={actions}
      spacing="normal"
      className={className}
    >
      {/* Search and Filters */}
      {(searchBar || filters) && (
        <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
          {searchBar && (
            <div className="flex-1 w-full sm:w-auto">
              {searchBar}
            </div>
          )}
          {filters && (
            <div className="flex items-center gap-2">
              {filters}
            </div>
          )}
        </div>
      )}
      
      {/* Main Content */}
      {children}
    </ResponsiveLayout>
  )
}

export function DetailPageLayout({ 
  children,
  title,
  subtitle,
  breadcrumbs,
  actions,
  tabs,
  className = ""
}: {
  children: React.ReactNode
  title: string
  subtitle?: string
  breadcrumbs?: React.ReactNode
  actions?: React.ReactNode
  tabs?: React.ReactNode
  className?: string
}) {
  return (
    <ResponsiveLayout
      title={title}
      subtitle={subtitle}
      actions={actions}
      spacing="normal"
      className={className}
    >
      {/* Breadcrumbs */}
      {breadcrumbs && (
        <div className="mb-4">
          {breadcrumbs}
        </div>
      )}
      
      {/* Tabs */}
      {tabs && (
        <div className="mb-6">
          {tabs}
        </div>
      )}
      
      {/* Content */}
      {children}
    </ResponsiveLayout>
  )
}

export function FormPageLayout({ 
  children,
  title,
  subtitle,
  actions,
  className = ""
}: {
  children: React.ReactNode
  title: string
  subtitle?: string
  actions?: React.ReactNode
  className?: string
}) {
  return (
    <ResponsiveLayout
      title={title}
      subtitle={subtitle}
      actions={actions}
      maxWidth="2xl"
      spacing="normal"
      className={className}
    >
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        {children}
      </div>
    </ResponsiveLayout>
  )
}

// Utility components for common layout patterns
export function ContentSection({ 
  title,
  subtitle,
  children,
  actions,
  className = ""
}: {
  title?: string
  subtitle?: string
  children: React.ReactNode
  actions?: React.ReactNode
  className?: string
}) {
  return (
    <div className={`space-y-4 ${className}`}>
      {(title || actions) && (
        <div className="flex items-center justify-between">
          <div>
            {title && (
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex items-center gap-2">
              {actions}
            </div>
          )}
        </div>
      )}
      {children}
    </div>
  )
}

export function GridSection({ 
  children,
  cols = { default: 1, sm: 2, lg: 3, xl: 4 },
  gap = "4",
  className = ""
}: {
  children: React.ReactNode
  cols?: {
    default: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: "2" | "3" | "4" | "6" | "8"
  className?: string
}) {
  const { default: def, sm, md, lg, xl } = cols
  const gridClasses = `
    grid gap-${gap} grid-cols-${def}
    ${sm ? `sm:grid-cols-${sm}` : ''}
    ${md ? `md:grid-cols-${md}` : ''}
    ${lg ? `lg:grid-cols-${lg}` : ''}
    ${xl ? `xl:grid-cols-${xl}` : ''}
    ${className}
  `
  
  return (
    <div className={gridClasses}>
      {children}
    </div>
  )
}
