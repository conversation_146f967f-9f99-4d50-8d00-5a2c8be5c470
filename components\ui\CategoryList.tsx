"use client"

import React, { useState } from "react"
import { CategoryCard } from "./CategoryCard"
import { ChevronDown, ChevronUp, SortAsc, SortDesc, Filter, FolderTree } from "lucide-react"

interface Category {
  id: number
  name: string
  description?: string
  type: "income" | "expense"
  color: string
  icon?: string
  isActive?: boolean
  transactionCount?: number
  totalAmount?: number
}

interface CategoryListProps {
  categories: Category[]
  onEdit?: (category: Category) => void
  formatCurrency?: (amount: number) => string
  showFilters?: boolean
}

type SortField = "name" | "type" | "transactionCount" | "totalAmount"
type SortDirection = "asc" | "desc"

export function CategoryList({ 
  categories, 
  onEdit, 
  onDelete, 
  formatCurrency,
  showFilters = true 
}: CategoryListProps) {
  const [sortField, setSortField] = useState<SortField>("name")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")
  const [showSortOptions, setShowSortOptions] = useState(false)
  const [filterType, setFilterType] = useState<"all" | "income" | "expense">("all")

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
    setShowSortOptions(false)
  }

  const filteredCategories = categories.filter(category => {
    if (filterType === "all") return true
    return category.type === filterType
  })

  const sortedCategories = [...filteredCategories].sort((a, b) => {
    let aValue: any
    let bValue: any

    switch (sortField) {
      case "name":
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case "type":
        aValue = a.type
        bValue = b.type
        break
      case "transactionCount":
        aValue = a.transactionCount || 0
        bValue = b.transactionCount || 0
        break
      case "totalAmount":
        aValue = a.totalAmount || 0
        bValue = b.totalAmount || 0
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
    return 0
  })

  const getSortLabel = (field: SortField) => {
    const labels = {
      name: "Name",
      type: "Type",
      transactionCount: "Transactions",
      totalAmount: "Amount"
    }
    return labels[field]
  }

  if (categories.length === 0) {
    return (
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-3xl border border-gray-200/50 dark:border-gray-700/50 p-8 text-center">
        <div className="text-gray-400 dark:text-gray-500 mb-2">
          <FolderTree size={48} className="mx-auto mb-4 opacity-50" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No categories found
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Create your first category to organize your transactions.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Filter and Sort Controls */}
      {showFilters && (
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {filteredCategories.length} categor{filteredCategories.length !== 1 ? 'ies' : 'y'}
              </span>
              
              {/* Type Filter */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Type:</span>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as "all" | "income" | "expense")}
                  className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All</option>
                  <option value="income">Income</option>
                  <option value="expense">Expense</option>
                </select>
              </div>
            </div>
            
            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortOptions(!showSortOptions)}
                className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                {sortDirection === "asc" ? <SortAsc size={16} /> : <SortDesc size={16} />}
                Sort by {getSortLabel(sortField)}
                {showSortOptions ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>

              {showSortOptions && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-10">
                  {(["name", "type", "transactionCount", "totalAmount"] as SortField[]).map((field) => (
                    <button
                      key={field}
                      onClick={() => handleSort(field)}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                        sortField === field 
                          ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20" 
                          : "text-gray-700 dark:text-gray-300"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        {getSortLabel(field)}
                        {sortField === field && (
                          sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Category Cards */}
      <div className="space-y-3">
        {sortedCategories.map((category) => (
          <CategoryCard
            key={category.id}
            category={category}
            onEdit={onEdit}
            onDelete={onDelete}
            formatCurrency={formatCurrency}
          />
        ))}
      </div>
    </div>
  )
}
