/**
 * RecurringTransaction GraphQL Hooks
 * 
 * React hooks for recurring transaction-related GraphQL operations
 */

import { extractNodes, extractPaginationInfo, useConnectionData } from '@/lib/utils/connection-helpers'
import { useApolloClient, useMutation, useQuery } from '@apollo/client'
import React from 'react'
import { convertToClientError } from '../errors'
import {
  CREATE_RECURRING_TRANSACTION,
  DELETE_RECURRING_TRANSACTION,
  GET_ACTIVE_RECURRING_TRANSACTIONS,
  GET_RECURRING_TRANSACTIONS,
  GET_RECURRING_TRANSACTIONS_BY_FREQUENCY,
  GET_RECURRING_TRANSACTIONS_BY_KIND,
  GET_RECURRING_TRANSACTION_BY_ID,
  SEARCH_RECURRING_TRANSACTIONS,
  UPDATE_RECURRING_TRANSACTION
} from '../operations'
import type {
  TransactionKind
} from '../types'

// ==================== QUERY HOOKS ====================

/**
 * Hook to get recurring transactions with pagination and connection helpers
 */
export function useRecurringTransactions(variables?: {
  first?: number
  after?: string
  search?: string
  kind?: TransactionKind
  frequency?: string
  isActive?: boolean
}) {
  const queryResult = useQuery(GET_RECURRING_TRANSACTIONS, {
    variables,
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true
  })

  // Extract connection data using helper
  const connectionData = useConnectionData({
    data: queryResult.data?.recurringTransactions,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    // Connection-specific data
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.recurringTransactions,
    // Pagination info
    pageInfo: queryResult.data?.recurringTransactions?.pageInfo,
    // Helper methods
    extractNodes: () => extractNodes(queryResult.data?.recurringTransactions),
    extractPaginationInfo: () => extractPaginationInfo(queryResult.data?.recurringTransactions)
  }
}

/**
 * Hook to get recurring transaction by ID
 */
export function useRecurringTransaction(id: string) {
  return useQuery(GET_RECURRING_TRANSACTION_BY_ID, {
    variables: { id },
    errorPolicy: 'all',
    skip: !id
  })
}

/**
 * Hook to get active recurring transactions with connection helpers
 */
export function useActiveRecurringTransactions() {
  const queryResult = useQuery(GET_ACTIVE_RECURRING_TRANSACTIONS, {
    errorPolicy: 'all'
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.activeRecurringTransactions,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.activeRecurringTransactions
  }
}

/**
 * Hook to get recurring transactions by kind with connection helpers
 */
export function useRecurringTransactionsByKind(kind: TransactionKind) {
  const queryResult = useQuery(GET_RECURRING_TRANSACTIONS_BY_KIND, {
    variables: { kind },
    errorPolicy: 'all',
    skip: !kind
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.recurringTransactionsByKind,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.recurringTransactionsByKind
  }
}

/**
 * Hook to get recurring transactions by frequency with connection helpers
 */
export function useRecurringTransactionsByFrequency(frequency: string) {
  const queryResult = useQuery(GET_RECURRING_TRANSACTIONS_BY_FREQUENCY, {
    variables: { frequency },
    errorPolicy: 'all',
    skip: !frequency
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.recurringTransactionsByFrequency,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.recurringTransactionsByFrequency
  }
}

/**
 * Hook to search recurring transactions with connection helpers
 */
export function useSearchRecurringTransactions(search: string, first: number = 20) {
  const queryResult = useQuery(SEARCH_RECURRING_TRANSACTIONS, {
    variables: { search, first },
    errorPolicy: 'all',
    skip: !search
  })

  const connectionData = useConnectionData({
    data: queryResult.data?.searchRecurringTransactions,
    loading: queryResult.loading,
    error: queryResult.error
  })

  return {
    ...queryResult,
    items: connectionData.items,
    totalCount: connectionData.totalCount,
    hasNextPage: connectionData.hasNextPage,
    hasPreviousPage: connectionData.hasPreviousPage,
    connection: queryResult.data?.searchRecurringTransactions
  }
}

// ==================== MUTATION HOOKS ====================

/**
 * Hook to create recurring transaction
 */
export function useCreateRecurringTransaction() {
  return useMutation(CREATE_RECURRING_TRANSACTION, {
    refetchQueries: ['GetRecurringTransactions', 'GetActiveRecurringTransactions'],
    onError: (error) => {
      console.error('Create recurring transaction error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to update recurring transaction
 */
export function useUpdateRecurringTransaction() {
  return useMutation(UPDATE_RECURRING_TRANSACTION, {
    refetchQueries: ['GetRecurringTransactions', 'GetRecurringTransactionById', 'GetActiveRecurringTransactions'],
    onError: (error) => {
      console.error('Update recurring transaction error:', convertToClientError(error))
    }
  })
}

/**
 * Hook to delete recurring transaction
 */
export function useDeleteRecurringTransaction() {
  return useMutation(DELETE_RECURRING_TRANSACTION, {
    refetchQueries: ['GetRecurringTransactions', 'GetActiveRecurringTransactions'],
    onError: (error) => {
      console.error('Delete recurring transaction error:', convertToClientError(error))
    }
  })
}

// ==================== UTILITY HOOKS ====================

/**
 * Hook to refresh recurring transaction data
 */
export function useRefreshRecurringTransactions() {
  const client = useApolloClient()

  return () => {
    client.refetchQueries({
      include: ['GetRecurringTransactions', 'GetActiveRecurringTransactions']
    })
  }
}

/**
 * Hook to clear recurring transaction cache
 */
export function useClearRecurringTransactionCache() {
  const client = useApolloClient()

  return () => {
    client.cache.evict({ fieldName: 'recurringTransactions' })
    client.cache.gc()
  }
}

/**
 * Hook to get recurring transaction statistics
 */
export function useRecurringTransactionStats() {
  const { data: recurringTransactions, loading } = useActiveRecurringTransactions()

  const stats = React.useMemo(() => {
    if (!recurringTransactions?.recurringTransactions?.edges) {
      return {
        totalTransactions: 0,
        totalIncomeAmount: 0,
        totalExpenseAmount: 0,
        totalTransferAmount: 0,
        byFrequency: {},
        byKind: {
          income: 0,
          expense: 0,
          transfer: 0
        }
      }
    }

    const transactions = recurringTransactions.recurringTransactions.edges.map(edge => edge.node)
    const totalTransactions = transactions.length

    const totalIncomeAmount = transactions
      .filter((t: any) => t.kind === 'INCOME')
      .reduce((sum: number, t: any) => sum + t.amount, 0)

    const totalExpenseAmount = transactions
      .filter((t: any) => t.kind === 'EXPENSE')
      .reduce((sum: number, t: any) => sum + t.amount, 0)

    const totalTransferAmount = transactions
      .filter((t: any) => t.kind === 'TRANSFER')
      .reduce((sum: number, t: any) => sum + t.amount, 0)

    const byFrequency = transactions.reduce((acc: Record<string, number>, t: any) => {
      acc[t.frequency] = (acc[t.frequency] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const byKind = {
      income: transactions.filter((t: any) => t.kind === 'INCOME').length,
      expense: transactions.filter((t: any) => t.kind === 'EXPENSE').length,
      transfer: transactions.filter((t: any) => t.kind === 'TRANSFER').length
    }

    return {
      totalTransactions,
      totalIncomeAmount,
      totalExpenseAmount,
      totalTransferAmount,
      byFrequency,
      byKind
    }
  }, [recurringTransactions])

  return { stats, loading }
}

/**
 * Hook to toggle recurring transaction status
 */
export function useToggleRecurringTransactionStatus() {
  const [updateRecurringTransaction] = useUpdateRecurringTransaction()

  return (id: string, isActive: boolean) => {
    return updateRecurringTransaction({
      variables: {
        id,
        input: { isActive }
      }
    })
  }
}
