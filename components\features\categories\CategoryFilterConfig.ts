// Filter Configuration for Categories

import { FilterConfig, FilterDefinition, SortOption } from '@/types/ui'
import { CategoryWithBudget } from '@/types/entities'

// Define filter options
const typeOptions = [
  { value: 'all', label: 'Tất cả loại', count: 0 },
  { value: 'income', label: 'Thu nhập', count: 0 },
  { value: 'expense', label: 'Chi tiêu', count: 0 }
]

const statusOptions = [
  { value: 'all', label: 'Tất cả trạng thái', count: 0 },
  { value: 'active', label: 'Đang hoạt động', count: 0 },
  { value: 'inactive', label: 'Đã ẩn', count: 0 }
]

const budgetOptions = [
  { value: 'all', label: 'Tất cả', count: 0 },
  { value: 'with_budget', label: 'Có ngân sách', count: 0 },
  { value: 'without_budget', label: 'Không có ngân sách', count: 0 }
]

// Define filter definitions
const categoryFilters: FilterDefinition<CategoryWithBudget>[] = [
  {
    key: 'type',
    label: 'Loại',
    type: 'select',
    options: typeOptions,
    predicate: (category, value) => {
      if (value === 'all') return true
      return category.type === value
    },
    defaultValue: 'all'
  },
  {
    key: 'status',
    label: 'Trạng thái',
    type: 'select',
    options: statusOptions,
    predicate: (category, value) => {
      if (value === 'all') return true
      if (value === 'active') return category.isActive
      if (value === 'inactive') return !category.isActive
      return true
    },
    defaultValue: 'all'
  },
  {
    key: 'budget',
    label: 'Ngân sách',
    type: 'select',
    options: budgetOptions,
    predicate: (category, value) => {
      if (value === 'all') return true
      const hasBudget = !!(category.budgetConfig || category.incomeTargetConfig)
      if (value === 'with_budget') return hasBudget
      if (value === 'without_budget') return !hasBudget
      return true
    },
    defaultValue: 'all'
  },
  {
    key: 'parent',
    label: 'Danh mục cha',
    type: 'select',
    options: [
      { value: 'all', label: 'Tất cả', count: 0 },
      { value: 'root', label: 'Danh mục gốc', count: 0 },
      { value: 'sub', label: 'Danh mục con', count: 0 }
    ],
    predicate: (category, value) => {
      if (value === 'all') return true
      if (value === 'root') return !category.parentId
      if (value === 'sub') return !!category.parentId
      return true
    },
    defaultValue: 'all'
  }
]

// Define sort options
const categorySortOptions: SortOption<CategoryWithBudget>[] = [
  {
    key: 'name_asc',
    label: 'Tên (A-Z)',
    compareFn: (a, b) => a.name.localeCompare(b.name, 'vi')
  },
  {
    key: 'name_desc',
    label: 'Tên (Z-A)',
    compareFn: (a, b) => b.name.localeCompare(a.name, 'vi')
  },
  {
    key: 'type_asc',
    label: 'Loại (Thu nhập trước)',
    compareFn: (a, b) => {
      if (a.type === b.type) return a.name.localeCompare(b.name, 'vi')
      return a.type === 'income' ? -1 : 1
    }
  },
  {
    key: 'type_desc',
    label: 'Loại (Chi tiêu trước)',
    compareFn: (a, b) => {
      if (a.type === b.type) return a.name.localeCompare(b.name, 'vi')
      return a.type === 'expense' ? -1 : 1
    }
  },
  {
    key: 'created_desc',
    label: 'Mới nhất',
    compareFn: (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  },
  {
    key: 'created_asc',
    label: 'Cũ nhất',
    compareFn: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  },
  {
    key: 'updated_desc',
    label: 'Cập nhật gần nhất',
    compareFn: (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  },
  {
    key: 'budget_desc',
    label: 'Ngân sách cao nhất',
    compareFn: (a, b) => {
      const aBudget = a.budgetConfig?.amount || a.incomeTargetConfig?.amount || 0
      const bBudget = b.budgetConfig?.amount || b.incomeTargetConfig?.amount || 0
      return bBudget - aBudget
    }
  },
  {
    key: 'budget_asc',
    label: 'Ngân sách thấp nhất',
    compareFn: (a, b) => {
      const aBudget = a.budgetConfig?.amount || a.incomeTargetConfig?.amount || 0
      const bBudget = b.budgetConfig?.amount || b.incomeTargetConfig?.amount || 0
      return aBudget - bBudget
    }
  }
]

// Create filter configuration
export const categoryFilterConfig: FilterConfig<CategoryWithBudget> = {
  searchFields: ['name', 'description'],
  filters: categoryFilters,
  sortOptions: categorySortOptions,
  defaultSort: categorySortOptions[0] // Name A-Z
}

// Helper function to update filter options with counts
export const updateCategoryFilterCounts = (
  config: FilterConfig<CategoryWithBudget>,
  categories: CategoryWithBudget[]
): FilterConfig<CategoryWithBudget> => {
  const updatedFilters = config.filters.map(filter => {
    if (!filter.options) return filter

    const updatedOptions = filter.options.map(option => {
      let count = 0
      
      if (filter.key === 'type') {
        if (option.value === 'all') count = categories.length
        else count = categories.filter(cat => cat.type === option.value).length
      } else if (filter.key === 'status') {
        if (option.value === 'all') count = categories.length
        else if (option.value === 'active') count = categories.filter(cat => cat.isActive).length
        else if (option.value === 'inactive') count = categories.filter(cat => !cat.isActive).length
      } else if (filter.key === 'budget') {
        if (option.value === 'all') count = categories.length
        else if (option.value === 'with_budget') {
          count = categories.filter(cat => !!(cat.budgetConfig || cat.incomeTargetConfig)).length
        } else if (option.value === 'without_budget') {
          count = categories.filter(cat => !(cat.budgetConfig || cat.incomeTargetConfig)).length
        }
      } else if (filter.key === 'parent') {
        if (option.value === 'all') count = categories.length
        else if (option.value === 'root') count = categories.filter(cat => !cat.parentId).length
        else if (option.value === 'sub') count = categories.filter(cat => !!cat.parentId).length
      }

      return { ...option, count }
    })

    return { ...filter, options: updatedOptions }
  })

  return { ...config, filters: updatedFilters }
}

// Export individual pieces for flexibility
export { categoryFilters, categorySortOptions, typeOptions, statusOptions, budgetOptions }
export default categoryFilterConfig
