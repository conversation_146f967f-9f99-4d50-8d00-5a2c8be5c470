"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { Category } from "@/lib/generated/prisma"
import { Edit, Trash2, TrendingUp, TrendingDown, Eye, EyeOff, ExternalLink, Target, DollarSign } from "lucide-react"

interface CategoryCardProps {
  category: Category
  onEdit?: (category: Category) => void
  onToggleActive?: (category: Category) => void
  onView?: (category: Category) => void
  showUsageStats?: boolean
  usageCount?: number
  totalAmount?: number
}

export function CategoryCard({
  category,
  onEdit,
  onToggleActive,
  onView,
  showUsageStats = false,
  usageCount = 0,
  totalAmount = 0
}: CategoryCardProps) {
  const router = useRouter()

  const handleViewCategory = () => {
    console.log('CategoryCard: handleViewCategory called for category:', category.id)
    if (onView) {
      console.log('CategoryCard: Using onView callback')
      onView(category)
    } else {
      console.log('CategoryCard: Using router navigation')
      try {
        router.push(`/categories/${category.id}`)
      } catch (error) {
        console.error('CategoryCard: Router navigation failed:', error)
        // Fallback to window.location
        window.location.href = `/categories/${category.id}`
      }
    }
  }
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const getTypeIcon = () => {
    return category.type === "income" ? (
      <TrendingUp size={16} className="text-green-500" />
    ) : (
      <TrendingDown size={16} className="text-red-500" />
    )
  }

  const getTypeColor = () => {
    return category.type === "income"
      ? "text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"
      : "text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"
  }

  const getStatusColor = () => {
    return category.isActive
      ? "text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"
      : "text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-700"
  }

  return (
    <div className={`group bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4 hover:bg-white dark:hover:bg-gray-800 hover:shadow-lg hover:border-gray-300/50 dark:hover:border-gray-600/50 transition-all duration-200 h-full flex flex-col ${
      !category.isActive ? "opacity-60" : ""
    }`}>

      {/* Category Info */}
      <div className="flex-1 space-y-2">
        {/* Name and Type Badge */}
        <div className="flex items-start justify-between gap-2">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {/* Color Indicator */}
            <div
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: category.color || '#6B7280' }}
            />
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 flex-1">
              {category.name}
            </h3>
          </div>

          {/* Type Badge */}
          <span className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
            category.type === "income"
              ? "bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400"
              : "bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400"
          }`}>
            {category.type === "income" ? "Thu nhập" : "Chi tiêu"}
          </span>
        </div>

        {/* Description */}
        {category.description && (
          <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1">
            {category.description}
          </p>
        )}

        {/* Budget/Target or Usage Stats */}
        <div className="flex items-center justify-between text-xs">
          {false /* TODO: Add budget/target support */ ? (
            <>
              <div className="text-gray-600 dark:text-gray-400">
                <span className="font-medium">{category.type === "expense" ? "Ngân sách" : "Mục tiêu"}</span>
                {(category.budgetPeriod || category.incomeTargetPeriod) && (
                  <span className="text-gray-500 dark:text-gray-400 ml-1">
                    /{category.type === "expense" ? category.budgetPeriod : category.incomeTargetPeriod}
                  </span>
                )}
              </div>
              <div className={`font-semibold ${
                category.type === "expense"
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-green-600 dark:text-green-400"
              }`}>
                {formatCurrency(category.type === "expense" ? category.budgetAmount! : category.incomeTarget!)}
              </div>
            </>
          ) : showUsageStats ? (
            <>
              <div className="text-gray-600 dark:text-gray-400">
                <span className="font-semibold text-gray-900 dark:text-gray-100">{usageCount}</span> giao dịch
              </div>
              <div className={`font-semibold ${
                category.type === "income"
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}>
                {formatCurrency(totalAmount)}
              </div>
            </>
          ) : null}
        </div>

        {/* Progress Bar */}
        {false /* TODO: Add budget/target support */ && showUsageStats && totalAmount > 0 && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  category.type === "expense"
                    ? totalAmount > (category.budgetAmount || 0)
                      ? "bg-red-500"
                      : "bg-blue-500"
                    : totalAmount >= (category.incomeTarget || 0)
                      ? "bg-green-500"
                      : "bg-yellow-500"
                }`}
                style={{
                  width: `${Math.min(
                    (totalAmount / (category.type === "expense" ? category.budgetAmount! : category.incomeTarget!)) * 100,
                    100
                  )}%`
                }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Footer - Status and Action Buttons */}
      <div className="flex items-center justify-between gap-2 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        {/* Status Indicator */}
        <div className="flex items-center gap-1">
          <div className={`w-2 h-2 rounded-full ${
            category.isActive ? "bg-green-400" : "bg-gray-400"
          }`} />
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {category.isActive ? "Hoạt động" : "Đã ẩn"}
          </span>
        </div>

        {/* Action Buttons - Show on hover */}
        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="flex items-center gap-1">
            <button
              onClick={handleViewCategory}
              className="p-1.5 text-gray-400 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
              title="Xem chi tiết"
            >
              <ExternalLink size={14} />
            </button>

            {onToggleActive && (
              <button
                onClick={() => onToggleActive(category)}
                className={`p-1.5 rounded-lg transition-colors ${
                  category.isActive
                    ? "text-gray-400 hover:text-orange-600 hover:bg-orange-50 dark:hover:bg-orange-900/20"
                    : "text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20"
                }`}
                title={category.isActive ? "Ẩn danh mục" : "Hiện danh mục"}
              >
                {category.isActive ? <EyeOff size={14} /> : <Eye size={14} />}
              </button>
            )}

            {onEdit && (
              <button
                onClick={() => onEdit(category)}
                className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                title="Chỉnh sửa"
              >
                <Edit size={14} />
              </button>
            )}


          </div>
        </div>
      </div>
    </div>
  )
}
