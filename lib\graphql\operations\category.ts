/**
 * GraphQL Operations for Category Management
 * 
 * Queries and mutations for category operations
 */

import { gql } from '@apollo/client'

// ==================== FRAGMENTS ====================

export const CATEGORY_FRAGMENT = gql`
  fragment CategoryFragment on Category {
    id
    name
    description
    type
    color
    icon
    parentId
    userId
    isActive
    createdAt
    updatedAt
  }
`

export const CATEGORY_WITH_PARENT_FRAGMENT = gql`
  fragment CategoryWithParentFragment on Category {
    ...CategoryFragment
    parent {
      id
      name
      type
      color
      icon
    }
  }
  ${CATEGORY_FRAGMENT}
`

export const CATEGORY_WITH_CHILDREN_FRAGMENT = gql`
  fragment CategoryWithChildrenFragment on Category {
    ...CategoryFragment
    children {
      id
      name
      type
      color
      icon
    }
  }
  ${CATEGORY_FRAGMENT}
`

export const CATEGORY_FULL_FRAGMENT = gql`
  fragment CategoryFullFragment on Category {
    ...CategoryFragment
    parent {
      id
      name
      type
      color
      icon
    }
    children {
      id
      name
      type
      color
      icon
    }
  }
  ${CATEGORY_FRAGMENT}
`

// ==================== QUERIES ====================

export const GET_CATEGORIES = gql`
  query GetCategories(
    $first: Int
    $after: String
    $search: String
    $type: CategoryType
    $parentId: UUID
    $isActive: Boolean
  ) {
    categories(
      first: $first
      after: $after
      search: $search
      type: $type
      parentId: $parentId
      isActive: $isActive
    ) {
      edges {
        node {
          ...CategoryWithParentFragment
        }
        cursor
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      totalCount
    }
  }
  ${CATEGORY_WITH_PARENT_FRAGMENT}
`

export const GET_CATEGORY_BY_ID = gql`
  query GetCategoryById($id: UUID!) {
    category(id: $id) {
      ...CategoryFullFragment
      transactions(first: 20) {
        edges {
          node {
            id
            kind
            amount
            date
            description
            account {
              id
              name
            }
          }
        }
      }
      categoryGoals {
        id
        name
        targetAmount
        currentAmount
        startDate
        endDate
      }
    }
  }
  ${CATEGORY_FULL_FRAGMENT}
`

export const GET_CATEGORIES_BY_TYPE = gql`
  query GetCategoriesByType($type: CategoryType!, $first: Int = 100) {
    categories(type: $type, first: $first, isActive: true) {
      edges {
        node {
          ...CategoryWithChildrenFragment
        }
      }
      totalCount
    }
  }
  ${CATEGORY_WITH_CHILDREN_FRAGMENT}
`

export const GET_ROOT_CATEGORIES = gql`
  query GetRootCategories($type: CategoryType) {
    categories(parentId: null, type: $type, first: 100, isActive: true) {
      edges {
        node {
          ...CategoryWithChildrenFragment
        }
      }
      totalCount
    }
  }
  ${CATEGORY_WITH_CHILDREN_FRAGMENT}
`

export const SEARCH_CATEGORIES = gql`
  query SearchCategories($search: String!, $type: CategoryType, $first: Int = 20) {
    categories(search: $search, type: $type, first: $first) {
      edges {
        node {
          ...CategoryWithParentFragment
        }
      }
      totalCount
    }
  }
  ${CATEGORY_WITH_PARENT_FRAGMENT}
`

export const GET_CATEGORY_TREE = gql`
  query GetCategoryTree($type: CategoryType) {
    categories(type: $type, first: 1000, isActive: true) {
      edges {
        node {
          ...CategoryFullFragment
        }
      }
      totalCount
    }
  }
  ${CATEGORY_FULL_FRAGMENT}
`

// ==================== MUTATIONS ====================

export const CREATE_CATEGORY = gql`
  mutation CreateCategory($input: CreateCategoryInput!) {
    createCategory(input: $input) {
      ...CategoryFullFragment
    }
  }
  ${CATEGORY_FULL_FRAGMENT}
`

export const UPDATE_CATEGORY = gql`
  mutation UpdateCategory($id: UUID!, $input: UpdateCategoryInput!) {
    updateCategory(id: $id, input: $input) {
      ...CategoryFullFragment
    }
  }
  ${CATEGORY_FULL_FRAGMENT}
`

export const DELETE_CATEGORY = gql`
  mutation DeleteCategory($id: UUID!) {
    deleteCategory(id: $id)
  }
`
