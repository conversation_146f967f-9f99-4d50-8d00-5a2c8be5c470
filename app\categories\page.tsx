"use client"

import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { CategoryCard } from "@/components/features/categories/CategoryCard"
import { ListPageLayout } from "@/components/layout/ResponsiveLayout"
import { UnifiedFilterControls, useUnifiedFilters } from "@/components/shared/controls/UnifiedFilterControls"
import { EnhancedGenericList } from "@/components/shared/lists/EnhancedGenericList"
import { CategorySidebar } from "@/components/ui/CategorySidebar"
import { Button } from "@/components/ui/button"
import { FormCategory } from "@/components/ui/form-types"
import { Progress } from "@/components/ui/progress"
import { useAuth } from "@/contexts/AuthContext"
import { CategoryWithGoal } from "@/lib/graphql"
import { formatCurrency } from "@/lib/utils/currency"
import { CategoryApiService } from "@/services/client"
import { Folder, Plus, Tag, Target, TrendingDown, TrendingUp } from "lucide-react"
import { useRouter } from "next/navigation"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"






function CategoriesPageContent() {
  const router = useRouter()
  const { user } = useAuth()

  // State management
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<CategoryWithGoal | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [categories, setCategories] = useState<CategoryWithGoal[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  // Fetch categories
  const fetchCategories = useCallback(async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      const response = await CategoryApiService.getCategoriesWithGoal({})
      if (response.success) {
        const rawData = response.data || []
        console.log('Raw categories data:', rawData.length)

        // Deduplicate categories by ID to prevent duplicate display
        const uniqueCategories = rawData.reduce((acc: CategoryWithGoal[], current: CategoryWithGoal) => {
          const existingIndex = acc.findIndex(item => item.id === current.id)
          if (existingIndex === -1) {
            acc.push(current)
          } else {
            // If duplicate found, keep the one with more complete data (has categoryGoal)
            if (current.categoryGoal && !acc[existingIndex].categoryGoal) {
              acc[existingIndex] = current
            }
          }
          return acc
        }, [])

        console.log('Unique categories after deduplication:', uniqueCategories.length)
        setCategories(uniqueCategories)
      } else {
        setError(response.error || 'Không thể tải danh sách danh mục')
      }
    } catch (err) {
      setError('Đã xảy ra lỗi khi tải danh mục')
      console.error('Categories error:', err)
    } finally {
      setLoading(false)
    }
  }, [user?.id])

  // Initial data load
  useEffect(() => {
    console.log('CategoriesPageContent: Initial data load triggered')
    fetchCategories()
  }, [fetchCategories])

  // Debug logging for categories state changes
  useEffect(() => {
    console.log('Categories state updated:', {
      count: categories.length,
      ids: categories.map(c => c.id),
      duplicateIds: categories.map(c => c.id).filter((id, index, arr) => arr.indexOf(id) !== index)
    })
  }, [categories])

  // Unified filters
  const {
    filteredData: filteredCategories,
    activeFilter,
    setActiveFilter,
    searchTerm,
    setSearchTerm,
    activeSortField,
    currentSortDirection,
    handleSortChange
  } = useUnifiedFilters({
    data: categories,
    filterField: 'type',
    searchFields: ['name', 'description'],
    sortField: 'name',
    sortDirection: 'asc'
  })

  // Empty message based on filters
  const getEmptyMessage = () => {
    if (filteredCategories.length === 0 && categories.length > 0) {
      return "Không tìm thấy danh mục nào phù hợp với bộ lọc hiện tại"
    }
    return "Chưa có danh mục nào. Hãy tạo danh mục đầu tiên để bắt đầu phân loại giao dịch!"
  }

  // Filter tabs for category types
  const filterTabs = useMemo(() => [
    { value: 'all', label: 'Tất cả', count: categories.length },
    { 
      value: 'income', 
      label: 'Thu nhập', 
      count: categories.filter(c => c.type === 'income').length 
    },
    { 
      value: 'expense', 
      label: 'Chi tiêu', 
      count: categories.filter(c => c.type === 'expense').length 
    }
  ], [categories])

  // Sort options
  const sortOptions = [
    { value: 'name', label: 'Tên danh mục' },
    { value: 'type', label: 'Loại danh mục' },
    { value: 'createdAt', label: 'Ngày tạo' }
  ]

  // Statistics
  const stats = useMemo(() => {
    const total = categories.length
    const active = categories.filter(cat => cat.isActive).length
    const income = categories.filter(cat => cat.type === 'income').length
    const expense = categories.filter(cat => cat.type === 'expense').length
    const withGoals = categories.filter(cat => cat.categoryGoal).length

    return [
      {
        label: "Tổng danh mục",
        value: total.toString(),
        icon: <Tag className="h-4 w-4" />,
        color: "bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300"
      },
      {
        label: "Đang hoạt động",
        value: active.toString(),
        icon: <Tag className="h-4 w-4" />,
        color: "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300"
      },
      {
        label: "Thu nhập",
        value: income.toString(),
        icon: <TrendingUp className="h-4 w-4" />,
        color: "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300"
      },
      {
        label: "Chi tiêu",
        value: expense.toString(),
        icon: <TrendingDown className="h-4 w-4" />,
        color: "bg-rose-100 text-rose-700 dark:bg-rose-900/50 dark:text-rose-300"
      },
      {
        label: "Có mục tiêu",
        value: withGoals.toString(),
        icon: <Folder className="h-4 w-4" />,
        color: "bg-purple-100 text-purple-700 dark:bg-purple-900/50 dark:text-purple-300"
      }
    ]
  }, [categories])

  // Handlers
  const handleAddCategory = (_type?: 'INCOME' | 'EXPENSE') => {
    setSelectedCategory(null)
    setIsEditing(false)
    setSidebarOpen(true)
  }

  const handleEditCategory = (category: CategoryWithGoal) => {
    setSelectedCategory(category)
    setIsEditing(true)
    setSidebarOpen(true)
  }

  const handleToggleActive = async (category: CategoryWithGoal) => {
    try {
      setLoading(true)
      const response = await CategoryApiService.updateStatus(category.id, !category.isActive)
      if (response.success) {
        await fetchCategories()
        setError(null)
      } else {
        setError('Không thể cập nhật trạng thái danh mục')
      }
    } catch (err) {
      setError('Đã xảy ra lỗi khi cập nhật danh mục')
      console.error('Toggle category error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleViewCategory = (category: CategoryWithGoal) => {
    router.push(`/categories/${category.id}`)
  }

  const handleSaveCategory = async (categoryData: FormCategory) => {
    try {
      setLoading(true)
      let response

      if (isEditing && selectedCategory) {
        response = await CategoryApiService.updateCategoryWithGoal(categoryData)
      } else {
        response = await CategoryApiService.createCategoryWithGoal(categoryData)
      }

      if (response?.success) {
        await fetchCategories()
        setSidebarOpen(false)
        setError(null)
      } else {
        setError(response?.error || 'Không thể lưu danh mục. Vui lòng thử lại.')
      }
    } catch (err) {
      setError('Đã xảy ra lỗi khi lưu danh mục')
      console.error('Save category error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCloseSidebar = () => {
    setSidebarOpen(false)
    setSelectedCategory(null)
    setIsEditing(false)
  }

  // Render category card with goal information
  const renderCategoryCard = useCallback((category: CategoryWithGoal) => {
    const hasGoal = Boolean(category.categoryGoal)
    const safeCurrentAmount = category.categoryGoal?.currentAmount || 0
    const safeTargetAmount = category.categoryGoal?.targetAmount || 0
    const progress = hasGoal && safeTargetAmount > 0
      ? Math.min(100, (safeCurrentAmount / safeTargetAmount) * 100)
      : 0;

    return (
      <div className="relative">
        <CategoryCard
          category={{
            ...category,
            description: category.description || undefined
          }}
          onEdit={() => handleEditCategory(category)}
          onView={() => handleViewCategory(category)}
          onToggleActive={handleToggleActive}
        />
        {hasGoal && category.categoryGoal && (
          <div className="mt-2 p-3 bg-muted/30 rounded-lg border border-muted-foreground/10">
            <div className="flex justify-between items-center text-sm mb-1">
              <span className="font-medium flex items-center gap-1">
                <Target className="w-3.5 h-3.5 mr-1" />
                Mục tiêu:
              </span>
              <span className="font-semibold">
                {formatCurrency(category.categoryGoal.targetAmount)}
              </span>
            </div>
            <div className="flex justify-between text-xs text-muted-foreground mb-2">
              <span>Tiến độ:</span>
              <span>{Math.min(100, Math.round(progress))}%</span>
            </div>
            <Progress 
              value={Math.min(100, progress)} 
              className="h-2" 
            />
            <div className="mt-2 text-xs text-muted-foreground space-y-0.5">
              <div className="flex justify-between">
                <span>Đã đạt:</span>
                <span className="font-medium">{formatCurrency(category.categoryGoal.currentAmount || 0)}</span>
              </div>
              {category.categoryGoal.startDate && (
                <div className="flex justify-between">
                  <span>Bắt đầu:</span>
                  <span>{new Date(category.categoryGoal.startDate).toLocaleDateString('vi-VN')}</span>
                </div>
              )}
              {category.categoryGoal.endDate && (
                <div className="flex justify-between">
                  <span>Kết thúc:</span>
                  <span>{new Date(category.categoryGoal.endDate).toLocaleDateString('vi-VN')}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    )
  }, [handleEditCategory, handleViewCategory, handleToggleActive])

  return (
    <ListPageLayout
      title="Quản lý danh mục"
      subtitle="Tạo và quản lý các danh mục thu nhập và chi tiêu"
      actions={
        <div className="flex flex-col sm:flex-row gap-2">
          <Button 
            variant="outline" 
            onClick={() => handleAddCategory('EXPENSE')}
            className="gap-1"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Chi tiêu</span>
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleAddCategory('INCOME')}
            className="gap-1"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Thu nhập</span>
          </Button>
        </div>
      }
      stats={stats}
    >
      <div className="space-y-4">
        <UnifiedFilterControls
          searchPlaceholder="Tìm kiếm danh mục..."
          searchValue={searchTerm}
          onSearchChange={setSearchTerm}
          filterTabs={filterTabs}
          activeFilter={activeFilter}
          onFilterChange={setActiveFilter}
          sortOptions={sortOptions}
          activeSortField={activeSortField}
          sortDirection={currentSortDirection}
          onSortChange={handleSortChange}
          className="mb-4"
        />

        <EnhancedGenericList
          data={filteredCategories}
          renderCard={renderCategoryCard}
          emptyMessage={getEmptyMessage()}  
          loading={loading}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
        />
      </div>

      <CategorySidebar
        isOpen={sidebarOpen}
        onClose={handleCloseSidebar}
        category={selectedCategory}
        isEditing={isEditing}
        onSave={handleSaveCategory}
      />
    </ListPageLayout>
  );
};
          

export default function CategoriesPage() {
  return <ProtectedRoute requireAuth={true}>
    <CategoriesPageContent />
  </ProtectedRoute>
}
