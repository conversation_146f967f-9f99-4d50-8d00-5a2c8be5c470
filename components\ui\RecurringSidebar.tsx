"use client"

import React, { useState, useEffect } from "react"
import { Repeat, DollarSign, Calendar, FileText, FolderTree, Clock, ArrowRightLeft } from "lucide-react"
import { BaseSidebar } from "./BaseSidebar"
import { SidebarForm, SidebarInputField, SidebarTextareaField } from "./SidebarForm"
import { SearchableCombobox } from "./SearchableCombobox"
import { SidebarActionFooter } from "./SidebarFooter"
import { getSidebarConfig } from "../../config/sidebarConfig"
import { Category, RecurringTransaction } from "@/lib/generated/prisma"





interface RecurringSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (recurring: RecurringTransaction) => void
  recurring?: RecurringTransaction | null
  categories: Category[]
  isEditing: boolean
}

interface FormData {
  name: string
  description: string
  amount: string
  type: "income" | "expense" | "transfer"
  categoryId: string
  frequency: string
  startDate: string
  endDate: string
}

interface FormErrors {
  name?: string
  amount?: string
  type?: string
  frequency?: string
  startDate?: string
  endDate?: string
  description?: string
  categoryId?: string
}

const TRANSACTION_TYPES = [
  { value: "expense", label: "Chi tiêu" },
  { value: "income", label: "Thu nhập" },
  { value: "transfer", label: "Chuyển khoản" }
]

const FREQUENCY_OPTIONS = [
  { value: "daily", label: "Hàng ngày" },
  { value: "weekly", label: "Hàng tuần" },
  { value: "monthly", label: "Hàng tháng" },
  { value: "quarterly", label: "Hàng quý" },
  { value: "yearly", label: "Hàng năm" }
]

interface SidebarFormProps {
  children: React.ReactNode
  onSubmit: (e: React.FormEvent) => void
  className?: string
  sidebarWidth: number
  isResizing: boolean
  isOpen: boolean
}

export function RecurringSidebar({
  isOpen,
  onClose,
  onSave,
  recurring,
  categories = [],
  isEditing
}: RecurringSidebarProps) {
  const config = getSidebarConfig('RECURRING')
  
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    amount: "",
    type: "expense",
    categoryId: "",
    frequency: "monthly",
    startDate: "",
    endDate: ""
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  
  // Initialize form data when sidebar opens or recurring changes
  useEffect(() => {
    if (isOpen) {
      if (recurring && isEditing) {
        setFormData({
          name: recurring.name || "",
          description: recurring.description || "",
          amount: recurring.amount.toString(),
          type: recurring.kind || "expense",
          categoryId: recurring.categoryId?.toString() || "",
          frequency: recurring.frequency,
          startDate: recurring.startDate ? new Date(recurring.startDate).toISOString().split('T')[0] : "",
          endDate: recurring.endDate ? new Date(recurring.endDate).toISOString().split('T')[0] : ""
        })
      } else {
        const now = new Date()
        setFormData({
          name: "",
          description: "",
          amount: "",
          type: "expense",
          categoryId: "",
          frequency: "monthly",
          startDate: now.toISOString().split('T')[0],
          endDate: ""
        })
      }
      setErrors({})
    }
  }, [isOpen, recurring, isEditing])
  
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "Tên giao dịch định kỳ là bắt buộc"
    }
    
    if (!formData.amount.trim()) {
      newErrors.amount = "Số tiền là bắt buộc"
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = "Số tiền phải là số dương"
    }
    
    if (!formData.type) {
      newErrors.type = "Loại giao dịch là bắt buộc"
    }
    
    if (!formData.frequency) {
      newErrors.frequency = "Tần suất là bắt buộc"
    }
    
    if (!formData.startDate) {
      newErrors.startDate = "Ngày bắt đầu là bắt buộc"
    }
    
    if (formData.endDate && formData.startDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
      newErrors.endDate = "Ngày kết thúc phải sau ngày bắt đầu"
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const calculateNextDate = (startDate: Date, frequency: string): Date => {
    const next = new Date(startDate)
    
    switch (frequency) {
      case 'daily':
        next.setDate(next.getDate() + 1)
        break
      case 'weekly':
        next.setDate(next.getDate() + 7)
        break
      case 'monthly':
        next.setMonth(next.getMonth() + 1)
        break
      case 'quarterly':
        next.setMonth(next.getMonth() + 3)
        break
      case 'yearly':
        next.setFullYear(next.getFullYear() + 1)
        break
    }
    
    return next
  }
  
  // Submit handler function with event parameter
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setLoading(true)
    try {
      const startDate = new Date(formData.startDate)
      const nextDate = calculateNextDate(startDate, formData.frequency)
      
      const recurringData: any = {
        id: recurring?.id || undefined,
        name: formData.name.trim(),
        description: formData.description.trim(),
        amount: Number(formData.amount),
        kind: formData.type,
        categoryId: formData.categoryId || undefined,
        frequency: formData.frequency,
        startDate: startDate.toISOString(),
        endDate: formData.endDate ? new Date(formData.endDate).toISOString() : undefined,
        isActive: recurring?.isActive ?? true
      }
      
      onSave(recurringData)
      handleClose()
    } catch (error) {
      console.error('Error saving recurring transaction:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const handleClose = () => {
    const now = new Date()
    setFormData({
      name: "",
      description: "",
      amount: "",
      type: "expense",
      categoryId: "",
      frequency: "monthly",
      startDate: now.toISOString().split('T')[0],
      endDate: ""
    })
    setErrors({})
    onClose()
  }
  

  
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }
  
  const filteredCategories = categories
    .filter(cat => formData.type === 'transfer' || cat.type === formData.type)
    .map(cat => ({
      value: cat.id.toString(),
      label: `${cat.icon} ${cat.name}`
    }))
  
  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa giao dịch định kỳ" : "Thêm giao dịch định kỳ mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${recurring?.description}` : "Tạo giao dịch tự động lặp lại theo chu kỳ"}
      storageKey="recurringSidebarWidth"
      config={config}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={() => {
            const fakeEvent = { preventDefault: () => {} } as React.FormEvent
            handleSubmit(fakeEvent)
            return undefined
          }}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={!formData.description.trim() || !formData.amount.trim()}
          sidebarWidth={500}
        />
      }
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={500}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* Transaction Name */}
        <SidebarInputField
          label="Tên giao dịch định kỳ"
          value={formData.name}
          onChange={(value) => handleInputChange('name', value)}
          placeholder="Nhập tên giao dịch định kỳ"
          required
          error={errors.name}
          icon={<Repeat size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={500}
        />
        
        {/* Amount and Type */}
        <SidebarInputField
          label="Số tiền"
          value={formData.amount}
          onChange={(value) => handleInputChange('amount', value)}
          placeholder="0"
          type="number"
          required
          error={errors.amount}
          icon={<DollarSign size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={500}
        />
        
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <ArrowRightLeft size={14} className="mr-2" />
            Loại giao dịch *
          </label>
          <SearchableCombobox
            value={formData.type}
            onChange={(value) => handleInputChange('type', value as "income" | "expense" | "transfer")}
            options={TRANSACTION_TYPES}
            placeholder="Chọn loại giao dịch..."
            disabled={loading}
          />
          {errors.type && (
            <p className="text-xs text-red-500">{errors.type}</p>
          )}
        </div>
        
        {/* Category */}
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <FolderTree size={14} className="mr-2" />
            Danh mục
          </label>
          <SearchableCombobox
            value={formData.categoryId}
            onChange={(value) => handleInputChange('categoryId', value)}
            options={[
              { value: "", label: "Chọn danh mục" },
              ...filteredCategories
            ]}
            placeholder="Chọn danh mục..."
            disabled={loading}
          />
        </div>
        
        {/* Frequency */}
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <Clock size={14} className="mr-2" />
            Tần suất lặp lại *
          </label>
          <SearchableCombobox
            value={formData.frequency}
            onChange={(value) => handleInputChange('frequency', value)}
            options={FREQUENCY_OPTIONS}
            placeholder="Chọn tần suất lặp lại..."
            disabled={loading}
          />
          {errors.frequency && (
            <p className="text-xs text-red-500">{errors.frequency}</p>
          )}
        </div>
        
        {/* Date Range */}
        <SidebarInputField
          label="Ngày bắt đầu"
          value={formData.startDate}
          onChange={(value) => handleInputChange('startDate', value)}
          type="date"
          required
          error={errors.startDate}
          icon={<Calendar size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={500}
        />
        
        <SidebarInputField
          label="Ngày kết thúc"
          value={formData.endDate}
          onChange={(value) => handleInputChange('endDate', value)}
          type="date"
          error={errors.endDate}
          icon={<Calendar size={14} />}
          disabled={loading}
          span="single"
          sidebarWidth={500}
        />
        
        {/* Description */}
        <SidebarTextareaField
          label="Mô tả"
          value={formData.description}
          onChange={(value) => handleInputChange('description', value)}
          placeholder="Mô tả chi tiết về giao dịch định kỳ (tùy chọn)"
          rows={3}
          icon={<FileText size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={500}
        />
      </SidebarForm>
    </BaseSidebar>
  )
}
