"use client"

import { useAuth } from "@/contexts/AuthContext"
import { useGlobalSidebar } from "@/contexts/GlobalSidebarContext"
import { useResizableAIAssistant } from "@/hooks/useResizableAIAssistant"
import { usePathname } from "next/navigation"
import React, { useEffect, useState } from "react"
import { Sidebar } from "../Sidebar"
import { AIAssistantButton } from "../ui/AIAssistantButton"
import { AIAssistantSidebar } from "../ui/AIAssistantSidebar"
import { useTaskbarManager } from "./TaskbarManager"

interface GlobalLayoutProps {
  children: React.ReactNode
}

export function GlobalLayout({ children }: GlobalLayoutProps) {
  const pathname = usePathname()
  const { isAuthenticated, isLoading } = useAuth()

  // Check if current page is login page
  const isLoginPage = pathname === '/login'

  // AI Assistant state
  const aiAssistant = useResizableAIAssistant()
  const {
    aiAssistantOpen,
    isResizing: aiIsResizing,
    openAIAssistant,
    closeAIAssistant
  } = aiAssistant

  // Taskbar integration
  const {
    activeItems,
    handleRestoreAccount,
    handleRestoreTransaction,
    handleRestoreContact,
    handleRestoreCategory,
    handleRestoreBudget,
    clearActiveItem,
    TaskbarManager: TaskbarManagerComponent
  } = useTaskbarManager()

  // Global sidebar state
  const {
    globalState,
    setMenuSidebarOpen,
    setMenuSidebarMinimal,
    getContentMargins,
    isMobile
  } = useGlobalSidebar()

  // Main sidebar state (for navigation)
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // Sync with global state
  useEffect(() => {
    setMenuSidebarOpen(sidebarOpen)
  }, [sidebarOpen, setMenuSidebarOpen])

  // Handle taskbar restore for different types
  useEffect(() => {
    if (activeItems.account) {
      console.log('Global: Restoring account from taskbar:', activeItems.account)
      // Dispatch custom event for account restoration
      window.dispatchEvent(new CustomEvent('taskbar-restore-account', {
        detail: activeItems.account
      }))
      clearActiveItem('account')
    }
  }, [activeItems.account, clearActiveItem])

  useEffect(() => {
    if (activeItems.transaction) {
      console.log('Global: Restoring transaction from taskbar:', activeItems.transaction)
      // Dispatch custom event for transaction restoration
      window.dispatchEvent(new CustomEvent('taskbar-restore-transaction', {
        detail: activeItems.transaction
      }))
      clearActiveItem('transaction')
    }
  }, [activeItems.transaction, clearActiveItem])

  useEffect(() => {
    if (activeItems.contact) {
      console.log('Global: Restoring contact from taskbar:', activeItems.contact)
      // Dispatch custom event for contact restoration
      window.dispatchEvent(new CustomEvent('taskbar-restore-contact', {
        detail: activeItems.contact
      }))
      clearActiveItem('contact')
    }
  }, [activeItems.contact, clearActiveItem])

  useEffect(() => {
    if (activeItems.category) {
      console.log('Global: Restoring category from taskbar:', activeItems.category)
      // Dispatch custom event for category restoration
      window.dispatchEvent(new CustomEvent('taskbar-restore-category', {
        detail: activeItems.category
      }))
      clearActiveItem('category')
    }
  }, [activeItems.category, clearActiveItem])

  useEffect(() => {
    if (activeItems.budget) {
      console.log('Global: Restoring budget from taskbar:', activeItems.budget)
      // Dispatch custom event for budget restoration
      window.dispatchEvent(new CustomEvent('taskbar-restore-budget', {
        detail: activeItems.budget
      }))
      clearActiveItem('budget')
    }
  }, [activeItems.budget, clearActiveItem])

  // If on login page or not authenticated, render without layout
  if (isLoginPage || (!isAuthenticated && !isLoading)) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 relative">
      {/* Main Navigation Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onMinimalChange={setMenuSidebarMinimal}
      />

      {/* Main Content Area */}
      <div
        className={`
          content-area-transition
          ${globalState.formSidebar.isResizing || aiIsResizing ? 'resizing' : ''}
        `}
        style={getContentMargins()}
      >
        {/* Sidebar Toggle Button (Mobile) */}
        <button
          onClick={() => setSidebarOpen(true)}
          className="lg:hidden fixed top-4 left-4 z-30 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700"
        >
          <svg className="w-6 h-6 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        {/* Page Content */}
        {children}
      </div>

      {/* AI Assistant Button */}
      <AIAssistantButton
        onClick={openAIAssistant}
        isOpen={aiAssistantOpen}
      />

      {/* AI Assistant Sidebar */}
      <AIAssistantSidebar
        isOpen={aiAssistantOpen}
        onClose={closeAIAssistant}
        isResizing={aiIsResizing}
      />

      {/* Global Taskbar */}
      {TaskbarManagerComponent}

      {/* Overlay for mobile sidebar */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}

// Hook for pages to listen to taskbar restore events
export function useGlobalTaskbarRestore() {
  const [restoredItem, setRestoredItem] = useState<any>(null)
  const [restoreType, setRestoreType] = useState<string | null>(null)

  useEffect(() => {
    const handleAccountRestore = (event: CustomEvent) => {
      setRestoredItem(event.detail)
      setRestoreType('account')
    }

    const handleTransactionRestore = (event: CustomEvent) => {
      setRestoredItem(event.detail)
      setRestoreType('transaction')
    }

    const handleContactRestore = (event: CustomEvent) => {
      setRestoredItem(event.detail)
      setRestoreType('contact')
    }

    const handleCategoryRestore = (event: CustomEvent) => {
      setRestoredItem(event.detail)
      setRestoreType('category')
    }

    const handleBudgetRestore = (event: CustomEvent) => {
      setRestoredItem(event.detail)
      setRestoreType('budget')
    }

    window.addEventListener('taskbar-restore-account', handleAccountRestore as EventListener)
    window.addEventListener('taskbar-restore-transaction', handleTransactionRestore as EventListener)
    window.addEventListener('taskbar-restore-contact', handleContactRestore as EventListener)
    window.addEventListener('taskbar-restore-category', handleCategoryRestore as EventListener)
    window.addEventListener('taskbar-restore-budget', handleBudgetRestore as EventListener)

    return () => {
      window.removeEventListener('taskbar-restore-account', handleAccountRestore as EventListener)
      window.removeEventListener('taskbar-restore-transaction', handleTransactionRestore as EventListener)
      window.removeEventListener('taskbar-restore-contact', handleContactRestore as EventListener)
      window.removeEventListener('taskbar-restore-category', handleCategoryRestore as EventListener)
      window.removeEventListener('taskbar-restore-budget', handleBudgetRestore as EventListener)
    }
  }, [])

  const clearRestored = () => {
    setRestoredItem(null)
    setRestoreType(null)
  }

  return {
    restoredItem,
    restoreType,
    clearRestored
  }
}
