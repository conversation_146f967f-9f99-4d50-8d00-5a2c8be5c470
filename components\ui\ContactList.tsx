"use client"

import React, { useState } from "react"
import { ContactCard } from "./ContactCard"
import { ChevronDown, ChevronUp, SortAsc, SortDesc, Filter } from "lucide-react"

interface Contact {
  id: number
  userId: number
  name: string
  phone?: string
  email?: string
  bankName?: string
  accountNumber?: string
  description?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

interface ContactListProps {
  contacts: Contact[]
  onEdit?: (contact: Contact) => void
  showFilters?: boolean
}

type SortField = "name" | "phone" | "email" | "bankName"
type SortDirection = "asc" | "desc"

export function ContactList({ contacts, onEdit, showFilters = true }: ContactListProps) {
  const [sortField, setSortField] = useState<SortField>("name")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")
  const [showSortOptions, setShowSortOptions] = useState(false)

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
    setShowSortOptions(false)
  }

  const sortedContacts = [...contacts].sort((a, b) => {
    let aValue: any
    let bValue: any

    switch (sortField) {
      case "name":
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case "phone":
        aValue = (a.phone || "").toLowerCase()
        bValue = (b.phone || "").toLowerCase()
        break
      case "email":
        aValue = (a.email || "").toLowerCase()
        bValue = (b.email || "").toLowerCase()
        break
      case "bankName":
        aValue = (a.bankName || "").toLowerCase()
        bValue = (b.bankName || "").toLowerCase()
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
    return 0
  })

  const getSortLabel = (field: SortField) => {
    const labels = {
      name: "Name",
      phone: "Phone",
      email: "Email",
      bankName: "Bank"
    }
    return labels[field]
  }

  if (contacts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 dark:text-gray-500 text-lg mb-2">No contacts found</div>
        <div className="text-gray-500 dark:text-gray-400 text-sm">
          Add your first contact to get started
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Sort Controls */}
      {showFilters && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {contacts.length} contact{contacts.length !== 1 ? 's' : ''}
          </div>
          
          <div className="relative">
            <button
              onClick={() => setShowSortOptions(!showSortOptions)}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <Filter size={14} />
              Sort by {getSortLabel(sortField)}
              {sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />}
              {showSortOptions ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </button>

            {showSortOptions && (
              <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-10">
                {(["name", "phone", "email", "bankName"] as SortField[]).map((field) => (
                  <button
                    key={field}
                    onClick={() => handleSort(field)}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                      sortField === field 
                        ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20" 
                        : "text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      {getSortLabel(field)}
                      {sortField === field && (
                        sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Desktop Table Header */}
      <div className="hidden md:block bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
        <div className="grid grid-cols-12 gap-3 items-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          <div className="col-span-3">Name</div>
          <div className="col-span-2">Phone</div>
          <div className="col-span-3">Email</div>
          <div className="col-span-3">Bank Info</div>
          <div className="col-span-1 text-right">Actions</div>
        </div>
      </div>

      {/* Contact Cards */}
      <div className="space-y-3">
        {sortedContacts.map((contact) => (
          <ContactCard
            key={contact.id}
            contact={contact}
            onEdit={onEdit}

          />
        ))}
      </div>
    </div>
  )
}
