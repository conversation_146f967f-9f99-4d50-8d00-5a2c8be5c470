/**
 * CategoryGoal GraphQL Resolvers
 * 
 * Resolvers for CategoryGoal queries and mutations using Prisma directly
 */

import { GraphQLContext, requireAuth, requireOwnershipOrAdmin } from '../context'
import { prisma } from '@/lib/database'
import { GraphQLError } from 'graphql'

interface CategoryGoalArgs {
  id: string
}

interface CategoryGoalsArgs {
  first?: number
  after?: string
  search?: string
  categoryId?: string
  period?: 'weekly' | 'monthly' | 'yearly' | 'custom' | 'none'
  isActive?: boolean
}

interface CreateCategoryGoalArgs {
  input: {
    name: string
    description?: string
    amount: number
    period: 'weekly' | 'monthly' | 'yearly' | 'custom' | 'none'
    startDate: Date
    endDate?: Date
    color?: string
    categoryId: string
  }
}

interface UpdateCategoryGoalArgs {
  id: string
  input: {
    name?: string
    description?: string
    amount?: number
    period?: 'weekly' | 'monthly' | 'yearly' | 'custom' | 'none'
    startDate?: Date
    endDate?: Date
    color?: string
    categoryId?: string
    isActive?: boolean
  }
}

// Helper function to build connection response
function buildConnection<T>(
  items: T[],
  totalCount: number,
  first?: number,
  after?: string
) {
  const edges = items.map((item: any, index) => ({
    node: item,
    cursor: Buffer.from(`${after ? parseInt(Buffer.from(after, 'base64').toString()) + index + 1 : index}`).toString('base64')
  }))

  const hasNextPage = first ? items.length === first : false
  const hasPreviousPage = !!after

  return {
    edges,
    pageInfo: {
      hasNextPage,
      hasPreviousPage,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount
  }
}

export const categoryGoalResolvers = {
  Query: {
    categoryGoals: async (_: any, args: CategoryGoalsArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const { first = 25, after, search, categoryId, period, isActive } = args
      
      // Convert cursor to page number
      const page = after ? Math.floor(parseInt(Buffer.from(after, 'base64').toString()) / first) + 1 : 1

      // Build where clause
      const where: any = {
        userId: user.id
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (categoryId) where.categoryId = categoryId
      if (period) where.period = period
      if (isActive !== undefined) where.isActive = isActive

      // Convert cursor to skip value
      const skip = after ? parseInt(Buffer.from(after, 'base64').toString()) : 0

      const [categoryGoals, totalCount] = await Promise.all([
        prisma.categoryGoal.findMany({
          where,
          include: {
            category: true
          },
          skip,
          take: first,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.categoryGoal.count({ where })
      ])

      return buildConnection(categoryGoals, totalCount, first, after)
    },

    categoryGoal: async (_: any, args: CategoryGoalArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const categoryGoal = await prisma.categoryGoal.findFirst({
        where: {
          id: args.id,
          userId: user.id
        },
        include: {
          category: true
        }
      })

      if (!categoryGoal) {
        throw new GraphQLError('Category goal not found')
      }

      // Check ownership
      requireOwnershipOrAdmin(context, categoryGoal.userId)

      return categoryGoal
    }
  },

  Mutation: {
    createCategoryGoal: async (_: any, args: CreateCategoryGoalArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // Verify category exists and belongs to user
        const category = await prisma.category.findFirst({
          where: {
            id: input.categoryId,
            userId: user.id
          }
        })

        if (!category) {
          throw new GraphQLError('Category not found')
        }

        const categoryGoal = await prisma.categoryGoal.create({
          data: {
            name: input.name,
            description: input.description,
            amount: input.amount,
            period: input.period,
            startDate: input.startDate,
            endDate: input.endDate,
            color: input.color || '#45B7D1',
            isDefault: input.isDefault || false,
            categoryId: input.categoryId,
            userId: user.id
          },
          include: {
            category: true
          }
        })
        
        return categoryGoal
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to create category goal')
      }
    },

    updateCategoryGoal: async (_: any, args: UpdateCategoryGoalArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // First check if category goal exists and user has permission
        const existingCategoryGoal = await prisma.categoryGoal.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingCategoryGoal) {
          throw new GraphQLError('Category goal not found')
        }

        requireOwnershipOrAdmin(context, existingCategoryGoal.userId)

        const categoryGoal = await prisma.categoryGoal.update({
          where: { id: args.id },
          data: {
            name: input.name,
            description: input.description,
            amount: input.amount,
            period: input.period,
            startDate: input.startDate,
            endDate: input.endDate,
            color: input.color,
            categoryId: input.categoryId,
            isActive: input.isActive
          },
          include: {
            category: true
          }
        })
        
        return categoryGoal
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update category goal')
      }
    },

    deleteCategoryGoal: async (_: any, args: CategoryGoalArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        // First check if category goal exists and user has permission
        const existingCategoryGoal = await prisma.categoryGoal.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })

        if (!existingCategoryGoal) {
          throw new GraphQLError('Category goal not found')
        }

        requireOwnershipOrAdmin(context, existingCategoryGoal.userId)

        // Soft delete by setting isActive to false
        await prisma.categoryGoal.update({
          where: { id: args.id },
          data: { isActive: false }
        })

        return true
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to delete category goal')
      }
    }
  },

  CategoryGoal: {
    user: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.user) {
        return parent.user
      }

      return await context.prisma.user.findUnique({
        where: { id: parent.userId }
      })
    },

    category: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.category) {
        return parent.category
      }

      return await context.prisma.category.findUnique({
        where: { id: parent.categoryId }
      })
    },

    // Computed fields from service
    currentAmount: (parent: any) => parent.currentAmount || 0,
    progress: (parent: any) => parent.progress || 0,
    remainingAmount: (parent: any) => parent.remainingAmount || parent.amount || 0
  }
}
