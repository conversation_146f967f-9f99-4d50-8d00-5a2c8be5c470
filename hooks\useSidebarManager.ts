"use client"

import { useState, useCallback } from "react"

export type SidebarType =
  | 'contact'
  | 'transaction'
  | 'account'
  | 'category'
  | 'budget'
  | 'tag'
  | 'spendingLimit'
  | 'user'
  | 'recurring'
  | 'aiAssistant'
  | 'accountType'
  | 'contactType'
  | 'debitCard'

interface SidebarState {
  [key: string]: boolean
}

interface SidebarData {
  [key: string]: any
}

interface UseSidebarManagerReturn {
  sidebarStates: SidebarState
  sidebarData: SidebarData
  openSidebar: (type: SidebarType, data?: any) => void
  closeSidebar: (type: SidebarType) => void
  closeAllSidebars: () => void
  isSidebarOpen: (type: SidebarType) => boolean
  isAnySidebarOpen: () => boolean
  getSidebarData: (type: SidebarType) => any
  setSidebarData: (type: SidebarType, data: any) => void
}

/**
 * Hook to manage multiple sidebars with independence and auto-close logic
 * 
 * Features:
 * - Each sidebar is independent
 * - Opening a new sidebar closes unrelated sidebars
 * - AIAssistant sidebar can coexist with other sidebars
 * - Proper state management for multiple sidebars
 */
export function useSidebarManager(): UseSidebarManagerReturn {
  const [sidebarStates, setSidebarStates] = useState<SidebarState>({
    contact: false,
    transaction: false,
    account: false,
    category: false,
    budget: false,
    tag: false,
    spendingLimit: false,
    user: false,
    recurring: false,
    aiAssistant: false
  })

  const [sidebarData, setSidebarDataState] = useState<SidebarData>({})

  const openSidebar = useCallback((type: SidebarType, data?: any) => {
    setSidebarStates(prev => {
      const newState = { ...prev }

      // Only close other form sidebars if opening a different form sidebar
      // AIAssistant can coexist with other sidebars
      // Keep sidebars open when navigating between pages
      if (type !== 'aiAssistant') {
        Object.keys(newState).forEach(key => {
          if (key !== type && key !== 'aiAssistant' && newState[key] === true) {
            // Only close if opening a different form sidebar
            newState[key] = false
          }
        })
      }

      // Open the requested sidebar
      newState[type] = true

      return newState
    })

    // Set sidebar data if provided
    if (data !== undefined) {
      setSidebarDataState(prev => ({
        ...prev,
        [type]: data
      }))
    }
  }, [])

  const closeSidebar = useCallback((type: SidebarType) => {
    setSidebarStates(prev => ({
      ...prev,
      [type]: false
    }))

    // Clear sidebar data when closing
    setSidebarDataState(prev => ({
      ...prev,
      [type]: undefined
    }))
  }, [])

  const closeAllSidebars = useCallback(() => {
    setSidebarStates(prev => {
      const newState = { ...prev }
      Object.keys(newState).forEach(key => {
        newState[key] = false
      })
      return newState
    })

    // Clear all sidebar data
    setSidebarDataState({})
  }, [])

  const isSidebarOpen = useCallback((type: SidebarType) => {
    return sidebarStates[type] || false
  }, [sidebarStates])

  const isAnySidebarOpen = useCallback(() => {
    return Object.values(sidebarStates).some(isOpen => isOpen)
  }, [sidebarStates])

  const getSidebarData = useCallback((type: SidebarType) => {
    return sidebarData[type]
  }, [sidebarData])

  const setSidebarData = useCallback((type: SidebarType, data: any) => {
    setSidebarDataState(prev => ({
      ...prev,
      [type]: data
    }))
  }, [])

  return {
    sidebarStates,
    sidebarData,
    openSidebar,
    closeSidebar,
    closeAllSidebars,
    isSidebarOpen,
    isAnySidebarOpen,
    getSidebarData,
    setSidebarData
  }
}

/**
 * Hook for pages that need specific sidebar management
 * Provides convenient methods for common sidebar operations
 */
export function usePageSidebarManager(pageSidebarType: SidebarType) {
  const {
    sidebarStates,
    sidebarData,
    openSidebar,
    closeSidebar,
    closeAllSidebars,
    isSidebarOpen,
    isAnySidebarOpen,
    getSidebarData,
    setSidebarData
  } = useSidebarManager()

  const openPageSidebar = useCallback((data?: any) => {
    openSidebar(pageSidebarType, data)
  }, [openSidebar, pageSidebarType])

  const closePageSidebar = useCallback(() => {
    closeSidebar(pageSidebarType)
  }, [closeSidebar, pageSidebarType])

  const isPageSidebarOpen = useCallback(() => {
    return isSidebarOpen(pageSidebarType)
  }, [isSidebarOpen, pageSidebarType])

  const getPageSidebarData = useCallback(() => {
    return getSidebarData(pageSidebarType)
  }, [getSidebarData, pageSidebarType])

  const setPageSidebarData = useCallback((data: any) => {
    setSidebarData(pageSidebarType, data)
  }, [setSidebarData, pageSidebarType])

  return {
    // General sidebar management
    sidebarStates,
    sidebarData,
    openSidebar,
    closeSidebar,
    closeAllSidebars,
    isSidebarOpen,
    isAnySidebarOpen,
    getSidebarData,
    setSidebarData,

    // Page-specific sidebar management
    openPageSidebar,
    closePageSidebar,
    isPageSidebarOpen: isPageSidebarOpen(),
    getPageSidebarData,
    setPageSidebarData
  }
}
