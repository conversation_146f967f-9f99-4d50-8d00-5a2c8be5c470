"use client"

import React, { useState } from "react"
import { Plus, Trash2, Edit, Palette } from "lucide-react"
import { AccountType } from "@/lib/generated/prisma"

// Default account types for the system
const defaultAccountTypes: AccountType[] = [
  {
    id: "cash",
    name: "Tiền mặt",
    // description: "Tiền mặt trong ví", // TODO: Add description field to AccountType model
    color: "#10b981",
    icon: "wallet",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "bank",
    name: "<PERSON><PERSON><PERSON> khoản ngân hàng",
    // description: "Tài khoản tiết kiệm hoặc vãng lai", // TODO: Add description field
    color: "#3b82f6",
    icon: "building2",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "credit",
    name: "Thẻ tín dụng",
    // description: "Thẻ tín dụng", // TODO: Add description field
    color: "#ef4444",
    icon: "credit-card",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "investment",
    name: "Đầu tư",
    // description: "Tài khoản đầu tư", // TODO: Add description field
    color: "#8b5cf6",
    icon: "trending-up",
    isDefault: true,
    isActive: true,
    userId: "",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

interface AccountTypeManagerProps {
  accountTypes: AccountType[]
  selectedType: AccountType | null
  onTypeSelect: (type: AccountType) => void
  onTypeCreate: (type: Omit<AccountType, 'id'>) => void
  onTypeUpdate: (id: string, type: Partial<AccountType>) => void
  onTypeDelete: (id: string) => void
  disabled?: boolean
}

const PRESET_COLORS = [
  "#f59e0b", "#3b82f6", "#ef4444", "#8b5cf6", "#10b981",
  "#f97316", "#06b6d4", "#84cc16", "#ec4899", "#6366f1"
]

export function AccountTypeManager({
  accountTypes,
  selectedType,
  onTypeSelect,
  onTypeCreate,
  onTypeUpdate,
  onTypeDelete,
  disabled = false
}: AccountTypeManagerProps) {
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingType, setEditingType] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    color: PRESET_COLORS[0],
    description: "",
    isBankAccount: false
  })

  const handleCreateType = () => {
    if (formData.name.trim()) {
      onTypeCreate({
        name: formData.name.trim(),
        color: formData.color,
        description: formData.description.trim() || undefined,
        isBankAccount: formData.isBankAccount,
        isCustom: true
      })
      setFormData({ name: "", color: PRESET_COLORS[0], description: "", isBankAccount: false })
      setShowCreateForm(false)
    }
  }

  const handleUpdateType = (id: string) => {
    if (formData.name.trim()) {
      onTypeUpdate(id, {
        name: formData.name.trim(),
        color: formData.color,
        description: formData.description.trim() || undefined,
        isBankAccount: formData.isBankAccount
      })
      setEditingType(null)
      setFormData({ name: "", color: PRESET_COLORS[0], description: "", isBankAccount: false })
    }
  }

  const startEdit = (type: AccountType) => {
    setFormData({
      name: type.name,
      color: type.color,
      description: type.description || "",
      isBankAccount: type.isBankAccount || false
    })
    setEditingType(type.id)
    setShowCreateForm(false)
  }

  const cancelEdit = () => {
    setEditingType(null)
    setShowCreateForm(false)
    setFormData({ name: "", color: PRESET_COLORS[0], description: "", isBankAccount: false })
  }

  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex items-center justify-between">
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
          Loại tài khoản
        </label>
        <button
          type="button"
          onClick={() => setShowCreateForm(!showCreateForm)}
          disabled={disabled}
          className="flex items-center gap-1 px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 disabled:opacity-50"
        >
          <Plus size={12} />
          Tạo loại mới
        </button>
      </div>

      {/* Account Types Grid */}
      <div className="grid grid-cols-2 gap-2">
        {accountTypes.map((type) => (
          <div key={type.id}>
            {editingType === type.id ? (
              // Edit Form
              <div className="border-2 border-blue-500 rounded-lg p-2 space-y-2">
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="Tên loại tài khoản"
                />
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="Mô tả (tùy chọn)"
                />
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id={`isBankAccount-edit-${type.id}`}
                    checked={formData.isBankAccount}
                    onChange={(e) => setFormData(prev => ({ ...prev, isBankAccount: e.target.checked }))}
                    className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label htmlFor={`isBankAccount-edit-${type.id}`} className="text-xs text-gray-700 dark:text-gray-300">
                    Là tài khoản ngân hàng
                  </label>
                </div>
                <div className="flex items-center gap-1">
                  {PRESET_COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, color }))}
                      className={`w-4 h-4 rounded-full border-2 ${
                        formData.color === color ? 'border-gray-900 dark:border-gray-100' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                <div className="flex gap-1">
                  <button
                    type="button"
                    onClick={() => handleUpdateType(type.id)}
                    className="flex-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Lưu
                  </button>
                  <button
                    type="button"
                    onClick={cancelEdit}
                    className="flex-1 px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    Hủy
                  </button>
                </div>
              </div>
            ) : (
              // Type Button
              <button
                type="button"
                onClick={() => onTypeSelect(type)}
                disabled={disabled}
                className={`w-full p-2 rounded-lg border-2 text-xs font-medium transition-all text-left ${
                  selectedType?.id === type.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-500/20'
                    : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: type.color }}
                    />
                    <span className="text-gray-900 dark:text-gray-100">{type.name}</span>
                  </div>
                  
                  {type.isCustom && (
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          startEdit(type)
                        }}
                        className="p-1 text-gray-400 hover:text-blue-600"
                      >
                        <Edit size={10} />
                      </button>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          onTypeDelete(type.id)
                        }}
                        className="p-1 text-gray-400 hover:text-red-600"
                      >
                        <Trash2 size={10} />
                      </button>
                    </div>
                  )}
                </div>
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Create Form */}
      {showCreateForm && (
        <div className="border-2 border-green-500 rounded-lg p-3 space-y-2">
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            placeholder="Tên loại tài khoản mới"
          />
          <input
            type="text"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            placeholder="Mô tả (tùy chọn)"
          />
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="isBankAccount"
              checked={formData.isBankAccount}
              onChange={(e) => setFormData(prev => ({ ...prev, isBankAccount: e.target.checked }))}
              className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="isBankAccount" className="text-xs text-gray-700 dark:text-gray-300">
              Là tài khoản ngân hàng
            </label>
          </div>
          <div className="flex items-center gap-1">
            <Palette size={12} className="text-gray-500" />
            {PRESET_COLORS.map((color) => (
              <button
                key={color}
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, color }))}
                className={`w-4 h-4 rounded-full border-2 ${
                  formData.color === color ? 'border-gray-900 dark:border-gray-100' : 'border-gray-300 dark:border-gray-600'
                }`}
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={handleCreateType}
              className="flex-1 px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
            >
              Tạo loại
            </button>
            <button
              type="button"
              onClick={() => setShowCreateForm(false)}
              className="flex-1 px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Hủy
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
