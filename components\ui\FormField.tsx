"use client"

import React from "react"
import { AlertCircle } from "lucide-react"

interface FormFieldProps {
  label: string
  error?: string
  required?: boolean
  children: React.ReactNode
  icon?: React.ReactNode
  className?: string
}

export function FormField({
  label,
  error,
  required = false,
  children,
  icon,
  className = ""
}: FormFieldProps) {
  return (
    <div className={`space-y-1.5 ${className}`}>
      <label className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center gap-1.5">
        {icon && <span className="text-gray-400">{icon}</span>}
        {label}
        {required && <span className="text-red-400 text-xs">*</span>}
      </label>
      {children}
      {error && (
        <div className="flex items-center gap-1 text-xs text-red-500 bg-red-50 dark:bg-red-900/20 px-2 py-1 rounded-md">
          <AlertCircle size={12} />
          {error}
        </div>
      )}
    </div>
  )
}

interface InputFieldProps {
  type?: string
  value: string | number
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  min?: string | number
  max?: string | number
  step?: string | number
  className?: string
}

export function InputField({
  type = "text",
  value,
  onChange,
  placeholder,
  disabled = false,
  min,
  max,
  step,
  className = ""
}: InputFieldProps) {
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      min={min}
      max={max}
      step={step}
      className={`w-full px-3 py-2.5 text-sm border border-gray-200 dark:border-gray-700 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white dark:focus:bg-gray-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-300 dark:hover:border-gray-600 ${className}`}
    />
  )
}

interface SelectFieldProps {
  value: string | number
  onChange: (value: string) => void
  options: Array<{ value: string | number; label: string; icon?: string }>
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function SelectField({
  value,
  onChange,
  options,
  placeholder = "Chọn...",
  disabled = false,
  className = ""
}: SelectFieldProps) {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`w-full px-3 py-2.5 text-sm border border-gray-200 dark:border-gray-700 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white dark:focus:bg-gray-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-300 dark:hover:border-gray-600 ${className}`}
    >
      <option value="">{placeholder}</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.icon ? `${option.icon} ${option.label}` : option.label}
        </option>
      ))}
    </select>
  )
}

interface CheckboxFieldProps {
  checked: boolean
  onChange: (checked: boolean) => void
  label: string
  disabled?: boolean
  className?: string
}

export function CheckboxField({
  checked,
  onChange,
  label,
  disabled = false,
  className = ""
}: CheckboxFieldProps) {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
      />
      <label className="text-sm text-gray-700 dark:text-gray-300">
        {label}
      </label>
    </div>
  )
}

interface TextareaFieldProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  rows?: number
  className?: string
}

export function TextareaField({
  value,
  onChange,
  placeholder,
  disabled = false,
  rows = 3,
  className = ""
}: TextareaFieldProps) {
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      rows={rows}
      className={`w-full px-3 py-2.5 text-sm border border-gray-200 dark:border-gray-700 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white dark:focus:bg-gray-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-300 dark:hover:border-gray-600 resize-vertical ${className}`}
    />
  )
}

interface CardFieldProps {
  title: string
  icon?: React.ReactNode
  children: React.ReactNode
  className?: string
  gradient?: boolean
}

export function CardField({
  title,
  icon,
  children,
  className = "",
  gradient = false
}: CardFieldProps) {
  return (
    <div className={`${gradient
      ? 'bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40 dark:from-blue-900/30 dark:via-indigo-900/20 dark:to-purple-900/10 border-blue-200/60 dark:border-blue-700/40'
      : 'bg-white/60 dark:bg-gray-800/60 border-gray-200/60 dark:border-gray-700/40'
    } backdrop-blur-sm rounded-2xl p-4 sm:p-5 border hover:shadow-lg hover:shadow-gray-200/50 dark:hover:shadow-gray-900/50 transition-all duration-300 ${className}`}>
      <h3 className="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 sm:mb-4 flex items-center gap-2">
        {icon && <span className="text-gray-500 dark:text-gray-400">{icon}</span>}
        {title}
      </h3>
      <div className="space-y-3 sm:space-y-4">
        {children}
      </div>
    </div>
  )
}
