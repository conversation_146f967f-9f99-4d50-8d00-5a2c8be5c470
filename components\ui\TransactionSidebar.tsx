"use client"

import { useResizableSidebar } from "@/hooks/useResizableSidebar"
import { Account, Category, Contact, Transaction } from "@/lib/generated/prisma"
import { ArrowUpDown, Building2, Calendar, Clock, CreditCard, DollarSign, FileText, MapPin, Tag, User, Wallet } from "lucide-react"
import React, { useEffect, useState } from "react"
import { AddEditContactModal } from "./AddEditContactModal"
import { BaseSidebar } from "./BaseSidebar"
import { NewContactDialog } from "./ContactSelector"
import { SearchableCombobox } from "./SearchableCombobox"
import { SidebarActionFooter } from "./SidebarFooter"
import { SidebarForm, SidebarFormField, SidebarInputField } from "./SidebarForm"


// Account and Category interfaces are now imported from generated Prisma types

interface TransactionSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (transaction: Transaction) => void
  transaction?: Transaction | null
  isEditing: boolean
  accounts?: Account[]
  categories?: Category[]
  contacts?: Contact[]
  onContactCreated?: (contact: Contact) => void
}

interface FormData {
  // Improved account selection structure
  kind: "income" | "expense" | "transfer"

  // Source account (always user's account for expense/transfer)
  fromAccountId: string

  // Destination account (user's account for income, contact account for expense)
  toAccountId: string

  // Contact selection for expense/income/transfer
  expenseContactId: string    // Contact for expense transactions
  incomeContactId: string     // Contact for income transactions
  transferContactId: string   // Contact for transfer transactions

  // Specific account selection within selected contact
  expenseContactAccountId: string
  incomeContactAccountId: string
  transferContactAccountId: string

  categoryId: string
  amount: string
  description: string
  location: string
  date: string
  time: string
  isRecurring: boolean
}

interface FormErrors {
  kind?: string
  fromAccountId?: string
  toAccountId?: string
  expenseContactId?: string
  incomeContactId?: string
  transferContactId?: string
  expenseContactAccountId?: string
  incomeContactAccountId?: string
  transferContactAccountId?: string
  categoryId?: string
  amount?: string
  description?: string
  location?: string
  date?: string
  time?: string
}

export function TransactionSidebar({
  isOpen,
  onClose,
  onSave,
  transaction,
  isEditing,
  accounts,
  categories,
  contacts,
  onContactCreated
}: TransactionSidebarProps) {


  // Dynamic sidebar width using resizable hook
  const {
    sidebarWidth,
    isResizing
  } = useResizableSidebar({
    storageKey: 'transactionSidebarWidth',
    isOpen,
    config: {
      DEFAULT_WIDTH: 500,
      MIN_WIDTH: 400,
      MAX_WIDTH_PERCENTAGE: 0.7
    }
  })
  
  const [formData, setFormData] = useState<FormData>({
    kind: "expense",
    fromAccountId: "",
    toAccountId: "",
    expenseContactId: "",
    incomeContactId: "",
    transferContactId: "",
    expenseContactAccountId: "",
    incomeContactAccountId: "",
    transferContactAccountId: "",
    categoryId: "",
    amount: "",
    description: "",
    location: "",
    date: "",
    time: new Date().toTimeString().slice(0, 5), // Default to current time
    isRecurring: false
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  const [showNewContactDialog, setShowNewContactDialog] = useState(false)
  const [showAddContactModal, setShowAddContactModal] = useState(false)
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [selectedContactForTransfer, setSelectedContactForTransfer] = useState<Contact | null>(null)
  const [pendingContactName, setPendingContactName] = useState("")



  // Helper functions for account selection based on transaction type

  // Get user accounts for source account selection (expense/transfer)
  const getUserAccountOptions = () => {
    const baseOptions = [{ value: "", label: "Chọn tài khoản" }]
    return [
      ...baseOptions,
      ...(accounts || [])
        .filter(account =>
          formData.kind === 'transfer'
            ? account.id.toString() !== formData.toAccountId
            : true
        )
        .map(account => ({
          value: account.id.toString(),
          label: account.name
        }))
    ]
  }

  // Get contact options for contact selection
  const getContactOptions = () => {
    const baseOptions = [{ value: "", label: "Chọn liên hệ" }]

    if (formData.kind === 'transfer') {
      // For transfer, include user's own contact and other contacts
      return [
        ...baseOptions,
        ...(contacts || []).map(contact => ({
          value: contact.id.toString(),
          label: contact.name
        }))
      ]
    } else {
      // For expense/income, only other contacts
      return [
        ...baseOptions,
        ...(contacts || [])
          .map(contact => ({
            value: contact.id.toString(),
            label: contact.name
          }))
      ]
    }
  }

  // Get contact account options for selected contact
  const getContactAccountOptions = (contact: Contact | null) => {
    const baseOptions = [{ value: "", label: "Chọn tài khoản" }]
    if (!contact?.accounts) return baseOptions

    return [
      ...baseOptions,
      ...contact.accounts.map(account => ({
        value: account.id.toString(),
        label: account.name
      }))
    ]
  }

  // Get current selected contact based on transaction type
  const getCurrentSelectedContact = () => {
    switch (formData.kind) {
      case 'expense':
        return formData.expenseContactId
          ? contacts?.find(c => c.id.toString() === formData.expenseContactId) || null
          : null
      case 'income':
        return formData.incomeContactId
          ? contacts?.find(c => c.id.toString() === formData.incomeContactId) || null
          : null
      case 'transfer':
        return formData.transferContactId
          ? contacts?.find(c => c.id.toString() === formData.transferContactId) || null
          : null
      default:
        return null
    }
  }

  const currentSelectedContact = getCurrentSelectedContact()

  // Initialize form data when sidebar opens or transaction changes
  useEffect(() => {
    if (isOpen) {
      if (transaction && isEditing) {


        const dateValue = typeof transaction.date === 'string' ? transaction.date : transaction.date.toISOString()

        const newFormData = {
          kind: transaction.kind,
          fromAccountId: transaction.kind === 'income'
            ? ""
            : transaction.accountId?.toString() || "",
          toAccountId: transaction.kind === 'expense'
            ? ""
            : transaction.accountId?.toString() || "",
          expenseContactId: transaction.kind === 'expense' && transaction.transferToDetails?.contactId
            ? transaction.transferToDetails.contactId.toString()
            : "",
          incomeContactId: transaction.kind === 'income' && transaction.fromAccount?.contactId
            ? transaction.fromAccount.contactId.toString()
            : "",
          transferContactId: transaction.kind === 'transfer' && transaction.transferToDetails?.contactId
            ? transaction.transferToDetails.contactId.toString()
            : "",
          expenseContactAccountId: "",
          incomeContactAccountId: "",
          transferContactAccountId: "",
          categoryId: transaction.categoryId && transaction.categoryId !== 0
            ? transaction.categoryId.toString()
            : "",
          amount: transaction.amount?.toString() || "",
          description: transaction.description || "",
          location: transaction.location || "",
          date: dateValue.split('T')[0],
          time: new Date(dateValue).toTimeString().slice(0, 5),
          isRecurring: false /* TODO: Add recurring support */ || false
        }


        setFormData(newFormData)

        // Set selected contacts for editing
        if (transaction.transferToDetails?.contactId) {
          const contact = (contacts || []).find(c => c.id === transaction.transferToDetails?.contactId)
          setSelectedContact(contact || null)
        }
        if (transaction.fromAccount?.contactId) {
          const contact = (contacts || []).find(c => c.id === transaction.fromAccount?.contactId)
          setSelectedContact(contact || null)
        }
      } else {
        // Reset form for new transaction
        const now = new Date()
        setFormData({
          kind: "expense",
          fromAccountId: "",
          toAccountId: "",
          expenseContactId: "",
          incomeContactId: "",
          transferContactId: "",
          expenseContactAccountId: "",
          incomeContactAccountId: "",
          transferContactAccountId: "",
          categoryId: "",
          amount: "",
          description: "",
          location: "",
          date: now.toISOString().split('T')[0],
          time: now.toTimeString().slice(0, 5),
          isRecurring: false
        })
        setSelectedContact(null)
        setSelectedContactForTransfer(null)
      }
      setErrors({})
    }
  }, [isOpen, transaction, isEditing, contacts])

  // Filter categories based on transaction kind
  const availableCategories = (categories || []).filter(cat =>
    formData.kind === 'transfer' || cat.type === formData.kind
  )



  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.kind) {
      newErrors.kind = "Vui lòng chọn loại giao dịch"
    }

    // Note: All account and contact fields are now optional
    // Users can leave transferFrom, transferTo, and accounts blank

    // Only validate same account transfer if both accounts are selected
    if (formData.kind === 'transfer' &&
        formData.fromAccountId &&
        formData.toAccountId &&
        formData.fromAccountId === formData.toAccountId) {
      newErrors.toAccountId = "Không thể chuyển khoản đến cùng một tài khoản"
    }

    // Category is optional for all transaction types
    // if (formData.kind !== 'transfer' && !formData.categoryId.trim()) {
    //   newErrors.categoryId = "Vui lòng chọn danh mục"
    // }

    if (!formData.amount.trim()) {
      newErrors.amount = "Vui lòng nhập số tiền"
    } else if (parseFloat(formData.amount) <= 0) {
      newErrors.amount = "Số tiền phải lớn hơn 0"
    }

    // Description is now optional
    // if (!formData.description.trim()) {
    //   newErrors.description = "Vui lòng nhập mô tả"
    // }

    if (!formData.date.trim()) {
      newErrors.date = "Vui lòng chọn ngày"
    }

    if (!formData.time.trim()) {
      newErrors.time = "Vui lòng chọn thời gian"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await handleSubmit()
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    setLoading(true)
    try {
      const dateTime = new Date(`${formData.date}T${formData.time}`)

      // Determine accountId based on transaction type (can be undefined if not selected)
      let accountId: number | undefined
      let categoryId: number | undefined

      switch (formData.kind) {
        case 'expense':
          // For expense: fromAccount is user's account (source)
          accountId = formData.fromAccountId ? parseInt(formData.fromAccountId) : undefined
          categoryId = formData.categoryId ? parseInt(formData.categoryId) : undefined
          break
        case 'income':
          // For income: toAccount is user's account (destination)
          accountId = formData.toAccountId ? parseInt(formData.toAccountId) : undefined
          categoryId = formData.categoryId ? parseInt(formData.categoryId) : undefined
          break
        case 'transfer':
          // For transfer: fromAccount is user's account (source)
          accountId = formData.fromAccountId ? parseInt(formData.fromAccountId) : undefined
          categoryId = undefined
          break
        default:
          accountId = undefined
      }

      // Contact details are handled separately - not needed for basic transaction creation

      // Create transaction data with proper structure for API
      let transactionData: any = {
        id: transaction?.id || 0,
        userId: transaction?.userId || 1,
        amount: parseFloat(formData.amount),
        kind: formData.kind,
        description: formData.description.trim() || 'No description',
        location: formData.location.trim() || undefined,
        date: dateTime,
        note: formData.description.trim() || undefined,
        attachments: [],
        isCleared: false,
        isReconciled: false,
        isRecurring: formData.isRecurring,
        createdAt: transaction?.createdAt || new Date(),
        updatedAt: new Date()
      }

      // Add specific fields based on transaction kind
      if (formData.kind === 'transfer') {
        transactionData.fromAccountId = formData.fromAccountId || undefined
        transactionData.toAccountId = formData.toAccountId || undefined
      } else {
        transactionData.accountId = accountId || undefined
        transactionData.categoryId = categoryId || undefined
      }

      onSave(transactionData)
      handleClose()
    } catch (error) {
      console.error('Error saving transaction:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    const now = new Date()
    setFormData({
      kind: "expense",
      fromAccountId: "",
      toAccountId: "",
      expenseContactId: "",
      incomeContactId: "",
      transferContactId: "",
      expenseContactAccountId: "",
      incomeContactAccountId: "",
      transferContactAccountId: "",
      categoryId: "",
      amount: "",
      description: "",
      location: "",
      date: now.toISOString().split('T')[0],
      time: now.toTimeString().slice(0, 5),
      isRecurring: false
    })
    setErrors({})
    setSelectedContact(null)
    setSelectedContactForTransfer(null)
    setPendingContactName("")
    onClose()
  }



  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value }
      
      // If changing transaction kind, reset category
      if (field === 'kind') {
        newData.categoryId = ""
      }
      
      return newData
    })
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }



  // Handle transaction kind change
  const handleTypeChange = (value: string) => {
    const kind = value as "income" | "expense" | "transfer"
    setFormData(prev => ({
      ...prev,
      kind,
      fromAccountId: "",
      toAccountId: "",
      expenseContactId: "",
      incomeContactId: "",
      transferContactId: "",
      expenseContactAccountId: "",
      incomeContactAccountId: "",
      transferContactAccountId: "",
      categoryId: ""
      // Keep location, description, date, time as they are
    }))
    setSelectedContact(null)
    setSelectedContactForTransfer(null)
  }

  // Handle user account selection (for source accounts)
  const handleFromAccountChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      fromAccountId: value
    }))
  }

  // Handle user account selection (for destination accounts)
  const handleToAccountChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      toAccountId: value
    }))
  }

  // Handle contact selection based on transaction type
  const handleContactChange = (value: string) => {
    const contact = contacts?.find(c => c.id.toString() === value) || null

    switch (formData.kind) {
      case 'expense':
        setFormData(prev => ({
          ...prev,
          expenseContactId: value,
          expenseContactAccountId: "" // Reset account selection
        }))
        setSelectedContact(contact)
        break
      case 'income':
        setFormData(prev => ({
          ...prev,
          incomeContactId: value,
          incomeContactAccountId: "" // Reset account selection
        }))
        setSelectedContact(contact)
        break
      case 'transfer':
        setFormData(prev => ({
          ...prev,
          transferContactId: value,
          transferContactAccountId: "" // Reset account selection
        }))
        setSelectedContactForTransfer(contact)
        break
    }
  }

  // Handle contact account selection
  const handleContactAccountChange = (value: string) => {
    switch (formData.kind) {
      case 'expense':
        setFormData(prev => ({
          ...prev,
          expenseContactAccountId: value
        }))
        break
      case 'income':
        setFormData(prev => ({
          ...prev,
          incomeContactAccountId: value
        }))
        break
      case 'transfer':
        setFormData(prev => ({
          ...prev,
          transferContactAccountId: value
        }))
        break
    }
  }

  const handleContactCreated = (newContact: Contact) => {
    setShowAddContactModal(false)
    onContactCreated?.(newContact)
    setSelectedContact(newContact)

    // Set contact based on transaction kind
    switch (formData.kind) {
      case 'expense':
        handleInputChange('expenseContactId', newContact.id.toString())
        break
      case 'income':
        handleInputChange('incomeContactId', newContact.id.toString())
        break
      case 'transfer':
        handleInputChange('transferContactId', newContact.id.toString())
        break
    }

    setPendingContactName("")
  }

  const handleCloseContactModal = () => {
    setShowAddContactModal(false)
    setPendingContactName("")
  }

  return (
    <>
      <BaseSidebar
        isOpen={isOpen}
        onClose={handleClose}
        title={isEditing ? "Chỉnh sửa giao dịch" : "Thêm giao dịch mới"}
        subtitle={isEditing ? "Cập nhật thông tin giao dịch" : "Tạo giao dịch mới trong hệ thống"}
        storageKey="transactionSidebarWidth"
        config={{
          DEFAULT_WIDTH: 500,
          MIN_WIDTH: 400,
          MAX_WIDTH_PERCENTAGE: 0.7
        }}
        footerContent={
          <SidebarActionFooter
            onCancel={handleClose}
            onSave={handleSubmit}
            saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
            loading={loading}
            saveDisabled={!formData.amount}
            sidebarWidth={sidebarWidth}
          />
        }
      >
        <SidebarForm
          onSubmit={handleFormSubmit}
          sidebarWidth={sidebarWidth}
          isResizing={isResizing}
          isOpen={isOpen}
        >
          {/* Transaction Type - Always at top */}
          <SidebarFormField
            label="Loại giao dịch"
            icon={<ArrowUpDown size={14} />}
            span="full"
            sidebarWidth={sidebarWidth}
            required
            error={errors.kind}
          >
            <SearchableCombobox
              value={formData.kind}
              onChange={handleTypeChange}
              options={[
                { value: "expense", label: "Chi tiêu" },
                { value: "income", label: "Thu nhập" },
                { value: "transfer", label: "Chuyển khoản" }
              ]}
              placeholder="Chọn loại giao dịch"
              disabled={loading}
            />

          </SidebarFormField>

          {/* Source Account - For expense and transfer */}
          {(formData.kind === 'expense' || formData.kind === 'transfer') && (
            <SidebarFormField
              label="Tài khoản nguồn"
              icon={<Wallet size={14} />}
              span="full"
              sidebarWidth={sidebarWidth}
              error={errors.fromAccountId}
            >
              <SearchableCombobox
                value={formData.fromAccountId}
                onChange={handleFromAccountChange}
                options={getUserAccountOptions()}
                placeholder="Chọn tài khoản nguồn (tùy chọn)"
                disabled={loading}
              />
            </SidebarFormField>
          )}

          {/* Destination Account - For income */}
          {formData.kind === 'income' && (
            <SidebarFormField
              label="Tài khoản nhận"
              icon={<Building2 size={14} />}
              span="full"
              sidebarWidth={sidebarWidth}
              error={errors.toAccountId}
            >
              <SearchableCombobox
                value={formData.toAccountId}
                onChange={handleToAccountChange}
                options={getUserAccountOptions()}
                placeholder="Chọn tài khoản nhận (tùy chọn)"
                disabled={loading}
              />
            </SidebarFormField>
          )}

          {/* Contact Selection - For expense and income */}
          {(formData.kind === 'expense' || formData.kind === 'income') && (
            <SidebarFormField
              label={formData.kind === 'expense' ? "Người nhận" : "Người trả"}
              icon={<User size={14} />}
              span="full"
              sidebarWidth={sidebarWidth}
              error={
                formData.kind === 'expense'
                  ? errors.expenseContactId
                  : errors.incomeContactId
              }
            >
              <SearchableCombobox
                value={
                  formData.kind === 'expense'
                    ? formData.expenseContactId
                    : formData.incomeContactId
                }
                onChange={handleContactChange}
                options={getContactOptions()}
                placeholder={
                  formData.kind === 'expense'
                    ? "Chọn người nhận (tùy chọn)"
                    : "Chọn người trả (tùy chọn)"
                }
                disabled={loading}
              />
            </SidebarFormField>
          )}

          {/* Contact Account Selection - For expense and income */}
          {((formData.kind === 'expense' && formData.expenseContactId) ||
            (formData.kind === 'income' && formData.incomeContactId)) && (
            <SidebarFormField
              label="Tài khoản liên hệ"
              icon={<CreditCard size={14} />}
              span="full"
              sidebarWidth={sidebarWidth}
              error={
                formData.kind === 'expense'
                  ? errors.expenseContactAccountId
                  : errors.incomeContactAccountId
              }
            >
              <SearchableCombobox
                value={
                  formData.kind === 'expense'
                    ? formData.expenseContactAccountId
                    : formData.incomeContactAccountId
                }
                onChange={handleContactAccountChange}
                options={getContactAccountOptions(currentSelectedContact)}
                placeholder="Chọn tài khoản (tùy chọn)"
                disabled={loading}
              />
            </SidebarFormField>
          )}

          {/* Transfer Contact Selection */}
          {formData.kind === 'transfer' && (
            <SidebarFormField
              label="Chuyển đến"
              icon={<User size={14} />}
              span="full"
              sidebarWidth={sidebarWidth}
              error={errors.transferContactId}
            >
              <SearchableCombobox
                value={formData.transferContactId}
                onChange={handleContactChange}
                options={getContactOptions()}
                placeholder="Chọn người nhận (tùy chọn)"
                disabled={loading}
              />
            </SidebarFormField>
          )}

          {/* Transfer Contact Account Selection */}
          {formData.kind === 'transfer' && formData.transferContactId && (
            <SidebarFormField
              label="Tài khoản đích"
              icon={<CreditCard size={14} />}
              span="full"
              sidebarWidth={sidebarWidth}
              error={errors.transferContactAccountId}
            >
              <SearchableCombobox
                value={formData.transferContactAccountId}
                onChange={handleContactAccountChange}
                options={getContactAccountOptions(currentSelectedContact)}
                placeholder="Chọn tài khoản đích (tùy chọn)"
                disabled={loading}
              />
            </SidebarFormField>
          )}

          {/* Category - Only for non-transfer transactions - Full width */}
          {formData.kind !== 'transfer' && (
            <SidebarFormField
              label="Danh mục"
              icon={<Tag size={14} />}
              span="full"
              sidebarWidth={sidebarWidth}
              error={errors.categoryId}
            >
              <SearchableCombobox
                value={formData.categoryId}
                onChange={(value) => handleInputChange('categoryId', value)}
                options={[
                  { value: "", label: "Chọn danh mục" },
                  ...availableCategories.map(category => ({
                    value: category.id.toString(),
                    label: category.name
                  }))
                ]}
                placeholder="Chọn danh mục (tùy chọn)"
                disabled={loading}
              />
            </SidebarFormField>
          )}



          {/* Date - Full width */}
          <SidebarInputField
            label="Ngày"
            value={formData.date}
            onChange={(value) => handleInputChange('date', value)}
            type="date"
            required
            error={errors.date}
            icon={<Calendar size={14} />}
            disabled={loading}
            span="full"
            sidebarWidth={sidebarWidth}
          />

          {/* Time - Full width */}
          <SidebarInputField
            label="Thời gian"
            value={formData.time}
            onChange={(value) => handleInputChange('time', value)}
            type="time"
            required
            error={errors.time}
            icon={<Clock size={14} />}
            disabled={loading}
            span="full"
            sidebarWidth={sidebarWidth}
          />

          {/* Row 2: Amount - Full width */}
          <SidebarInputField
            label="Số tiền"
            value={formData.amount}
            onChange={(value) => handleInputChange('amount', value)}
            placeholder="0"
            type="number"
            required
            error={errors.amount}
            icon={<DollarSign size={14} />}
            disabled={loading}
            span="full"
            sidebarWidth={sidebarWidth}
          />

          {/* Row 3: Description - Full width */}
          <SidebarInputField
            label="Mô tả"
            value={formData.description}
            onChange={(value) => handleInputChange('description', value)}
            placeholder="Nhập mô tả giao dịch (tùy chọn)"
            error={errors.description}
            icon={<FileText size={14} />}
            disabled={loading}
            span="full"
            sidebarWidth={sidebarWidth}
          />

          {/* Location - Full width */}
          <SidebarInputField
            label="Địa điểm"
            value={formData.location}
            onChange={(value) => handleInputChange('location', value)}
            placeholder="Nhập địa điểm (tùy chọn)"
            error={errors.location}
            icon={<MapPin size={14} />}
            disabled={loading}
            span="full"
            sidebarWidth={sidebarWidth}
          />


        </SidebarForm>
      </BaseSidebar>

      {/* Contact Dialogs */}
      {showNewContactDialog && (
        <NewContactDialog
          isOpen={showNewContactDialog}
          onClose={() => setShowNewContactDialog(false)}
          onConfirm={(permanent: boolean) => {
            setShowNewContactDialog(false)
            if (permanent) {
              setShowAddContactModal(true)
            }
          }}
          contactName={pendingContactName}
        />
      )}

      {showAddContactModal && (
        <AddEditContactModal
          isOpen={showAddContactModal}
          onClose={handleCloseContactModal}
          onSave={handleContactCreated}
          contact={null}
          isEditing={false}
        />
      )}
    </>
  )
}
