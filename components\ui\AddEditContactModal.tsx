"use client"

import React, { useState, useEffect } from "react"
import { Modal } from "./Modal"
import { <PERSON>ton } from "./button"
import { Plus, Trash2, Star } from "lucide-react"
import { BasicContactInfo } from "./contact-modal/BasicContactInfo"
import { BankAccountsManager } from "./contact-modal/BankAccountsManager"
import { Contact } from "@/lib/generated/prisma"
import { Account } from "@/lib/generated/prisma"

interface AddEditContactModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (contact: Contact) => void
  contact?: Contact | null
  isEditing: boolean
}

export function AddEditContactModal({
  isOpen,
  onClose,
  onSave,
  contact,
  isEditing
}: AddEditContactModalProps) {
  const [contactData, setContactData] = useState<Contact>({
    id: '0',
    userId: '1',
    name: "",
    phone: "",
    email: "",
    accounts: [],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  })

  // Reset form when modal opens/closes or contact changes
  useEffect(() => {
    if (isOpen && contact && isEditing) {
      setContactData({
        ...contact,
        accounts: [...(contact.accounts || [])]
      })
    } else if (isOpen) {
      // Initialize with default values for new contact
      setContactData({
        id: Date.now().toString(),
        userId: '1',
        name: "",
        phone: "",
        email: "",
        accounts: [],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
  }, [isOpen, contact, isEditing])

  const handleBasicInfoChange = (field: string, value: string) => {
    setContactData(prev => ({ ...prev, [field]: value }))
  }

  const handleAddAccount = (account: Account) => {
    setContactData(prev => ({
      ...prev,
      accounts: [...(prev.accounts || []), account]
    }))
  }

  const handleUpdateAccount = (accountId: number, field: keyof Account, value: any) => {
    setContactData(prev => ({
      ...prev,
      accounts: prev.accounts?.map(acc => 
        acc.id === accountId ? { ...acc, [field]: value } : acc
      ) || []
    }))
  }

  const handleDeleteAccount = (accountId: number) => {
    setContactData(prev => ({
      ...prev,
      accounts: prev.accounts?.filter(acc => acc.id !== accountId) || []
    }))
  }

  const handleSave = () => {
    // Save changes
    onSave({
      ...contactData,
      updatedAt: new Date()
    })
    onClose()
  }

  const isValid = contactData.name.trim().length > 0

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={isEditing ? "Chỉnh sửa liên hệ" : "Thêm liên hệ mới"}>
      <div className="space-y-6">
        <BasicContactInfo
          name={contactData.name}
          phone={contactData.phone || ""}
          email={contactData.email || ""}
          description={contactData.description || ""}
          onChange={handleBasicInfoChange}
        />

        <BankAccountsManager
          accounts={contactData.accounts || []}
          onAdd={handleAddAccount}
          onUpdate={handleUpdateAccount}
          onDelete={handleDeleteAccount}
        />

        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={onClose}>Hủy</Button>
          <Button onClick={handleSave} disabled={!isValid}>
            {isEditing ? "Cập nhật" : "Tạo mới"}
          </Button>
        </div>
      </div>
    </Modal>
  )
}
