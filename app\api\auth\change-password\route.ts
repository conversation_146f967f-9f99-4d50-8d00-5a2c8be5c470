import { NextRequest } from 'next/server'
import { z } from 'zod'
import {
  with<PERSON>rror<PERSON><PERSON><PERSON>,
  successResponse,
  validateRequest
} from '@/lib/api/utils'
import { AuthService } from '@/services/server'

// ==================== VALIDATION SCHEMAS ====================

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// ==================== POST /api/auth/change-password ====================

export const POST = withErrorHandler(async (request: NextRequest) => {
  const { userId } = await AuthService.verifyRequest(request)

  const data = await validateRequest(request, changePasswordSchema)

  // Call AuthService to change password
  await AuthService.changePassword(userId, {
    currentPassword: data.currentPassword,
    newPassword: data.newPassword
  })

  return successResponse({
    message: 'Password changed successfully'
  })
})

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
