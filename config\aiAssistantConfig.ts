export const AI_ASSISTANT_CONFIG = {
  // Width constraints
  MIN_WIDTH: 320, // Minimum width in pixels
  DEFAULT_WIDTH: 384, // Default width in pixels (24rem = 384px)
  MAX_WIDTH_PERCENTAGE: 40, // Maximum width as percentage of viewport width
  
  // Responsive breakpoints
  MO<PERSON>LE_BREAKPOINT: 1024, // lg breakpoint in pixels
  
  // Animation settings
  TRANSITION_DURATION: 300, // Transition duration in milliseconds
  TRANSITION_EASING: 'ease-in-out',
  
  // Resize handle settings
  RESIZE_HANDLE_WIDTH: 4, // Width of resize handle in pixels
  RESIZE_HANDLE_HOVER_WIDTH: 8, // Width when hovering
  
  // Storage key for persisting width
  STORAGE_KEY: 'ai-assistant-sidebar-width',
  
  // Z-index layers
  Z_INDEX: {
    SIDEBAR: 60,
    OVERLAY: 55,
    BUTTON: 50,
    RESIZE_HANDLE: 61
  }
} as const

export type AIAssistantConfig = typeof AI_ASSISTANT_CONFIG

// Helper functions
export const getMaxWidth = (viewportWidth: number): number => {
  return Math.floor((viewportWidth * AI_ASSISTANT_CONFIG.MAX_WIDTH_PERCENTAGE) / 100)
}

export const clampWidth = (width: number, viewportWidth: number): number => {
  const maxWidth = getMaxWidth(viewportWidth)
  return Math.max(
    AI_ASSISTANT_CONFIG.MIN_WIDTH,
    Math.min(width, maxWidth)
  )
}

export const isMobile = (): boolean => {
  if (typeof window === 'undefined') return false
  return window.innerWidth < AI_ASSISTANT_CONFIG.MOBILE_BREAKPOINT
}

export const saveWidth = (width: number): void => {
  if (typeof window === 'undefined') return
  localStorage.setItem(AI_ASSISTANT_CONFIG.STORAGE_KEY, width.toString())
}

export const loadWidth = (): number => {
  if (typeof window === 'undefined') return AI_ASSISTANT_CONFIG.DEFAULT_WIDTH
  
  const saved = localStorage.getItem(AI_ASSISTANT_CONFIG.STORAGE_KEY)
  if (!saved) return AI_ASSISTANT_CONFIG.DEFAULT_WIDTH
  
  const width = parseInt(saved, 10)
  if (isNaN(width)) return AI_ASSISTANT_CONFIG.DEFAULT_WIDTH
  
  // Validate against current viewport
  return clampWidth(width, window.innerWidth)
}
