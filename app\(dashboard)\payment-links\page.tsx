"use client"

import React, { useState, useEffect } from "react"
import { Plus, Search, Filter, Link } from "lucide-react"
import { PageHeader } from "@/components/layout"
import { PaymentAppLinkCard } from "@/components/ui/PaymentAppLinkCard"
import { FilterSidebar } from "@/components/ui/FilterSidebar"
import { Pagination } from "@/components/ui/pagination"
import { AccountPaymentAppWithRelations } from "@/types/entities"
import { Account } from "@/lib/generated/prisma"
import { AccountPaymentAppApiService, AccountApiService } from "@/services/client"
import { useToast } from "@/hooks/use-toast"

interface PaymentAppLinkFilters {
  search: string
  accountId: string
  paymentAppId: string
  isActive: string
}

export default function PaymentLinksPage() {
  // State management
  const [paymentLinks, setPaymentLinks] = useState<AccountPaymentAppWithRelations[]>([])
  const [bankAccounts, setBankAccounts] = useState<Account[]>([])
  const [paymentApps, setPaymentApps] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(25)

  // Sidebar states
  const [filterSidebarOpen, setFilterSidebarOpen] = useState(false)

  // Filter states
  const [filters, setFilters] = useState<PaymentAppLinkFilters>({
    search: "",
    accountId: "",
    paymentAppId: "",
    isActive: ""
  })

  const { toast } = useToast()

  // Load data
  useEffect(() => {
    loadPaymentLinks()
    loadAccounts()
  }, [currentPage, itemsPerPage, filters])

  const loadPaymentLinks = async () => {
    try {
      setLoading(true)
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        accountId: filters.accountId || undefined,
        paymentAppId: filters.paymentAppId || undefined,
        isActive: filters.isActive ? filters.isActive === 'true' : undefined
      }

      const response = await AccountPaymentAppApiService.getAccountPaymentApps(params)
      setPaymentLinks(response.data)
      setTotalPages(response.totalPages)
      setTotalItems(response.total)
    } catch (error) {
      console.error('Error loading payment links:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách liên kết thanh toán",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const loadAccounts = async () => {
    try {
      const response = await AccountApiService.getAccounts({ limit: 100 })
      
      // Separate bank accounts and payment apps
      const bankAccounts = response.data.filter(account => 
        account.accountType?.isBankAccount
      )
      const paymentApps = response.data.filter(account => 
        account.accountType?.isPaymentApp
      )
      
      setBankAccounts(bankAccounts)
      setPaymentApps(paymentApps)
    } catch (error) {
      console.error('Error loading accounts:', error)
    }
  }

  // Event handlers
  const handleUnlinkPaymentApp = async (link: AccountPaymentAppWithRelations) => {
    if (!confirm(`Bạn có chắc chắn muốn hủy liên kết giữa "${link.account?.name}" và "${link.paymentApp?.name}"?`)) {
      return
    }

    try {
      await AccountPaymentAppApiService.deleteAccountPaymentApp(link.id)
      toast({
        title: "Thành công",
        description: "Đã hủy liên kết thanh toán"
      })
      loadPaymentLinks()
    } catch (error) {
      console.error('Error unlinking payment app:', error)
      toast({
        title: "Lỗi",
        description: "Không thể hủy liên kết thanh toán",
        variant: "destructive"
      })
    }
  }

  const handleToggleStatus = async (link: AccountPaymentAppWithRelations) => {
    try {
      const response = await AccountPaymentAppApiService.updateAccountPaymentApp(link.id, {
        isActive: !link.isActive
      })
      if (response.success) {
        toast({
          title: "Thành công",
          description: `Đã ${link.isActive ? 'tạm dừng' : 'kích hoạt'} liên kết`
        })
        loadPaymentLinks()
      } else {
        toast({
          title: "Lỗi",
          description: response.error || "Không thể thay đổi trạng thái liên kết",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error toggling link status:', error)
      toast({
        title: "Lỗi",
        description: "Không thể thay đổi trạng thái liên kết",
        variant: "destructive"
      })
    }
  }

  const handleCreateLink = () => {
    // TODO: Open create link sidebar/modal
    toast({
      title: "Thông báo",
      description: "Tính năng tạo liên kết mới sẽ được bổ sung",
      variant: "default"
    })
  }

  const handleFilterChange = (newFilters: Partial<PaymentAppLinkFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setCurrentPage(1)
  }

  const handleClearFilters = () => {
    setFilters({
      search: "",
      accountId: "",
      paymentAppId: "",
      isActive: ""
    })
    setCurrentPage(1)
  }

  // Filter options
  const bankAccountOptions = bankAccounts.map(account => ({
    value: account.id,
    label: account.name
  }))

  const paymentAppOptions = paymentApps.map(app => ({
    value: app.id,
    label: app.name
  }))

  const statusOptions = [
    { value: "true", label: "Đang hoạt động" },
    { value: "false", label: "Không hoạt động" }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <PageHeader
        title="Liên kết thanh toán"
        subtitle={`Quản lý ${totalItems} liên kết thanh toán`}
        icon={<Link size={24} />}
        actions={
          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm liên kết..."
                value={filters.search}
                onChange={(e) => handleFilterChange({ search: e.target.value })}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Button */}
            <button
              onClick={() => setFilterSidebarOpen(true)}
              className="flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Filter size={16} className="mr-2" />
              Lọc
            </button>

            {/* Add Button */}
            <button
              onClick={handleCreateLink}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus size={16} className="mr-2" />
              Tạo liên kết
            </button>
          </div>
        }
      />

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="text-center py-12">
            <div className="text-gray-500">Đang tải...</div>
          </div>
        ) : paymentLinks.length === 0 ? (
          <div className="text-center py-12">
            <Link size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Chưa có liên kết thanh toán nào
            </h3>
            <p className="text-gray-500 mb-6">
              Liên kết tài khoản ngân hàng với ứng dụng thanh toán để quản lý dễ dàng hơn
            </p>
            <button
              onClick={handleCreateLink}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus size={16} className="mr-2" />
              Tạo liên kết đầu tiên
            </button>
          </div>
        ) : (
          <>
            {/* Payment Links Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {paymentLinks.map((link) => (
                <PaymentAppLinkCard
                  key={link.id}
                  link={link}
                  onUnlink={handleUnlinkPaymentApp}
                  onToggleStatus={handleToggleStatus}
                  viewMode="bank-to-app"
                />
              ))}
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={setCurrentPage}
              onItemsPerPageChange={setItemsPerPage}
            />
          </>
        )}
      </div>

      {/* Filter Sidebar */}
      <FilterSidebar
        isOpen={filterSidebarOpen}
        onClose={() => setFilterSidebarOpen(false)}
        onApply={handleFilterChange}
        onClear={handleClearFilters}
        title="Lọc liên kết thanh toán"
        filters={[
          {
            key: 'accountId',
            label: 'Tài khoản ngân hàng',
            type: 'select',
            value: filters.accountId,
            options: bankAccountOptions
          },
          {
            key: 'paymentAppId',
            label: 'Ứng dụng thanh toán',
            type: 'select',
            value: filters.paymentAppId,
            options: paymentAppOptions
          },
          {
            key: 'isActive',
            label: 'Trạng thái',
            type: 'select',
            value: filters.isActive,
            options: statusOptions
          }
        ]}
      />
    </div>
  )
}
