/**
 * GraphQL Connection Types
 * 
 * TypeScript interfaces for GraphQL Relay-style connections
 */

// ==================== CORE CONNECTION TYPES ====================

export interface PageInfo {
  hasNextPage: boolean
  hasPreviousPage: boolean
  startCursor?: string | null
  endCursor?: string | null
}

export interface Edge<T> {
  node: T
  cursor: string
}

export interface Connection<T> {
  edges: Edge<T>[]
  pageInfo: PageInfo
  totalCount: number
}

// ==================== ENTITY-SPECIFIC CONNECTION TYPES ====================

import type {
  User,
  Account,
  Transaction,
  Category,
  Contact,
  CategoryGoal,
  RecurringTransaction,
  AccountType,
  ContactType
} from '@/lib/generated/prisma'
import { CategoryWithGoal } from '@/lib/graphql/types'

export interface UserEdge extends Edge<User> { }
export interface UserConnection extends Connection<User> { }

export interface AccountEdge extends Edge<Account> { }
export interface AccountConnection extends Connection<Account> { }

export interface TransactionEdge extends Edge<Transaction> { }
export interface TransactionConnection extends Connection<Transaction> { }

export interface CategoryEdge extends Edge<Category> { }
export interface CategoryConnection extends Connection<Category> { }

export interface ContactEdge extends Edge<Contact> { }
export interface ContactConnection extends Connection<Contact> { }

export interface CategoryGoalEdge extends Edge<CategoryGoal> { }
export interface CategoryGoalConnection extends Connection<CategoryGoal> { }

export interface RecurringTransactionEdge extends Edge<RecurringTransaction> { }
export interface RecurringTransactionConnection extends Connection<RecurringTransaction> { }

export interface AccountTypeEdge extends Edge<AccountType> { }
export interface AccountTypeConnection extends Connection<AccountType> { }

export interface ContactTypeEdge extends Edge<ContactType> { }
export interface ContactTypeConnection extends Connection<ContactType> { }

// ==================== CONNECTION RESPONSE TYPES ====================

export interface ConnectionResponse<T> {
  success: boolean
  data: Connection<T>
  message?: string
  error?: string
  timestamp: string
}

// Specific response types for each entity
export type UserConnectionResponse = ConnectionResponse<User>
export type AccountConnectionResponse = ConnectionResponse<Account>
export type TransactionConnectionResponse = ConnectionResponse<Transaction>
export type CategoryConnectionResponse = ConnectionResponse<Category>
export type CategoryWithGoalConnectionResponse = ConnectionResponse<CategoryWithGoal>
export type ContactConnectionResponse = ConnectionResponse<Contact>
export type CategoryGoalConnectionResponse = ConnectionResponse<CategoryGoal>
export type RecurringTransactionConnectionResponse = ConnectionResponse<RecurringTransaction>
export type AccountTypeConnectionResponse = ConnectionResponse<AccountType>
export type ContactTypeConnectionResponse = ConnectionResponse<ContactType>

// ==================== CONNECTION QUERY PARAMETERS ====================

export interface ConnectionQueryParams {
  first?: number
  after?: string
  last?: number
  before?: string
}

export interface UserConnectionParams extends ConnectionQueryParams {
  search?: string
  role?: string
  isActive?: boolean
}

export interface AccountConnectionParams extends ConnectionQueryParams {
  search?: string
  accountTypeId?: string
  isActive?: boolean
  minBalance?: number
  maxBalance?: number
}

export interface TransactionConnectionParams extends ConnectionQueryParams {
  search?: string
  accountId?: string
  categoryId?: string
  contactId?: string
  kind?: string
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
}

export interface CategoryConnectionParams extends ConnectionQueryParams {
  search?: string
  type?: string
  parentId?: string
  isActive?: boolean
}

export interface ContactConnectionParams extends ConnectionQueryParams {
  search?: string
  contactTypeId?: string
  isActive?: boolean
}

export interface CategoryGoalConnectionParams extends ConnectionQueryParams {
  search?: string
  categoryId?: string
  period?: string
  isActive?: boolean
}

export interface RecurringTransactionConnectionParams extends ConnectionQueryParams {
  search?: string
  accountId?: string
  categoryId?: string
  frequency?: string
  isActive?: boolean
}

export interface AccountTypeConnectionParams extends ConnectionQueryParams {
  search?: string
  category?: string
  isActive?: boolean
}

export interface ContactTypeConnectionParams extends ConnectionQueryParams {
  search?: string
  isActive?: boolean
}

// ==================== UTILITY TYPES ====================

// Helper type to extract node type from connection
export type NodeType<T> = T extends Connection<infer U> ? U : never

// Helper type to extract edge type from connection
export type EdgeType<T> = T extends Connection<infer U> ? Edge<U> : never

// Helper type for connection variables
export interface ConnectionVariables extends ConnectionQueryParams {
  [key: string]: any
}

// ==================== PAGINATION HELPERS ====================

export interface PaginationInfo {
  hasNextPage: boolean
  hasPreviousPage: boolean
  totalCount: number
  currentCount: number
  startCursor?: string | null
  endCursor?: string | null
}

export function extractPaginationInfo<T>(connection: Connection<T>): PaginationInfo {
  return {
    hasNextPage: connection.pageInfo.hasNextPage,
    hasPreviousPage: connection.pageInfo.hasPreviousPage,
    totalCount: connection.totalCount,
    currentCount: connection.edges.length,
    startCursor: connection.pageInfo.startCursor,
    endCursor: connection.pageInfo.endCursor
  }
}

export function extractNodes<T>(connection: Connection<T>): T[] {
  return connection.edges.map(edge => edge.node)
}

export function extractEdges<T>(connection: Connection<T>): Edge<T>[] {
  return connection.edges
}

// ==================== CONNECTION BUILDERS ====================

export function createEmptyConnection<T>(): Connection<T> {
  return {
    edges: [],
    pageInfo: {
      hasNextPage: false,
      hasPreviousPage: false,
      startCursor: null,
      endCursor: null
    },
    totalCount: 0
  }
}

export function createConnectionResponse<T>(
  connection: Connection<T>,
  success: boolean = true,
  message?: string,
  error?: string
): ConnectionResponse<T> {
  return {
    success,
    data: connection,
    message,
    error,
    timestamp: new Date().toISOString()
  }
}

// ==================== TYPE GUARDS ====================

export function isConnection<T>(obj: any): obj is Connection<T> {
  return (
    obj &&
    typeof obj === 'object' &&
    Array.isArray(obj.edges) &&
    obj.pageInfo &&
    typeof obj.pageInfo === 'object' &&
    typeof obj.totalCount === 'number'
  )
}

export function isConnectionResponse<T>(obj: any): obj is ConnectionResponse<T> {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.success === 'boolean' &&
    obj.data &&
    isConnection(obj.data)
  )
}
