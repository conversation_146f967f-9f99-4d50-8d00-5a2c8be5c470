"use client"

import React, { useState, useMemo, useRef } from "react"
import { Search, Filter, SortAsc, SortDesc, ChevronDown, ChevronUp, X } from "lucide-react"
import { DataDisplayCard } from "./DataDisplayCard"
import { TransactionPagination, useTransactionPagination } from "./TransactionPagination"
import { PortalDropdown } from "./PortalDropdown"
import { Button } from "./button"
import { useResponsiveGrid } from "@/hooks/useResponsiveGrid"

interface GenericListProps<T> {
  // Data
  data: T[]
  
  // Display configuration
  renderCard?: (item: T, index: number) => React.ReactNode
  renderRow?: (item: T, index: number) => React.ReactNode
  displayMode?: "cards" | "table" | "list"
  
  // Search & Filter
  searchable?: boolean
  searchPlaceholder?: string
  searchFields?: (keyof T)[]
  onSearch?: (searchTerm: string, filteredData: T[]) => void
  
  // Sorting
  sortable?: boolean
  sortFields?: Array<{
    key: keyof T
    label: string
    sortFn?: (a: T, b: T) => number
  }>
  defaultSort?: {
    field: keyof T
    direction: "asc" | "desc"
  }
  
  // Pagination
  paginated?: boolean
  defaultItemsPerPage?: number
  itemsPerPageOptions?: number[]
  
  // Actions
  onAdd?: () => void
  onEdit?: (item: T) => void
  onView?: (item: T) => void
  
  // Custom filters
  customFilters?: React.ReactNode
  onFilterChange?: (filters: any) => void
  
  // Layout
  className?: string
  emptyMessage?: string
  loadingMessage?: string
  loading?: boolean
  
  // Grid configuration for cards
  gridCols?: {
    default: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    "2xl"?: number
  }

  // Sidebar state for responsive adjustments
  sidebarOpen?: boolean
  sidebarWidth?: number
}

export function GenericList<T extends { id: string | number }>({
  data,
  renderCard,
  renderRow,
  displayMode = "cards",
  searchable = true,
  searchPlaceholder = "Tìm kiếm...",
  searchFields = [],
  onSearch,
  sortable = true,
  sortFields = [],
  defaultSort,
  paginated = true,
  defaultItemsPerPage = 25,
  itemsPerPageOptions = [10, 25, 50, 100],
  onAdd,
  onEdit,
  onView,
  customFilters,
  onFilterChange,
  className = "",
  emptyMessage = "Không có dữ liệu",
  loadingMessage = "Đang tải...",
  loading = false,
  gridCols = { default: 1, sm: 2, lg: 3, xl: 4 },
  sidebarOpen = false,
  sidebarWidth = 400
}: GenericListProps<T>) {
  // State
  const [searchTerm, setSearchTerm] = useState("")
  const [sortField, setSortField] = useState<keyof T | null>(defaultSort?.field || null)
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">(defaultSort?.direction || "asc")
  const [showSortOptions, setShowSortOptions] = useState(false)
  const sortButtonRef = useRef<HTMLButtonElement>(null)
  
  // Filtered and sorted data
  const processedData = useMemo(() => {
    let filtered = [...data]
    
    // Apply search filter
    if (searchTerm && searchFields.length > 0) {
      filtered = filtered.filter(item => 
        searchFields.some(field => {
          const value = item[field]
          if (typeof value === 'string') {
            return value.toLowerCase().includes(searchTerm.toLowerCase())
          }
          if (typeof value === 'number') {
            return value.toString().includes(searchTerm)
          }
          return false
        })
      )
    }
    
    // Apply sorting
    if (sortField && sortFields.length > 0) {
      const sortConfig = sortFields.find(f => f.key === sortField)
      if (sortConfig) {
        filtered.sort((a, b) => {
          if (sortConfig.sortFn) {
            return sortDirection === "asc" ? sortConfig.sortFn(a, b) : sortConfig.sortFn(b, a)
          }
          
          const aVal = a[sortField]
          const bVal = b[sortField]
          
          if (aVal < bVal) return sortDirection === "asc" ? -1 : 1
          if (aVal > bVal) return sortDirection === "asc" ? 1 : -1
          return 0
        })
      }
    }
    
    return filtered
  }, [data, searchTerm, searchFields, sortField, sortDirection, sortFields])
  
  // Pagination
  const {
    currentPage,
    itemsPerPage,
    totalPages,
    startIndex,
    endIndex,
    handlePageChange,
    handleItemsPerPageChange
  } = useTransactionPagination(processedData.length, defaultItemsPerPage)
  
  const paginatedData = paginated 
    ? processedData.slice(startIndex, endIndex)
    : processedData
  
  // Handlers
  const handleSort = (field: keyof T) => {
    if (sortField === field) {
      setSortDirection(prev => prev === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
    setShowSortOptions(false)
  }
  
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    onSearch?.(value, processedData)
  }
  
  const clearSearch = () => {
    setSearchTerm("")
    onSearch?.("", data)
  }
  
  // Responsive grid with sidebar awareness
  const { gridClasses } = useResponsiveGrid({
    baseGrid: gridCols,
    sidebarOpen,
    sidebarWidth,
    minCardWidth: 280
  })
  
  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="text-gray-500 dark:text-gray-400">{loadingMessage}</div>
        </div>
      </div>
    )
  }
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
        <div className="flex items-center gap-3 flex-1 w-full sm:w-auto">
          {/* Search */}
          {searchable && (
            <div className="relative flex-1 sm:flex-initial sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full pl-10 pr-10 py-2 h-8 text-xs border border-gray-200 dark:border-gray-600 rounded-lg bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200"
              />
              {searchTerm && (
                <button
                  onClick={clearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X size={14} />
                </button>
              )}
            </div>
          )}
          
          {/* Custom Filters */}
          {customFilters}
        </div>
        
        <div className="flex items-center gap-2">
          {/* Sort Controls */}
          {sortable && sortFields.length > 0 && (
            <div className="relative">
              <button
                ref={sortButtonRef}
                onClick={() => setShowSortOptions(!showSortOptions)}
                className="flex items-center gap-1 px-3 py-1 h-8 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 bg-white/80 dark:bg-gray-700/80 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg transition-all duration-200"
              >
                {sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />}
                <span className="whitespace-nowrap">Sắp xếp</span>
                {showSortOptions ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
              </button>
              
              <PortalDropdown
                isOpen={showSortOptions}
                onClose={() => setShowSortOptions(false)}
                triggerRef={sortButtonRef}
              >
                {sortFields.map((field) => (
                  <button
                    key={String(field.key)}
                    onClick={() => handleSort(field.key)}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                      sortField === field.key
                        ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
                        : "text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      {field.label}
                      {sortField === field.key && (
                        sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                      )}
                    </div>
                  </button>
                ))}
              </PortalDropdown>
            </div>
          )}
          
          {/* Add Button */}
          {onAdd && (
            <Button onClick={onAdd} size="sm" className="h-8">
              Thêm mới
            </Button>
          )}
        </div>
      </div>
      
      {/* Content */}
      {paginatedData.length === 0 ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-gray-500 dark:text-gray-400">{emptyMessage}</div>
        </div>
      ) : (
        <>
          {/* Data Display */}
          {displayMode === "cards" && (
            <div className={`${gridClasses} auto-rows-fr`}>
              {paginatedData.map((item, index) => (
                <div key={String(item.id)} className="h-full">
                  {renderCard ? renderCard(item, index) : (
                    <DataDisplayCard
                      id={item.id}
                      title={String(item.id)}
                      onEdit={onEdit ? () => onEdit(item) : undefined}

                      onView={onView ? () => onView(item) : undefined}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
          
          {displayMode === "list" && (
            <div className="space-y-2">
              {paginatedData.map((item, index) => (
                <div key={String(item.id)}>
                  {renderRow ? renderRow(item, index) : (
                    <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                      {String(item.id)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
          
          {/* Pagination */}
          {paginated && totalPages > 1 && (
            <TransactionPagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={processedData.length}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              itemsPerPageOptions={itemsPerPageOptions}
            />
          )}
        </>
      )}
    </div>
  )
}
