"use client"

import React, { useState, useRef, useEffect } from "react"
import { Check, ChevronDown, Search, X } from "lucide-react"

export interface MultiSelectOption {
  value: string
  label: string
  disabled?: boolean
}

interface MultiSelectDropdownProps {
  options: MultiSelectOption[]
  selectedValues: string[]
  onChange: (selectedValues: string[]) => void
  placeholder?: string
  searchPlaceholder?: string
  className?: string
  disabled?: boolean
  maxHeight?: string
  showSearch?: boolean
  allowSelectAll?: boolean
  selectAllLabel?: string
}

export function MultiSelectDropdown({
  options,
  selectedValues,
  onChange,
  placeholder = "Chọn...",
  searchPlaceholder = "Tìm kiếm...",
  className = "",
  disabled = false,
  maxHeight = "200px",
  showSearch = true,
  allowSelectAll = true,
  selectAllLabel = "Chọn tất cả"
}: MultiSelectDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm("")
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Handle option selection
  const handleOptionToggle = (value: string) => {
    if (selectedValues.includes(value)) {
      onChange(selectedValues.filter(v => v !== value))
    } else {
      onChange([...selectedValues, value])
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    const availableValues = filteredOptions
      .filter(option => !option.disabled)
      .map(option => option.value)
    
    if (selectedValues.length === availableValues.length) {
      // Deselect all filtered options
      onChange(selectedValues.filter(v => !availableValues.includes(v)))
    } else {
      // Select all filtered options
      const newSelected = [...new Set([...selectedValues, ...availableValues])]
      onChange(newSelected)
    }
  }

  // Get display text
  const getDisplayText = () => {
    if (selectedValues.length === 0) return placeholder
    if (selectedValues.length === 1) {
      const option = options.find(opt => opt.value === selectedValues[0])
      return option?.label || selectedValues[0]
    }
    return `${selectedValues.length} mục đã chọn`
  }

  // Check if all filtered options are selected
  const allFilteredSelected = filteredOptions
    .filter(option => !option.disabled)
    .every(option => selectedValues.includes(option.value))

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full h-8 px-3 bg-white/80 dark:bg-gray-700/80 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 dark:text-gray-100 text-xs transition-all duration-200 flex items-center justify-between ${
          disabled ? "opacity-50 cursor-not-allowed" : "hover:border-gray-300 dark:hover:border-gray-500"
        }`}
      >
        <span className="truncate text-left">
          {getDisplayText()}
        </span>
        <div className="flex items-center gap-1">
          {selectedValues.length > 0 && (
            <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-1.5 py-0.5 rounded text-xs font-medium">
              {selectedValues.length}
            </span>
          )}
          <ChevronDown 
            size={14} 
            className={`transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`} 
          />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
          {/* Search Input */}
          {showSearch && (
            <div className="p-2 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full h-8 pl-9 pr-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 dark:text-gray-100 text-xs"
                />
              </div>
            </div>
          )}

          {/* Options List */}
          <div className="max-h-48 overflow-y-auto" style={{ maxHeight }}>
            {/* Select All Option */}
            {allowSelectAll && filteredOptions.length > 0 && (
              <div
                onClick={handleSelectAll}
                className="flex items-center gap-2 px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-700"
              >
                <div className={`w-4 h-4 border border-gray-300 dark:border-gray-600 rounded flex items-center justify-center ${
                  allFilteredSelected ? "bg-blue-500 border-blue-500" : ""
                }`}>
                  {allFilteredSelected && <Check size={12} className="text-white" />}
                </div>
                <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                  {selectAllLabel}
                </span>
              </div>
            )}

            {/* Individual Options */}
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <div
                  key={option.value}
                  onClick={() => !option.disabled && handleOptionToggle(option.value)}
                  className={`flex items-center gap-2 px-3 py-2 transition-colors ${
                    option.disabled
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                  }`}
                >
                  <div className={`w-4 h-4 border border-gray-300 dark:border-gray-600 rounded flex items-center justify-center ${
                    selectedValues.includes(option.value) ? "bg-blue-500 border-blue-500" : ""
                  }`}>
                    {selectedValues.includes(option.value) && (
                      <Check size={12} className="text-white" />
                    )}
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 truncate">
                    {option.label}
                  </span>
                </div>
              ))
            ) : (
              <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                Không tìm thấy kết quả
              </div>
            )}
          </div>

          {/* Clear All Button */}
          {selectedValues.length > 0 && (
            <div className="p-2 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => onChange([])}
                className="w-full flex items-center justify-center gap-1 px-3 py-1.5 text-xs text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
              >
                <X size={12} />
                Xóa tất cả
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
