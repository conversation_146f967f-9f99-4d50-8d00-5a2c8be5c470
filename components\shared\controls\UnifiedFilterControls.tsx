"use client"

import React, { useRef, useState } from 'react'
import { Search, SortAsc, SortDesc, ChevronUp, ChevronDown } from 'lucide-react'
import { PortalDropdown } from '../../ui/PortalDropdown'

// ==================== TYPES ====================

export interface FilterTab {
  value: string
  label: string
  count: number
}

export interface SortOption {
  value: string
  label: string
}

export interface UnifiedFilterControlsProps {
  // Filter tabs (e.g., "All", "Income", "Expense")
  filterTabs?: FilterTab[]
  activeFilter?: string
  onFilterChange?: (filter: string) => void

  // Status/Category dropdown
  statusOptions?: Array<{ value: string; label: string }>
  activeStatus?: string
  onStatusChange?: (status: string) => void
  statusLabel?: string

  // Search
  searchValue?: string
  onSearchChange?: (value: string) => void
  searchPlaceholder?: string

  // Sort
  sortOptions?: SortOption[]
  activeSortField?: string
  sortDirection?: 'asc' | 'desc'
  onSortChange?: (field: string) => void

  // Item count
  itemCount?: number
  itemLabel?: string

  // Additional custom controls
  customControls?: React.ReactNode

  // Styling
  className?: string
}

// ==================== COMPONENT ====================

export function UnifiedFilterControls({
  filterTabs = [],
  activeFilter = 'all',
  onFilterChange,
  statusOptions = [],
  activeStatus = 'all',
  onStatusChange,
  statusLabel = 'Trạng thái',
  searchValue = '',
  onSearchChange,
  searchPlaceholder = 'Tìm kiếm...',
  sortOptions = [],
  activeSortField = '',
  sortDirection = 'asc',
  onSortChange,
  itemCount = 0,
  itemLabel = 'mục',
  customControls,
  className = ''
}: UnifiedFilterControlsProps) {
  const [showSortOptions, setShowSortOptions] = useState(false)
  const sortButtonRef = useRef<HTMLButtonElement>(null)

  const handleSort = (field: string) => {
    onSortChange?.(field)
    setShowSortOptions(false)
  }

  const getSortLabel = (field: string) => {
    const option = sortOptions.find(opt => opt.value === field)
    return option?.label || field
  }

  return (
    <div className={`bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3 mb-4 ${className}`}>
      <div className="flex flex-wrap items-center gap-2">
        
        {/* Filter Tabs */}
        {filterTabs.length > 0 && (
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-0.5 h-8">
            {filterTabs.map((filter) => (
              <button
                key={filter.value}
                onClick={() => onFilterChange?.(filter.value)}
                className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 h-full flex items-center ${
                  activeFilter === filter.value
                    ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm"
                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>
        )}

        {/* Status/Category Dropdown */}
        {statusOptions.length > 0 && (
          <select
            value={activeStatus}
            onChange={(e) => onStatusChange?.(e.target.value)}
            className="px-3 py-1 h-8 text-xs border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-gray-100 transition-all duration-200"
          >
            <option value="all">Tất cả {statusLabel.toLowerCase()}</option>
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )}

        {/* Search */}
        <div className="flex-1 min-w-[200px] relative">
          <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => onSearchChange?.(e.target.value)}
            className="w-full h-8 pl-9 pr-3 bg-white/80 dark:bg-gray-700/80 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 dark:text-gray-100 text-xs transition-all duration-200"
          />
        </div>

        {/* Custom Controls */}
        {customControls}

        {/* Item Count */}
        <div className="flex items-center justify-center px-3 py-1 h-8 bg-gray-50 dark:bg-gray-700/50 rounded-lg text-xs text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
          {itemCount} {itemLabel}
        </div>

        {/* Sort Controls */}
        {sortOptions.length > 0 && (
          <div className="relative">
            <button
              ref={sortButtonRef}
              onClick={() => setShowSortOptions(!showSortOptions)}
              className="flex items-center gap-1 px-3 py-1 h-8 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 bg-white/80 dark:bg-gray-700/80 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg transition-all duration-200"
            >
              {sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />}
              <span className="whitespace-nowrap">Sắp xếp</span>
              {showSortOptions ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </button>

            <PortalDropdown
              isOpen={showSortOptions}
              onClose={() => setShowSortOptions(false)}
              triggerRef={sortButtonRef}
            >
              {sortOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleSort(option.value)}
                  className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeSortField === option.value
                      ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    {option.label}
                    {activeSortField === option.value && (
                      sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                    )}
                  </div>
                </button>
              ))}
            </PortalDropdown>
          </div>
        )}
      </div>
    </div>
  )
}

// ==================== HELPER HOOKS ====================

export interface UseUnifiedFiltersOptions<T> {
  data: T[]
  filterField?: keyof T
  searchFields?: (keyof T)[]
  sortField?: keyof T
  sortDirection?: 'asc' | 'desc'
}

export function useUnifiedFilters<T>({
  data,
  filterField,
  searchFields = [],
  sortField,
  sortDirection = 'asc'
}: UseUnifiedFiltersOptions<T>) {
  const [activeFilter, setActiveFilter] = useState('all')
  const [activeStatus, setActiveStatus] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [activeSortField, setActiveSortField] = useState<string>(sortField || '')
  const [currentSortDirection, setSortDirection] = useState<'asc' | 'desc'>(sortDirection)

  // Filter and sort data - handle empty/undefined data and ensure it's an array
  if (!Array.isArray(data)) {
    console.warn('useUnifiedFilters received non-array data:', typeof data, data)
  }
  const safeData = Array.isArray(data) ? data : []
  const filteredData = safeData
    .filter(item => {
      // Filter by type/category
      if (activeFilter !== 'all' && filterField) {
        if (item[filterField] !== activeFilter) return false
      }

      // Filter by status
      if (activeStatus !== 'all') {
        // This would need to be customized per use case
        const isActive = (item as any).isActive
        if (activeStatus === 'active' && !isActive) return false
        if (activeStatus === 'inactive' && isActive) return false
      }

      // Search filter
      if (searchTerm && searchFields.length > 0) {
        const searchLower = searchTerm.toLowerCase()
        const matches = searchFields.some(field => {
          const value = item[field]
          return String(value).toLowerCase().includes(searchLower)
        })
        if (!matches) return false
      }

      return true
    })
    .sort((a, b) => {
      if (!activeSortField) return 0

      const aValue = (a as any)[activeSortField]
      const bValue = (b as any)[activeSortField]

      let comparison = 0
      if (aValue > bValue) comparison = 1
      if (aValue < bValue) comparison = -1

      return currentSortDirection === 'asc' ? comparison : -comparison
    })

  const handleSortChange = (field: string) => {
    if (activeSortField === field) {
      setSortDirection(currentSortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setActiveSortField(field)
      setSortDirection('asc')
    }
  }

  return {
    filteredData,
    activeFilter,
    setActiveFilter,
    activeStatus,
    setActiveStatus,
    searchTerm,
    setSearchTerm,
    activeSortField,
    currentSortDirection,
    handleSortChange
  }
}
