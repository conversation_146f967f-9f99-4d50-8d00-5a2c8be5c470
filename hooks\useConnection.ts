/**
 * Custom React Hooks for GraphQL Connections
 * 
 * Hooks to simplify working with GraphQL connection format in React components
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { extractNodes, extractPaginationInfo, useConnectionData } from '@/lib/utils/connection-helpers'
import type { Connection, ConnectionResponse } from '@/types/graphql-connections'

// ==================== BASIC CONNECTION HOOK ====================

export interface UseConnectionOptions<T> {
  fetchFunction: (params?: any) => Promise<ConnectionResponse<T>>
  initialParams?: any
  autoFetch?: boolean
  onSuccess?: (data: Connection<T>) => void
  onError?: (error: any) => void
}

export interface UseConnectionResult<T> {
  // Data
  items: T[]
  connection: Connection<T> | null
  
  // Pagination
  totalCount: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  startCursor?: string | null
  endCursor?: string | null
  
  // State
  loading: boolean
  error: any
  
  // Actions
  fetch: (params?: any) => Promise<void>
  refetch: () => Promise<void>
  fetchMore: (cursor?: string) => Promise<void>
  reset: () => void
}

export function useConnection<T>(options: UseConnectionOptions<T>): UseConnectionResult<T> {
  const { fetchFunction, initialParams, autoFetch = true, onSuccess, onError } = options
  
  const [connection, setConnection] = useState<Connection<T> | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<any>(null)
  const [lastParams, setLastParams] = useState(initialParams)

  const fetch = useCallback(async (params?: any) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetchFunction(params)
      
      if (response.success && response.data) {
        setConnection(response.data)
        setLastParams(params)
        onSuccess?.(response.data)
      } else {
        const errorMsg = response.error || 'Failed to fetch data'
        setError(errorMsg)
        onError?.(errorMsg)
      }
    } catch (err: any) {
      setError(err)
      onError?.(err)
    } finally {
      setLoading(false)
    }
  }, [fetchFunction, onSuccess, onError])

  const refetch = useCallback(() => {
    return fetch(lastParams)
  }, [fetch, lastParams])

  const fetchMore = useCallback(async (cursor?: string) => {
    if (!cursor) return
    
    const params = { ...lastParams, after: cursor }
    await fetch(params)
  }, [fetch, lastParams])

  const reset = useCallback(() => {
    setConnection(null)
    setError(null)
    setLastParams(initialParams)
  }, [initialParams])

  // Auto-fetch on mount
  useEffect(() => {
    if (autoFetch) {
      fetch(initialParams)
    }
  }, []) // Only run on mount

  // Derived values
  const items = useMemo(() => extractNodes(connection), [connection])
  const paginationInfo = useMemo(() => extractPaginationInfo(connection), [connection])

  return {
    // Data
    items,
    connection,
    
    // Pagination
    totalCount: paginationInfo.totalCount,
    hasNextPage: paginationInfo.hasNextPage,
    hasPreviousPage: paginationInfo.hasPreviousPage,
    startCursor: paginationInfo.startCursor,
    endCursor: paginationInfo.endCursor,
    
    // State
    loading,
    error,
    
    // Actions
    fetch,
    refetch,
    fetchMore,
    reset
  }
}

// ==================== PAGINATED CONNECTION HOOK ====================

export interface UsePaginatedConnectionOptions<T> extends UseConnectionOptions<T> {
  pageSize?: number
}

export interface UsePaginatedConnectionResult<T> extends UseConnectionResult<T> {
  // Pagination state
  currentPage: number
  pageSize: number
  totalPages: number
  
  // Pagination actions
  goToPage: (page: number) => Promise<void>
  nextPage: () => Promise<void>
  previousPage: () => Promise<void>
  setPageSize: (size: number) => void
}

export function usePaginatedConnection<T>(
  options: UsePaginatedConnectionOptions<T>
): UsePaginatedConnectionResult<T> {
  const { pageSize: initialPageSize = 25, ...connectionOptions } = options
  
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSizeState] = useState(initialPageSize)
  
  const connectionResult = useConnection({
    ...connectionOptions,
    initialParams: { ...connectionOptions.initialParams, first: pageSize },
    autoFetch: false // We'll handle fetching manually
  })

  const totalPages = useMemo(() => {
    return Math.ceil(connectionResult.totalCount / pageSize)
  }, [connectionResult.totalCount, pageSize])

  const goToPage = useCallback(async (page: number) => {
    if (page < 1 || page > totalPages) return
    
    setCurrentPage(page)
    
    // Calculate cursor for the page
    const offset = (page - 1) * pageSize
    const cursor = offset > 0 ? Buffer.from(offset.toString()).toString('base64') : undefined
    
    await connectionResult.fetch({
      ...connectionOptions.initialParams,
      first: pageSize,
      after: cursor
    })
  }, [totalPages, pageSize, connectionResult.fetch, connectionOptions.initialParams])

  const nextPage = useCallback(async () => {
    if (connectionResult.hasNextPage) {
      await goToPage(currentPage + 1)
    }
  }, [connectionResult.hasNextPage, goToPage, currentPage])

  const previousPage = useCallback(async () => {
    if (currentPage > 1) {
      await goToPage(currentPage - 1)
    }
  }, [currentPage, goToPage])

  const setPageSize = useCallback((size: number) => {
    setPageSizeState(size)
    setCurrentPage(1) // Reset to first page
    // Refetch with new page size
    connectionResult.fetch({
      ...connectionOptions.initialParams,
      first: size
    })
  }, [connectionResult.fetch, connectionOptions.initialParams])

  // Auto-fetch on mount
  useEffect(() => {
    if (connectionOptions.autoFetch !== false) {
      goToPage(1)
    }
  }, []) // Only run on mount

  return {
    ...connectionResult,
    
    // Pagination state
    currentPage,
    pageSize,
    totalPages,
    
    // Pagination actions
    goToPage,
    nextPage,
    previousPage,
    setPageSize
  }
}

// ==================== INFINITE SCROLL CONNECTION HOOK ====================

export interface UseInfiniteConnectionResult<T> extends Omit<UseConnectionResult<T>, 'fetchMore'> {
  // Infinite scroll specific
  allItems: T[]
  hasMore: boolean
  loadMore: () => Promise<void>
  isLoadingMore: boolean
}

export function useInfiniteConnection<T>(
  options: UseConnectionOptions<T>
): UseInfiniteConnectionResult<T> {
  const [allConnections, setAllConnections] = useState<Connection<T>[]>([])
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  const connectionResult = useConnection(options)

  // Combine all items from all connections
  const allItems = useMemo(() => {
    return allConnections.flatMap(conn => extractNodes(conn))
  }, [allConnections])

  // Update connections when new data is fetched
  useEffect(() => {
    if (connectionResult.connection) {
      setAllConnections(prev => {
        // If this is a fresh fetch (not load more), replace all connections
        if (!connectionResult.connection?.pageInfo.hasPreviousPage) {
          return [connectionResult.connection]
        }
        // Otherwise, append to existing connections
        return [...prev, connectionResult.connection]
      })
    }
  }, [connectionResult.connection])

  const loadMore = useCallback(async () => {
    if (!connectionResult.hasNextPage || isLoadingMore) return

    setIsLoadingMore(true)
    try {
      await connectionResult.fetchMore(connectionResult.endCursor || undefined)
    } finally {
      setIsLoadingMore(false)
    }
  }, [connectionResult.hasNextPage, connectionResult.fetchMore, connectionResult.endCursor, isLoadingMore])

  const hasMore = connectionResult.hasNextPage

  return {
    ...connectionResult,
    items: connectionResult.items, // Current page items
    allItems, // All loaded items
    hasMore,
    loadMore,
    isLoadingMore
  }
}

// ==================== ENTITY-SPECIFIC HOOKS ====================

// Re-export for convenience
export { useConnectionData } from '@/lib/utils/connection-helpers'
