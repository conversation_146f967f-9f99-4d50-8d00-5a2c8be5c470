"use client"

import React, { useState, useMemo, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { ListPageLayout } from "@/components/layout/ResponsiveLayout"
import { EnhancedGenericList } from "@/components/shared/lists/EnhancedGenericList"
import { UnifiedFilterControls, useUnifiedFilters } from "@/components/shared/controls/UnifiedFilterControls"
import { ContactCard } from "@/components/features/contacts/ContactCard"
import { ContactSidebar } from "@/components/ui/ContactSidebar"
import { Button } from "@/components/ui/button"

import { ContactApiService } from "@/services/client"
import { ContactTypeApiService } from "@/services/client/contactTypeApi"
import { Plus, Users, UserPlus, Building, Heart } from "lucide-react"

function ContactsPageContent() {
  const router = useRouter()
  const { user } = useAuth()

  // State management
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedContact, setSelectedContact] = useState<any>(null)
  const [contacts, setContacts] = useState<any[]>([])
  const [contactTypes, setContactTypes] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)

  // Fetch contacts
  useEffect(() => {
    const fetchContacts = async () => {
      try {
        setLoading(true)
        const response = await ContactApiService.getContacts({})
        if (response.success) {
          setContacts(response.data || [])
        } else {
          setError('Failed to load contacts')
        }
      } catch (err) {
        setError('Failed to load contacts')
        console.error('Contacts error:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchContacts()
  }, [user?.id])

  // Fetch contact types
  useEffect(() => {
    const fetchContactTypes = async () => {
      try {
        const response = await ContactTypeApiService.getContactTypes()
        if (response.success) {
          setContactTypes(response.data || [])
        }
      } catch (error) {
        console.error('Error fetching contact types:', error)
      }
    }

    fetchContactTypes()
  }, [])

  // Unified filters
  const {
    filteredData: filteredContacts,
    activeFilter,
    setActiveFilter,
    searchTerm,
    setSearchTerm,
    activeSortField,
    currentSortDirection,
    handleSortChange
  } = useUnifiedFilters({
    data: contacts,
    filterField: 'contactTypeId',
    searchFields: ['name', 'email', 'phone'],
    sortField: 'name',
    sortDirection: 'asc'
  })

  // Filter tabs for contact types
  const filterTabs = [
    { value: 'all', label: 'Tất cả', count: contacts.length },
    ...contactTypes.map(type => ({
      value: type.id,
      label: type.name,
      count: contacts.filter(c => c.contactTypeId === type.id).length
    }))
  ]

  // Sort options
  const sortOptions = [
    { value: 'name', label: 'Tên liên hệ' },
    { value: 'email', label: 'Email' },
    { value: 'phone', label: 'Số điện thoại' },
    { value: 'createdAt', label: 'Ngày tạo' }
  ]

  // Statistics
  const stats = useMemo(() => {
    const total = contacts.length
    const active = contacts.filter(c => c.isActive).length
    const withEmail = contacts.filter(c => c.email).length
    const withPhone = contacts.filter(c => c.phone).length

    return [
      {
        label: "Tổng liên hệ",
        value: total.toString(),
        icon: <Users size={14} className="text-blue-500" />
      },
      {
        label: "Đang hoạt động",
        value: active.toString(),
        icon: <UserPlus size={14} className="text-green-500" />
      },
      {
        label: "Có email",
        value: withEmail.toString(),
        icon: <Building size={14} className="text-purple-500" />
      },
      {
        label: "Có số điện thoại",
        value: withPhone.toString(),
        icon: <Heart size={14} className="text-pink-500" />
      }
    ]
  }, [contacts])

  // Event handlers
  const handleAddContact = (_type?: 'personal' | 'business' | 'family' | 'vendor') => {
    setSelectedContact(null)
    setIsEditing(false)
    setSidebarOpen(true)
    // If type is specified, we could pass it to the sidebar
  }

  const handleEditContact = (contact: any) => {
    setSelectedContact(contact)
    setIsEditing(true)
    setSidebarOpen(true)
  }



  const handleViewContact = (contact: any) => {
    console.log('ContactsPage: handleViewContact called for contact:', contact.id)
    router.push(`/contacts/${contact.id}`)
  }



  const handleSaveContact = async (contact: any) => {
    try {
      // Validate required fields
      if (!contact.name?.trim()) {
        setError('Vui lòng nhập tên liên hệ')
        return
      }

      if (contact.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
        setError('Vui lòng nhập email hợp lệ')
        return
      }

      setLoading(true)

      // Prepare contact data
      const contactData = {
        name: contact.name.trim(),
        email: contact.email?.trim() || '',
        phone: contact.phone?.trim() || '',
        address: contact.address?.trim() || '',
        description: contact.description?.trim() || '',
        contactTypeId: contact.contactTypeId || null,
        isActive: Boolean(contact.isActive !== false)
      }

      let response
      if (isEditing) {
        response = await ContactApiService.updateContact(contact.id, contactData)
      } else {
        response = await ContactApiService.createContact(contactData)
      }

      if (response.success) {
        // Refresh contacts list
        const contactsResponse = await ContactApiService.getContacts({})
        if (contactsResponse.success) {
          setContacts(contactsResponse.data || [])
        }
        setSidebarOpen(false)
        setError(null)
        console.log(`Liên hệ đã được ${isEditing ? 'cập nhật' : 'tạo'} thành công`)
      } else {
        setError(typeof response.error === 'string' ? response.error : 'Không thể lưu liên hệ. Vui lòng thử lại.')
      }
    } catch (error: any) {
      console.error('Error saving contact:', error)
      setError(error?.message || 'Không thể lưu liên hệ. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }

  // const handleCloseSidebar = () => {
  //   setSidebarOpen(false)
  //   setSelectedContact(null)
  //   setIsEditing(false)
  // }

  // Render contact card
  const renderContactCard = (contact: any) => (
    <ContactCard
      contact={contact}
      onEdit={handleEditContact}

      onView={handleViewContact}
      transactionCount={Math.floor(Math.random() * 20)} // TODO: Get real data
      variant="default"
    />
  )

  // Empty message based on filters
  const getEmptyMessage = () => {
    if (filteredContacts.length === 0 && contacts.length > 0) {
      return "Không tìm thấy liên hệ nào phù hợp với bộ lọc hiện tại"
    }
    return "Chưa có liên hệ nào. Hãy thêm liên hệ đầu tiên để bắt đầu quản lý danh bạ!"
  }

  return (
    <>
      <ListPageLayout
        title="Liên hệ"
        subtitle="Quản lý danh sách liên hệ cho giao dịch"
        stats={stats}
        actions={
          <Button
            onClick={() => handleAddContact()}
            className="flex items-center gap-2"
          >
            <Plus size={16} />
            Thêm liên hệ
          </Button>
        }
      >
        {/* Unified Filter Controls */}
        <UnifiedFilterControls
          filterTabs={filterTabs}
          activeFilter={activeFilter}
          onFilterChange={setActiveFilter}
          searchValue={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Tìm kiếm liên hệ..."
          sortOptions={sortOptions}
          activeSortField={activeSortField}
          sortDirection={currentSortDirection}
          onSortChange={handleSortChange}
          itemCount={filteredContacts.length}
          itemLabel="liên hệ"
        />

        {/* Enhanced Generic List */}
        <EnhancedGenericList
          data={filteredContacts}
          renderCard={renderContactCard}
          loading={loading}
          error={error}
          emptyMessage={getEmptyMessage()}
          searchable={false}
          sortable={false}
          filterable={false}
          paginated={true}
          defaultItemsPerPage={25}
          itemsPerPageOptions={[10, 25, 50, 100]}
          gridCols={{ default: 1, sm: 2, lg: 3, xl: 4, '2xl': 4 }}
          sidebarOpen={sidebarOpen}
          sidebarWidth={400}
          onItemClick={handleViewContact}
        />
      </ListPageLayout>

      {/* Contact Sidebar */}
      <ContactSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onSave={handleSaveContact}
        contact={selectedContact}
        isEditing={isEditing}
      />

      {/* Toast Notifications */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 animate-fade-in">
          <div className="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="flex-1">{error}</span>
            <button
              onClick={() => setError(null)}
              className="text-white hover:text-gray-200 p-1"
              aria-label="Đóng"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Global Loading Indicator */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 flex items-center gap-3 shadow-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-700 dark:text-gray-200 text-sm font-medium">Đang xử lý...</span>
          </div>
        </div>
      )}
    </>
  )
}

export default function ContactsPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <ContactsPageContent />
    </ProtectedRoute>
  )
}
