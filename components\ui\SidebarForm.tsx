"use client"

import React from "react"
import { Form<PERSON>ield, InputField, SelectField, TextareaField, CheckboxField } from "./FormField"
import { useSidebarLayout } from "@/hooks/useSidebarLayout"

interface SidebarFormProps {
  children: React.ReactNode
  onSubmit?: (e: React.FormEvent) => void
  className?: string
  sidebarWidth: number
  isResizing: boolean
  isOpen: boolean
}

export function SidebarForm({
  children,
  onSubmit,
  className = "",
  sidebarWidth,
  isResizing,
  isOpen
}: SidebarFormProps) {
  const { layoutClass } = useSidebarLayout({
    isOpen,
    sidebarWidth,
    isResizing
  })
  
  return (
    <form
      onSubmit={onSubmit}
      className={`
        sidebar-form-responsive
        ${layoutClass}
        ${className}
      `}
    >
      {children}
    </form>
  )
}

interface SidebarFormFieldProps {
  label: string
  error?: string
  required?: boolean
  icon?: React.ReactNode
  children: React.ReactNode
  className?: string
  span?: 'single' | 'double' | 'full'
  sidebarWidth: number
}

export function SidebarFormField({
  label,
  error,
  required = false,
  icon,
  children,
  className = "",
  span = 'single',
  sidebarWidth
}: SidebarFormFieldProps) {
  const getFieldSpan = (fieldType: 'single' | 'double' | 'full'): string => {
    const columnCount = sidebarWidth < 400 ? 1 : sidebarWidth < 600 ? 1 : sidebarWidth < 800 ? 2 : 3
    
    switch (fieldType) {
      case 'single':
        return 'col-span-1'
      case 'double':
        return columnCount >= 2 ? 'col-span-2' : 'col-span-1'
      case 'full':
        return `col-span-${columnCount}`
      default:
        return 'col-span-1'
    }
  }
  
  return (
    <div className={`${getFieldSpan(span)} ${className}`}>
      <FormField
        label={label}
        error={error}
        required={required}
        icon={icon}
      >
        {children}
      </FormField>
    </div>
  )
}

interface SidebarFormSectionProps {
  title: string
  description?: string
  children: React.ReactNode
  collapsible?: boolean
  defaultExpanded?: boolean
  className?: string
  sidebarWidth: number
}

export function SidebarFormSection({
  title,
  description,
  children,
  collapsible = false,
  defaultExpanded = true,
  className = "",
  sidebarWidth
}: SidebarFormSectionProps) {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded)
  const shouldShowSections = sidebarWidth >= 600
  
  if (!shouldShowSections) {
    return <>{children}</>
  }
  
  return (
    <div className={`sidebar-form-section ${className}`}>
      <div 
        className={`
          form-section-header
          ${collapsible ? 'cursor-pointer' : ''}
        `}
        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}
      >
        <h3 className="form-section-title">{title}</h3>
        {description && (
          <p className="form-section-description">{description}</p>
        )}
        {collapsible && (
          <button
            type="button"
            className="form-section-toggle"
            aria-expanded={isExpanded}
          >
            {isExpanded ? '−' : '+'}
          </button>
        )}
      </div>
      
      {isExpanded && (
        <div className="form-section-content">
          {children}
        </div>
      )}
    </div>
  )
}

// Specialized form field components for common use cases
interface SidebarInputFieldProps {
  label: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  type?: string
  required?: boolean
  error?: string
  icon?: React.ReactNode
  disabled?: boolean
  span?: 'single' | 'double' | 'full'
  sidebarWidth: number
  className?: string
}

export function SidebarInputField({
  label,
  value,
  onChange,
  placeholder,
  type = "text",
  required = false,
  error,
  icon,
  disabled = false,
  span = 'single',
  sidebarWidth,
  className = ""
}: SidebarInputFieldProps) {
  return (
    <SidebarFormField
      label={label}
      error={error}
      required={required}
      icon={icon}
      span={span}
      sidebarWidth={sidebarWidth}
      className={className}
    >
      <InputField
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
      />
    </SidebarFormField>
  )
}

interface SidebarSelectFieldProps {
  label: string
  value: string
  onChange: (value: string) => void
  options: Array<{ value: string; label: string }>
  required?: boolean
  error?: string
  icon?: React.ReactNode
  disabled?: boolean
  span?: 'single' | 'double' | 'full'
  sidebarWidth: number
  className?: string
}

export function SidebarSelectField({
  label,
  value,
  onChange,
  options,
  required = false,
  error,
  icon,
  disabled = false,
  span = 'single',
  sidebarWidth,
  className = ""
}: SidebarSelectFieldProps) {
  return (
    <SidebarFormField
      label={label}
      error={error}
      required={required}
      icon={icon}
      span={span}
      sidebarWidth={sidebarWidth}
      className={className}
    >
      <SelectField
        value={value}
        onChange={onChange}
        options={options}
        disabled={disabled}
      />
    </SidebarFormField>
  )
}

interface SidebarTextareaFieldProps {
  label: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  rows?: number
  required?: boolean
  error?: string
  icon?: React.ReactNode
  disabled?: boolean
  span?: 'single' | 'double' | 'full'
  sidebarWidth: number
  className?: string
}

export function SidebarTextareaField({
  label,
  value,
  onChange,
  placeholder,
  rows = 3,
  required = false,
  error,
  icon,
  disabled = false,
  span = 'full',
  sidebarWidth,
  className = ""
}: SidebarTextareaFieldProps) {
  return (
    <SidebarFormField
      label={label}
      error={error}
      required={required}
      icon={icon}
      span={span}
      sidebarWidth={sidebarWidth}
      className={className}
    >
      <TextareaField
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        rows={rows}
        disabled={disabled}
      />
    </SidebarFormField>
  )
}
