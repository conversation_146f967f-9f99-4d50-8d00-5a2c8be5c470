"use client"

import React from "react"
import { Contact } from "@/lib/generated/prisma"

interface FormData {
  type: "income" | "expense" | "transfer"
  transferTo: string
  transferToContactId: string
  transferToNote: string
  transferFromContactId: string
  transferFromNote: string
  isRecurring: boolean
  notes: string
  location: string
  reference: string
}

interface FormErrors {
  transferTo?: string
  transferToContactId?: string
  transferFromContactId?: string
}

interface TransferFieldsProps {
  formData: FormData
  errors: FormErrors
  contacts: Contact[]
  filteredContacts: Contact[]
  loading: boolean
  showContactDropdown: boolean
  onInputChange: (field: keyof FormData, value: string | boolean) => void
  onLegacyContactSelect: (contact: Contact) => void
  setShowContactDropdown: (show: boolean) => void
  onCreateContact?: () => void
}

export function TransferFields({
  formData,
  errors,
  contacts,
  filteredContacts,
  loading,
  showContactDropdown,
  onInputChange,
  onLegacyContactSelect,
  setShowContactDropdown,
  onCreateContact
}: TransferFieldsProps) {
  if (formData.type !== "transfer") {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
          Additional Details
        </h3>
        
        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notes
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => onInputChange('notes', e.target.value)}
            placeholder="Additional notes (optional)"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors resize-none"
            disabled={loading}
          />
        </div>

        {/* Location & Reference Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Location
            </label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => onInputChange('location', e.target.value)}
              placeholder="Location (optional)"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors"
              disabled={loading}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Reference
            </label>
            <input
              type="text"
              value={formData.reference}
              onChange={(e) => onInputChange('reference', e.target.value)}
              placeholder="Reference number (optional)"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors"
              disabled={loading}
            />
          </div>
        </div>

        {/* Recurring */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isRecurring"
            checked={formData.isRecurring}
            onChange={(e) => onInputChange('isRecurring', e.target.checked)}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            disabled={loading}
          />
          <label htmlFor="isRecurring" className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            Recurring transaction
          </label>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
        Transfer Details
      </h3>
      
      {/* Transfer To Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Transfer To *
        </label>
        <div className="relative">
          <input
            type="text"
            value={formData.transferTo}
            onChange={(e) => {
              onInputChange('transferTo', e.target.value)
              setShowContactDropdown(true)
            }}
            placeholder="Enter recipient name (will create new contact if not exists)"
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
              errors.transferTo
                ? 'border-red-500 dark:border-red-400'
                : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
          />

          {/* Contact Dropdown */}
          {showContactDropdown && (
            <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto">
              {filteredContacts.length > 0 && (
                <>
                  {filteredContacts.map((contact) => (
                    <button
                      key={contact.id}
                      type="button"
                      onClick={() => onLegacyContactSelect(contact)}
                      className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      disabled={loading}
                    >
                      <div className="font-medium text-gray-900 dark:text-gray-100">{contact.name}</div>
                      {contact.phone && (
                        <div className="text-sm text-gray-500 dark:text-gray-400">{contact.phone}</div>
                      )}
                    </button>
                  ))}
                  <div className="border-t border-gray-200 dark:border-gray-600"></div>
                </>
              )}

              {/* Add New Contact Button */}
              {onCreateContact && (
                <button
                  type="button"
                  onClick={() => {
                    setShowContactDropdown(false)
                    onCreateContact()
                  }}
                  className="w-full px-3 py-2 text-left hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors text-emerald-600 dark:text-emerald-400 font-medium flex items-center gap-2"
                  disabled={loading}
                >
                  <span className="text-lg">+</span>
                  <span>Thêm liên hệ mới</span>
                </button>
              )}
            </div>
          )}
        </div>

        {/* New Contact Indicator */}
        {formData.transferTo &&
         !contacts.some(contact => contact.name.toLowerCase() === formData.transferTo.toLowerCase()) &&
         formData.transferTo.trim().length > 0 && (
          <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              ✨ New contact "{formData.transferTo}" will be created automatically
            </p>
          </div>
        )}

        {errors.transferTo && (
          <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.transferTo}</p>
        )}
      </div>

      {/* Transfer Notes */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Transfer To Note
          </label>
          <input
            type="text"
            value={formData.transferToNote}
            onChange={(e) => onInputChange('transferToNote', e.target.value)}
            placeholder="Note for recipient"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors"
            disabled={loading}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Transfer From Note
          </label>
          <input
            type="text"
            value={formData.transferFromNote}
            onChange={(e) => onInputChange('transferFromNote', e.target.value)}
            placeholder="Note for sender"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors"
            disabled={loading}
          />
        </div>
      </div>
    </div>
  )
}
