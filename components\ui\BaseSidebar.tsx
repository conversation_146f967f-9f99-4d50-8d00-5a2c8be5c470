"use client"

import React, { useEffect, useRef } from "react"
import { X, GripVertical } from "lucide-react"
import { Button } from "./button"
import { useResizableSidebar } from "@/hooks/useResizableSidebar"
import { useFormSidebarIntegration } from "@/contexts/GlobalSidebarContext"

export interface SidebarConfig {
  DEFAULT_WIDTH: number
  MIN_WIDTH: number
  MAX_WIDTH_PERCENTAGE: number
  TRANSITION_DURATION: number
  TRANSITION_EASING: string
  ENABLE_RESIZE: boolean
  ENABLE_OVERLAY_CLOSE: boolean
  Z_INDEX: {
    OVERLAY: number
    SIDEBAR: number
    RESIZE_HANDLE: number
  }
}

export const DEFAULT_SIDEBAR_CONFIG: SidebarConfig = {
  DEFAULT_WIDTH: 400,
  MIN_WIDTH: 300,
  MAX_WIDTH_PERCENTAGE: 0.6,
  TRANSITION_DURATION: 300,
  TRANSITION_EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  ENA<PERSON>E_RESIZE: true,
  ENABLE_OVERLAY_CLOSE: true,
  Z_INDEX: {
    OVERLAY: 55,
    SIDEBAR: 56,
    RESIZE_HANDLE: 57
  }
}

export interface BaseSidebarProps {
  isOpen: boolean
  onClose: () => void
  title: string
  subtitle?: string
  children: React.ReactNode
  
  // Configuration
  config?: Partial<SidebarConfig>
  storageKey: string
  
  // Footer
  footerContent?: React.ReactNode
  
  // Styling
  className?: string
  headerClassName?: string
  contentClassName?: string
  
  // Accessibility
  ariaLabel?: string
  ariaDescribedBy?: string
}

function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = React.useState(false)
  
  React.useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }
    
    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])
  
  return isMobile
}

export function BaseSidebar({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  config: userConfig = {},
  storageKey,
  footerContent,
  className = "",
  headerClassName = "",
  contentClassName = "",
  ariaLabel,
  ariaDescribedBy
}: BaseSidebarProps) {
  const isMobile = useIsMobile()
  const sidebarRef = useRef<HTMLDivElement>(null)
  const config = { ...DEFAULT_SIDEBAR_CONFIG, ...userConfig }
  
  const {
    sidebarWidth,
    isResizing,
    handleResizeStart,
    sidebarStyle,
  } = useResizableSidebar({
    storageKey,
    isOpen,
    config
  })

  // Integrate with global sidebar state with real-time updates
  useFormSidebarIntegration(isOpen, sidebarWidth, storageKey, isResizing)
  
  // Simple animation state - no complex animation classes needed
  const animationClass = ""
  
  // Keyboard navigation
  useEffect(() => {
    if (!isOpen) return
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }
    
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, onClose])
  
  // Focus management
  useEffect(() => {
    if (isOpen && sidebarRef.current) {
      // Focus the sidebar when it opens
      const focusableElement = sidebarRef.current.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement
      
      if (focusableElement) {
        focusableElement.focus()
      }
    }
  }, [isOpen])
  
  // Focus trap
  useEffect(() => {
    if (!isOpen) return
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab' || !sidebarRef.current) return
      
      const focusableElements = sidebarRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
      
      const firstElement = focusableElements[0] as HTMLElement
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
      
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus()
          e.preventDefault()
        }
      }
    }
    
    document.addEventListener('keydown', handleTabKey)
    return () => document.removeEventListener('keydown', handleTabKey)
  }, [isOpen])
  
  if (!isOpen) {
    return null
  }
  
  return (
    <>
      {/* Overlay for mobile and backdrop */}
      <div
        className={`fixed inset-0 bg-black/50 transition-opacity duration-300 ${
          isMobile ? 'z-[55]' : 'lg:hidden z-[55]'
        }`}
        onClick={config.ENABLE_OVERLAY_CLOSE ? onClose : undefined}
        style={{ zIndex: config.Z_INDEX.OVERLAY }}
      />
      
      {/* Sidebar */}
      <div
        ref={sidebarRef}
        role="dialog"
        aria-modal="true"
        aria-label={ariaLabel || title}
        aria-describedby={ariaDescribedBy}
        className={`
          fixed top-0 right-0 h-full
          bg-white dark:bg-gray-900
          border-l border-gray-200 dark:border-gray-700
          shadow-2xl
          flex flex-col
          ${className}
        `}
        style={{
          ...sidebarStyle,
          zIndex: config.Z_INDEX.SIDEBAR,
          transition: isResizing ? 'none' : `all ${config.TRANSITION_DURATION}ms ${config.TRANSITION_EASING}`
        }}
      >
        {/* Resize Handle */}
        {config.ENABLE_RESIZE && !isMobile && (
          <div
            className="absolute left-0 top-0 w-1 h-full cursor-col-resize hover:bg-blue-500/20 transition-colors group"
            onMouseDown={handleResizeStart}
            style={{ zIndex: config.Z_INDEX.RESIZE_HANDLE }}
          >
            <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 p-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <GripVertical size={16} className="text-gray-400" />
            </div>
          </div>
        )}
        
        {/* Header */}
        <div className={`
          flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700
          bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm
          ${headerClassName}
        `}>
          <div className="flex-1 min-w-0">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              {title}
            </h2>
            {subtitle && (
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate mt-0.5">
                {subtitle}
              </p>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="ml-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            aria-label="Đóng sidebar"
          >
            <X size={20} />
          </Button>
        </div>
        
        {/* Content */}
        <div className={`
          flex-1 overflow-y-auto sidebar-content
          ${contentClassName}
        `}>
          {children}
        </div>

        {/* Footer */}
        {footerContent && (
          <div className="shrink-0 w-full">
            {footerContent}
          </div>
        )}
      </div>
    </>
  )
}
