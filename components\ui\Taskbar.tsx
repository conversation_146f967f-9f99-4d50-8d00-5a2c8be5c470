"use client"

import React, { useState, useRef, useEffect } from "react"
import { X, MoreVertical } from "lucide-react"
import { useTaskbar, TaskbarItem, getTaskbarIcon, getTaskbarDisplayTitle } from "@/contexts/TaskbarContext"

interface TaskbarProps {
  onItemClick: (item: TaskbarItem) => void
  className?: string
}

interface TooltipState {
  visible: boolean
  content: string
  x: number
  y: number
}

export function Taskbar({ onItemClick, className = "" }: TaskbarProps) {
  const { items, removeItem } = useTaskbar()
  const [tooltip, setTooltip] = useState<TooltipState>({
    visible: false,
    content: "",
    x: 0,
    y: 0
  })
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean
    itemId: string
    x: number
    y: number
  }>({
    visible: false,
    itemId: "",
    x: 0,
    y: 0
  })

  const tooltipRef = useRef<HTMLDivElement>(null)
  const contextMenuRef = useRef<HTMLDivElement>(null)

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setContextMenu(prev => ({ ...prev, visible: false }))
      }
    }

    if (contextMenu.visible) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [contextMenu.visible])

  // Handle ESC key to close context menu
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setContextMenu(prev => ({ ...prev, visible: false }))
        setTooltip(prev => ({ ...prev, visible: false }))
      }
    }

    document.addEventListener('keydown', handleEscKey)
    return () => document.removeEventListener('keydown', handleEscKey)
  }, [])

  const handleItemClick = (item: TaskbarItem) => {
    setContextMenu(prev => ({ ...prev, visible: false }))
    setTooltip(prev => ({ ...prev, visible: false }))
    onItemClick(item)
  }

  const handleItemHover = (event: React.MouseEvent, item: TaskbarItem) => {
    const rect = event.currentTarget.getBoundingClientRect()
    const displayTitle = getTaskbarDisplayTitle(item)
    
    setTooltip({
      visible: true,
      content: displayTitle,
      x: rect.left - 10, // Position to the left of the taskbar
      y: rect.top + rect.height / 2
    })
  }

  const handleItemLeave = () => {
    setTooltip(prev => ({ ...prev, visible: false }))
  }

  const handleContextMenu = (event: React.MouseEvent, item: TaskbarItem) => {
    event.preventDefault()
    event.stopPropagation()
    
    setContextMenu({
      visible: true,
      itemId: item.id,
      x: event.clientX,
      y: event.clientY
    })
  }

  const handleRemoveItem = (itemId: string) => {
    removeItem(itemId)
    setContextMenu(prev => ({ ...prev, visible: false }))
  }

  const handleCloseItem = (event: React.MouseEvent, itemId: string) => {
    event.preventDefault()
    event.stopPropagation()
    removeItem(itemId)
  }

  if (items.length === 0) {
    return null
  }

  return (
    <>
      {/* Taskbar */}
      <div
        className={`
          fixed right-2 top-1/2 transform -translate-y-1/2 z-30
          flex flex-col gap-2 p-2 bg-white dark:bg-gray-800 
          border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg
          max-h-[70vh] overflow-y-auto
          ${className}
        `}
      >
        {items.map((item) => (
          <div
            key={item.id}
            className="relative group"
          >
            <button
              onClick={() => handleItemClick(item)}
              onMouseEnter={(e) => handleItemHover(e, item)}
              onMouseLeave={handleItemLeave}
              onContextMenu={(e) => handleContextMenu(e, item)}
              className="
                w-12 h-12 flex items-center justify-center
                bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600
                border border-gray-200 dark:border-gray-600 rounded-lg
                transition-all duration-200 hover:shadow-md
                text-lg relative overflow-hidden
              "
              title={getTaskbarDisplayTitle(item)}
            >
              {/* Icon */}
              <span className="text-xl">{getTaskbarIcon(item.type)}</span>

              {/* Editing indicator */}
              {item.isEditing && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-blue-500 rounded-full border-2 border-white dark:border-gray-700" />
              )}
            </button>

            {/* Close button on hover - outside button */}
            <div
              onClick={(e) => handleCloseItem(e, item.id)}
              className="
                absolute -top-1 -right-1 w-5 h-5
                bg-red-500 hover:bg-red-600 text-white rounded-full
                flex items-center justify-center text-xs cursor-pointer
                opacity-0 group-hover:opacity-100 transition-opacity duration-200
                z-10
              "
            >
              <X className="w-3 h-3" />
            </div>
          </div>
        ))}
      </div>

      {/* Tooltip */}
      {tooltip.visible && (
        <div
          ref={tooltipRef}
          className="
            fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 dark:bg-gray-700
            rounded-lg shadow-lg pointer-events-none max-w-xs
            transform -translate-x-full -translate-y-1/2
          "
          style={{
            left: tooltip.x,
            top: tooltip.y
          }}
        >
          {tooltip.content}
          {/* Arrow */}
          <div className="absolute top-1/2 right-0 transform translate-x-full -translate-y-1/2">
            <div className="w-0 h-0 border-l-4 border-l-gray-900 dark:border-l-gray-700 border-t-4 border-t-transparent border-b-4 border-b-transparent" />
          </div>
        </div>
      )}

      {/* Context Menu */}
      {contextMenu.visible && (
        <div
          ref={contextMenuRef}
          className="
            fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
            rounded-lg shadow-lg py-1 min-w-[120px]
          "
          style={{
            left: contextMenu.x,
            top: contextMenu.y
          }}
        >
          <button
            onClick={() => handleRemoveItem(contextMenu.itemId)}
            className="
              w-full px-3 py-2 text-left text-sm text-red-600 dark:text-red-400
              hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150
              flex items-center gap-2
            "
          >
            <X className="w-4 h-4" />
            Close
          </button>
        </div>
      )}
    </>
  )
}

// Hook for managing taskbar integration in sidebars
export function useTaskbarIntegration(
  type: TaskbarItem['type'],
  isOpen: boolean,
  data?: any,
  isEditing?: boolean,
  title?: string
) {
  const { addItem, removeItem, getItem } = useTaskbar()
  const [taskbarId, setTaskbarId] = useState<string | null>(null)

  // Generate unique ID for this sidebar instance
  useEffect(() => {
    if (isOpen && !taskbarId) {
      const id = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      setTaskbarId(id)
    }
  }, [isOpen, taskbarId, type])

  // Add to taskbar when sidebar opens
  useEffect(() => {
    if (isOpen && taskbarId) {
      const subtitle = data?.name || data?.description || (isEditing ? 'Edit' : 'New')
      
      addItem({
        id: taskbarId,
        type,
        title: title || `${isEditing ? 'Edit' : 'Add'} ${type}`,
        subtitle,
        data,
        isEditing
      })
    }
  }, [isOpen, taskbarId, type, data, isEditing, title, addItem])

  // Remove from taskbar when sidebar closes
  const handleClose = () => {
    if (taskbarId) {
      removeItem(taskbarId)
      setTaskbarId(null)
    }
  }

  // Minimize to taskbar (keep in taskbar but close sidebar)
  const handleMinimize = () => {
    // The item stays in taskbar, sidebar just closes
    // This is handled by the parent component
  }

  return {
    taskbarId,
    handleClose,
    handleMinimize,
    isInTaskbar: taskbarId ? !!getItem(taskbarId) : false
  }
}
