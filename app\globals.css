@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import sidebar animations */
@import '../styles/sidebar-animations.css';

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 0.375rem;
  }

  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
    border-radius: 0.375rem;
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 0.375rem;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(156 163 175);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --color-primary: #10b981;
    --color-primary-dark: #059669;
    --sidebar-width: 224px; /* Updated for new sidebar width */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Layout Components */
  .page-background {
    @apply min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800;
  }

  .main-content {
    @apply flex-1 p-3 lg:p-4 space-y-4;
  }

  .mobile-menu-btn {
    @apply lg:hidden fixed top-4 left-4 z-40 p-2 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg shadow-lg transition-colors;
  }

  /* Content Cards */
  .content-card {
    @apply rounded-3xl border shadow-xl overflow-hidden;
  }

  .content-card-glass {
    @apply content-card bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50;
  }

  .content-card-solid {
    @apply content-card bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700;
  }

  .content-card-header {
    @apply border-b border-gray-200/50 dark:border-gray-700/50 p-4 flex items-center justify-between;
  }

  /* Page Headers */
  .page-title {
    @apply text-2xl font-bold text-gray-900 dark:text-gray-100;
  }

  .page-subtitle {
    @apply text-sm text-gray-600 dark:text-gray-400 mt-1;
  }

  .page-icon {
    @apply text-blue-600 dark:text-blue-400;
  }

  /* Filter Bars */
  .filter-bar {
    @apply bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3;
  }

  .filter-tabs {
    @apply flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1;
  }

  .filter-tab {
    @apply px-3 py-1 rounded text-xs font-medium transition-colors;
  }

  .filter-tab-active {
    @apply filter-tab bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm;
  }

  .filter-tab-inactive {
    @apply filter-tab text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100;
  }

  .search-input {
    @apply w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 text-sm;
  }

  /* Buttons */
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500 shadow-lg hover:shadow-xl px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 focus:ring-gray-500 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-ghost {
    @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-gray-500 px-3 py-2 text-xs font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-danger {
    @apply text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20 px-3 py-2 text-xs font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-success {
    @apply text-green-600 hover:text-green-700 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20 px-3 py-2 text-xs font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Status Badges */
  .badge {
    @apply px-2 py-1 rounded-full text-sm font-medium;
  }

  .badge-success {
    @apply badge bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300;
  }

  .badge-warning {
    @apply badge bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300;
  }

  .badge-danger {
    @apply badge bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300;
  }

  .badge-info {
    @apply badge bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300;
  }

  .badge-neutral {
    @apply badge bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent;
  }

  .gradient-text-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* Glass effect */
  .glass {
    @apply bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm;
  }

  .glass-strong {
    @apply bg-white/90 dark:bg-gray-800/90 backdrop-blur-md;
  }
}

/* Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

[data-theme="blue"] {
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
}

[data-theme="red"] {
  --color-primary: #ef4444;
  --color-primary-dark: #dc2626;
}

[data-theme="purple"] {
  --color-primary: #8b5cf6;
  --color-primary-dark: #7c3aed;
}

[data-theme="orange"] {
  --color-primary: #f59e0b;
  --color-primary-dark: #d97706;
}

.text-primary {
  color: var(--color-primary);
}

.bg-primary {
  background-color: var(--color-primary);
}

.border-primary {
  border-color: var(--color-primary);
}

.hover\:bg-primary:hover {
  background-color: var(--color-primary-dark);
}

/* Glass effect */
.glass {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Simplified animations */
.animate-fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced scrollbar for sidebar */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Dark mode scrollbar */
.dark .scrollbar-thin {
  scrollbar-color: rgba(75, 85, 99, 0.3) transparent;
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.3);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.5);
}

/* Compact table styles */
.table-compact td {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
}

.table-compact th {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
}

/* Compact form styles */
.form-compact input,
.form-compact select,
.form-compact textarea {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.form-compact label {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

/* Sidebar specific styles */
.sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow: hidden;
}

/* Ensure main content doesn't overlap with fixed sidebar */
.main-content {
  transition: margin-left 0.3s ease;
}

.main-content.sidebar-expanded {
  margin-left: 224px;
}

.main-content.sidebar-minimal {
  margin-left: 56px;
}

@media (max-width: 1024px) {
  .main-content.sidebar-expanded,
  .main-content.sidebar-minimal {
    margin-left: 0;
  }
}
