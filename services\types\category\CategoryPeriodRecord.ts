
export type { CategoryPeriodRecord };


import { BaseTableActiveable } from "../base";
/**
 * on the ennd of period, it will create a record of sumary of the category
 */
interface CategoryPeriodRecord extends BaseTableActiveable {
        /** the final goalId config (make snapshot then asign id) */
        categoryGoalId: string;
        categoryId: string;
        targetAmount: number;
        reachedAmount: number;
        period: string;
}