"use client"
import {
  ArrowUpDown,
  Bell,
  Building2,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Contact,
  CreditCard,
  FolderTree,
  Globe,
  Home,
  LogOut,
  Moon,
  PieChart,
  RefreshCw,
  Settings,
  Sun,
  TrendingUp,
  User,
  Users,
  Wallet,
  X,
  Link as LinkIcon
} from "lucide-react"
import { useTheme } from "next-themes"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useLanguage } from "@/contexts/LanguageContext"

type Props = {
  isOpen: boolean
  onClose: () => void
  onMinimalChange?: (isMinimal: boolean) => void
}

export function Sidebar({ isOpen, onClose, onMinimalChange }: Props) {
  const pathname = usePathname()
  const [isMinimal, setIsMinimal] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [collapsedGroups, setCollapsedGroups] = useState<Record<string, boolean>>({})
  const { theme, setTheme } = useTheme()
  const { language, setLanguage, t } = useLanguage()
  const { user, logout } = useAuth()
  const [showLanguageMenu, setShowLanguageMenu] = useState(false)

  // Prevent hydration mismatch for theme
  useEffect(() => {
    setMounted(true)

    // Load collapsed groups from localStorage
    const savedCollapsedGroups = localStorage.getItem('sidebarCollapsedGroups')
    if (savedCollapsedGroups) {
      try {
        setCollapsedGroups(JSON.parse(savedCollapsedGroups))
      } catch (error) {
        console.error('Error parsing collapsed groups from localStorage:', error)
      }
    }
  }, [])

  // Save collapsed groups to localStorage
  useEffect(() => {
    if (mounted) {
      localStorage.setItem('sidebarCollapsedGroups', JSON.stringify(collapsedGroups))
    }
  }, [collapsedGroups, mounted])

  // Auto-expand group if any sub-item is active
  useEffect(() => {
    if (mounted) {
      const activeGroup = menuGroups.find(group => isGroupActive(group))
      if (activeGroup && isGroupCollapsed(activeGroup.id)) {
        setCollapsedGroups(prev => ({
          ...prev,
          [activeGroup.id]: false
        }))
      }
    }
  }, [pathname, mounted])

  // Single menu items
  const singleMenuItems = [
    { href: "/", label: t('nav.dashboard'), icon: Home },
    { href: "/categories", label: t('nav.categories'), icon: FolderTree },
    { href: "/contacts", label: "Liên hệ", icon: Contact },
    { href: "/analytics", label: t('nav.analytics'), icon: PieChart },
  ]

  // Grouped menu items
  const menuGroups = [
    {
      id: "accounts",
      label: "Tài khoản",
      icon: Wallet,
      mainHref: "/accounts", // Trang chính của nhóm
      items: [
        { href: "/accounts", label: "Xem tài khoản", icon: Wallet },
        { href: "/account-types", label: "Quản lý loại tài khoản", icon: Building2 },
        { href: "/debit-cards", label: "Thẻ ghi nợ", icon: CreditCard },
        { href: "/payment-links", label: "Liên kết thanh toán", icon: LinkIcon },
      ]
    },
    {
      id: "transactions",
      label: "Giao dịch",
      icon: ArrowUpDown,
      mainHref: "/transactions", // Trang chính của nhóm
      items: [
        { href: "/transactions", label: "Xem giao dịch", icon: ArrowUpDown },
        { href: "/recurring", label: "Giao dịch định kỳ", icon: RefreshCw },
      ]
    },

  ]

  const adminItems = [
    { href: "/users", label: t('nav.users'), icon: Users },
    { href: "/admin", label: t('nav.admin'), icon: Settings },
  ]

  // Helper functions
  const toggleGroup = (groupId: string) => {
    setCollapsedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }))
  }

  const isGroupCollapsed = (groupId: string) => {
    return collapsedGroups[groupId] || false
  }

  const isGroupActive = (group: typeof menuGroups[0]) => {
    return group.items.some(item => pathname === item.href) || pathname === group.mainHref
  }

  const isItemActive = (href: string) => {
    return pathname === href
  }

  const languages = [
    { code: "en" as const, name: t('language.english'), flag: "🇺🇸" },
    { code: "vi" as const, name: t('language.vietnamese'), flag: "🇻🇳" },
    { code: "zh" as const, name: t('language.chinese'), flag: "🇨🇳" },
  ]

  const currentLanguage = languages.find((lang) => lang.code === language) || languages[0]

  useEffect(() => {
    onMinimalChange?.(isMinimal)
  }, [isMinimal, onMinimalChange])

  const toggleMinimal = () => {
    setIsMinimal(!isMinimal)
  }

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && <div className="fixed inset-0 bg-black/30 z-40 lg:hidden" onClick={onClose} />}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 z-50 h-screen bg-white dark:bg-gray-900
          border-r border-gray-200 dark:border-gray-700 transform transition-all duration-300
          overflow-hidden
          ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
          ${isMinimal ? "w-14" : "w-56"}
        `}
      >
        {/* Header with Logo */}
        <div className="flex items-center justify-between p-2.5 border-b border-gray-200 dark:border-gray-700 h-14">
          {!isMinimal ? (
            <div className="flex items-center gap-2">
              <div className="w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xs">F</span>
              </div>
              <div>
                <h1 className="text-sm font-bold text-gray-900 dark:text-gray-100">Finance Manager</h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">Personal Finance</p>
              </div>
            </div>
          ) : (
            <div className="w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto">
              <span className="text-white font-bold text-xs">F</span>
            </div>
          )}

          <div className="flex items-center gap-1">
            <button
              onClick={toggleMinimal}
              className="hidden lg:flex p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title={isMinimal ? "Expand menu" : "Minimize menu"}
            >
              {isMinimal ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
            </button>
            <button
              onClick={onClose}
              className="lg:hidden p-1.5 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <X size={14} />
            </button>
          </div>
        </div>

        <div className="flex flex-col h-[calc(100vh-3.5rem)]">
          {/* Main Navigation */}
          <nav className="flex-1 p-2 space-y-1 overflow-y-auto scrollbar-thin">
            {/* Single Menu Items */}
            <div className="mb-3">
              {!isMinimal && (
                <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1.5 px-2">
                  Main Menu
                </h3>
              )}
              <ul className="space-y-0.5">
                {singleMenuItems.map((item) => {
                  const Icon = item.icon
                  const isActive = isItemActive(item.href)

                  return (
                    <li key={item.href}>
                      <Link
                        href={item.href}
                        onClick={onClose}
                        className={`
                          flex items-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors
                          ${
                            isActive
                              ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300"
                              : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                          }
                          ${isMinimal ? "justify-center" : ""}
                        `}
                        title={isMinimal ? item.label : ""}
                      >
                        <Icon size={16} className={isActive ? "text-emerald-600" : "text-gray-500"} />
                        {!isMinimal && <span className="font-medium">{item.label}</span>}
                      </Link>
                    </li>
                  )
                })}
              </ul>
            </div>

            {/* Grouped Menu Items */}
            <div className="mb-3">
              <ul className="space-y-1">
                {menuGroups.map((group) => {
                  const GroupIcon = group.icon
                  const isCollapsed = isGroupCollapsed(group.id)
                  const isActive = isGroupActive(group)

                  return (
                    <li key={group.id}>
                      {/* Group Header */}
                      <div className="relative">
                        {isMinimal ? (
                          /* Minimal Mode - Click to navigate to main page */
                          <Link
                            href={group.mainHref}
                            onClick={onClose}
                            className={`
                              w-full flex items-center justify-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors
                              ${
                                isActive
                                  ? "bg-emerald-50 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300"
                                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                              }
                            `}
                            title={group.label}
                          >
                            <GroupIcon size={16} className={isActive ? "text-emerald-600" : "text-gray-500"} />
                          </Link>
                        ) : (
                          /* Full Mode - Split navigation and toggle */
                          <div className={`
                            flex items-center rounded-lg text-xs transition-colors
                            ${
                              isActive
                                ? "bg-emerald-50 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300"
                                : "text-gray-700 dark:text-gray-300"
                            }
                          `}>
                            {/* Navigation Area */}
                            <Link
                              href={group.mainHref}
                              onClick={onClose}
                              className="flex items-center gap-2.5 px-2.5 py-2 flex-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-l-lg transition-colors"
                            >
                              <GroupIcon size={16} className={isActive ? "text-emerald-600" : "text-gray-500"} />
                              <span className="font-medium">{group.label}</span>
                            </Link>

                            {/* Toggle Area */}
                            <button
                              onClick={() => toggleGroup(group.id)}
                              className="flex items-center justify-center px-2 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-r-lg transition-colors"
                              title={isCollapsed ? "Mở rộng menu" : "Thu gọn menu"}
                            >
                              {isCollapsed ? (
                                <ChevronDown size={14} className="text-gray-400" />
                              ) : (
                                <ChevronUp size={14} className="text-gray-400" />
                              )}
                            </button>
                          </div>
                        )}
                      </div>

                      {/* Sub-menu Items */}
                      {!isMinimal && !isCollapsed && (
                        <ul className="mt-1 ml-4 space-y-0.5">
                          {group.items.map((item) => {
                            const ItemIcon = item.icon
                            const isItemActiveState = isItemActive(item.href)

                            return (
                              <li key={item.href}>
                                <Link
                                  href={item.href}
                                  onClick={onClose}
                                  className={`
                                    flex items-center gap-2.5 px-2.5 py-1.5 rounded-lg text-xs transition-colors
                                    ${
                                      isItemActiveState
                                        ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300"
                                        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
                                    }
                                  `}
                                >
                                  <ItemIcon size={14} className={isItemActiveState ? "text-emerald-600" : "text-gray-400"} />
                                  <span className="font-medium">{item.label}</span>
                                </Link>
                              </li>
                            )
                          })}
                        </ul>
                      )}
                    </li>
                  )
                })}
              </ul>
            </div>

            {/* Admin Section */}
            <div>
              {!isMinimal && (
                <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1.5 px-2">
                  Administration
                </h3>
              )}
              <ul className="space-y-0.5">
                {adminItems.map((item) => {
                  const Icon = item.icon
                  const isActive = pathname === item.href

                  return (
                    <li key={item.href}>
                      <Link
                        href={item.href}
                        onClick={onClose}
                        className={`
                          flex items-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors
                          ${
                            isActive
                              ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300"
                              : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
                          }
                          ${isMinimal ? "justify-center" : ""}
                        `}
                        title={isMinimal ? item.label : ""}
                      >
                        <Icon size={16} />
                        {!isMinimal && <span className="font-medium">{item.label}</span>}
                      </Link>
                    </li>
                  )
                })}
              </ul>
            </div>
          </nav>

          {/* Bottom Section - Settings & User Options */}
          <div className="border-t border-gray-200 dark:border-gray-700 mt-auto">
            {/* Goal Progress (only when expanded) */}
            {!isMinimal && (
              <div className="p-2.5">
                <div className="bg-emerald-50 dark:bg-emerald-900/20 rounded-lg p-2.5">
                  <div className="flex items-center gap-1.5 mb-1.5">
                    <TrendingUp size={12} className="text-emerald-600" />
                    <span className="text-xs font-medium text-gray-900 dark:text-gray-100">Monthly Goal</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mb-1.5">
                    <div className="bg-emerald-500 h-1.5 rounded-full w-3/4"></div>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">75% completed</p>
                </div>
              </div>
            )}

            {/* User Options */}
            <div className="p-2.5 space-y-0.5">
              {!isMinimal && (
                <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1.5 px-2">
                  {t('nav.settings')}
                </h3>
              )}

              {/* Language Selector */}
              <div className="relative">
                <button
                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                  className={`
                    flex items-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors w-full
                    text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800
                    ${isMinimal ? "justify-center" : ""}
                  `}
                  title={isMinimal ? `Language: ${currentLanguage.name}` : ""}
                >
                  <Globe size={16} />
                  {!isMinimal && (
                    <span className="font-medium">
                      {currentLanguage.flag} {currentLanguage.name}
                    </span>
                  )}
                </button>

                {showLanguageMenu && !isMinimal && (
                  <div className="absolute bottom-full left-0 mb-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1.5 z-50">
                    {languages.map((lang) => (
                      <button
                        key={lang.code}
                        onClick={() => {
                          setLanguage(lang.code)
                          setShowLanguageMenu(false)
                        }}
                        className={`w-full flex items-center gap-2.5 px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                          language === lang.code
                            ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                            : "text-gray-700 dark:text-gray-300"
                        }`}
                      >
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Theme Toggle */}
              <button
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className={`
                  flex items-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors w-full
                  text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800
                  ${isMinimal ? "justify-center" : ""}
                `}
                title={isMinimal ? (mounted && theme === "dark" ? "Switch to Light Mode" : "Switch to Dark Mode") : ""}
              >
                {mounted ? (
                  theme === "dark" ? <Sun size={16} /> : <Moon size={16} />
                ) : (
                  <Sun size={16} />
                )}
                {!isMinimal && (
                  <span className="font-medium">
                    {mounted ? (theme === "dark" ? "Light Mode" : "Dark Mode") : "Light Mode"}
                  </span>
                )}
              </button>

              {/* Notifications */}
              <button
                className={`
                  flex items-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors w-full relative
                  text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800
                  ${isMinimal ? "justify-center" : ""}
                `}
                title={isMinimal ? "Notifications" : ""}
              >
                <div className="relative">
                  <Bell size={16} />
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </div>
                {!isMinimal && <span className="font-medium">Notifications</span>}
              </button>

              {/* User Profile */}
              <div className="space-y-1">
                <button
                  className={`
                    flex items-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors w-full
                    text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800
                    ${isMinimal ? "justify-center" : ""}
                  `}
                  title={isMinimal ? user?.name || 'User Profile' : ""}
                >
                  <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <User size={10} className="text-white" />
                  </div>
                  {!isMinimal && (
                    <div className="flex-1 text-left">
                      <div className="font-medium">{user?.name || 'User'}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-500">{user?.email}</div>
                    </div>
                  )}
                </button>

                {/* Logout Button */}
                <button
                  onClick={logout}
                  className={`
                    flex items-center gap-2.5 px-2.5 py-2 rounded-lg text-xs transition-colors w-full
                    text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20
                    ${isMinimal ? "justify-center" : ""}
                  `}
                  title={isMinimal ? 'Logout' : ""}
                >
                  <LogOut size={14} />
                  {!isMinimal && <span className="font-medium">Đăng xuất</span>}
                </button>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Click outside to close language menu */}
      {showLanguageMenu && <div className="fixed inset-0 z-40" onClick={() => setShowLanguageMenu(false)} />}
    </>
  )
}
