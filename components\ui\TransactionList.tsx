"use client"

import React, { useState, useEffect, useRef } from "react"
import { TransactionCard } from "./TransactionCard"
import { ChevronDown, ChevronUp, SortAsc, SortDesc, Filter } from "lucide-react"
import { getVisibleColumns, RESPONSIVE_BREAKPOINTS, isColumnVisible, generateGridTemplate } from "../../config/transactionTableConfig"
import { TransactionPagination, useTransactionPagination } from "./TransactionPagination"
import { PortalDropdown } from "./PortalDropdown"

interface Transaction {
  id: number
  date: string
  description: string
  category: string
  categoryColor?: string
  categoryIcon?: string
  account: string
  transferFrom: string
  type: "income" | "expense" | "transfer"
  amount: string
  rawAmount: number
  transferTo?: string
}

interface TransactionListProps {
  transactions: Transaction[]
  onEdit?: (transaction: Transaction) => void
  onDelete?: (id: number) => void
  showFilters?: boolean
}

type SortField = "date" | "description" | "amount" | "category" | "transferFrom" | "transferTo"
type SortDirection = "asc" | "desc"

export function TransactionList({
  transactions,
  onEdit,
  onDelete,
  showFilters = true
}: TransactionListProps) {
  // Pagination state
  const {
    currentPage,
    itemsPerPage,
    totalPages,
    startIndex,
    endIndex,
    handlePageChange,
    handleItemsPerPageChange
  } = useTransactionPagination(transactions.length)

  const [isLoading, setIsLoading] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const sortButtonRef = useRef<HTMLButtonElement>(null)
  const [sortField, setSortField] = useState<SortField>("date")
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc")
  const [showSortOptions, setShowSortOptions] = useState(false)
  const [containerWidth, setContainerWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : RESPONSIVE_BREAKPOINTS.desktop
  )
  const containerRef = useRef<HTMLDivElement>(null)

  // Track container width for responsive behavior
  useEffect(() => {
    if (!containerRef.current) return

    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        // Use content box width for more accurate measurement
        const width = entry.contentBoxSize?.[0]?.inlineSize || entry.contentRect.width
        setContainerWidth(width)
      }
    })

    resizeObserver.observe(containerRef.current)
    return () => resizeObserver.disconnect()
  }, [])

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc")
    }
    setShowSortOptions(false)
  }

  const sortedTransactions = [...transactions].sort((a, b) => {
    let aValue: any
    let bValue: any

    switch (sortField) {
      case "date":
        aValue = new Date(a.date).getTime()
        bValue = new Date(b.date).getTime()
        break
      case "amount":
        aValue = a.rawAmount
        bValue = b.rawAmount
        break
      case "description":
        aValue = a.description.toLowerCase()
        bValue = b.description.toLowerCase()
        break
      case "category":
        aValue = a.category.toLowerCase()
        bValue = b.category.toLowerCase()
        break
      case "transferFrom":
        aValue = a.transferFrom.toLowerCase()
        bValue = b.transferFrom.toLowerCase()
        break
      case "transferTo":
        aValue = (a.transferTo || "").toLowerCase()
        bValue = (b.transferTo || "").toLowerCase()
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
    return 0
  })

  // Get paginated transactions
  const paginatedTransactions = sortedTransactions.slice(startIndex, endIndex)

  // Handle page change with loading state and scroll to top
  const handlePageChangeWithLoading = async (page: number) => {
    setIsLoading(true)
    handlePageChange(page)

    // Scroll to top of transaction list
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
    }

    // Simulate loading delay for better UX
    setTimeout(() => setIsLoading(false), 150)
  }

  const getSortLabel = (field: SortField) => {
    const labels = {
      date: "Date",
      description: "Description",
      amount: "Amount",
      category: "Category",
      transferFrom: "Transfer From",
      transferTo: "Transfer To"
    }
    return labels[field]
  }

  // Get visible columns for current container width
  const visibleColumns = getVisibleColumns(containerWidth)
  const isMobile = containerWidth < RESPONSIVE_BREAKPOINTS.tablet
  const gridTemplate = generateGridTemplate(visibleColumns)

  if (transactions.length === 0) {
    return (
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-3xl border border-gray-200/50 dark:border-gray-700/50 p-8 text-center">
        <div className="text-gray-400 dark:text-gray-500 mb-2">
          <Filter size={48} className="mx-auto mb-4 opacity-50" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No transactions found
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Try adjusting your filters or add some transactions to get started.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4" ref={containerRef}>
      {/* Sort Controls */}
      {showFilters && (
        <div className="bg-white/70 dark:bg-gray-800/70 rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {sortedTransactions.length} transaction{sortedTransactions.length !== 1 ? 's' : ''} total
              </span>
            </div>
            
            <div className="relative">
              <button
                ref={sortButtonRef}
                onClick={() => setShowSortOptions(!showSortOptions)}
                className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                {sortDirection === "asc" ? <SortAsc size={16} /> : <SortDesc size={16} />}
                Sort by {getSortLabel(sortField)}
                {showSortOptions ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>

              <PortalDropdown
                isOpen={showSortOptions}
                onClose={() => setShowSortOptions(false)}
                triggerRef={sortButtonRef}
              >
                {(["date", "amount", "description", "category", "transferFrom", "transferTo"] as SortField[]).map((field) => (
                  <button
                    key={field}
                    onClick={() => handleSort(field)}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                      sortField === field
                        ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
                        : "text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      {getSortLabel(field)}
                      {sortField === field && (
                        sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                      )}
                    </div>
                  </button>
                ))}
              </PortalDropdown>
            </div>
          </div>
        </div>
      )}

      {/* Sticky Table Header - Desktop Only */}
      <div className="hidden md:block sticky top-0 z-10 bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4 shadow-sm">
        <div
          className="grid gap-3 items-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
          style={{
            gridTemplateColumns: gridTemplate
          }}
        >
          {visibleColumns.map((column) => (
            <div
              key={column.key}
              className={column.align === "right" ? "text-right" : column.align === "center" ? "text-center" : "text-left"}
            >
              {column.label}
            </div>
          ))}
        </div>
      </div>

      {/* Scrollable Transaction Container */}
      <div
        ref={scrollContainerRef}
        className="max-h-[600px] lg:max-h-[70vh] overflow-y-auto overflow-x-visible scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent"
        style={{ scrollBehavior: 'smooth' }}
      >
        <div className="space-y-3">
          {isLoading ? (
            // Loading skeleton
            <div className="space-y-3">
              {Array.from({ length: Math.min(itemsPerPage, 5) }).map((_, index) => (
                <div key={index} className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4 animate-pulse">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      <div className="w-32 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    </div>
                    <div className="w-20 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Actual transaction cards
            paginatedTransactions.map((transaction) => (
              <TransactionCard
                key={transaction.id}
                transaction={transaction}
                onEdit={onEdit}
                onDelete={onDelete}
                visibleColumns={visibleColumns}
                screenWidth={containerWidth}
              />
            ))
          )}
        </div>
      </div>

      {/* Pagination Controls */}
      <TransactionPagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={sortedTransactions.length}
        itemsPerPage={itemsPerPage}
        onPageChange={handlePageChangeWithLoading}
        onItemsPerPageChange={handleItemsPerPageChange}
        isLoading={isLoading}
      />
    </div>
  )
}
