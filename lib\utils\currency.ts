/**
 * Currency Formatting Utilities
 * 
 * Shared utilities for currency formatting and calculations
 */

// ==================== CURRENCY CONFIGURATION ====================

export interface CurrencyConfig {
  code: string
  symbol: string
  locale: string
  decimals: number
}

export const CURRENCIES: Record<string, CurrencyConfig> = {
  VND: {
    code: 'VND',
    symbol: '₫',
    locale: 'vi-VN',
    decimals: 0
  },
  USD: {
    code: 'USD',
    symbol: '$',
    locale: 'en-US',
    decimals: 2
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    locale: 'de-DE',
    decimals: 2
  }
}

export const DEFAULT_CURRENCY = 'VND'

// ==================== FORMATTING FUNCTIONS ====================

/**
 * Format currency amount with proper locale and symbol
 */
export function formatCurrency(
  amount: number,
  currencyCode: string = DEFAULT_CURRENCY,
  options?: {
    showSymbol?: boolean
    showCode?: boolean
    minimumFractionDigits?: number
    maximumFractionDigits?: number
  }
): string {
  const currency = CURRENCIES[currencyCode] || CURRENCIES[DEFAULT_CURRENCY]
  const {
    showSymbol = true,
    showCode = false,
    minimumFractionDigits = currency.decimals,
    maximumFractionDigits = currency.decimals
  } = options || {}

  const formatter = new Intl.NumberFormat(currency.locale, {
    style: showSymbol ? 'currency' : 'decimal',
    currency: currency.code,
    minimumFractionDigits,
    maximumFractionDigits
  })

  let formatted = formatter.format(amount)

  // Add currency code if requested
  if (showCode && !showSymbol) {
    formatted += ` ${currency.code}`
  }

  return formatted
}

/**
 * Format currency for Vietnamese locale (most common use case)
 */
export function formatVND(amount: number): string {
  return formatCurrency(amount, 'VND', {
    showSymbol: true,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  })
}

/**
 * Format currency for display in tables/cards (compact format)
 */
export function formatCurrencyCompact(
  amount: number,
  currencyCode: string = DEFAULT_CURRENCY
): string {
  const absAmount = Math.abs(amount)
  
  if (absAmount >= 1000000000) {
    return formatCurrency(amount / 1000000000, currencyCode) + 'B'
  }
  if (absAmount >= 1000000) {
    return formatCurrency(amount / 1000000, currencyCode) + 'M'
  }
  if (absAmount >= 1000) {
    return formatCurrency(amount / 1000, currencyCode) + 'K'
  }
  
  return formatCurrency(amount, currencyCode)
}

/**
 * Format currency without symbol (numbers only)
 */
export function formatCurrencyNumber(
  amount: number,
  currencyCode: string = DEFAULT_CURRENCY
): string {
  return formatCurrency(amount, currencyCode, {
    showSymbol: false,
    showCode: false
  })
}

/**
 * Format currency with sign indicator for positive/negative amounts
 */
export function formatCurrencyWithSign(
  amount: number,
  currencyCode: string = DEFAULT_CURRENCY,
  options?: {
    positiveSign?: string
    negativeSign?: string
    zeroSign?: string
  }
): string {
  const {
    positiveSign = '+',
    negativeSign = '-',
    zeroSign = ''
  } = options || {}

  const formatted = formatCurrency(Math.abs(amount), currencyCode)
  
  if (amount > 0) {
    return `${positiveSign}${formatted}`
  } else if (amount < 0) {
    return `${negativeSign}${formatted}`
  } else {
    return `${zeroSign}${formatted}`
  }
}

// ==================== PARSING FUNCTIONS ====================

/**
 * Parse currency string back to number
 */
export function parseCurrency(
  value: string,
  currencyCode: string = DEFAULT_CURRENCY
): number {
  const currency = CURRENCIES[currencyCode] || CURRENCIES[DEFAULT_CURRENCY]
  
  // Remove currency symbols and non-numeric characters except decimal point
  let cleaned = value
    .replace(new RegExp(`[${currency.symbol}${currency.code}]`, 'g'), '')
    .replace(/[^\d.,\-]/g, '')
  
  // Handle different decimal separators
  if (currency.locale.includes('de') || currency.locale.includes('fr')) {
    // European format: 1.234,56
    cleaned = cleaned.replace(/\./g, '').replace(',', '.')
  } else {
    // US format: 1,234.56
    cleaned = cleaned.replace(/,/g, '')
  }
  
  const parsed = parseFloat(cleaned)
  return isNaN(parsed) ? 0 : parsed
}

/**
 * Validate currency input
 */
export function isValidCurrencyInput(value: string): boolean {
  const parsed = parseCurrency(value)
  return !isNaN(parsed) && isFinite(parsed)
}

// ==================== CALCULATION UTILITIES ====================

/**
 * Add currency amounts with proper precision
 */
export function addCurrency(
  amount1: number,
  amount2: number,
  currencyCode: string = DEFAULT_CURRENCY
): number {
  const currency = CURRENCIES[currencyCode] || CURRENCIES[DEFAULT_CURRENCY]
  const multiplier = Math.pow(10, currency.decimals)
  
  return Math.round((amount1 * multiplier + amount2 * multiplier)) / multiplier
}

/**
 * Subtract currency amounts with proper precision
 */
export function subtractCurrency(
  amount1: number,
  amount2: number,
  currencyCode: string = DEFAULT_CURRENCY
): number {
  const currency = CURRENCIES[currencyCode] || CURRENCIES[DEFAULT_CURRENCY]
  const multiplier = Math.pow(10, currency.decimals)
  
  return Math.round((amount1 * multiplier - amount2 * multiplier)) / multiplier
}

/**
 * Multiply currency amount with proper precision
 */
export function multiplyCurrency(
  amount: number,
  multiplier: number,
  currencyCode: string = DEFAULT_CURRENCY
): number {
  const currency = CURRENCIES[currencyCode] || CURRENCIES[DEFAULT_CURRENCY]
  const precision = Math.pow(10, currency.decimals)
  
  return Math.round((amount * multiplier * precision)) / precision
}

/**
 * Calculate percentage of currency amount
 */
export function calculatePercentage(
  amount: number,
  percentage: number,
  currencyCode: string = DEFAULT_CURRENCY
): number {
  return multiplyCurrency(amount, percentage / 100, currencyCode)
}

// ==================== COMPARISON UTILITIES ====================

/**
 * Compare currency amounts with precision handling
 */
export function compareCurrency(
  amount1: number,
  amount2: number,
  currencyCode: string = DEFAULT_CURRENCY
): number {
  const currency = CURRENCIES[currencyCode] || CURRENCIES[DEFAULT_CURRENCY]
  const multiplier = Math.pow(10, currency.decimals)
  
  const rounded1 = Math.round(amount1 * multiplier)
  const rounded2 = Math.round(amount2 * multiplier)
  
  return rounded1 - rounded2
}

/**
 * Check if currency amounts are equal
 */
export function isCurrencyEqual(
  amount1: number,
  amount2: number,
  currencyCode: string = DEFAULT_CURRENCY
): boolean {
  return compareCurrency(amount1, amount2, currencyCode) === 0
}

// ==================== DISPLAY UTILITIES ====================

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currencyCode: string = DEFAULT_CURRENCY): string {
  const currency = CURRENCIES[currencyCode] || CURRENCIES[DEFAULT_CURRENCY]
  return currency.symbol
}

/**
 * Get currency display name
 */
export function getCurrencyName(currencyCode: string = DEFAULT_CURRENCY): string {
  const names: Record<string, string> = {
    VND: 'Vietnamese Dong',
    USD: 'US Dollar',
    EUR: 'Euro'
  }
  return names[currencyCode] || currencyCode
}

/**
 * Format currency for different contexts
 */
export const currencyFormatters = {
  // For display in cards and lists
  display: (amount: number, currency?: string) => formatVND(amount),
  
  // For input fields
  input: (amount: number, currency?: string) => formatCurrencyNumber(amount, currency),
  
  // For compact display (with K, M, B suffixes)
  compact: (amount: number, currency?: string) => formatCurrencyCompact(amount, currency),
  
  // For signed amounts (income/expense)
  signed: (amount: number, currency?: string) => formatCurrencyWithSign(amount, currency),
  
  // For accounting (parentheses for negative)
  accounting: (amount: number, currency?: string) => {
    if (amount < 0) {
      return `(${formatCurrency(Math.abs(amount), currency)})`
    }
    return formatCurrency(amount, currency)
  }
}

// ==================== EXPORT DEFAULT FORMATTER ====================

// Most commonly used formatter for Vietnamese currency
export default formatVND
