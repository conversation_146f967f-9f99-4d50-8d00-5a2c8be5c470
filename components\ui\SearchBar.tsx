"use client"

import React, { useState, useRef } from "react"
import { Search, X, Filter, ChevronDown } from "lucide-react"
import { SearchableCombobox } from "./SearchableCombobox"
import { PortalDropdown } from "./PortalDropdown"

interface SearchBarProps {
  // Basic search
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  
  // Advanced search with combobox
  searchType?: "text" | "combobox"
  options?: Array<{
    value: string
    label: string
  }>
  
  // Quick filters
  quickFilters?: Array<{
    key: string
    label: string
    value: any
    active?: boolean
    onClick: (key: string, value: any) => void
  }>
  
  // Filter dropdown
  showFilterButton?: boolean
  onFilterClick?: () => void
  hasActiveFilters?: boolean
  
  // Styling
  size?: "sm" | "md" | "lg"
  variant?: "default" | "minimal"
  
  // Events
  onClear?: () => void
  onEnter?: (value: string) => void
}

const sizeClasses = {
  sm: "h-8 text-xs px-3",
  md: "h-10 text-sm px-4", 
  lg: "h-12 text-base px-5"
}

const iconSizes = {
  sm: 14,
  md: 16,
  lg: 18
}

export function SearchBar({
  value = "",
  onChange,
  placeholder = "Tìm kiếm...",
  className = "",
  searchType = "text",
  options = [],
  quickFilters = [],
  showFilterButton = false,
  onFilterClick,
  hasActiveFilters = false,
  size = "md",
  variant = "default",
  onClear,
  onEnter
}: SearchBarProps) {
  const [internalValue, setInternalValue] = useState(value)
  const [showQuickFilters, setShowQuickFilters] = useState(false)
  const quickFiltersRef = useRef<HTMLButtonElement>(null)
  
  const handleChange = (newValue: string) => {
    setInternalValue(newValue)
    onChange?.(newValue)
  }
  
  const handleClear = () => {
    setInternalValue("")
    onChange?.("")
    onClear?.()
  }
  
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onEnter?.(internalValue)
    }
  }
  
  const baseClasses = `
    relative flex items-center gap-2
    ${variant === "minimal" 
      ? "bg-transparent border-0 focus-within:bg-white/50 dark:focus-within:bg-gray-800/50" 
      : "bg-white/80 dark:bg-gray-700/80 border border-gray-200 dark:border-gray-600"
    }
    rounded-lg transition-all duration-200
    focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:border-blue-500
    ${className}
  `
  
  return (
    <div className="flex items-center gap-2 w-full">
      {/* Main Search Input */}
      <div className={baseClasses}>
        <Search 
          className="text-gray-400 flex-shrink-0 ml-3" 
          size={iconSizes[size]} 
        />
        
        {searchType === "text" ? (
          <input
            type="text"
            value={internalValue}
            onChange={(e) => handleChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            className={`
              flex-1 bg-transparent border-0 outline-none
              text-gray-900 dark:text-gray-100 
              placeholder-gray-500 dark:placeholder-gray-400
              ${sizeClasses[size]}
            `}
          />
        ) : (
          <div className="flex-1">
            <SearchableCombobox
              value={internalValue}
              onChange={handleChange}
              options={options}
              placeholder={placeholder}
              className="border-0 bg-transparent focus:ring-0"
            />
          </div>
        )}
        
        {internalValue && (
          <button
            onClick={handleClear}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-3 transition-colors"
          >
            <X size={iconSizes[size]} />
          </button>
        )}
      </div>
      
      {/* Quick Filters */}
      {quickFilters.length > 0 && (
        <div className="relative">
          <button
            ref={quickFiltersRef}
            onClick={() => setShowQuickFilters(!showQuickFilters)}
            className={`
              flex items-center gap-1 px-3 py-1 rounded-lg text-xs font-medium 
              transition-all duration-200 border
              ${quickFilters.some(f => f.active)
                ? "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800"
                : "bg-white/80 text-gray-600 dark:bg-gray-700/80 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-600"
              }
              ${sizeClasses[size]}
            `}
          >
            <span>Bộ lọc nhanh</span>
            <ChevronDown size={12} />
          </button>
          
          <PortalDropdown
            isOpen={showQuickFilters}
            onClose={() => setShowQuickFilters(false)}
            triggerRef={quickFiltersRef}
          >
            {quickFilters.map((filter) => (
              <button
                key={filter.key}
                onClick={() => {
                  filter.onClick(filter.key, filter.value)
                  setShowQuickFilters(false)
                }}
                className={`
                  w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 
                  transition-colors
                  ${filter.active
                    ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-700 dark:text-gray-300"
                  }
                `}
              >
                {filter.label}
              </button>
            ))}
          </PortalDropdown>
        </div>
      )}
      
      {/* Filter Button */}
      {showFilterButton && (
        <button
          onClick={onFilterClick}
          className={`
            flex items-center gap-1 px-3 py-1 rounded-lg text-xs font-medium 
            transition-all duration-200 border
            ${hasActiveFilters
              ? "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800"
              : "bg-white/80 text-gray-600 dark:bg-gray-700/80 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-600"
            }
            ${sizeClasses[size]}
          `}
        >
          <Filter size={iconSizes[size]} />
          <span className="whitespace-nowrap">Bộ lọc</span>
        </button>
      )}
    </div>
  )
}

// Specialized search bars for common use cases
export function TransactionSearchBar({ 
  onSearch, 
  onFilterClick, 
  hasActiveFilters,
  accounts = [],
  categories = []
}: {
  onSearch?: (term: string) => void
  onFilterClick?: () => void
  hasActiveFilters?: boolean
  accounts?: Array<{ id: number, name: string }>
  categories?: Array<{ id: number, name: string }>
}) {
  const quickFilters = [
    {
      key: "today",
      label: "Hôm nay",
      value: "today",
      onClick: (key: string) => console.log("Filter:", key)
    },
    {
      key: "week",
      label: "Tuần này", 
      value: "week",
      onClick: (key: string) => console.log("Filter:", key)
    },
    {
      key: "month",
      label: "Tháng này",
      value: "month", 
      onClick: (key: string) => console.log("Filter:", key)
    }
  ]
  
  return (
    <SearchBar
      placeholder="Tìm kiếm giao dịch..."
      onChange={onSearch}
      quickFilters={quickFilters}
      showFilterButton={true}
      onFilterClick={onFilterClick}
      hasActiveFilters={hasActiveFilters}
      size="sm"
    />
  )
}

export function ContactSearchBar({ 
  onSearch,
  searchType = "text"
}: {
  onSearch?: (term: string) => void
  searchType?: "text" | "combobox"
}) {
  return (
    <SearchBar
      placeholder="Tìm kiếm liên hệ..."
      onChange={onSearch}
      searchType={searchType}
      size="sm"
    />
  )
}

export function AccountSearchBar({ 
  onSearch,
  accounts = []
}: {
  onSearch?: (term: string) => void
  accounts?: Array<{ id: number, name: string }>
}) {
  const options = accounts.map(account => ({
    value: account.id.toString(),
    label: account.name
  }))
  
  return (
    <SearchBar
      placeholder="Tìm kiếm tài khoản..."
      onChange={onSearch}
      searchType="combobox"
      options={options}
      size="sm"
    />
  )
}
