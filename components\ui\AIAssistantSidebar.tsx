"use client"

import React, { useState, useRef, useEffect } from "react"
import { <PERSON>, Send, Bo<PERSON>, User, <PERSON><PERSON><PERSON>, ArrowRight, GripVertical } from "lucide-react"
import { Button } from "./button"
import { AIService } from "../../services/aiService"
import { AI_ASSISTANT_CONFIG, isMobile } from "../../config/aiAssistantConfig"
import { useAISidebarIntegration } from "@/contexts/GlobalSidebarContext"

interface Message {
  id: string
  type: "user" | "assistant"
  content: string
  timestamp: Date
  suggestions?: string[]
  quickActions?: Array<{
    label: string
    action: string
  }>
}

interface AIAssistantSidebarProps {
  isOpen: boolean
  onClose: () => void
  sidebarWidth?: number
  isResizing?: boolean
  onResizeStart?: (e: React.MouseEvent) => void
  style?: React.CSSProperties
}

export function AIAssistantSidebar({
  isOpen,
  onClose,
  sidebarWidth = AI_ASSISTANT_CONFIG.DEFAULT_WIDTH,
  isResizing = false,
  onResizeStart,
  style = {}
}: AIAssistantSidebarProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      type: "assistant",
      content: "Xin chào! Tôi là AI Assistant của bạn. Tôi có thể giúp bạn:\n\n• Phân tích chi tiêu và thu nhập\n• Tư vấn quản lý ngân sách\n• Hướng dẫn sử dụng các tính năng\n• Trả lời câu hỏi về tài chính cá nhân\n\nBạn cần hỗ trợ gì hôm nay?",
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Integrate with global sidebar state
  useAISidebarIntegration(isOpen, sidebarWidth)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen) {
      inputRef.current?.focus()
    }
  }, [isOpen])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")
    setIsLoading(true)

    // Generate AI response using AIService
    setTimeout(() => {
      const aiResponseData = AIService.generateResponse(userMessage.content)
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: aiResponseData.content,
        suggestions: aiResponseData.suggestions,
        quickActions: aiResponseData.quickActions,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, aiResponse])
      setIsLoading(false)
    }, 1000 + Math.random() * 1500)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion)
    handleSendMessage()
  }

  const handleQuickActionClick = (action: string) => {
    // Handle quick actions (could navigate to specific pages or open modals)
    console.log("Quick action:", action)
    // For now, just send a message about the action
    const actionMessage = `Hướng dẫn: ${action}`
    setInputValue(actionMessage)
    handleSendMessage()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-[55] lg:hidden transition-opacity duration-300"
          onClick={onClose}
        />
      )}

      {/* AI Assistant Sidebar */}
      <div
        className={`fixed right-0 top-0 h-full bg-white dark:bg-gray-900 shadow-xl z-[60] transform ${
          isOpen ? "translate-x-0" : "translate-x-full"
        } ${
          isResizing
            ? "border-l-2 border-blue-500"
            : "border-l border-gray-200 dark:border-gray-700"
        }`}
        style={{
          ...style,
          width: isMobile() ? '100%' : `${sidebarWidth}px`,
          transition: isResizing ? 'none' : `all ${AI_ASSISTANT_CONFIG.TRANSITION_DURATION}ms ${AI_ASSISTANT_CONFIG.TRANSITION_EASING}`
        }}
      >
        {/* Resize Handle */}
        {!isMobile() && onResizeStart && (
          <div
            className="absolute top-0 h-full cursor-col-resize group"
            onMouseDown={onResizeStart}
            style={{
              left: `-8px`,
              width: `16px`,
              zIndex: AI_ASSISTANT_CONFIG.Z_INDEX.RESIZE_HANDLE
            }}
          >
            {/* Visual indicator line */}
            <div className="absolute left-1/2 top-0 h-full w-0.5 bg-gray-300 dark:bg-gray-600 group-hover:bg-blue-500 transition-colors duration-200 transform -translate-x-1/2" />

            {/* Grip icon */}
            <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white dark:bg-gray-800 rounded p-1 shadow-sm">
              <GripVertical className="w-3 h-3 text-blue-500" />
            </div>
          </div>
        )}

      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">AI Assistant</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Hỗ trợ tài chính 24/7
              {isResizing && !isMobile() && (
                <span className="ml-2 text-blue-500 font-mono">
                  {sidebarWidth}px
                </span>
              )}
            </p>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="w-8 h-8 p-0"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Chat Content */}
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 h-[calc(100vh-140px)]">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
              >
                <div className={`flex items-start space-x-2 max-w-[80%] ${
                  message.type === "user" ? "flex-row-reverse space-x-reverse" : ""
                }`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.type === "user" 
                      ? "bg-blue-500" 
                      : "bg-gradient-to-r from-purple-500 to-pink-500"
                  }`}>
                    {message.type === "user" ? (
                      <User className="w-4 h-4 text-white" />
                    ) : (
                      <Bot className="w-4 h-4 text-white" />
                    )}
                  </div>
                  <div className="space-y-2">
                    <div className={`rounded-lg p-3 ${
                      message.type === "user"
                        ? "bg-blue-500 text-white"
                        : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    }`}>
                      <p className="text-sm whitespace-pre-line">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.type === "user" ? "text-blue-100" : "text-gray-500 dark:text-gray-400"
                      }`}>
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>

                    {/* Quick Actions */}
                    {message.type === "assistant" && message.quickActions && (
                      <div className="space-y-1">
                        {message.quickActions.map((action, index) => (
                          <button
                            key={index}
                            onClick={() => handleQuickActionClick(action.action)}
                            className="flex items-center space-x-2 w-full text-left px-3 py-2 text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                          >
                            <ArrowRight className="w-3 h-3" />
                            <span>{action.label}</span>
                          </button>
                        ))}
                      </div>
                    )}

                    {/* Suggestions */}
                    {message.type === "assistant" && message.suggestions && (
                      <div className="space-y-1">
                        <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Gợi ý câu hỏi:</p>
                        {message.suggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="block w-full text-left px-3 py-2 text-xs bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex items-start space-x-2 max-w-[80%]">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Hỏi về quản lý tài chính..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 text-sm"
                disabled={isLoading}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                size="sm"
                className="px-3"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </> 
      </div>
    </>
  )
}
