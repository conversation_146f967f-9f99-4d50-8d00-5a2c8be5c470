/**
 * AccountPaymentApp API Client Service
 * 
 * Client-side service for account-payment app link operations
 */

import { AccountPaymentApp, Account } from '@/lib/generated/prisma'
import { PaginatedResponse, ApiResponse } from '@/types/api'
import { ApiClient } from './apiClient'

// ==================== TYPES ====================

export interface AccountPaymentAppFilters {
  accountId?: string
  paymentAppId?: string
  isActive?: boolean
}

export interface AccountPaymentAppQueryParams extends AccountPaymentAppFilters {
  page?: number
  limit?: number
}

export interface CreateAccountPaymentAppRequest {
  accountId: string
  paymentAppId: string
}

export interface UpdateAccountPaymentAppRequest {
  isActive?: boolean
}



// ==================== API CLIENT ====================

export class AccountPaymentAppApiService {
  private static readonly BASE_URL = '/account-payment-apps'

  /**
   * Get all account-payment app links with pagination and filtering
   */
  static async getAccountPaymentApps(params: AccountPaymentAppQueryParams = {}): Promise<PaginatedResponse<AccountPaymentApp>> {
    const searchParams = new URLSearchParams()
    
    if (params.page) searchParams.set('page', params.page.toString())
    if (params.limit) searchParams.set('limit', params.limit.toString())
    if (params.accountId) searchParams.set('accountId', params.accountId)
    if (params.paymentAppId) searchParams.set('paymentAppId', params.paymentAppId)
    if (params.isActive !== undefined) searchParams.set('isActive', params.isActive.toString())

    const url = `${this.BASE_URL}?${searchParams.toString()}` 
    return ApiClient.get<PaginatedResponse<AccountPaymentApp>>(url)
  }

  /**
   * Get account-payment app link by ID
   */
  static async getAccountPaymentApp(id: string): Promise<ApiResponse<AccountPaymentApp>> {
    return ApiClient.get<ApiResponse<AccountPaymentApp>>(`${this.BASE_URL}/${id}`)
  }

  /**
   * Create new account-payment app link
   */
  static async createAccountPaymentApp(data: CreateAccountPaymentAppRequest): Promise<ApiResponse<AccountPaymentApp>> {
    return ApiClient.post<ApiResponse<AccountPaymentApp>>(this.BASE_URL, data)
  }

  /**
   * Update account-payment app link
   */
  static async updateAccountPaymentApp(id: string, data: UpdateAccountPaymentAppRequest): Promise<ApiResponse<AccountPaymentApp>> {
    return ApiClient.put<ApiResponse<AccountPaymentApp>>(`${this.BASE_URL}/${id}`, data)
  }

  /**
   * Delete account-payment app link (soft delete)
   */
  static async deleteAccountPaymentApp(id: string): Promise<{ success: boolean }> {
    return ApiClient.delete<{ success: boolean }>(`${this.BASE_URL}/${id}`)
  }

  /**
   * Get payment apps linked to specific account
   */
  static async getPaymentAppsByAccount(accountId: string): Promise<ApiResponse<AccountPaymentApp[]>> {
    return ApiClient.get<ApiResponse<AccountPaymentApp[]>>(`/accounts/${accountId}/payment-apps`)
  }

  /**
   * Link payment app to account
   */
  static async linkPaymentAppToAccount(
    accountId: string, 
    paymentAppId: string
  ): Promise<ApiResponse<AccountPaymentApp>> {
    return ApiClient.post<ApiResponse<AccountPaymentApp>>(`/accounts/${accountId}/payment-apps`, {
      paymentAppId
    })
  }

  /**
   * Unlink payment app from account
   */
  static async unlinkPaymentAppFromAccount(
    accountId: string, 
    paymentAppId: string
  ): Promise<{ success: boolean }> {
    return ApiClient.delete<{ success: boolean }>(
      `/accounts/${accountId}/payment-apps?paymentAppId=${paymentAppId}`
    )
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get active links only
   */
  static async getActiveAccountPaymentApps(): Promise<PaginatedResponse<AccountPaymentApp>> {
    return this.getAccountPaymentApps({ isActive: true })
  }

  /**
   * Get links for specific account
   */
  static async getLinksByAccount(accountId: string): Promise<PaginatedResponse<AccountPaymentApp>> {
    return this.getAccountPaymentApps({ accountId })
  }

  /**
   * Get links for specific payment app
   */
  static async getLinksByPaymentApp(paymentAppId: string): Promise<PaginatedResponse<AccountPaymentApp>> {
    return this.getAccountPaymentApps({ paymentAppId })
  }

  /**
   * Check if link exists
   */
  static async linkExists(id: string): Promise<boolean> {
    try {
      await this.getAccountPaymentApp(id)
      return true
    } catch {
      return false
    }
  }

  /**
   * Check if account and payment app are already linked
   */
  static async isLinked(accountId: string, paymentAppId: string): Promise<boolean> {
    try {
      const links = await this.getAccountPaymentApps({ 
        accountId, 
        paymentAppId, 
        isActive: true 
      })
      return links.success && links.data && links.data.length > 0 || false
    } catch {
      return false
    }
  }

  /**
   * Get all payment apps available for linking to an account
   */
  static async getAvailablePaymentApps(excludeAccountId?: string): Promise<ApiResponse<Account[]>> {
    const searchParams = new URLSearchParams()
    if (excludeAccountId) {
      searchParams.set('excludeAccountId', excludeAccountId)
    }

    const url = `/accounts/payment-apps/available?${searchParams.toString()}`
    return await ApiClient.get<ApiResponse<Account[]>>(url)
  }


  /**
   * Bulk unlink multiple payment apps from an account
   */
  static async bulkUnlinkPaymentApps(
    accountId: string, 
    paymentAppIds: string[]
  ): Promise<{ success: boolean }[]> {
    const promises = paymentAppIds.map(paymentAppId =>
      this.unlinkPaymentAppFromAccount(accountId, paymentAppId)
    )
    return Promise.all(promises)
  }
}
