import jwt from 'jsonwebtoken'
import { AUTH_CONFIG } from './config'

// JWT payload interface
export interface JWTPayload {
  userId: string
  email: string
  role: 'admin' | 'user'
  iat?: number
  exp?: number
}

// Token pair interface
export interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

/**
 * Generate access token
 */
export function generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, AUTH_CONFIG.JWT_SECRET, {
    expiresIn: AUTH_CONFIG.JWT_EXPIRES_IN,
    issuer: 'money-management-app',
    audience: 'money-management-users'
  })
}

/**
 * Generate refresh token
 */
export function generateRefreshToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, AUTH_CONFIG.JWT_SECRET, {
    expiresIn: AUTH_CONFIG.REFRESH_TOKEN_EXPIRES_IN,
    issuer: 'money-management-app',
    audience: 'money-management-users'
  })
}

/**
 * Generate token pair (access + refresh)
 */
export function generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp'>): TokenPair {
  const accessToken = generateAccessToken(payload)
  const refreshToken = generateRefreshToken(payload)
  
  return {
    accessToken,
    refreshToken,
    expiresIn: getTokenExpirationTime()
  }
}


/**
 * Verify and decode JWT token
 */
export function verifyToken(token: string): JWTPayload {
  try {
    const decoded = jwt.verify(token, AUTH_CONFIG.JWT_SECRET, {
      issuer: 'money-management-app',
      audience: 'money-management-users'
    }) as JWTPayload
    
    return decoded
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Token expired')
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid token')
    } else {
      throw new Error('Token verification failed')
    }
  }
}

/**
 * Decode token without verification (for expired tokens)
 */
export function decodeToken(token: string): JWTPayload | null {
  try {
    return jwt.decode(token) as JWTPayload
  } catch {
    return null
  }
}

/**
 * Check if token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const decoded = decodeToken(token)
    if (!decoded || !decoded.exp) return true
    
    return Date.now() >= decoded.exp * 1000
  } catch {
    return true
  }
}

/**
 * Get token expiration time in seconds
 */
export function getTokenExpirationTime(): number {
  const expiresIn = AUTH_CONFIG.JWT_EXPIRES_IN
  
  // Parse time string (e.g., "7d", "24h", "60m", "3600s")
  const timeMatch = expiresIn.match(/^(\d+)([dhms])$/)
  if (!timeMatch) return 3600 // Default 1 hour
  
  const [, value, unit] = timeMatch
  const num = parseInt(value, 10)
  
  switch (unit) {
    case 'd': return num * 24 * 60 * 60
    case 'h': return num * 60 * 60
    case 'm': return num * 60
    case 's': return num
    default: return 3600
  }
}

/**
 * Extract token from Authorization header
 */
export function extractBearerToken(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }
  
  return authHeader.substring(7) // Remove 'Bearer ' prefix
}
