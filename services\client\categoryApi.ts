/**
 * Category API Service
 *
 * Handles category-related API operations using GraphQL client
 */
import { apolloClient } from '@/lib/graphql/client'
import {
  convertToClientError
} from '@/lib/graphql/errors'
import { CATEGORY_WITH_PARENT_FRAGMENT, CREATE_CATEGORY, DELETE_CATEGORY, GET_CATEGORIES, GET_CATEGORIES_BY_TYPE, GET_CATEGORY_BY_ID, GET_CATEGORY_TREE, GET_ROOT_CATEGORIES, SEARCH_CATEGORIES, UPDATE_CATEGORY } from '@/lib/graphql/operations'
import { gql } from '@apollo/client'

import { CreateCategoryInput, UpdateCategoryInput } from '@/app/api/graphql/types/category'
import { FormCategory } from '@/components/ui/form-types'
import { Category, CategoryType, CategoryWithGoal } from '@/lib/graphql'
import {
  CategoryWithGoalInput,
  CategoryWithGoalUpdateInput,
  CREATE_CATEGORY_WITH_GOAL,
  UPDATE_CATEGORY_WITH_GOAL
} from '@/lib/graphql/operations/categoryWithGoal'
import type {
  ApiResponse,
  CreateCategoryRequest,
  UpdateCategoryRequest
} from '@/types/api'
import type {
  CategoryConnectionParams,
  CategoryConnectionResponse
} from '@/types/graphql-connections'


export class CategoryApiService {
  /**
   * Create a new category with an optional goal
   * @param formData The form data containing category and goal information
   * @returns Promise with the created category and optional goal data
   */
  static async createCategoryWithGoal(formData: FormCategory): Promise<ApiResponse<CategoryWithGoal>> {
    try {
      // Prepare the input for the mutation
      const input: CategoryWithGoalInput = {
        name: formData.name,
        description: formData.description,
        type: formData.type?.toLowerCase() as 'income' | 'expense',
        color: formData.color,
        parentId: formData.parentId,
        isActive: formData.isActive ?? true,
        // Only include goal if targetAmount is provided and greater than 0
        ...(formData.categoryGoal?.targetAmount && formData.categoryGoal.targetAmount > 0
          ? {
            goal: {
              name: formData.name, // Use category name as default goal name
              description: formData.description,
              amount: formData.categoryGoal.targetAmount,
              period: (formData.categoryGoal.period || 'monthly').toLowerCase() as 'weekly' | 'monthly' | 'yearly' | 'custom' | 'none',
              startDate: formData.categoryGoal.startDate || new Date().toISOString().split('T')[0],
              endDate: formData.categoryGoal.endDate,
              color: formData.color,
              isActive: true,
              isDefault: false
            }
          }
          : {})
      }

      const { data, errors } = await apolloClient.mutate({
        mutation: CREATE_CATEGORY_WITH_GOAL,
        variables: { input },
        // Update the cache with the new category
        // update: (cache, { data: { createCategoryWithGoal } }) => {
        //   // Update the categories list in the cache
        //   cache.modify({
        //     fields: {
        //       categories(existingCategories = []) {
        //         const newCategoryRef = cache.writeFragment({
        //           data: createCategoryWithGoal,
        //           fragment: gql`
        //             fragment NewCategory on Category {
        //               id
        //               name
        //               type
        //               color
        //               isActive
        //               categoryGoal {
        //                 id
        //                 amount
        //                 period
        //               }
        //             }
        //           `
        //         })
        //         return [...existingCategories, newCategoryRef]
        //       }
        //     }
        //   })
        // }
      })

      if (errors?.length) {
        throw new Error(errors.map(e => e.message).join('\n'))
      }

      return {
        success: true,
        data: data.createCategoryWithGoal
      }
    } catch (error: any) {
      console.error('Error creating category with goal:', error)
      return {
        success: false,
        error: convertToClientError(error).message
      }
    }
  }


  /**
   * Update a category along with its goal configuration
   * @param formData The form data containing category and goal information
   * @returns Promise with the updated category and goal data
   */
  static async updateCategoryWithGoal(formData: FormCategory): Promise<ApiResponse<CategoryWithGoal>> {
    try {
      if (!formData.id) {
        throw new Error('Category ID is required for update')
      }

      // Prepare the input for the mutation
      const input: CategoryWithGoalUpdateInput = {
        name: formData.name,
        description: formData.description,
        type: formData.type?.toLowerCase() as 'income' | 'expense',
        color: formData.color,
        parentId: formData.parentId || null,
        isActive: formData.isActive ?? true,
        goal: formData.categoryGoal?.targetAmount && formData.categoryGoal.targetAmount > 0
          ? {
            id: formData.categoryGoal.id || undefined, // Only include ID for updates, not for new goals
            name: formData.name, // Use category name as default goal name
            description: formData.description,
            amount: formData.categoryGoal.targetAmount,
            period: formData.categoryGoal.period?.toLowerCase() as 'weekly' | 'monthly' | 'yearly' | 'custom' | 'none',
            startDate: formData.categoryGoal.startDate || new Date().toISOString().split('T')[0],
            endDate: formData.categoryGoal.endDate || null,
            color: formData.color,
            isActive: true,
            isDefault: false
          }
          : null // If no target amount or target amount is 0, set goal to null
      }

      const { data, errors } = await apolloClient.mutate({
        mutation: UPDATE_CATEGORY_WITH_GOAL,
        variables: {
          id: formData.id,
          input
        },
        // Update the cache with the new data
        // update: (cache, { data: { updateCategoryWithGoal } }) => {
        //   // Update the category in the cache
        //   cache.modify({
        //     id: cache.identify({ __typename: 'Category', id: formData.id }),
        //     fields: {
        //       ...updateCategoryWithGoal,
        //       // Preserve any existing fields that aren't being updated
        //       __typename: 'Category'
        //     }
        //   })
        // }
      })

      if (errors?.length) {
        throw new Error(errors.map(e => e.message).join('\n'))
      }

      return {
        data: data.updateCategoryWithGoal,
        success: true,
        message: 'Category updated successfully'
      }
    } catch (error: any) {
      console.error('Error updating category with goal:', error)
      return {
        success: false,
        error: convertToClientError(error).message
      }
    }
  }


  /**
   * Get all categories with their main/active goal configuration
   * Only returns one goal per category (the default goal or most recent active goal)
   */
  static async getCategoriesWithGoal(params?: CategoryConnectionParams): Promise<ApiResponse<CategoryWithGoal[]>> {
    try {
      const { first = 25, after, search, type, parentId, isActive } = params || {}

      // Define the query to fetch categories with their goal configurations
      const GET_CATEGORIES_WITH_GOALS = gql`
        query GetCategoriesWithGoals(
          $first: Int
          $after: String
          $search: String
          $type: CategoryType
          $parentId: UUID
          $isActive: Boolean
        ) {
          categories(
            first: $first
            after: $after
            search: $search
            type: $type
            parentId: $parentId
            isActive: $isActive
          ) {
            edges {
              node {
                ...CategoryWithParentFragment
                # The resolver will handle returning the appropriate goal (default or most recent active)
                categoryGoal {
                  id
                  name
                  description
                  amount
                  currentAmount
                  period
                  startDate
                  endDate
                  color
                  isActive
                  isDefault
                  progress
                  remainingAmount
                  createdAt
                  updatedAt
                }
              }
              cursor
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
            totalCount
          }
        }
        ${CATEGORY_WITH_PARENT_FRAGMENT}
      `

      const { data } = await apolloClient.query({
        query: GET_CATEGORIES_WITH_GOALS,
        variables: {
          first,
          after,
          search,
          type: type?.toLowerCase() as CategoryType,
          parentId,
          isActive
        },
        fetchPolicy: 'network-only' as const, // Ensure fresh data without cache
        errorPolicy: 'all'
      })

      return {
        success: true,
        data: data.categories.edges.map((edge: any) => edge.node),
        message: 'Categories with main goal configurations retrieved successfully',
      }
    } catch (error: any) {
      console.error('Failed to fetch categories with goal configurations:', error)
      throw convertToClientError(error)
    }
  }
  // ==================== CATEGORY CRUD OPERATIONS ====================
  /**
   * Get all categories with filtering and pagination
   */
  static async getCategories(params?: CategoryConnectionParams): Promise<CategoryConnectionResponse> {
    try {
      const { first = 25, after, search, type, parentId, isActive } = params || {}
      const { data } = await apolloClient.query({
        query: GET_CATEGORIES,
        variables: {
          first,
          after,
          search,
          type: type?.toUpperCase() as CategoryType,
          parentId,
          isActive
        },
        // fetchPolicy: 'cache-first'
      })
      return {
        success: true,
        data: data.categories,
        message: 'categories retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to fetch categories:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get category by ID
   */
  static async getCategoryById(id: string): Promise<ApiResponse<Category>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CATEGORY_BY_ID,
        variables: { id },
        // fetchPolicy: 'cache-first'
      })
      if (!data.category) {
        throw new Error('Category not found')
      }
      return {
        data: data.category,
        success: true,
        message: 'Category retrieved successfully'
      }
    } catch (error: any) {
      console.error(`Failed to fetch category ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Create new category
   */
  static async createCategory(data: CreateCategoryRequest): Promise<ApiResponse<Category>> {
    try {
      const input: CreateCategoryInput = {
        name: data.name,
        description: data.description,
        type: data.type?.toUpperCase() as CategoryType,
        color: data.color || '#45B7D1',
        parentId: data.parentId
      }
      const { data: result } = await apolloClient.mutate({
        mutation: CREATE_CATEGORY,
        variables: { input },
        refetchQueries: ['GetCategories', 'GetCategoryTree']
      })
      return {
        data: result.createCategory,
        success: true,
        message: 'Category created successfully'
      }
    } catch (error: any) {
      console.error('Failed to create category:', error)
      throw convertToClientError(error)
    }
  }


  /**
   * Update category
   */
  static async updateCategory(id: string, data: UpdateCategoryRequest): Promise<ApiResponse<Category>> {
    try {
      const input: UpdateCategoryInput = {
        id,
        name: data.name,
        description: data.description,
        type: data.type?.toUpperCase() as CategoryType,
        color: data.color,
        parentId: data.parentId,
        isActive: data.isActive
      }
      const { data: result } = await apolloClient.mutate({
        mutation: UPDATE_CATEGORY,
        variables: { id, input },
        refetchQueries: ['GetCategories', 'GetCategoryById', 'GetCategoryTree']
      })
      return {
        data: result.updateCategory,
        success: true,
        message: 'Category updated successfully'
      }
    } catch (error: any) {
      console.error(`Failed to update category ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  /**
   * Update category status
   */
  static async updateStatus(id: string, isActive: boolean): Promise<ApiResponse<Category>> {
    return this.updateCategory(id, { isActive })
  }
  /**
   * Delete category
   */
  static async deleteCategory(id: string): Promise<ApiResponse<void>> {
    try {
      const { data: result } = await apolloClient.mutate({
        mutation: DELETE_CATEGORY,
        variables: { id },
        refetchQueries: ['GetCategories', 'GetCategoryTree']
      })
      return {
        data: undefined,
        success: result.deleteCategory,
        message: 'Category deleted successfully'
      }
    } catch (error: any) {
      console.error(`Failed to delete category ${id}:`, error)
      throw convertToClientError(error)
    }
  }
  // ==================== CONVENIENCE METHODS ====================
  /**
   * Search categories by name
   */
  static async searchCategories(query: string, first: number = 10): Promise<CategoryConnectionResponse> {
    try {
      const { data } = await apolloClient.query({
        query: SEARCH_CATEGORIES,
        variables: { search: query, first },
        fetchPolicy: 'cache-first'
      })

      return {
        success: true,
        data: data.categories,
        message: 'categories retrieved successfully',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      console.error('Failed to search categories:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get income categories only
   */
  static async getIncomeCategories(params?: Omit<CategoryConnectionParams, 'type'>): Promise<CategoryConnectionResponse> {
    return this.getCategories({
      ...params,
      type: 'income'
    })
  }
  /**
   * Get expense categories only
   */
  static async getExpenseCategories(params?: Omit<CategoryConnectionParams, 'type'>): Promise<CategoryConnectionResponse> {
    return this.getCategories({
      ...params,
      type: 'expense'
    })
  }
  /**
   * Get active categories only
   */
  static async getActiveCategories(params?: Omit<CategoryConnectionParams, 'isActive'>): Promise<CategoryConnectionResponse> {
    return this.getCategories({
      ...params,
      isActive: true
    })
  }
  /**
   * Get categories by type using GraphQL
   */
  static async getCategoriesByType(type: 'income' | 'expense'): Promise<ApiResponse<Category[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CATEGORIES_BY_TYPE,
        variables: { type: type.toUpperCase() as CategoryType },
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.categories.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Categories retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch categories by type:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get category tree structure
   */
  static async getCategoryTree(): Promise<ApiResponse<Category[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_CATEGORY_TREE,
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.categoryTree,
        success: true,
        message: 'Category tree retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch category tree:', error)
      throw convertToClientError(error)
    }
  }
  /**
   * Get root categories (no parent)
   */
  static async getRootCategories(): Promise<ApiResponse<Category[]>> {
    try {
      const { data } = await apolloClient.query({
        query: GET_ROOT_CATEGORIES,
        fetchPolicy: 'cache-first'
      })
      return {
        data: data.categories.edges.map((edge: any) => edge.node),
        success: true,
        message: 'Root categories retrieved successfully'
      }
    } catch (error: any) {
      console.error('Failed to fetch root categories:', error)
      throw convertToClientError(error)
    }
  }
}