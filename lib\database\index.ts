/**
 * Database Service Layer
 * 
 * Provides a unified interface for database operations
 * Abstracts Prisma client and provides common database patterns
 */

import { prisma } from '../prisma'
import type { 
  User, 
  Account, 
  Category, 
  Transaction, 
  Contact,
  AccountType,
  ContactType,
  UserPreferences,
  RecurringTransaction,
  CategoryGoal,
  AuditLog
} from '../generated/prisma'

export class DatabaseService {
  // ==================== USER OPERATIONS ====================
  
  static async createUser(data: any): Promise<User> {
    return prisma.user.create({
      data,
      include: {
        preferences: true
      }
    })
  }

  static async getUserById(id: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { id },
      include: {
        preferences: true
      }
    })
  }

  static async getUserByEmail(email: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { email },
      include: {
        preferences: true
      }
    })
  }

  static async updateUser(id: string, data: any): Promise<User> {
    return prisma.user.update({
      where: { id },
      data,
      include: {
        preferences: true
      }
    })
  }

  static async getUsers(params: any = {}): Promise<User[]> {
    const { page = 1, limit = 25, search, role, isActive } = params
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ]
    }
    
    if (role) where.role = role
    if (isActive !== undefined) where.isActive = isActive

    return prisma.user.findMany({
      where,
      include: {
        preferences: true
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { createdAt: 'desc' }
    })
  }

  // ==================== ACCOUNT OPERATIONS ====================
  
  static async createAccount(data: any): Promise<Account> {
    return prisma.account.create({
      data,
      include: {
        accountType: true,
        contact: true
      }
    })
  }

  static async getAccountById(id: string): Promise<Account | null> {
    return prisma.account.findUnique({
      where: { id },
      include: {
        accountType: true,
        contact: true
      }
    })
  }

  static async getAccounts(params: any = {}): Promise<Account[]> {
    const { page = 1, limit = 25, search, userId, isActive } = params
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }
    
    if (userId) where.userId = userId
    if (isActive !== undefined) where.isActive = isActive

    return prisma.account.findMany({
      where,
      include: {
        accountType: true,
        contact: true
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { createdAt: 'desc' }
    })
  }

  static async updateAccount(id: string, data: any): Promise<Account> {
    return prisma.account.update({
      where: { id },
      data,
      include: {
        accountType: true,
        contact: true
      }
    })
  }

  // ==================== TRANSACTION OPERATIONS ====================
  
  static async createTransaction(data: any): Promise<Transaction> {
    return prisma.transaction.create({
      data,
      include: {
        account: true,
        category: true,
        fromAccount: true,
        toAccount: true
      }
    })
  }

  static async getTransactionById(id: string): Promise<Transaction | null> {
    return prisma.transaction.findUnique({
      where: { id },
      include: {
        account: true,
        category: true,
        fromAccount: true,
        toAccount: true
      }
    })
  }

  static async getTransactions(params: any = {}): Promise<Transaction[]> {
    const { page = 1, limit = 25, search, userId, kind, accountId, categoryId, isActive } = params
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { description: { contains: search, mode: 'insensitive' } },
        { note: { contains: search, mode: 'insensitive' } }
      ]
    }
    
    if (userId) where.userId = userId
    if (kind) where.kind = kind
    if (accountId) where.accountId = accountId
    if (categoryId) where.categoryId = categoryId
    if (isActive !== undefined) where.isActive = isActive

    return prisma.transaction.findMany({
      where,
      include: {
        account: true,
        category: true,
        fromAccount: true,
        toAccount: true
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { date: 'desc' }
    })
  }

  static async updateTransaction(id: string, data: any): Promise<Transaction> {
    return prisma.transaction.update({
      where: { id },
      data,
      include: {
        account: true,
        category: true,
        fromAccount: true,
        toAccount: true
      }
    })
  }

  // ==================== CATEGORY OPERATIONS ====================
  
  static async createCategory(data: any): Promise<Category> {
    return prisma.category.create({
      data,
      include: {
        parent: true,
        children: true
      }
    })
  }

  static async getCategoryById(id: string): Promise<Category | null> {
    return prisma.category.findUnique({
      where: { id },
      include: {
        parent: true,
        children: true
      }
    })
  }

  static async getCategories(params: any = {}): Promise<Category[]> {
    const { page = 1, limit = 25, search, userId, type, isActive } = params
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }
    
    if (userId) where.ownerId = userId
    if (type) where.type = type
    if (isActive !== undefined) where.isActive = isActive

    return prisma.category.findMany({
      where,
      include: {
        parent: true,
        children: true
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { createdAt: 'desc' }
    })
  }

  static async updateCategory(id: string, data: any): Promise<Category> {
    return prisma.category.update({
      where: { id },
      data,
      include: {
        parent: true,
        children: true
      }
    })
  }

  // ==================== UTILITY METHODS ====================
  
  static async healthCheck(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      console.error('Database health check failed:', error)
      return false
    }
  }

  static async disconnect(): Promise<void> {
    await prisma.$disconnect()
  }
}

export { prisma }
export default DatabaseService
