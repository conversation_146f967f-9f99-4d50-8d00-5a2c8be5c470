import type React from "react"
import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "next-themes"
import { LanguageProvider } from "../contexts/LanguageContext"
import { TaskbarProvider } from "../contexts/TaskbarContext"
import { GlobalSidebarProvider } from "../contexts/GlobalSidebarContext"
import { AuthProvider } from "../contexts/AuthContext"
import { GlobalLayout } from "../components/layout/GlobalLayout"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Finance Manager",
  description: "Personal Finance Management Application",
  generator: 'v0.dev'
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <AuthProvider>
            <LanguageProvider>
              <TaskbarProvider>
                <GlobalSidebarProvider>
                  <GlobalLayout>
                    {children}
                  </GlobalLayout>
                </GlobalSidebarProvider>
              </TaskbarProvider>
            </LanguageProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
