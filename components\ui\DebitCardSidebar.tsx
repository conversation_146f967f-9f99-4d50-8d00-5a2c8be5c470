"use client"

import React, { useState, useEffect } from "react"
import { CreditCard, Type, Hash } from "lucide-react"
import { BaseSidebar } from "./BaseSidebar"
import { SidebarForm, SidebarInputField } from "./SidebarForm"
import { SearchableCombobox } from "./SearchableCombobox"
import { <PERSON>barActionFooter } from "./SidebarFooter"
import { DebitCard, Account } from "@/lib/generated/prisma"
import { DebitCardWithRelations } from "@/types/entities"
import { DebitCardApiService } from "@/services/client"

interface DebitCardSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (debitCard: DebitCardWithRelations) => void
  debitCard?: DebitCardWithRelations | null
  isEditing: boolean
  accountId?: string // For creating debit card for specific account
  accounts?: Account[] // Available accounts for selection
}

interface FormData {
  cardName: string
  cardNumber: string
  cardType: string
  accountId: string
}

interface FormErrors {
  cardName?: string
  cardNumber?: string
  cardType?: string
  accountId?: string
}

const CARD_TYPES = [
  { value: "VISA", label: "VISA" },
  { value: "MASTERCARD", label: "MASTERCARD" },
  { value: "AMEX", label: "AMERICAN EXPRESS" },
  { value: "JCB", label: "JCB" }
]

export function DebitCardSidebar({
  isOpen,
  onClose,
  onSave,
  debitCard,
  isEditing,
  accountId,
  accounts = []
}: DebitCardSidebarProps) {
  const [formData, setFormData] = useState<FormData>({
    cardName: "",
    cardNumber: "",
    cardType: "VISA",
    accountId: accountId || ""
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)

  // ESC key handler
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey)
      return () => document.removeEventListener('keydown', handleEscKey)
    }
  }, [isOpen])

  // Initialize form data when sidebar opens or debit card changes
  useEffect(() => {
    if (isOpen) {
      if (debitCard && isEditing) {
        setFormData({
          cardName: debitCard.cardName,
          cardNumber: debitCard.cardNumber,
          cardType: debitCard.cardType,
          accountId: debitCard.accountId
        })
      } else {
        setFormData({
          cardName: "",
          cardNumber: "",
          cardType: "VISA",
          accountId: accountId || ""
        })
      }
      setErrors({})
    }
  }, [isOpen, debitCard, isEditing, accountId])

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }

    // Simple auto-detect card type from card number (optional)
    if (field === 'cardNumber' && value.trim()) {
      try {
        // Basic card type detection based on first digit
        const firstDigit = value.trim().charAt(0)
        if (firstDigit === '4') {
          setFormData(prev => ({ ...prev, cardType: 'VISA' }))
        } else if (firstDigit === '5') {
          setFormData(prev => ({ ...prev, cardType: 'MASTERCARD' }))
        } else if (firstDigit === '3') {
          setFormData(prev => ({ ...prev, cardType: 'AMEX' }))
        }
      } catch (error) {
        // Ignore errors in card type detection
        console.log('Card type detection error:', error)
      }
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.cardName.trim()) {
      newErrors.cardName = "Tên thẻ là bắt buộc"
    }

    if (!formData.cardNumber.trim()) {
      newErrors.cardNumber = "Số thẻ là bắt buộc"
    } else if (formData.cardNumber.trim().length < 4) {
      newErrors.cardNumber = "Số thẻ phải có ít nhất 4 ký tự"
    }

    if (!formData.cardType) {
      newErrors.cardType = "Loại thẻ là bắt buộc"
    }

    if (!formData.accountId) {
      newErrors.accountId = "Tài khoản là bắt buộc"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      let result: DebitCard

      if (isEditing && debitCard) {
        result = await DebitCardApiService.updateDebitCard(debitCard.id, {
          cardName: formData.cardName.trim(),
          cardNumber: formData.cardNumber.trim(),
          cardType: formData.cardType
        })
      } else {
        result = await DebitCardApiService.createDebitCard({
          accountId: formData.accountId,
          cardName: formData.cardName.trim(),
          cardNumber: formData.cardNumber.trim(),
          cardType: formData.cardType
        })
      }

      onSave(result as DebitCardWithRelations)
      handleClose()
    } catch (error) {
      console.error('Error saving debit card:', error)
      // Handle error (show toast, etc.)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      cardName: "",
      cardNumber: "",
      cardType: "VISA",
      accountId: accountId || ""
    })
    setErrors({})
    onClose()
  }

  // Prepare account options
  const accountOptions = accounts.map(account => ({
    value: account.id,
    label: account.name
  }))

  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa thẻ ghi nợ" : "Thêm thẻ ghi nợ mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${debitCard?.cardName}` : "Tạo thẻ ghi nợ mới cho tài khoản ngân hàng"}
      storageKey="debitCardSidebarWidth"
      config={{
        DEFAULT_WIDTH: 400,
        MIN_WIDTH: 350,
        MAX_WIDTH_PERCENTAGE: 0.6
      }}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={() => {
            const fakeEvent = { preventDefault: () => {} } as React.FormEvent
            handleSubmit(fakeEvent)
          }}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={!formData.cardName.trim() || !formData.cardNumber.trim() || !formData.accountId}
          sidebarWidth={400}
        />
      }
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={400}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* Card Name */}
        <SidebarInputField
          label="Tên thẻ"
          value={formData.cardName}
          onChange={(value) => handleInputChange('cardName', value)}
          placeholder="Thẻ chính, Thẻ phụ..."
          required
          error={errors.cardName}
          icon={<Type size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
        />

        {/* Account Selection */}
        {!accountId && accountOptions.length > 0 && (
          <div className="space-y-1">
            <label className="text-sm font-medium text-gray-700 flex items-center">
              <CreditCard size={14} className="mr-2" />
              Tài khoản *
            </label>
            <SearchableCombobox
              value={formData.accountId}
              onChange={(value) => handleInputChange('accountId', value)}
              options={accountOptions}
              placeholder="Chọn tài khoản..."
              disabled={loading}
            />
            {errors.accountId && (
              <p className="text-xs text-red-500">{errors.accountId}</p>
            )}
          </div>
        )}

        {/* Card Number */}
        <SidebarInputField
          label="Số thẻ"
          value={formData.cardNumber}
          onChange={(value) => handleInputChange('cardNumber', value)}
          placeholder="1234 5678 9012 3456"
          required
          error={errors.cardNumber}
          icon={<Hash size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={400}
          maxLength={19}
        />

        {/* Card Type */}
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <CreditCard size={14} className="mr-2" />
            Loại thẻ *
          </label>
          <SearchableCombobox
            value={formData.cardType}
            onChange={(value) => handleInputChange('cardType', value)}
            options={CARD_TYPES}
            placeholder="Chọn loại thẻ..."
            disabled={loading}
          />
          {errors.cardType && (
            <p className="text-xs text-red-500">{errors.cardType}</p>
          )}
        </div>
      </SidebarForm>
    </BaseSidebar>
  )
}
