"use client"

import React, { useState, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { GenericListProps, ResponsiveGridCols } from '@/types/ui'
import { useResponsiveGrid } from '@/hooks/useResponsiveGrid'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, RotateCcw } from 'lucide-react'

/**
 * Enhanced GenericList - Improved version with better pagination, loading states, and responsive design
 */
export function EnhancedGenericList<T extends { id?: string | number }>({
  data,
  renderCard,
  loading = false,
  error = null,
  emptyMessage = "Không có dữ liệu",
  searchable = false,
  sortable = false,
  filterable = false,
  paginated = true,
  defaultItemsPerPage = 25,
  itemsPerPageOptions = [10, 25, 50, 100],
  gridCols = { default: 1, sm: 2, lg: 3, xl: 4, '2xl': 4 },
  sidebarOpen = false,
  sidebarWidth = 400,
  onItemClick,
  className,
  children
}: GenericListProps<T>) {
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(defaultItemsPerPage)

  // Responsive grid calculation
  const gridData = useResponsiveGrid({
    baseGrid: gridCols,
    sidebarOpen,
    sidebarWidth
  })

  // Pagination calculations
  const totalItems = data.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedData = paginated ? data.slice(startIndex, endIndex) : data

  // Reset page when data changes
  React.useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1)
    }
  }, [data.length, itemsPerPage, currentPage, totalPages])

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)))
  }

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1) // Reset to first page
    
    // Store in localStorage for persistence
    localStorage.setItem('genericList_itemsPerPage', newItemsPerPage.toString())
  }

  // Load items per page from localStorage on mount
  React.useEffect(() => {
    const stored = localStorage.getItem('genericList_itemsPerPage')
    if (stored && itemsPerPageOptions.includes(Number(stored))) {
      setItemsPerPage(Number(stored))
    }
  }, [itemsPerPageOptions])

  // Grid classes based on responsive columns
  const gridClasses = cn(
    'grid gap-4',
    {
      'grid-cols-1': gridData.gridConfig.default === 1,
      'grid-cols-2': gridData.gridConfig.default === 2,
      'grid-cols-3': gridData.gridConfig.default === 3,
      'grid-cols-4': gridData.gridConfig.default === 4,
      'grid-cols-5': gridData.gridConfig.default === 5,
      'grid-cols-6': gridData.gridConfig.default === 6,
    }
  )

  // Loading skeleton
  const renderLoadingSkeleton = () => (
    <div className={gridClasses}>
      {Array.from({ length: itemsPerPage }).map((_, index) => (
        <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Skeleton className="w-10 h-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-2/3" />
            <div className="flex gap-2 pt-2">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  // Error state
  const renderError = () => (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
        <RotateCcw className="w-8 h-8 text-red-600 dark:text-red-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        Có lỗi xảy ra
      </h3>
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        {error}
      </p>
      <Button 
        variant="outline" 
        onClick={() => window.location.reload()}
        className="flex items-center gap-2"
      >
        <RotateCcw size={16} />
        Thử lại
      </Button>
    </div>
  )

  // Empty state
  const renderEmpty = () => (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        Chưa có dữ liệu
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        {emptyMessage}
      </p>
    </div>
  )

  // Pagination component
  const renderPagination = () => {
    if (!paginated || totalPages <= 1) return null

    const getVisiblePages = () => {
      const delta = 2
      const range = []
      const rangeWithDots = []

      for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
        range.push(i)
      }

      if (currentPage - delta > 2) {
        rangeWithDots.push(1, '...')
      } else {
        rangeWithDots.push(1)
      }

      rangeWithDots.push(...range)

      if (currentPage + delta < totalPages - 1) {
        rangeWithDots.push('...', totalPages)
      } else {
        rangeWithDots.push(totalPages)
      }

      return rangeWithDots
    }

    return (
      <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        {/* Items per page selector */}
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <span>Hiển thị</span>
          <select
            value={itemsPerPage}
            onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
            className="border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            {itemsPerPageOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
          <span>trên tổng số {totalItems} mục</span>
        </div>

        {/* Page navigation */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft size={16} />
          </Button>

          {getVisiblePages().map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="px-2 py-1 text-gray-500">...</span>
              ) : (
                <Button
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page as number)}
                  className="h-8 w-8 p-0"
                >
                  {page}
                </Button>
              )}
            </React.Fragment>
          ))}

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-8 w-8 p-0"
          >
            <ChevronRight size={16} />
          </Button>
        </div>

        {/* Page info */}
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Trang {currentPage} / {totalPages}
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {children}
      
      {/* Main content area */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Content */}
        <div className="p-4">
          {loading ? (
            renderLoadingSkeleton()
          ) : error ? (
            renderError()
          ) : paginatedData.length === 0 ? (
            renderEmpty()
          ) : (
            <div className={gridClasses}>
              {paginatedData.map((item, index) => {
                // Use item.id if available, otherwise fall back to index
                const key = item.id ? String(item.id) : `item-${index}`
                return (
                  <div
                    key={key}
                    onClick={() => onItemClick?.(item)}
                  >
                    {renderCard(item)}
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Pagination */}
        {renderPagination()}
      </div>
    </div>
  )
}

export default EnhancedGenericList
