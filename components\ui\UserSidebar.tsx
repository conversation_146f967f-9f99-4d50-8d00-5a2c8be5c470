"use client"

import React, { useState, useEffect } from "react"
import { User as UserI<PERSON>, Mail, Shield, Lock, Eye, EyeOff } from "lucide-react"
import { BaseSidebar } from "./BaseSidebar"
import { SidebarForm, SidebarInputField } from "./SidebarForm"
import { SearchableCombobox } from "./SearchableCombobox"
import { SidebarActionFooter } from "./SidebarFooter"
import { getSidebarConfig } from "../../config/sidebarConfig"
import type { User } from "../../types/database"

interface UserSidebarProps {
  isOpen: boolean
  onClose: () => void
  onSave: (user: User) => void
  user?: User | null
  isEditing: boolean
}

interface FormData {
  name: string
  email: string
  role: "admin" | "user"
  password: string
  confirmPassword: string
  preferences: {
    currency: string
    language: string
    theme: string
  }
}

interface FormErrors {
  name?: string
  email?: string
  role?: string
  password?: string
  confirmPassword?: string
}

const USER_ROLES = [
  { value: "user", label: "Người dùng" },
  { value: "admin", label: "Quản trị viên" }
]

export function UserSidebar({
  isOpen,
  onClose,
  onSave,
  user,
  isEditing
}: UserSidebarProps) {
  const config = getSidebarConfig('USER')
  
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    role: "user",
    password: "",
    confirmPassword: "",
    preferences: {
      currency: "VND",
      language: "vi",
      theme: "light"
    }
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  // Initialize form data when sidebar opens or user changes
  useEffect(() => {
    if (isOpen) {
      if (user && isEditing) {
        setFormData({
          name: user.name,
          email: user.email,
          role: user.role,
          password: "",
          confirmPassword: "",
          preferences: user.preferences || {
            currency: "VND",
            language: "vi",
            theme: "light"
          }
        })
      } else {
        setFormData({
          name: "",
          email: "",
          role: "user",
          password: "",
          confirmPassword: "",
          preferences: {
            currency: "VND",
            language: "vi",
            theme: "light"
          }
        })
      }
      setErrors({})
    }
  }, [isOpen, user, isEditing])
  
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "Tên người dùng là bắt buộc"
    }
    
    if (!formData.email.trim()) {
      newErrors.email = "Email là bắt buộc"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email không hợp lệ"
    }
    
    if (!formData.role) {
      newErrors.role = "Vai trò là bắt buộc"
    }
    
    // Password validation for new users or when changing password
    if (!isEditing || formData.password) {
      if (!formData.password) {
        newErrors.password = "Mật khẩu là bắt buộc"
      } else if (formData.password.length < 6) {
        newErrors.password = "Mật khẩu phải có ít nhất 6 ký tự"
      }
      
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Xác nhận mật khẩu không khớp"
      }
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setLoading(true)
    try {
      const userData: User = {
        id: user?.id || 0,
        name: formData.name.trim(),
        email: formData.email.trim().toLowerCase(),
        role: formData.role,
        preferences: formData.preferences,
        createdAt: user?.createdAt || new Date(),
        updatedAt: new Date()
      }
      
      onSave(userData)
      handleClose()
    } catch (error) {
      console.error('Error saving user:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const handleClose = () => {
    setFormData({
      name: "",
      email: "",
      role: "user",
      password: "",
      confirmPassword: "",
      preferences: {
        currency: "VND",
        language: "vi",
        theme: "light"
      }
    })
    setErrors({})
    setShowPassword(false)
    setShowConfirmPassword(false)
    onClose()
  }
  

  
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }
  
  return (
    <BaseSidebar
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Chỉnh sửa người dùng" : "Thêm người dùng mới"}
      subtitle={isEditing ? `Cập nhật thông tin cho ${user?.name}` : "Tạo tài khoản người dùng mới"}
      storageKey="userSidebarWidth"
      config={config}
      footerContent={
        <SidebarActionFooter
          onCancel={handleClose}
          onSave={handleSubmit}
          onDelete={isEditing ? handleDelete : undefined}
          saveLabel={isEditing ? "Cập nhật" : "Tạo mới"}
          loading={loading}
          saveDisabled={!formData.name.trim() || !formData.email.trim()}
          showDelete={isEditing && !!onDelete}
          sidebarWidth={450}
        />
      }
    >
      <SidebarForm
        onSubmit={handleSubmit}
        sidebarWidth={450}
        isResizing={false}
        isOpen={isOpen}
      >
        {/* User Name */}
        <SidebarInputField
          label="Tên người dùng"
          value={formData.name}
          onChange={(value) => handleInputChange('name', value)}
          placeholder="Nhập tên người dùng"
          required
          error={errors.name}
          icon={<User size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={450}
        />
        
        {/* Email */}
        <SidebarInputField
          label="Email"
          value={formData.email}
          onChange={(value) => handleInputChange('email', value)}
          placeholder="<EMAIL>"
          type="email"
          required
          error={errors.email}
          icon={<Mail size={14} />}
          disabled={loading}
          span="full"
          sidebarWidth={450}
        />
        
        {/* Role */}
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700 flex items-center">
            <Shield size={14} className="mr-2" />
            Vai trò *
          </label>
          <SearchableCombobox
            value={formData.role}
            onChange={(value) => handleInputChange('role', value as "admin" | "user")}
            options={USER_ROLES}
            placeholder="Chọn vai trò..."
            disabled={loading}
          />
          {errors.role && (
            <p className="text-xs text-red-500">{errors.role}</p>
          )}
        </div>
        
        {/* Password */}
        <div className="col-span-full">
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Lock size={14} className="inline mr-1" />
            {isEditing ? "Mật khẩu mới (để trống nếu không thay đổi)" : "Mật khẩu *"}
          </label>
          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder={isEditing ? "Nhập mật khẩu mới" : "Nhập mật khẩu"}
              disabled={loading}
              className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              disabled={loading}
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.password && (
            <p className="text-xs text-red-500 mt-1">{errors.password}</p>
          )}
        </div>
        
        {/* Confirm Password */}
        {(!isEditing || formData.password) && (
          <div className="col-span-full">
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Lock size={14} className="inline mr-1" />
              Xác nhận mật khẩu *
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                placeholder="Nhập lại mật khẩu"
                disabled={loading}
                className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                disabled={loading}
              >
                {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-xs text-red-500 mt-1">{errors.confirmPassword}</p>
            )}
          </div>
        )}
      </SidebarForm>
    </BaseSidebar>
  )
}
