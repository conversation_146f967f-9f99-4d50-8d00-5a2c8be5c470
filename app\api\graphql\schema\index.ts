/**
 * GraphQL Schema Definitions
 * 
 * Main schema file that combines all type definitions
 */

import { gql } from 'graphql-tag'

export const typeDefs = gql`
  # Scalars
  scalar DateTime
  scalar UUID
  scalar JSON

  # Enums
  enum UserRole {
    admin
    user
  }

  enum TransactionKind {
    income
    expense
    transfer
  }

  enum CategoryType {
    income
    expense
  }

  enum AccountTypeCategory {
    cash
    bank
    credit_card
    investment
    loan
    other
  }

  enum Language {
    vi
    en
  }

  enum Theme {
    light
    dark
    system
  }

  enum Period {
    weekly
    monthly
    yearly
    custom
    none
  }

  # User Types
  type User {
    id: UUID!
    name: String!
    email: String!
    avatar: String
    role: UserRole!
    description: String
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    preferences: UserPreferences
    accounts: [Account!]!
    categories: [Category!]!
    contacts: [Contact!]!
    transactions: [Transaction!]!
    recurringTransactions: [RecurringTransaction!]!
    categoryGoals: [CategoryGoal!]!
    bankInfos: [BankInfo!]!
    debitCards: [DebitCard!]!
    accountPaymentApps: [AccountPaymentApp!]!
  }

  type UserPreferences {
    id: UUID!
    userId: UUID!
    name: String!
    currency: String!
    language: Language!
    theme: Theme!
    dateFormat: String
    numberFormat: String
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
  }

  # Account Types
  type AccountType {
    id: UUID!
    name: String!
    description: String
    category: AccountTypeCategory!
    color: String!
    icon: String
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    accounts: [Account!]!
  }

  type Account {
    id: UUID!
    name: String!
    description: String
    balance: Float!
    currency: String!
    color: String!
    icon: String
    isActive: Boolean!
    userId: UUID!
    accountTypeId: UUID!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
    accountType: AccountType!
    transactions: [Transaction!]!
    transferFromTransactions: [Transaction!]!
    transferToTransactions: [Transaction!]!
    accountPaymentApps: [AccountPaymentApp!]!
  }

  # Category Types
  type Category {
    id: UUID!
    name: String!
    description: String
    type: CategoryType!
    color: String!
    icon: String
    parentId: UUID
    userId: UUID!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
    parent: Category
    children: [Category!]!
    transactions: [Transaction!]!
    categoryGoal: CategoryGoal
    categoryGoals(filter: CategoryGoalFilter): [CategoryGoal!]!
    recurringTransactions: [RecurringTransaction!]!
  }

  # Transaction Types
  type Transaction {
    id: UUID!
    kind: TransactionKind!
    amount: Float!
    date: DateTime!
    description: String
    location: String
    note: String
    attachments: [String!]!
    isCleared: Boolean!
    isReconciled: Boolean!
    userId: UUID!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # For income/expense transactions
    accountId: UUID
    categoryId: UUID
    
    # For transfer transactions
    fromAccountId: UUID
    toAccountId: UUID
    
    # Relations
    user: User!
    account: Account
    category: Category
    fromAccount: Account
    toAccount: Account
  }

  # Contact Types
  type ContactType {
    id: UUID!
    name: String!
    description: String
    color: String!
    icon: String
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    contacts: [Contact!]!
  }

  type Contact {
    id: UUID!
    name: String!
    email: String
    phone: String
    address: String
    note: String
    avatar: String
    userId: UUID!
    contactTypeId: UUID!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
    contactType: ContactType!
  }

  # Recurring Transaction Types
  type RecurringTransaction {
    id: UUID!
    name: String!
    description: String
    amount: Float!
    kind: TransactionKind!
    frequency: String!
    startDate: DateTime!
    endDate: DateTime
    isActive: Boolean!
    userId: UUID!
    accountId: UUID
    categoryId: UUID
    contactId: UUID
    fromAccountId: UUID
    toAccountId: UUID
    createdAt: DateTime!
    updatedAt: DateTime!

    # Relations
    user: User!
    account: Account
    category: Category
    contact: Contact
    fromAccount: Account
    toAccount: Account
  }

  # Category Goal Types
  # Category Goal Types
  type CategoryGoal {
    id: UUID!
    name: String!
    description: String
    amount: Float!
    period: Period!
    startDate: DateTime!
    endDate: DateTime
    color: String!
    isActive: Boolean!
    isDefault: Boolean!
    userId: UUID!
    categoryId: UUID!
    createdAt: DateTime!
    updatedAt: DateTime!

    # Computed fields
    currentAmount: Float!
    progress: Float!
    remainingAmount: Float!

    # Relations
    user: User!
    category: Category!
  }

  # Input type for filtering category goals
  input CategoryGoalFilter {
    isActive: Boolean
    isDefault: Boolean
  }

  # Bank Info Types
  type BankInfo {
    id: UUID!
    bankName: String!
    accountNumber: String!
    accountHolderName: String!
    branchName: String
    swiftCode: String
    routingNumber: String
    note: String
    userId: UUID!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
  }

  # Debit Card Types
  type DebitCard {
    id: UUID!
    cardNumber: String!
    cardHolderName: String!
    expiryDate: String!
    bankName: String!
    cardType: String
    note: String
    userId: UUID!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
  }

  # Account Payment App Types
  type AccountPaymentApp {
    id: UUID!
    appName: String!
    accountIdentifier: String!
    note: String
    userId: UUID!
    accountId: UUID!
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    
    # Relations
    user: User!
    account: Account!
  }

  # Pagination Types
  type PageInfo {
    hasNextPage: Boolean!
    hasPreviousPage: Boolean!
    startCursor: String
    endCursor: String
  }

  # Connection Types
  type UserConnection {
    edges: [UserEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type UserEdge {
    node: User!
    cursor: String!
  }

  type AccountConnection {
    edges: [AccountEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type AccountEdge {
    node: Account!
    cursor: String!
  }

  type CategoryConnection {
    edges: [CategoryEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type CategoryEdge {
    node: Category!
    cursor: String!
  }

  type TransactionConnection {
    edges: [TransactionEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type TransactionEdge {
    node: Transaction!
    cursor: String!
  }

  type ContactConnection {
    edges: [ContactEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type ContactEdge {
    node: Contact!
    cursor: String!
  }

  type RecurringTransactionConnection {
    edges: [RecurringTransactionEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type RecurringTransactionEdge {
    node: RecurringTransaction!
    cursor: String!
  }

  type CategoryGoalConnection {
    edges: [CategoryGoalEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type CategoryGoalEdge {
    node: CategoryGoal!
    cursor: String!
  }

  type BankInfoConnection {
    edges: [BankInfoEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type BankInfoEdge {
    node: BankInfo!
    cursor: String!
  }

  type DebitCardConnection {
    edges: [DebitCardEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type DebitCardEdge {
    node: DebitCard!
    cursor: String!
  }

  type AccountPaymentAppConnection {
    edges: [AccountPaymentAppEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type AccountPaymentAppEdge {
    node: AccountPaymentApp!
    cursor: String!
  }

  type CategoryGoalConnection {
    edges: [CategoryGoalEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type CategoryGoalEdge {
    node: CategoryGoal!
    cursor: String!
  }

  type RecurringTransactionConnection {
    edges: [RecurringTransactionEdge!]!
    pageInfo: PageInfo!
    totalCount: Int!
  }

  type RecurringTransactionEdge {
    node: RecurringTransaction!
    cursor: String!
  }

  # Input Types
  input CreateUserInput {
    name: String!
    email: String!
    password: String!
    avatar: String
    role: UserRole = user
    description: String
    preferences: CreateUserPreferencesInput
  }

  input CreateUserPreferencesInput {
    currency: String = "VND"
    language: Language = vi
    theme: Theme = system
    dateFormat: String
    numberFormat: String
  }

  input UpdateUserInput {
    name: String
    email: String
    avatar: String
    role: UserRole
    description: String
    isActive: Boolean
  }

  input UpdateUserPreferencesInput {
    name: String
    currency: String
    language: Language
    theme: Theme
    dateFormat: String
    numberFormat: String
  }

  input CreateAccountInput {
    name: String!
    description: String
    balance: Float = 0
    currency: String = "VND"
    color: String = "#45B7D1"
    icon: String
    accountTypeId: UUID!
  }

  input UpdateAccountInput {
    name: String
    description: String
    balance: Float
    currency: String
    color: String
    icon: String
    accountTypeId: UUID
    isActive: Boolean
  }

  input CreateCategoryInput {
    name: String!
    description: String
    type: CategoryType!
    color: String = "#45B7D1"
    icon: String
    parentId: UUID
  }

  input UpdateCategoryInput {
    name: String
    description: String
    type: CategoryType
    color: String
    icon: String
    parentId: UUID
    isActive: Boolean
  }

  input CategoryGoalInput {
    name: String!
    description: String
    amount: Float!
    period: Period!
    startDate: DateTime!
    endDate: DateTime
    color: String
    isActive: Boolean
    isDefault: Boolean
  }

  input UpdateCategoryGoalInput {
    name: String
    description: String
    amount: Float
    period: Period
    startDate: DateTime
    endDate: DateTime
    color: String
    isActive: Boolean
    isDefault: Boolean
  }

  input CreateCategoryWithGoalInput {
    name: String!
    description: String
    type: CategoryType!
    color: String! = "#45B7D1"
    icon: String
    parentId: UUID
    goal: CategoryGoalInput!
  }

  input UpdateCategoryWithGoalInput {
    name: String
    description: String
    type: CategoryType
    color: String
    icon: String
    parentId: UUID
    isActive: Boolean
    goal: UpdateCategoryGoalInput
  }

  input CategoryGoalInput {
    name: String!
    description: String
    amount: Float!
    period: Period!
    startDate: DateTime!
    endDate: DateTime
    color: String
    isActive: Boolean
    isDefault: Boolean
  }

  input UpdateCategoryGoalInput {
    name: String
    description: String
    amount: Float
    period: Period
    startDate: DateTime
    endDate: DateTime
    color: String
    isActive: Boolean
    isDefault: Boolean
  }

  input CreateCategoryWithGoalInput {
    name: String!
    description: String
    type: CategoryType!
    color: String! = "#45B7D1"
    icon: String
    parentId: UUID
    goal: CategoryGoalInput!
  }

  input UpdateCategoryWithGoalInput {
    name: String
    description: String
    type: CategoryType
    color: String
    icon: String
    parentId: UUID
    isActive: Boolean
    goal: UpdateCategoryGoalInput
  }

  input CreateTransactionInput {
    kind: TransactionKind!
    amount: Float!
    date: DateTime!
    description: String
    location: String
    note: String
    attachments: [String!]
    accountId: UUID
    categoryId: UUID
    fromAccountId: UUID
    toAccountId: UUID
  }

  input UpdateTransactionInput {
    kind: TransactionKind
    amount: Float
    date: DateTime
    description: String
    location: String
    note: String
    attachments: [String!]
    isCleared: Boolean
    isReconciled: Boolean
    accountId: UUID
    categoryId: UUID
    fromAccountId: UUID
    toAccountId: UUID
    isActive: Boolean
  }

  input CreateContactInput {
    name: String!
    email: String
    phone: String
    address: String
    note: String
    avatar: String
    contactTypeId: UUID!
  }

  input UpdateContactInput {
    name: String
    email: String
    phone: String
    address: String
    note: String
    avatar: String
    contactTypeId: UUID
    isActive: Boolean
  }

  input CreateCategoryGoalInput {
    name: String!
    description: String
    amount: Float!
    period: Period!
    startDate: DateTime!
    endDate: DateTime
    color: String = "#45B7D1"
    isDefault: Boolean = false
    categoryId: UUID!
  }

  input UpdateCategoryGoalInput {
    name: String
    description: String
    amount: Float
    period: Period
    startDate: DateTime
    endDate: DateTime
    color: String
    categoryId: UUID
    isActive: Boolean
  }

  input CreateRecurringTransactionInput {
    name: String!
    description: String
    amount: Float!
    kind: TransactionKind!
    frequency: String!
    startDate: DateTime!
    endDate: DateTime
    accountId: UUID
    categoryId: UUID
    contactId: UUID
    fromAccountId: UUID
    toAccountId: UUID
  }

  input UpdateRecurringTransactionInput {
    name: String
    description: String
    amount: Float
    kind: TransactionKind
    frequency: String
    startDate: DateTime
    endDate: DateTime
    accountId: UUID
    categoryId: UUID
    contactId: UUID
    fromAccountId: UUID
    toAccountId: UUID
    isActive: Boolean
  }

  # Query Types
  type Query {
    # User queries
    me: User!
    users(first: Int, after: String, search: String, role: UserRole, isActive: Boolean): UserConnection!
    user(id: UUID!): User
    
    # Category with goal queries
    categoryWithGoal(id: UUID!): Category
    categoriesWithGoals(
      first: Int
      after: String
      search: String
      type: CategoryType
      parentId: UUID
      isActive: Boolean
    ): CategoryConnection!

    # Account queries
    accounts(first: Int, after: String, search: String, accountTypeId: UUID, isActive: Boolean): AccountConnection!
    account(id: UUID!): Account
    accountTypes: [AccountType!]!

    # Category queries
    categories(first: Int, after: String, search: String, type: CategoryType, parentId: UUID, isActive: Boolean): CategoryConnection!
    category(id: UUID!): Category

    # Transaction queries
    transactions(
      first: Int
      after: String
      search: String
      kind: TransactionKind
      accountId: UUID
      categoryId: UUID
      startDate: DateTime
      endDate: DateTime
      isActive: Boolean
    ): TransactionConnection!
    transaction(id: UUID!): Transaction

    # Contact queries
    contacts(first: Int, after: String, search: String, contactTypeId: UUID, isActive: Boolean): ContactConnection!
    contact(id: UUID!): Contact
    contactTypes: [ContactType!]!

    # Bank info queries
    bankInfos(first: Int, after: String, search: String, isActive: Boolean): BankInfoConnection!
    bankInfo(id: UUID!): BankInfo

    # Debit card queries
    debitCards(first: Int, after: String, search: String, isActive: Boolean): DebitCardConnection!
    debitCard(id: UUID!): DebitCard

    # Account payment app queries
    accountPaymentApps(first: Int, after: String, search: String, accountId: UUID, isActive: Boolean): AccountPaymentAppConnection!
    accountPaymentApp(id: UUID!): AccountPaymentApp

    # Category goal queries
    categoryGoals(first: Int, after: String, search: String, categoryId: UUID, period: Period, isActive: Boolean): CategoryGoalConnection!
    categoryGoal(id: UUID!): CategoryGoal

    # Recurring transaction queries
    recurringTransactions(first: Int, after: String, search: String, kind: TransactionKind, frequency: String, isActive: Boolean): RecurringTransactionConnection!
    recurringTransaction(id: UUID!): RecurringTransaction
  }

  # Mutation Types
  type Mutation {
    # User mutations
    createUser(input: CreateUserInput!): User!
    updateUser(id: UUID!, input: UpdateUserInput!): User!
    deleteUser(id: UUID!): Boolean!
    
    # Category with goal mutations
    createCategoryWithGoal(input: CreateCategoryWithGoalInput!): Category!
    updateCategoryWithGoal(id: UUID!, input: UpdateCategoryWithGoalInput!): Category!
    updateUserPreferences(input: UpdateUserPreferencesInput!): UserPreferences!

    # Account mutations
    createAccount(input: CreateAccountInput!): Account!
    updateAccount(id: UUID!, input: UpdateAccountInput!): Account!
    deleteAccount(id: UUID!): Boolean!

    # Category mutations
    createCategory(input: CreateCategoryInput!): Category!
    updateCategory(id: UUID!, input: UpdateCategoryInput!): Category!
    deleteCategory(id: UUID!): Boolean!

    # Transaction mutations
    createTransaction(input: CreateTransactionInput!): Transaction!
    updateTransaction(id: UUID!, input: UpdateTransactionInput!): Transaction!
    deleteTransaction(id: UUID!): Boolean!

    # Contact mutations
    createContact(input: CreateContactInput!): Contact!
    updateContact(id: UUID!, input: UpdateContactInput!): Contact!
    deleteContact(id: UUID!): Boolean!

    # Category goal mutations
    createCategoryGoal(input: CreateCategoryGoalInput!): CategoryGoal!
    updateCategoryGoal(id: UUID!, input: UpdateCategoryGoalInput!): CategoryGoal!
    deleteCategoryGoal(id: UUID!): Boolean!

    # Recurring transaction mutations
    createRecurringTransaction(input: CreateRecurringTransactionInput!): RecurringTransaction!
    updateRecurringTransaction(id: UUID!, input: UpdateRecurringTransactionInput!): RecurringTransaction!
    deleteRecurringTransaction(id: UUID!): Boolean!
  }
`
