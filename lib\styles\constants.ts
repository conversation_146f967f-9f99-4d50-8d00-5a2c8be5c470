/**
 * Style Constants
 * 
 * Centralized style constants for consistent UI/UX across the application
 */

// Color palette
export const colors = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a'
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d'
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d'
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f'
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827'
  }
}

// Spacing scale
export const spacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '0.75rem',    // 12px
  lg: '1rem',       // 16px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '2rem',    // 32px
  '4xl': '2.5rem',  // 40px
  '5xl': '3rem',    // 48px
  '6xl': '4rem'     // 64px
}

// Typography scale
export const typography = {
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem'     // 48px
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75'
  }
}

// Border radius
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  full: '9999px'
}

// Shadows
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)'
}

// Animation durations
export const animation = {
  fast: '150ms',
  normal: '200ms',
  slow: '300ms',
  slower: '500ms'
}

// Z-index scale
export const zIndex = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070
}

// Breakpoints
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

// Component-specific constants
export const components = {
  button: {
    height: {
      sm: '2rem',      // 32px
      md: '2.5rem',    // 40px
      lg: '3rem'       // 48px
    },
    padding: {
      sm: '0.5rem 0.75rem',
      md: '0.75rem 1rem',
      lg: '1rem 1.5rem'
    }
  },
  input: {
    height: {
      sm: '2rem',      // 32px
      md: '2.5rem',    // 40px
      lg: '3rem'       // 48px
    },
    padding: '0.75rem'
  },
  card: {
    padding: '1.5rem',
    borderRadius: borderRadius.lg,
    shadow: shadows.md
  },
  modal: {
    maxWidth: {
      sm: '28rem',     // 448px
      md: '32rem',     // 512px
      lg: '42rem',     // 672px
      xl: '48rem'      // 768px
    }
  },
  sidebar: {
    width: {
      sm: '16rem',     // 256px
      md: '20rem',     // 320px
      lg: '24rem'      // 384px
    }
  }
}

// Layout constants
export const layout = {
  header: {
    height: '4rem'     // 64px
  },
  sidebar: {
    width: '16rem',    // 256px
    collapsedWidth: '4rem' // 64px
  },
  container: {
    maxWidth: '80rem', // 1280px
    padding: '1rem'
  },
  grid: {
    gap: '1.5rem',     // 24px
    columns: {
      mobile: 1,
      tablet: 2,
      desktop: 3,
      wide: 4
    }
  }
}

// Form constants
export const forms = {
  fieldSpacing: '1rem',
  labelSpacing: '0.5rem',
  errorColor: colors.error[500],
  focusColor: colors.primary[500],
  borderColor: colors.gray[300],
  borderColorDark: colors.gray[600]
}

// Status colors
export const status = {
  active: colors.success[500],
  inactive: colors.gray[400],
  pending: colors.warning[500],
  error: colors.error[500],
  info: colors.primary[500]
}

// Transaction type colors
export const transactionTypes = {
  income: colors.success[500],
  expense: colors.error[500],
  transfer: colors.primary[500]
}

// Export all constants as a single object for convenience
export const theme = {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
  animation,
  zIndex,
  breakpoints,
  components,
  layout,
  forms,
  status,
  transactionTypes
}
