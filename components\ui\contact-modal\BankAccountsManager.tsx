"use client"

import React from "react"
import { <PERSON><PERSON> } from "../button"
import { Plus, Trash2, Building2, CreditCard, User, DollarSign } from "lucide-react"
import { Account } from "@/lib/generated/prisma"

interface AccountsManagerProps {
  accounts: Account[]
  onAdd: (account: Account) => void
  onUpdate: (accountId: number, field: keyof Account, value: any) => void
  onDelete: (accountId: number) => void
}

export function BankAccountsManager({
  accounts,
  onAdd,
  onUpdate,
  onDelete
}: AccountsManagerProps) {
  const handleAddAccount = () => {
    const newAccount: Account = {
      id: Date.now(),
      userId: 1,
      name: "",
      type: "bank",
      balance: 0,
      currency: "VND",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      description: "",
      bankName: "",
      accountNumber: ""
    }
    
    onAdd(newAccount)
  }
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2 flex-1">
          Tài khoản
        </h3>
        <Button
          type="button"
          onClick={handleAddAccount}
          variant="outline"
          size="sm"
          className="ml-4"
        >
          <Plus className="w-4 h-4 mr-1" />
          Thêm tài khoản
        </Button>
      </div>

      {accounts.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <p>Chưa có tài khoản nào</p>
          <p className="text-sm">Nhấn "Thêm tài khoản" để thêm tài khoản</p>
        </div>
      ) : (
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {accounts.map((account, index) => (
            <div key={account.id} className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Tài khoản {index + 1}
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => onDelete(account.id)}
                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                >
                  <Trash2 size={16} />
                </button>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Tên tài khoản *
                  </label>
                  <input
                    type="text"
                    value={account.name}
                    onChange={(e) => onUpdate(account.id, 'name', e.target.value)}
                    placeholder="VD: Vietcombank - Chính"
                    className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Loại tài khoản *
                  </label>
                  <select
                    value={account.type}
                    onChange={(e) => onUpdate(account.id, 'type', e.target.value)}
                    className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  >
                    <option value="cash">Tiền mặt</option>
                    <option value="bank">Ngân hàng</option>
                    <option value="credit">Thẻ tín dụng</option>
                    <option value="investment">Đầu tư</option>
                    <option value="custom">Tùy chỉnh</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Số dư
                  </label>
                  <input
                    type="number"
                    value={account.balance}
                    onChange={(e) => onUpdate(account.id, 'balance', parseFloat(e.target.value) || 0)}
                    placeholder="0"
                    className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Tiền tệ
                  </label>
                  <input
                    type="text"
                    value={account.currency}
                    onChange={(e) => onUpdate(account.id, 'currency', e.target.value)}
                    placeholder="VND"
                    className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  />
                </div>
              </div>

              {account.type === "bank" && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Ngân hàng
                    </label>
                    <input
                      type="text"
                      value={account.bankName || ""}
                      onChange={(e) => onUpdate(account.id, 'bankName', e.target.value)}
                      placeholder="VD: Vietcombank"
                      className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Số tài khoản
                    </label>
                    <input
                      type="text"
                      value={account.accountNumber || ""}
                      onChange={(e) => onUpdate(account.id, 'accountNumber', e.target.value)}
                      placeholder="VD: **********"
                      className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                    />
                  </div>
                </div>
              )}

              <div className="mt-3">
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Mô tả
                </label>
                <input
                  type="text"
                  value={account.description || ""}
                  onChange={(e) => onUpdate(account.id, 'description', e.target.value)}
                  placeholder="Mô tả thêm về tài khoản"
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
