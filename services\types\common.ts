/**
 * Common GraphQL types and interfaces used across the application
 */

// Scalar types
export type UUID = string;
export type DateTime = string; // ISO 8601 string format
export type JSON = Record<string, any>;

// Enums from Prisma schema
export enum AccountTypeType {
  NONE = 'none',
  CASH = 'cash',
  BANK = 'bank',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  E_WALLET = 'e_wallet',
  INVESTMENT = 'investment',
  LOAN = 'loan',
  OTHER = 'other'
}

export enum TransactionType {
  INCOME = 'INCOME',
  EXPENSE = 'EXPENSE',
  TRANSFER = 'TRANSFER',
  ADJUSTMENT = 'ADJUSTMENT'
}

export enum CategoryType {
  INCOME = 'INCOME',
  EXPENSE = 'EXPENSE',
  TRANSFER = 'TRANSFER',
  NONE = 'NONE'
}

export enum Language {
  EN = 'en',
  VI = 'vi'
}

export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system'
}

export enum PeriodType {
  NONE = 'NONE',
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  BIWEEKLY = 'BIWEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  BIANNUALLY = 'BIANNUALLY',
  YEARLY = 'YEARLY',
  CUSTOM = 'CUSTOM'
}

// Common status enums
export enum Status {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED'
}

// Common boolean-like enums
export enum BooleanEnum {
  TRUE = 'TRUE',
  FALSE = 'FALSE'
}

// Pagination types
export interface Connection<T> {
  edges: Array<{
    node: T;
    cursor: string;
  }>;
  pageInfo: {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    startCursor?: string;
    endCursor?: string;
  };
  totalCount: number;
}

export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor?: string;
  endCursor?: string;
}

export interface Edge<T> {
  node: T;
  cursor: string;
}

// Common input types
export interface PaginationInput {
  first?: number;
  after?: string;
  last?: number;
  before?: string;
}

export interface SortInput {
  field: string;
  direction: 'ASC' | 'DESC';
}

// Common response types
export interface BaseResponse {
  success: boolean;
  message?: string;
  errors?: Array<{ message: string; field?: string }>;
}

// Common filter input
export interface DateRangeInput {
  from?: DateTime;
  to?: DateTime;
}

// Common relation types
export type WithTimestamps = {
  createdAt: DateTime;
  updatedAt: DateTime;
  deletedAt?: DateTime;
};

export type WithSoftDelete = {
  isActive: boolean;
  deletedAt?: DateTime;
};

export type OmitOnEdit<T, K extends keyof T = never> = Omit<T, 'createdAt' | 'updatedAt' | 'deletedAt' | K>;
export type OmitOnCreate<T, K extends keyof T = never> = Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | K>;
