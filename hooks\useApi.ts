/**
 * useApi Hook
 * 
 * Generic hook for API calls with loading, error, and success states
 */

import { useState, useCallback } from 'react'

// ==================== TYPES ====================

export interface ApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
  success: boolean
}

export interface UseApiOptions {
  onSuccess?: (data: any) => void
  onError?: (error: string) => void
  showToast?: boolean
}

export interface UseApiReturn<T> extends ApiState<T> {
  execute: (...args: any[]) => Promise<T | null>
  reset: () => void
  setData: (data: T | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// ==================== HOOK ====================

/**
 * Generic API hook for handling async operations
 */
export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
): UseApiReturn<T> {
  const { onSuccess, onError, showToast = true } = options

  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false
  })

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false
    }))

    try {
      const result = await apiFunction(...args)
      
      setState(prev => ({
        ...prev,
        data: result,
        loading: false,
        success: true
      }))

      if (onSuccess) {
        onSuccess(result)
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        success: false
      }))

      if (onError) {
        onError(errorMessage)
      }

      if (showToast && typeof window !== 'undefined') {
        // You can integrate with your toast system here
        console.error('API Error:', errorMessage)
      }

      return null
    }
  }, [apiFunction, onSuccess, onError, showToast])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false
    })
  }, [])

  const setData = useCallback((data: T | null) => {
    setState(prev => ({ ...prev, data }))
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }))
  }, [])

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }))
  }, [])

  return {
    ...state,
    execute,
    reset,
    setData,
    setLoading,
    setError
  }
}

// ==================== SPECIALIZED HOOKS ====================

/**
 * Hook for API calls that return paginated data
 */
export function usePaginatedApi<T = any>(
  apiFunction: (...args: any[]) => Promise<{ data: T[]; pagination: any }>,
  options: UseApiOptions = {}
) {
  return useApi(apiFunction, options)
}

/**
 * Hook for API calls that create/update resources
 */
export function useMutationApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
) {
  return useApi(apiFunction, {
    showToast: true,
    ...options
  })
}

/**
 * Hook for API calls that delete resources
 */
export function useDeleteApi(
  apiFunction: (...args: any[]) => Promise<any>,
  options: UseApiOptions = {}
) {
  return useApi(apiFunction, {
    showToast: true,
    onSuccess: (data) => {
      console.log('Resource deleted successfully')
      if (options.onSuccess) {
        options.onSuccess(data)
      }
    },
    ...options
  })
}

export default useApi
