/**
 * GraphQL Context
 * 
 * Creates context for GraphQL resolvers with authentication and database access
 */

import { NextRequest } from 'next/server'
import { prisma } from '@/lib/database'
import { verifyToken, extractBearerToken } from '@/lib/auth/jwt'
import { User } from '@/lib/generated/prisma'

export interface GraphQLContext {
  prisma: typeof prisma
  user?: User
  userId?: string
  isAuthenticated: boolean
  request: NextRequest
}

export async function createContext({ req }: { req: NextRequest }): Promise<GraphQLContext> {
  const context: GraphQLContext = {
    prisma,
    isAuthenticated: false,
    request: req
  }

  try {
    // Try to authenticate the user
    const authHeader = req.headers.get('authorization')
    const token = extractBearerToken(authHeader || '')

    if (token) {
        // Verify token using JWT utility
        const payload = verifyToken(token)

        if (payload && payload.userId) {
          // Get user from database
          const user = await prisma.user.findUnique({
            where: { 
              id: payload.userId,
              isActive: true 
            },
            include: {
              preferences: true
            }
          })

          if (user) {
            context.user = user
            context.userId = user.id
            context.isAuthenticated = true
          }
        }
      }
  } catch (error) {
    // Authentication failed, but we don't throw error here
    // Some queries might be public or handle authentication themselves
    console.warn('GraphQL authentication failed:', error)
  }

  return context
}

/**
 * Helper function to require authentication in resolvers
 */
export function requireAuth(context: GraphQLContext): User {
  if (!context.isAuthenticated || !context.user) {
    throw new Error('Authentication required')
  }
  return context.user
}

/**
 * Helper function to require admin role
 */
export function requireAdmin(context: GraphQLContext): User {
  const user = requireAuth(context)
  if (user.role !== 'admin') {
    throw new Error('Admin access required')
  }
  return user
}

/**
 * Helper function to check if user owns resource or is admin
 */
export function requireOwnershipOrAdmin(context: GraphQLContext, resourceUserId: string): User {
  const user = requireAuth(context)
  if (user.role !== 'admin' && user.id !== resourceUserId) {
    throw new Error('Access denied: insufficient permissions')
  }
  return user
}
