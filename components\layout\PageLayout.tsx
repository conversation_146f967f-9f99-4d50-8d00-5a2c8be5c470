"use client"

import { useState, ReactNode } from "react"
import { Sidebar } from "../Sidebar"

import { Menu } from "lucide-react"

interface PageLayoutProps {
  children: ReactNode
  className?: string
}

export function PageLayout({ children, className = "" }: PageLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarMinimal, setSidebarMinimal] = useState(false)

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 ${className}`}>
      <div className="flex">
        <Sidebar 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)} 
          onMinimalChange={setSidebarMinimal} 
        />

        <main className="flex-1 p-3 lg:p-4 space-y-3">
          {children}
        </main>
      </div>
    </div>
  )
}
