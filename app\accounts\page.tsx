"use client"

import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AccountCard } from "@/components/features/accounts/AccountCard"
import { ListPageLayout } from "@/components/layout/ResponsiveLayout"
import { useUnifiedFilters } from "@/components/shared/controls/UnifiedFilterControls"
import { GenericList } from "@/components/ui/GenericList"
import { useAuth } from "@/contexts/AuthContext"
import { useEffect, useRef, useState } from "react"

import { useGlobalTaskbarRestore } from "@/components/layout/GlobalLayout"
import { AccountSidebarWithChildren } from "@/components/ui/AccountSidebarWithChildren"
import { Button } from "@/components/ui/button"
import { PortalDropdown } from "@/components/ui/PortalDropdown"
import { useSidebarManager } from "@/hooks/useSidebarManager"
import { AccountApiService } from "@/services/client"
import { AccountWithRelations } from "@/types/entities"
import { Building2, ChevronDown, ChevronUp, CreditCard, Plus, Search, SortAsc, SortDesc, Wallet } from "lucide-react"

function AccountsPageContent() {
  const { user } = useAuth()
  const {
    openSidebar,
    closeSidebar,
    isSidebarOpen
  } = useSidebarManager()

  const [selectedAccount, setSelectedAccount] = useState<AccountWithRelations | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [accounts, setAccounts] = useState<AccountWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Ref to prevent duplicate API calls
  const accountsLoadedRef = useRef(false)

  const [showSortOptions, setShowSortOptions] = useState(false)
  const sortButtonRef = useRef<HTMLButtonElement>(null)

  // Global taskbar restore
  const { restoredItem, restoreType, clearRestored } = useGlobalTaskbarRestore()

  // Fetch accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      if (!user?.id) {
        setError('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.')
        setLoading(false)
        return
      }

      // Prevent duplicate calls
      if (accountsLoadedRef.current) {
        return
      }

      try {
        accountsLoadedRef.current = true
        setLoading(true)
        console.log('🔄 Loading accounts...')

        const response = await AccountApiService.getAccounts({})
        if (response.success) {
          console.log('✅ Accounts loaded from API:', response.data)
          // Extract items array from paginated response
          const accountsData = (response.data as any)?.items || response.data || []
          setAccounts(Array.isArray(accountsData) ? accountsData : [])
        } else {
          setError('Không thể tải danh sách tài khoản')
        }
      } catch (err) {
        setError('Không thể tải danh sách tài khoản')
        console.error('❌ Accounts error:', err)
        // Reset ref on error so it can retry
        accountsLoadedRef.current = false
      } finally {
        setLoading(false)
      }
    }

    fetchAccounts()
  }, [user?.id])

  // Calculate statistics - handle empty/undefined accounts
  const safeAccounts = accounts || []
  console.log('DEBUG: safeAccounts type:', typeof safeAccounts, 'isArray:', Array.isArray(safeAccounts), 'length:', safeAccounts.length)

  // Unified filters
  const {
    filteredData: filteredAccounts,
    activeFilter,
    setActiveFilter,
    searchTerm,
    setSearchTerm,
    activeSortField,
    currentSortDirection,
    handleSortChange
  } = useUnifiedFilters({
    data: safeAccounts,
    filterField: 'accountTypeId',
    searchFields: ['name', 'description'],
    sortField: 'name',
    sortDirection: 'asc'
  })
  const totalAccounts = safeAccounts.length
  const activeAccounts = safeAccounts.filter(account => account.isActive !== false).length
  const inactiveAccounts = safeAccounts.filter(account => account.isActive === false).length
  const totalBalance = safeAccounts.reduce((sum, account) => sum + account.balance, 0)

  // Filter tabs for account types (matching recurring page pattern)
  const filterTabs = [
    { value: 'all', label: 'Tất cả', count: totalAccounts },
    { value: 'active', label: 'Hoạt động', count: activeAccounts },
    { value: 'inactive', label: 'Không hoạt động', count: inactiveAccounts }
  ]

  // Sort options
  const sortOptions = [
    { value: 'name', label: 'Tên tài khoản' },
    { value: 'balance', label: 'Số dư' },
    { value: 'accountTypeId', label: 'Loại tài khoản' },
    { value: 'createdAt', label: 'Ngày tạo' }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount)
  }



  // Account handlers
  const handleAddAccount = () => {
    if (!user?.id) {
      setError('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.')
      return
    }
    
    setSelectedAccount(null)
    setIsEditing(false)
    openSidebar('account')
  }

  const handleEditAccount = (account: AccountWithRelations) => {
    console.log('handleEditAccount called with account:', account)
    console.log('Account accountTypeId:', account.accountTypeId)
    setSelectedAccount(account)
    setIsEditing(true)
    openSidebar('account')
  }



  const handleCloseAccountSidebar = () => {
    closeSidebar('account')
    setSelectedAccount(null)
    setIsEditing(false)
  }

  const handleSaveAccount = async (accountData: any) => {
    if (!user?.id) {
      setError('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.')
      return
    }

    try {
      // Validate required fields
      if (!accountData.name?.trim()) {
        setError('Vui lòng nhập tên tài khoản')
        return
      }

      if (!accountData.accountTypeId) {
        setError('Vui lòng chọn loại tài khoản')
        return
      }

      if (accountData.balance !== undefined && isNaN(Number(accountData.balance))) {
        setError('Số dư phải là một số hợp lệ')
        return
      }

      setLoading(true)

      // Prepare account data
      const processedData = {
        ...accountData,
        name: accountData.name.trim(),
        description: accountData.description?.trim() || '',
        balance: Number(accountData.balance) || 0,
        isActive: Boolean(accountData.isActive)
      }

      let response
      if (isEditing) {
        response = await AccountApiService.updateAccount(accountData.id, processedData)
      } else {
        response = await AccountApiService.createAccount(processedData)
      }

      if (response.success) {
        // Optimistic update instead of full refresh
        if (isEditing) {
          // Update existing account
          setAccounts(prev => prev.map(acc =>
            acc.id === accountData.id
              ? { ...acc, ...processedData }
              : acc
          ))
        } else {
          // Add new account
          if (response.data) {
            setAccounts(prev => [...prev, response.data as AccountWithRelations])
          }
        }

        handleCloseAccountSidebar()
        setError(null)
        console.log(`Tài khoản đã được ${isEditing ? 'cập nhật' : 'tạo'} thành công`)
      } else {
        setError(typeof response.error === 'string' ? response.error : 'Không thể lưu tài khoản. Vui lòng thử lại.')
      }
    } catch (error: any) {
      console.error('Error saving account:', error)
      setError(error?.message || 'Không thể lưu tài khoản. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }







  // Handle global taskbar restore
  useEffect(() => {
    if (restoredItem && restoreType === 'account') {
      console.log('Restoring account from global taskbar:', restoredItem)
      if (restoredItem.data) {
        setSelectedAccount(restoredItem.data)
        setIsEditing(restoredItem.isEditing || false)
      }
      openSidebar('account')
      clearRestored()
    }
  }, [restoredItem, restoreType, clearRestored, openSidebar])

  return (
    <>
      <ListPageLayout
        title="Tài khoản"
        subtitle="Quản lý các tài khoản tài chính của bạn"
        stats={[
          {
            label: "Tổng số dư",
            value: formatCurrency(totalBalance),
            icon: <Wallet size={14} className="text-blue-500" />
          },
          {
            label: "Tổng tài khoản",
            value: totalAccounts.toString(),
            icon: <Building2 size={14} className="text-gray-500" />
          },
          {
            label: "Đang hoạt động",
            value: activeAccounts.toString(),
            icon: <CreditCard size={14} className="text-green-500" />
          }
        ]}
        actions={
          <Button onClick={handleAddAccount} className="flex items-center gap-2">
            <Plus size={16} />
            Thêm mới
          </Button>
        }

      >
        {/* Search, Filter and Sort Row */}
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-3 mb-4">
          <div className="flex flex-wrap items-center gap-2">
            {/* Type Filter Tabs */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-0.5 h-8">
              {filterTabs.map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => setActiveFilter(filter.value)}
                  className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 h-full flex items-center ${
                    activeFilter === filter.value
                      ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>

            {/* Search */}
            <div className="flex-1 min-w-[200px] relative">
              <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm tài khoản..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full h-8 pl-9 pr-3 bg-white/80 dark:bg-gray-700/80 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 dark:text-gray-100 text-xs transition-all duration-200"
              />
            </div>

            {/* Account Count */}
            <div className="flex items-center justify-center px-3 py-1 h-8 bg-gray-50 dark:bg-gray-700/50 rounded-lg text-xs text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-600">
              {filteredAccounts.length} tài khoản
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                ref={sortButtonRef}
                onClick={() => setShowSortOptions(!showSortOptions)}
                className="flex items-center gap-1 px-3 py-1 h-8 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 bg-white/80 dark:bg-gray-700/80 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg transition-all duration-200"
              >
                {currentSortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />}
                <span className="whitespace-nowrap">Sắp xếp</span>
                {showSortOptions ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
              </button>

              <PortalDropdown
                isOpen={showSortOptions}
                onClose={() => setShowSortOptions(false)}
                triggerRef={sortButtonRef}
              >
                {sortOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleSortChange(option.value)}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                      activeSortField === option.value
                        ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20"
                        : "text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      {option.label}
                      {activeSortField === option.value && (
                        currentSortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                      )}
                    </div>
                  </button>
                ))}
              </PortalDropdown>
            </div>
          </div>
        </div>

        <GenericList
          data={filteredAccounts}
          renderCard={(account) => (
            <AccountCard
              account={account}
              onEdit={() => handleEditAccount(account)}

              onToggleActive={async () => {
                // Optimistic update
                const updatedAccounts = accounts.map(acc =>
                  acc.id === account.id
                    ? { ...acc, isActive: !acc.isActive }
                    : acc
                )
                setAccounts(updatedAccounts)

                try {
                  // API call
                  const updatedAccount = {
                    isActive: !account.isActive,
                    description: account.description || undefined
                  }
                  const response = await AccountApiService.updateAccount(account.id, updatedAccount)

                  if (!response.success) {
                    // Rollback on error
                    setAccounts(accounts)
                    console.error('Failed to update account:', response.error)
                  }
                } catch (error) {
                  // Rollback on error
                  setAccounts(accounts)
                  console.error('Error updating account:', error)
                }
              }}
            />
          )}
          gridCols={{ default: 1, sm: 2, lg: 3, xl: 4 }}
          emptyMessage={
            filteredAccounts.length === 0 && accounts.length > 0
              ? `Không tìm thấy tài khoản nào phù hợp với bộ lọc hiện tại`
              : "Chưa có tài khoản nào. Hãy tạo tài khoản đầu tiên!"
          }
          loading={loading}
          searchable={false}
          sortable={false}
          paginated={true}
          defaultItemsPerPage={25}
          itemsPerPageOptions={[10, 25, 50, 100]}
          sidebarOpen={isSidebarOpen('account')}
          sidebarWidth={400}
        />
      </ListPageLayout>

      {/* Account Sidebar */}
      <AccountSidebarWithChildren
        isOpen={isSidebarOpen('account')}
        onClose={handleCloseAccountSidebar}
        onSave={handleSaveAccount}
        account={selectedAccount}
        isEditing={isEditing}
      />



      {/* Toast Notifications */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 animate-fade-in">
          <div className="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="flex-1">{error}</span>
            <button
              onClick={() => setError(null)}
              className="text-white hover:text-gray-200 p-1"
              aria-label="Đóng"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Global Loading Indicator */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 flex items-center gap-3 shadow-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-700 dark:text-gray-200 text-sm font-medium">Đang xử lý...</span>
          </div>
        </div>
      )}
    </>
  )
}

export default function AccountsPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <AccountsPageContent />
    </ProtectedRoute>
  )
}
