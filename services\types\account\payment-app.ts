import { BaseTableActiveable } from '../base';
import { UUID } from '../common';

export type {
  PaymentApp,
  PaymentAppBase,
  PaymentAppWithRelations,
  CreatePaymentAppInput,
  UpdatePaymentAppInput,
  PaymentAppFilterInput
};

/**
 * Base interface for PaymentApp
 */
interface PaymentAppBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  color: string;
  icon: string | null;
  userId: UUID;
  accountName: string | null;
  phoneNumber: string | null;
}

/**
 * PaymentApp with all relations
 */
interface PaymentAppWithRelations extends PaymentAppBase {
  user: UserBase;
  accountPaymentAppLinks: AccountPaymentAppLink[];
}

/**
 * Union type for PaymentApp
 */
type PaymentApp = PaymentAppBase | PaymentAppWithRelations;

/**
 * Input for creating a new payment app
 */
interface CreatePaymentAppInput {
  name: string;
  description?: string | null;
  color?: string;
  icon?: string | null;
  accountName?: string | null;
  phoneNumber?: string | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing payment app
 */
interface UpdatePaymentAppInput {
  id: UUID;
  name?: string;
  description?: string | null;
  color?: string;
  icon?: string | null;
  accountName?: string | null;
  phoneNumber?: string | null;
  isActive?: boolean;
}

/**
 * Input for filtering payment apps
 */
interface PaymentAppFilterInput {
  search?: string;
  userId?: UUID;
  isActive?: boolean;
}

// Import related types
import { UserBase } from '../user';
import { AccountPaymentAppLink } from './account';
