

// Import types that CategoryGoal depends on
import { CreateCategoryInput, UpdateCategoryInput, CategoryBase } from './category';
import { CategoryGoalBase, CreateCategoryGoalInput, UpdateCategoryGoalInput } from './categoryGoal';

/**
 * Type for category goal with category data
 */
interface CategoryWithGoal extends CategoryBase {
        categoryGoal?: CategoryGoalBase;
}

/**
 * Input type for creating/updating a category with a goal
 */
interface UpdateCategoryWithGoalInput extends UpdateCategoryInput {
        categoryGoal?: Omit<CreateCategoryGoalInput, 'categoryId'> | Omit<UpdateCategoryGoalInput, 'categoryId'>;
}

interface CreateCategoryWithGoalInput extends CreateCategoryInput {
        categoryGoal?: Omit<CreateCategoryGoalInput, 'categoryId'> | Omit<UpdateCategoryGoalInput, 'categoryId'>;
}


export type { CategoryWithGoal, UpdateCategoryWithGoalInput, CreateCategoryWithGoalInput }