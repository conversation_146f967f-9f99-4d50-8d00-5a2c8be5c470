/**
 * RecurringTransaction GraphQL Resolvers
 * 
 * Resolvers for RecurringTransaction queries and mutations using Prisma directly
 */

import { GraphQLContext, requireAuth, requireOwnershipOrAdmin } from '../context'
import { prisma } from '@/lib/database'
import { GraphQLError } from 'graphql'

interface RecurringTransactionArgs {
  id: string
}

interface RecurringTransactionsArgs {
  first?: number
  after?: string
  search?: string
  kind?: 'income' | 'expense' | 'transfer'
  frequency?: string
  isActive?: boolean
}

interface CreateRecurringTransactionArgs {
  input: {
    name: string
    description?: string
    amount: number
    kind: 'income' | 'expense' | 'transfer'
    frequency: string
    startDate: Date
    endDate?: Date
    accountId?: string
    categoryId?: string
    contactId?: string
    fromAccountId?: string
    toAccountId?: string
  }
}

interface UpdateRecurringTransactionArgs {
  id: string
  input: {
    name?: string
    description?: string
    amount?: number
    kind?: 'income' | 'expense' | 'transfer'
    frequency?: string
    startDate?: Date
    endDate?: Date
    accountId?: string
    categoryId?: string
    contactId?: string
    fromAccountId?: string
    toAccountId?: string
    isActive?: boolean
  }
}

// Helper function to build connection response
function buildConnection<T>(
  items: T[],
  totalCount: number,
  first?: number,
  after?: string
) {
  const edges = items.map((item: any, index) => ({
    node: item,
    cursor: Buffer.from(`${after ? parseInt(Buffer.from(after, 'base64').toString()) + index + 1 : index}`).toString('base64')
  }))

  const hasNextPage = first ? items.length === first : false
  const hasPreviousPage = !!after

  return {
    edges,
    pageInfo: {
      hasNextPage,
      hasPreviousPage,
      startCursor: edges.length > 0 ? edges[0].cursor : null,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : null
    },
    totalCount
  }
}

export const recurringTransactionResolvers = {
  Query: {
    recurringTransactions: async (_: any, args: RecurringTransactionsArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const { first = 25, after, search, kind, frequency, isActive } = args
      
      // Convert cursor to page number
      const page = after ? Math.floor(parseInt(Buffer.from(after, 'base64').toString()) / first) + 1 : 1

      // Build where clause
      const where: any = {
        userId: user.id
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (kind) where.kind = kind
      if (frequency) where.frequency = frequency
      if (isActive !== undefined) where.isActive = isActive

      // Convert cursor to skip value
      const skip = after ? parseInt(Buffer.from(after, 'base64').toString()) : 0

      const [recurringTransactions, totalCount] = await Promise.all([
        prisma.recurringTransaction.findMany({
          where,
          include: {
            account: true,
            category: true,
            contact: true
          },
          skip,
          take: first,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.recurringTransaction.count({ where })
      ])

      return buildConnection(recurringTransactions, totalCount, first, after)
    },

    recurringTransaction: async (_: any, args: RecurringTransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      const recurringTransaction = await prisma.recurringTransaction.findFirst({
        where: {
          id: args.id,
          userId: user.id
        },
        include: {
          account: true,
          category: true,
          contact: true
        }
      })

      if (!recurringTransaction) {
        throw new GraphQLError('Recurring transaction not found')
      }

      // Check ownership
      requireOwnershipOrAdmin(context, recurringTransaction.userId)

      return recurringTransaction
    }
  },

  Mutation: {
    createRecurringTransaction: async (_: any, args: CreateRecurringTransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        const { input } = args

        // Verify account exists and belongs to user
        const account = await prisma.account.findFirst({
          where: {
            id: input.accountId,
            userId: user.id
          }
        })

        if (!account) {
          throw new GraphQLError('Account not found')
        }

        const recurringTransaction = await prisma.recurringTransaction.create({
          data: {
            name: input.name,
            description: input.description,
            amount: input.amount,
            kind: input.kind,
            frequency: input.frequency,
            startDate: input.startDate,
            endDate: input.endDate,
            accountId: input.accountId,
            categoryId: input.categoryId,
            contactId: input.contactId,
            fromAccountId: input.fromAccountId,
            toAccountId: input.toAccountId,
            userId: user.id
          },
          include: {
            account: true,
            category: true,
            contact: true
          }
        })
        
        return recurringTransaction
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to create recurring transaction')
      }
    },

    updateRecurringTransaction: async (_: any, args: UpdateRecurringTransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        // First check if recurring transaction exists and user has permission
        const existingTransaction = await prisma.recurringTransaction.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })
        if (!existingTransaction) {
          throw new GraphQLError('Recurring transaction not found')
        }

        requireOwnershipOrAdmin(context, existingTransaction.userId)

        const { input } = args

        const recurringTransaction = await prisma.recurringTransaction.update({
          where: { id: args.id },
          data: {
            name: input.name,
            description: input.description,
            amount: input.amount,
            kind: input.kind,
            frequency: input.frequency,
            startDate: input.startDate,
            endDate: input.endDate,
            accountId: input.accountId,
            categoryId: input.categoryId,
            contactId: input.contactId,
            fromAccountId: input.fromAccountId,
            toAccountId: input.toAccountId,
            isActive: input.isActive
          },
          include: {
            account: true,
            category: true,
            contact: true
          }
        })
        
        return recurringTransaction
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to update recurring transaction')
      }
    },

    deleteRecurringTransaction: async (_: any, args: RecurringTransactionArgs, context: GraphQLContext) => {
      const user = requireAuth(context)

      try {
        // First check if recurring transaction exists and user has permission
        const existingTransaction = await prisma.recurringTransaction.findFirst({
          where: {
            id: args.id,
            userId: user.id
          }
        })
        if (!existingTransaction) {
          throw new GraphQLError('Recurring transaction not found')
        }

        requireOwnershipOrAdmin(context, existingTransaction.userId)

        // Soft delete by setting isActive to false
        await prisma.recurringTransaction.update({
          where: { id: args.id },
          data: { isActive: false }
        })

        return true
      } catch (error: any) {
        throw new GraphQLError(error.message || 'Failed to delete recurring transaction')
      }
    }
  },

  RecurringTransaction: {
    user: async (parent: any, _: any, context: GraphQLContext) => {
      if (parent.user) {
        return parent.user
      }

      return await context.prisma.user.findUnique({
        where: { id: parent.userId }
      })
    },

    account: async (parent: any, _: any, context: GraphQLContext) => {
      if (!parent.accountId) {
        return null
      }

      if (parent.account) {
        return parent.account
      }

      return await context.prisma.account.findUnique({
        where: { id: parent.accountId },
        include: { accountType: true }
      })
    },

    category: async (parent: any, _: any, context: GraphQLContext) => {
      if (!parent.categoryId) {
        return null
      }

      if (parent.category) {
        return parent.category
      }

      return await context.prisma.category.findUnique({
        where: { id: parent.categoryId }
      })
    },

    contact: async (parent: any, _: any, context: GraphQLContext) => {
      if (!parent.contactId) {
        return null
      }

      if (parent.contact) {
        return parent.contact
      }

      return await context.prisma.contact.findUnique({
        where: { id: parent.contactId },
        include: { contactType: true }
      })
    },

    fromAccount: async (parent: any, _: any, context: GraphQLContext) => {
      if (!parent.fromAccountId) {
        return null
      }

      if (parent.fromAccount) {
        return parent.fromAccount
      }

      return await context.prisma.account.findUnique({
        where: { id: parent.fromAccountId },
        include: { accountType: true }
      })
    },

    toAccount: async (parent: any, _: any, context: GraphQLContext) => {
      if (!parent.toAccountId) {
        return null
      }

      if (parent.toAccount) {
        return parent.toAccount
      }

      return await context.prisma.account.findUnique({
        where: { id: parent.toAccountId },
        include: { accountType: true }
      })
    }
  }
}
