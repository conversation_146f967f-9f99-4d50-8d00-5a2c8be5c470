"use client"

import React from "react"
import { Link, Unlink, Smartphone, Building2 } from "lucide-react"
import { AccountPaymentApp } from "@/lib/generated/prisma"
import { AccountPaymentAppWithRelations } from "@/types/entities"

interface PaymentAppLinkCardProps {
  link: AccountPaymentAppWithRelations
  onUnlink?: (link: AccountPaymentAppWithRelations) => void
  onToggleStatus?: (link: AccountPaymentAppWithRelations) => void
  showActions?: boolean
  compact?: boolean
  viewMode?: 'bank-to-app' | 'app-to-bank' // Show from bank perspective or app perspective
}

export function PaymentAppLinkCard({
  link,
  onUnlink,
  onToggleStatus,
  showActions = true,
  compact = false,
  viewMode = 'bank-to-app'
}: PaymentAppLinkCardProps) {
  const getAppColor = (appName: string) => {
    switch (appName.toLowerCase()) {
      case 'momo':
        return 'bg-pink-500'
      case 'zalopay':
        return 'bg-blue-500'
      case 'viettelpay':
        return 'bg-orange-500'
      case 'ví điện tử':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getAppIcon = (appName: string) => {
    return <Smartphone size={16} />
  }

  const getBankIcon = () => {
    return <Building2 size={16} />
  }

  const primaryAccount = viewMode === 'bank-to-app' ? link.account : link.paymentApp
  const secondaryAccount = viewMode === 'bank-to-app' ? link.paymentApp : link.account
  const primaryIcon = viewMode === 'bank-to-app' ? getBankIcon() : getAppIcon(primaryAccount?.name || '')
  const secondaryIcon = viewMode === 'bank-to-app' ? getAppIcon(secondaryAccount?.name || '') : getBankIcon()
  const primaryColor = viewMode === 'bank-to-app' ? 'bg-blue-500' : getAppColor(primaryAccount?.name || '')
  const secondaryColor = viewMode === 'bank-to-app' ? getAppColor(secondaryAccount?.name || '') : 'bg-blue-500'

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
        <div className="flex items-center space-x-3">
          {/* Primary Account */}
          <div className={`w-8 h-8 rounded-lg ${primaryColor} flex items-center justify-center text-white`}>
            {primaryIcon}
          </div>
          
          {/* Link Icon */}
          <div className="flex items-center">
            <Link size={14} className="text-gray-400" />
          </div>
          
          {/* Secondary Account */}
          <div className={`w-8 h-8 rounded-lg ${secondaryColor} flex items-center justify-center text-white`}>
            {secondaryIcon}
          </div>
          
          <div>
            <p className="font-medium text-gray-900">
              {primaryAccount?.name} → {secondaryAccount?.name}
            </p>
            <p className="text-sm text-gray-500">
              {link.isActive ? 'Đang hoạt động' : 'Không hoạt động'}
            </p>
          </div>
        </div>
        
        {showActions && (
          <div className="flex items-center space-x-2">
            {onToggleStatus && (
              <button
                onClick={() => onToggleStatus(link)}
                className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                  link.isActive 
                    ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                }`}
                title={link.isActive ? "Tạm dừng liên kết" : "Kích hoạt liên kết"}
              >
                {link.isActive ? 'Tạm dừng' : 'Kích hoạt'}
              </button>
            )}
            {onUnlink && (
              <button
                onClick={() => onUnlink(link)}
                className="p-1 text-red-600 hover:text-red-800 transition-colors"
                title="Hủy liên kết"
              >
                <Unlink size={16} />
              </button>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 hover:border-gray-300 transition-all duration-200 hover:shadow-md group">
      {/* Link Header */}
      <div className="p-6 pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Primary Account */}
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-xl ${primaryColor} flex items-center justify-center text-white`}>
                {primaryIcon}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{primaryAccount?.name}</h3>
                <p className="text-sm text-gray-500">{primaryAccount?.description}</p>
              </div>
            </div>
            
            {/* Link Arrow */}
            <div className="flex items-center px-2">
              <Link size={20} className="text-gray-400" />
            </div>
            
            {/* Secondary Account */}
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-xl ${secondaryColor} flex items-center justify-center text-white`}>
                {secondaryIcon}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{secondaryAccount?.name}</h3>
                <p className="text-sm text-gray-500">{secondaryAccount?.description}</p>
              </div>
            </div>
          </div>
          
          {showActions && (
            <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
              {onToggleStatus && (
                <button
                  onClick={() => onToggleStatus(link)}
                  className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    link.isActive 
                      ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                      : 'bg-green-100 text-green-700 hover:bg-green-200'
                  }`}
                  title={link.isActive ? "Tạm dừng liên kết" : "Kích hoạt liên kết"}
                >
                  {link.isActive ? 'Tạm dừng' : 'Kích hoạt'}
                </button>
              )}
              {onUnlink && (
                <button
                  onClick={() => onUnlink(link)}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                  title="Hủy liên kết"
                >
                  <Unlink size={18} />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Link Details */}
      <div className="px-6 pb-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500 mb-1">Tài khoản nguồn</p>
              <p className="font-medium text-gray-900">{link.account?.name}</p>
              <p className="text-sm text-gray-500">{link.account?.accountType?.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Ứng dụng thanh toán</p>
              <p className="font-medium text-gray-900">{link.paymentApp?.name}</p>
              <p className="text-sm text-gray-500">{link.paymentApp?.accountType?.name}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Status and Date */}
      <div className="px-6 pb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${link.isActive ? 'bg-green-500' : 'bg-gray-400'}`} />
            <span className={`text-sm font-medium ${link.isActive ? 'text-green-700' : 'text-gray-500'}`}>
              {link.isActive ? 'Đang hoạt động' : 'Không hoạt động'}
            </span>
          </div>
          
          <p className="text-xs text-gray-400">
            Liên kết: {new Date(link.createdAt).toLocaleDateString('vi-VN')}
          </p>
        </div>
      </div>
    </div>
  )
}
