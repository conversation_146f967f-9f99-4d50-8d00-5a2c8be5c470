/**
 * Apollo GraphQL Client Configuration
 * 
 * Centralized GraphQL client setup with authentication, caching, and error handling
 */

import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'
import { RetryLink } from '@apollo/client/link/retry'

// GraphQL endpoint
const httpLink = createHttpLink({
  uri: '/api/graphql',
  credentials: 'same-origin'
})

// Authentication link - adds JWT token to headers
const authLink = setContext((_, { headers }) => {
  // Get token from localStorage
  const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null
  
  return {
    headers: {
      ...headers,
      ...(token && { authorization: `Bearer ${token}` })
    }
  }
})

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path, extensions }) => {
      console.error(
        `GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`
      )
      
      // Handle authentication errors
      if (extensions?.code === 'UNAUTHENTICATED') {
        // Clear tokens and redirect to login
        if (typeof window !== 'undefined') {
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
          // You might want to redirect to login page here
          // window.location.href = '/login'
        }
      }
    })
  }

  if (networkError) {
    console.error(`Network error: ${networkError}`)
    
    // Handle network errors
    if (networkError.statusCode === 401) {
      // Clear tokens on 401
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
      }
    }
  }
})

// Retry link for failed requests
const retryLink = new RetryLink({
  delay: {
    initial: 300,
    max: Infinity,
    jitter: true
  },
  attempts: {
    max: 3,
    retryIf: (error, _operation) => !!error
  }
})

// Cache configuration
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        // Pagination for connections
        users: {
          keyArgs: ['search', 'role', 'isActive'],
          merge(existing, incoming) {
            if (!existing) return incoming
            
            return {
              ...incoming,
              edges: [...(existing.edges || []), ...(incoming.edges || [])]
            }
          }
        },
        accounts: {
          keyArgs: ['search', 'accountTypeId', 'isActive'],
          merge(existing, incoming) {
            if (!existing) return incoming
            
            return {
              ...incoming,
              edges: [...(existing.edges || []), ...(incoming.edges || [])]
            }
          }
        },
        transactions: {
          keyArgs: ['search', 'kind', 'accountId', 'categoryId', 'startDate', 'endDate', 'isActive'],
          merge(existing, incoming) {
            if (!existing) return incoming
            
            return {
              ...incoming,
              edges: [...(existing.edges || []), ...(incoming.edges || [])]
            }
          }
        },
        categories: {
          keyArgs: ['search', 'type', 'parentId', 'isActive'],
          merge(existing, incoming) {
            if (!existing) return incoming
            
            return {
              ...incoming,
              edges: [...(existing.edges || []), ...(incoming.edges || [])]
            }
          }
        },
        contacts: {
          keyArgs: ['search', 'contactTypeId', 'isActive'],
          merge(existing, incoming) {
            if (!existing) return incoming
            
            return {
              ...incoming,
              edges: [...(existing.edges || []), ...(incoming.edges || [])]
            }
          }
        }
      }
    },
    User: {
      fields: {
        accounts: {
          merge(existing, incoming) {
            return incoming
          }
        },
        transactions: {
          merge(existing, incoming) {
            return incoming
          }
        }
      }
    }
  }
})

// Create Apollo Client
export const apolloClient = new ApolloClient({
  link: from([errorLink, retryLink, authLink, httpLink]),
  cache,
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true
    },
    query: {
      errorPolicy: 'all'
    },
    mutate: {
      errorPolicy: 'all'
    }
  },
  connectToDevTools: process.env.NODE_ENV === 'development'
})

// Helper function to update auth token
export const updateAuthToken = (token: string | null) => {
  if (typeof window !== 'undefined') {
    if (token) {
      localStorage.setItem('accessToken', token)
    } else {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
    }
  }
  
  // Reset Apollo Client cache to clear any cached data
  apolloClient.resetStore()
}

// Helper function to clear cache
export const clearCache = () => {
  apolloClient.clearStore()
}

// Helper function to refetch all active queries
export const refetchQueries = () => {
  apolloClient.refetchQueries({
    include: 'active'
  })
}

export default apolloClient
