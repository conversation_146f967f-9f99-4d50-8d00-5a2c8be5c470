/**
 * Common Validation Utilities
 * 
 * Shared validation functions for consistent form validation across the application
 */

export interface ValidationResult {
  isValid: boolean
  error?: string
}

export interface ValidationRule {
  validate: (value: any) => ValidationResult
  message?: string
}

// Common validation rules
export const validationRules = {
  required: (fieldName: string): ValidationRule => ({
    validate: (value: any) => ({
      isValid: value !== null && value !== undefined && value !== '',
      error: `${fieldName} là bắt buộc`
    })
  }),

  minLength: (min: number, fieldName: string): ValidationRule => ({
    validate: (value: string) => ({
      isValid: !value || value.length >= min,
      error: `${fieldName} phải có ít nhất ${min} ký tự`
    })
  }),

  maxLength: (max: number, fieldName: string): ValidationRule => ({
    validate: (value: string) => ({
      isValid: !value || value.length <= max,
      error: `${fieldName} không được vượt quá ${max} ký tự`
    })
  }),

  email: (): ValidationRule => ({
    validate: (value: string) => {
      if (!value) return { isValid: true }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return {
        isValid: emailRegex.test(value),
        error: 'Email không hợp lệ'
      }
    }
  }),

  phone: (): ValidationRule => ({
    validate: (value: string) => {
      if (!value) return { isValid: true }
      const phoneRegex = /^[0-9+\-\s()]+$/
      return {
        isValid: phoneRegex.test(value) && value.length >= 10,
        error: 'Số điện thoại không hợp lệ'
      }
    }
  }),

  positiveNumber: (fieldName: string): ValidationRule => ({
    validate: (value: any) => {
      if (!value && value !== 0) return { isValid: true }
      const num = Number(value)
      return {
        isValid: !isNaN(num) && num > 0,
        error: `${fieldName} phải là số dương`
      }
    }
  }),

  nonNegativeNumber: (fieldName: string): ValidationRule => ({
    validate: (value: any) => {
      if (!value && value !== 0) return { isValid: true }
      const num = Number(value)
      return {
        isValid: !isNaN(num) && num >= 0,
        error: `${fieldName} phải là số không âm`
      }
    }
  }),

  currency: (fieldName: string): ValidationRule => ({
    validate: (value: any) => {
      if (!value && value !== 0) return { isValid: true }
      const num = Number(value)
      return {
        isValid: !isNaN(num) && num >= 0 && Number.isFinite(num),
        error: `${fieldName} phải là số tiền hợp lệ`
      }
    }
  }),

  date: (fieldName: string): ValidationRule => ({
    validate: (value: string) => {
      if (!value) return { isValid: true }
      const date = new Date(value)
      return {
        isValid: !isNaN(date.getTime()),
        error: `${fieldName} không hợp lệ`
      }
    }
  }),

  futureDate: (fieldName: string): ValidationRule => ({
    validate: (value: string) => {
      if (!value) return { isValid: true }
      const date = new Date(value)
      const now = new Date()
      return {
        isValid: !isNaN(date.getTime()) && date > now,
        error: `${fieldName} phải là ngày trong tương lai`
      }
    }
  }),

  pastDate: (fieldName: string): ValidationRule => ({
    validate: (value: string) => {
      if (!value) return { isValid: true }
      const date = new Date(value)
      const now = new Date()
      return {
        isValid: !isNaN(date.getTime()) && date <= now,
        error: `${fieldName} không được là ngày trong tương lai`
      }
    }
  }),

  custom: (validator: (value: any) => boolean, message: string): ValidationRule => ({
    validate: (value: any) => ({
      isValid: validator(value),
      error: message
    })
  })
}

// Validation composer
export function validateField(value: any, rules: ValidationRule[]): ValidationResult {
  for (const rule of rules) {
    const result = rule.validate(value)
    if (!result.isValid) {
      return result
    }
  }
  return { isValid: true }
}

// Form validation
export function validateForm(data: Record<string, any>, schema: Record<string, ValidationRule[]>): {
  isValid: boolean
  errors: Record<string, string>
} {
  const errors: Record<string, string> = {}
  let isValid = true

  for (const [field, rules] of Object.entries(schema)) {
    const result = validateField(data[field], rules)
    if (!result.isValid && result.error) {
      errors[field] = result.error
      isValid = false
    }
  }

  return { isValid, errors }
}

// Common validation schemas
export const commonSchemas = {
  transaction: {
    name: [validationRules.required('Tên giao dịch'), validationRules.maxLength(255, 'Tên giao dịch')],
    amount: [validationRules.required('Số tiền'), validationRules.positiveNumber('Số tiền')],
    date: [validationRules.required('Ngày giao dịch'), validationRules.date('Ngày giao dịch')],
    accountId: [validationRules.required('Tài khoản')]
  },

  account: {
    name: [validationRules.required('Tên tài khoản'), validationRules.maxLength(255, 'Tên tài khoản')],
    accountTypeId: [validationRules.required('Loại tài khoản')],
    balance: [validationRules.currency('Số dư')]
  },

  category: {
    name: [validationRules.required('Tên danh mục'), validationRules.maxLength(255, 'Tên danh mục')],
    type: [validationRules.required('Loại danh mục')],
    color: [validationRules.required('Màu sắc')]
  },

  contact: {
    name: [validationRules.required('Tên liên hệ'), validationRules.maxLength(255, 'Tên liên hệ')],
    email: [validationRules.email()],
    phone: [validationRules.phone()]
  },

  recurringTransaction: {
    name: [validationRules.required('Tên giao dịch định kỳ'), validationRules.maxLength(255, 'Tên giao dịch định kỳ')],
    amount: [validationRules.required('Số tiền'), validationRules.positiveNumber('Số tiền')],
    frequency: [validationRules.required('Tần suất')],
    startDate: [validationRules.required('Ngày bắt đầu'), validationRules.date('Ngày bắt đầu')]
  }
}

// Utility functions
export function sanitizeInput(value: string): string {
  return value?.trim() || ''
}

export function formatValidationErrors(errors: Record<string, string>): string {
  return Object.values(errors).join(', ')
}

export function getFirstValidationError(errors: Record<string, string>): string | null {
  const errorKeys = Object.keys(errors)
  return errorKeys.length > 0 ? errors[errorKeys[0]] : null
}
