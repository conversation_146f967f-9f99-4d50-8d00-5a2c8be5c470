"use client"

import React, { useState, useEffect } from "react"
import { Contact } from "@/lib/generated/prisma"
import { Modal } from "./Modal"
import { Button } from "./button"
import { Building2 } from "lucide-react"
import { BasicTransactionFields } from "./transaction-modal/BasicTransactionFields"
import { AmountDateTimeFields } from "./transaction-modal/AmountDateTimeFields"
import { TransferFields } from "./transaction-modal/TransferFields"
import { AddEditContactModal } from "./AddEditContactModal"

interface TransferDetails {
  contactId?: number
  note?: string
}

interface Transaction {
  id: number
  userId: number
  accountId: number
  categoryId: number
  amount: number
  type: "income" | "expense" | "transfer"
  description: string
  date: Date
  transferTo?: string
  transferToDetails?: TransferDetails
  transferFromDetails?: TransferDetails
  isRecurring: boolean
  recurringId?: number
  createdAt: Date
  updatedAt: Date
}

interface Account {
  id: number
  name: string
  type: string
  balance: number
  currency: string
  color?: string
  logo?: string
}

interface Category {
  id: number
  name: string
  type: "income" | "expense"
  color: string
  icon: string
}



interface AddEditTransactionModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (transaction: Transaction) => void
  transaction?: Transaction | null
  isEditing: boolean
  accounts: Account[]
  categories: Category[]
  contacts: Contact[]
  onContactCreated?: (contact: Contact) => void
}

interface FormData {
  accountId: string
  categoryId: string
  amount: string
  type: "income" | "expense" | "transfer"
  description: string
  date: string
  time: string
  transferTo: string
  transferToContactId: string
  transferToNote: string
  transferFromContactId: string
  transferFromNote: string
  isRecurring: boolean
  notes: string
  location: string
  reference: string
}

interface FormErrors {
  accountId?: string
  categoryId?: string
  amount?: string
  description?: string
  transferTo?: string
  transferToNote?: string
  transferFromNote?: string
  date?: string
  time?: string
}

export function AddEditTransactionModal({
  isOpen,
  onClose,
  onSave,
  onDelete,
  transaction,
  isEditing,
  accounts,
  categories,
  contacts,
  onContactCreated
}: AddEditTransactionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    accountId: "",
    categoryId: "",
    amount: "",
    type: "expense",
    description: "",
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    transferTo: "",
    transferToContactId: "",
    transferToNote: "",
    transferFromContactId: "",
    transferFromNote: "",
    isRecurring: false,
    notes: "",
    location: "",
    reference: ""
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  const [showContactDropdown, setShowContactDropdown] = useState(false)
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([])
  const [saveAndAddAnother, setSaveAndAddAnother] = useState(false)
  const [showNewContactConfirm, setShowNewContactConfirm] = useState(false)
  const [addToPermanentContacts, setAddToPermanentContacts] = useState(true)
  const [showAddContactModal, setShowAddContactModal] = useState(false)
  const [newContactData, setNewContactData] = useState({
    name: "",
    phone: "",
    email: "",
    description: "",
    paymentMethod: "bank" as "bank" | "cash",
    bankName: "",
    accountNumber: "",
    accountHolderName: ""
  })

  useEffect(() => {
    if (isOpen) {
      if (transaction && isEditing) {
        const transactionDate = new Date(transaction.date)
        setFormData({
          accountId: transaction.accountId.toString(),
          categoryId: transaction.categoryId.toString(),
          amount: transaction.amount.toString(),
          type: transaction.kind,
          description: transaction.description,
          date: transaction.date.toISOString().split('T')[0],
          time: transactionDate.toTimeString().slice(0, 5),
          transferTo: transaction.transferTo || "",
          transferToContactId: transaction.transferToDetails?.contactId?.toString() || "",
          transferToNote: transaction.transferToDetails?.note || "",
          transferFromContactId: transaction.fromAccount?.contactId?.toString() || "",
          transferFromNote: transaction.fromAccount?.note || "",
          isRecurring: false /* TODO: Add recurring support */,
          notes: "",
          location: "",
          reference: ""
        })
      } else {
        setFormData({
          accountId: "",
          categoryId: "",
          amount: "",
          type: "expense",
          description: "",
          date: new Date().toISOString().split('T')[0],
          time: new Date().toTimeString().slice(0, 5),
          transferTo: "",
          transferToContactId: "",
          transferToNote: "",
          transferFromContactId: "",
          transferFromNote: "",
          isRecurring: false,
          notes: "",
          location: "",
          reference: ""
        })
      }
      setErrors({})
    }
  }, [isOpen, transaction, isEditing])

  useEffect(() => {
    // Filter contacts based on transferTo input
    if (formData.transferTo) {
      const filtered = contacts.filter(contact =>
        contact.name.toLowerCase().includes(formData.transferTo.toLowerCase())
      )
      setFilteredContacts(filtered)
    } else {
      setFilteredContacts(contacts)
    }
  }, [formData.transferTo, contacts])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      // Ctrl/Cmd + Enter to save
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault()
        const form = document.querySelector('form')
        if (form) {
          form.requestSubmit()
        }
      }

      // Ctrl/Cmd + Shift + Enter to save and add another
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'Enter') {
        e.preventDefault()
        setSaveAndAddAnother(true)
        const form = document.querySelector('form')
        if (form) {
          form.requestSubmit()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen])





  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.accountId) {
      newErrors.accountId = "Account is required"
    }

    if (!formData.categoryId) {
      newErrors.categoryId = "Category is required"
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = "Amount must be greater than 0"
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required"
    }

    if (!formData.date) {
      newErrors.date = "Date is required"
    }

    if (!formData.time) {
      newErrors.time = "Time is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Helper function to create new contact if needed
  const createContactIfNeeded = async (contactName: string, isPermanent: boolean = true): Promise<Contact | null> => {
    if (!contactName.trim()) return null

    // Check if contact already exists
    const existingContact = contacts.find(contact =>
      contact.name.toLowerCase() === contactName.toLowerCase()
    )

    if (existingContact) return existingContact

    // For temporary contacts, return null (we'll just use the name in transaction)
    if (!isPermanent) {
      console.log('Using temporary contact name:', contactName.trim())
      return null
    }

    const newContact: Contact = {
      id: Date.now().toString(), // Temporary ID, will be replaced by backend
      userId: '1', // Should be current user ID
      name: newContactData.name.trim(),
      phone: newContactData.phone,
      email: newContactData.email,
      accounts: [],
      description: newContactData.description || `Auto-created from transfer transaction`,
      isActive: true,
      isUserContact: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    try {
      // Here you would call your API to create the contact
      // For now, we'll just add it to the local state
      console.log('Creating new permanent contact:', newContact)

      // Add to contacts list (this would normally be done via API call)
      // You might want to call a contact creation API here

      return newContact
    } catch (error) {
      console.error('Error creating contact:', error)
      return null
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    // Check if we need to create a new contact for transfer
    if (formData.type === 'transfer' && formData.transferTo) {
      const existingContact = contacts.find(contact =>
        contact.name.toLowerCase() === formData.transferTo.toLowerCase()
      )

      if (!existingContact && formData.transferTo.trim()) {
        // Show confirmation dialog for new contact creation
        setAddToPermanentContacts(true) // Default to checked
        setNewContactData({
          name: formData.transferTo.trim(),
          phone: "",
          email: "",
          description: "",
          paymentMethod: "bank",
          bankName: "",
          accountNumber: "",
          accountHolderName: formData.transferTo.trim()
        })
        setShowNewContactConfirm(true)
        return
      }
    }

    await processTransaction()
  }

  const processTransaction = async () => {
    setLoading(true)

    try {
      // Auto-create contact if needed for transfer transactions
      if (formData.type === 'transfer' && formData.transferTo) {
        await createContactIfNeeded(formData.transferTo, addToPermanentContacts)
      }
      // Combine date and time
      const combinedDateTime = new Date(`${formData.date}T${formData.time}:00`)

      // Prepare transfer details
      const transferToDetails: TransferDetails | undefined = formData.transferToContactId ? {
        contactId: parseInt(formData.transferToContactId),
        note: formData.transferToNote.trim() || undefined
      } : undefined

      const transferFromDetails: TransferDetails | undefined = formData.transferFromContactId ? {
        contactId: parseInt(formData.transferFromContactId),
        note: formData.transferFromNote.trim() || undefined
      } : undefined

      const transactionData: Transaction = {
        id: transaction?.id || 0,
        userId: 1, // Default user
        accountId: parseInt(formData.accountId),
        categoryId: parseInt(formData.categoryId),
        amount: parseFloat(formData.amount),
        type: formData.type,
        description: formData.description.trim(),
        date: combinedDateTime,
        transferTo: formData.transferTo.trim() || undefined,
        transferToDetails,
        transferFromDetails,

        isRecurring: formData.isRecurring,
        recurringId: transaction?.recurringId,
        createdAt: transaction?.createdAt || new Date(),
        updatedAt: new Date()
      }

      if (isEditing && transaction) {
        // Update existing transaction
        const response = await fetch(`/api/transactions/${transaction.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(transactionData)
        })

        if (!response.ok) {
          throw new Error('Failed to update transaction')
        }

        const updatedTransaction = await response.json()
        onSave(updatedTransaction)
      } else {
        // Create new transaction
        const response = await fetch('/api/transactions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(transactionData)
        })

        if (!response.ok) {
          throw new Error('Failed to create transaction')
        }

        const newTransaction = await response.json()
        onSave(newTransaction)
      }

      if (saveAndAddAnother && !isEditing) {
        // Reset form for new transaction but keep some fields
        setFormData(prev => ({
          accountId: prev.accountId, // Keep account
          categoryId: prev.categoryId, // Keep category
          type: prev.type, // Keep type
          amount: "",
          description: "",
          date: new Date().toISOString().split('T')[0],
          time: new Date().toTimeString().slice(0, 5),
          transferTo: "",
          transferToContactId: "",
          transferToNote: "",
          transferFromContactId: "",
          transferFromNote: "",
          isRecurring: false,
          notes: "",
          location: "",
          reference: ""
        }))
        setErrors({})
        setSaveAndAddAnother(false)
        // Focus on amount field
        setTimeout(() => {
          const amountField = document.querySelector('input[type="text"]') as HTMLInputElement
          if (amountField) amountField.focus()
        }, 100)
      } else {
        handleClose()
      }
    } catch (error) {
      console.error('Error saving transaction:', error)
      // You could add a toast notification here
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      accountId: "",
      categoryId: "",
      amount: "",
      type: "expense",
      description: "",
      date: new Date().toISOString().split('T')[0],
      time: new Date().toTimeString().slice(0, 5),
      transferTo: "",
      transferToContactId: "",
      transferToNote: "",
      transferFromContactId: "",
      transferFromNote: "",
      isRecurring: false,
      notes: "",
      location: "",
      reference: ""
    })
    setErrors({})
    setShowContactDropdown(false)
    onClose()
  }

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleLegacyContactSelect = (contact: Contact) => {
    setFormData(prev => ({ ...prev, transferTo: contact.name }))
    setShowContactDropdown(false)
  }

  const handleContactCreated = (newContact: Contact) => {
    // Close the contact modal
    setShowAddContactModal(false)

    // Update the contacts list in parent component
    onContactCreated?.(newContact)

    // Auto-select the new contact
    setFormData(prev => ({
      ...prev,
      transferTo: newContact.name,
      transferToContactId: newContact.id.toString()
    }))
  }

  const handleCloseContactModal = () => {
    setShowAddContactModal(false)
  }

  // Filter categories based on transaction type
  const availableCategories = categories.filter(category => {
    if (formData.type === "transfer") {
      return true // Transfer can use any category
    }
    return category.type === formData.type
  })

  return (
    <>
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={isEditing ? "Edit Transaction" : "Add New Transaction"}
      subtitle={isEditing ? "Update transaction details" : "Create a new transaction"}
      size="3xl"
    >
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Compact 2-column layout with enhanced styling */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 p-4 bg-gray-50/50 dark:bg-gray-800/50 rounded-xl border border-gray-200/50 dark:border-gray-700/50 hover:bg-gray-50/70 dark:hover:bg-gray-800/70 transition-colors duration-200">
          {/* Left Column: Basic Info */}
          <div className="space-y-3">
            <BasicTransactionFields
              formData={formData}
              errors={errors}
              accounts={accounts}
              categories={availableCategories}
              loading={loading}
              onInputChange={handleInputChange}
            />
          </div>

          {/* Right Column: Amount & Date */}
          <div className="space-y-3">
            <AmountDateTimeFields
              formData={formData}
              errors={errors}
              loading={loading}
              onInputChange={handleInputChange}
            />
          </div>
        </div>

        {/* Transfer Fields (full width when needed) */}
        {formData.type === 'transfer' && (
          <div className="p-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200/50 dark:border-blue-700/50 hover:from-blue-50/70 hover:to-indigo-50/70 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 transition-all duration-200">
            <div className="flex items-center gap-2 mb-3">
              <Building2 size={16} className="text-blue-600 dark:text-blue-400" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Transfer Details</h3>
            </div>
            <TransferFields
              formData={formData}
              errors={errors}
              contacts={contacts}
              filteredContacts={filteredContacts}
              loading={loading}
              showContactDropdown={showContactDropdown}
              onInputChange={handleInputChange}
              onLegacyContactSelect={handleLegacyContactSelect}
              setShowContactDropdown={setShowContactDropdown}
              onCreateContact={() => setShowAddContactModal(true)}
            />
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category *
            </label>
            <select
              value={formData.categoryId}
              onChange={(e) => handleInputChange('categoryId', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
                errors.categoryId 
                  ? 'border-red-500 dark:border-red-400' 
                  : 'border-gray-300 dark:border-gray-600'
              }`}
              disabled={loading}
            >
              <option value="">Select Category</option>
              {availableCategories.map((category) => (
                <option key={category.id} value={category.id.toString()}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
            {errors.categoryId && (
              <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.categoryId}</p>
            )}
          </div>

          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Amount (VND) *
            </label>
            <div className="relative">
              <input
                type="text"
                value={formData.amount}
                onChange={(e) => {
                  // Allow only numbers and commas for formatting
                  const value = e.target.value.replace(/[^\d]/g, '')
                  handleInputChange('amount', value)
                }}
                placeholder="0"
                className={`w-full px-3 py-2 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
                  errors.amount
                    ? 'border-red-500 dark:border-red-400'
                    : 'border-gray-300 dark:border-gray-600'
                }`}
                disabled={loading}
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm">
                VND
              </div>
            </div>
            {formData.amount && (
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                {parseInt(formData.amount || '0').toLocaleString('vi-VN')} VND
              </p>
            )}
            {errors.amount && (
              <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.amount}</p>
            )}

            {/* Quick Amount Buttons */}
            <div className="mt-2 flex flex-wrap gap-2">
              {[50000, 100000, 200000, 500000, 1000000, 2000000].map((amount) => (
                <button
                  key={amount}
                  type="button"
                  onClick={() => handleInputChange('amount', amount.toString())}
                  className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  disabled={loading}
                >
                  {(amount / 1000).toLocaleString()}K
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description *
          </label>
          <input
            type="text"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter description"
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
              errors.description 
                ? 'border-red-500 dark:border-red-400' 
                : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.description}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date *
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
                errors.date
                  ? 'border-red-500 dark:border-red-400'
                  : 'border-gray-300 dark:border-gray-600'
              }`}
              disabled={loading}
            />
            {errors.date && (
              <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.date}</p>
            )}
          </div>

          {/* Time */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Time *
            </label>
            <input
              type="time"
              value={formData.time}
              onChange={(e) => handleInputChange('time', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
                errors.time
                  ? 'border-red-500 dark:border-red-400'
                  : 'border-gray-300 dark:border-gray-600'
              }`}
              disabled={loading}
            />
            {errors.time && (
              <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.time}</p>
            )}
          </div>

          {/* Reference */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Reference #
            </label>
            <input
              type="text"
              value={formData.reference}
              onChange={(e) => handleInputChange('reference', e.target.value)}
              placeholder="Invoice #, Receipt #, etc."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors"
              disabled={loading}
            />
          </div>
        </div>





        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Location
            </label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="Store, restaurant, ATM, etc."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors"
              disabled={loading}
            />
          </div>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notes
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Additional notes or comments..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors resize-none"
            disabled={loading}
          />
        </div>

        {/* Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isRecurring"
              checked={formData.isRecurring}
              onChange={(e) => handleInputChange('isRecurring', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={loading}
            />
            <label htmlFor="isRecurring" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Recurring transaction
            </label>
          </div>

          {isEditing && (
            <div className="flex items-center justify-end">
              <button
                type="button"
                onClick={() => {
                  // Update date and time to current for duplication
                  setFormData(prev => ({
                    ...prev,
                    date: new Date().toISOString().split('T')[0],
                    time: new Date().toTimeString().slice(0, 5)
                  }))
                  // Note: This creates a duplicate with current data but new date/time
                }}
                className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                disabled={loading}
              >
                Duplicate as New
              </button>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 mt-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50 -mx-6 -mb-6 px-6 pb-6 rounded-b-xl">
          <Button
            type="button"
            onClick={handleClose}
            variant="outline"
            className="flex-1"
            disabled={loading}
          >
            Cancel
          </Button>

          {isEditing && onDelete && transaction && (
            <Button
              type="button"
              onClick={() => onDelete(transaction.id)}
              variant="destructive"
              className="flex-1"
              disabled={loading}
            >
              Delete
            </Button>
          )}

          {!isEditing && (
            <Button
              type="button"
              onClick={(e) => {
                setSaveAndAddAnother(true)
                handleSubmit(e as any)
              }}
              variant="outline"
              className="flex-1"
              disabled={loading}
            >
              {loading && saveAndAddAnother ? "Saving..." : "Save & Add Another"}
            </Button>
          )}

          <Button
            type="submit"
            className="flex-1"
            disabled={loading}
          >
            {loading ? "Saving..." : (isEditing ? "Update Transaction" : "Save Transaction")}
          </Button>
        </div>
      </form>
    </Modal>

    {/* Enhanced New Contact Creation Dialog */}
    <Modal
      isOpen={showNewContactConfirm}
      onClose={() => setShowNewContactConfirm(false)}
      title="Create New Contact"
      subtitle="Add complete contact information for future transactions"
      size="2xl"
    >
      <div className="space-y-4">
        {/* Contact Preview */}
        <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-xl">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <span className="text-blue-600 dark:text-blue-400 text-sm">👤</span>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                "{newContactData.name}"
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {addToPermanentContacts
                  ? "Will be saved to your contacts permanently"
                  : "Will be used for this transaction only"
                }
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information Form */}
        <div className="space-y-4">
          {/* Basic Contact Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Contact Name *
              </label>
              <input
                type="text"
                value={newContactData.name}
                onChange={(e) => setNewContactData({...newContactData, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                placeholder="Enter contact name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                value={newContactData.phone}
                onChange={(e) => setNewContactData({...newContactData, phone: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                placeholder="0987654321"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email Address
              </label>
              <input
                type="email"
                value={newContactData.email}
                onChange={(e) => setNewContactData({...newContactData, email: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          {/* Payment Method Selection */}
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Method
              </label>
              <div className="grid grid-cols-2 gap-2">
                <button
                  type="button"
                  onClick={() => setNewContactData({...newContactData, paymentMethod: "bank"})}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    newContactData.paymentMethod === "bank"
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                      : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                  }`}
                >
                  <Building2 size={20} className="mx-auto mb-1" />
                  <div className="text-sm font-medium">Bank Transfer</div>
                </button>
                <button
                  type="button"
                  onClick={() => setNewContactData({...newContactData, paymentMethod: "cash"})}
                  className={`p-3 rounded-lg border-2 transition-colors ${
                    newContactData.paymentMethod === "cash"
                      ? "border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                      : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                  }`}
                >
                  <span className="text-xl block mb-1">💵</span>
                  <div className="text-sm font-medium">Cash</div>
                </button>
              </div>
            </div>

          </div>
        </div>

        {/* Bank Account Details (conditional section) */}
        {newContactData.paymentMethod === "bank" && (
          <div className="space-y-3 p-4 bg-blue-50/50 dark:bg-blue-900/10 rounded-xl border border-blue-200/50 dark:border-blue-700/50">
            <div className="flex items-center gap-2 mb-3">
              <Building2 size={16} className="text-blue-600 dark:text-blue-400" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Bank Account Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Bank Name *
                </label>
                <input
                  type="text"
                  value={newContactData.bankName}
                  onChange={(e) => setNewContactData({...newContactData, bankName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="Vietcombank, BIDV, Techcombank..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Account Number *
                </label>
                <input
                  type="text"
                  value={newContactData.accountNumber}
                  onChange={(e) => setNewContactData({...newContactData, accountNumber: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="**********"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Account Holder Name *
                </label>
                <input
                  type="text"
                  value={newContactData.accountHolderName}
                  onChange={(e) => setNewContactData({...newContactData, accountHolderName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
                  placeholder="Full name as on bank account"
                />
              </div>
            </div>
          </div>
        )}

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description (Optional)
          </label>
          <textarea
            value={newContactData.description}
            onChange={(e) => setNewContactData({...newContactData, description: e.target.value})}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
            placeholder="Add notes about this contact..."
            rows={2}
          />
        </div>

        {/* Checkbox for permanent contact creation */}
        <div className="flex items-start space-x-3 p-3 bg-gray-50/70 dark:bg-gray-800/70 rounded-xl border border-gray-200/50 dark:border-gray-700/50">
          <input
            type="checkbox"
            id="addToPermanentContacts"
            checked={addToPermanentContacts}
            onChange={(e) => setAddToPermanentContacts(e.target.checked)}
            className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />
          <div className="flex-1">
            <label htmlFor="addToPermanentContacts" className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer">
              Add to my contacts permanently
            </label>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {addToPermanentContacts
                ? "Contact will be saved and available for future transactions. You can edit details later from the Contacts page."
                : "Contact name will only be stored with this transaction and won't appear in your contacts list."
              }
            </p>
          </div>
        </div>

        <div className="flex gap-3 pt-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowNewContactConfirm(false)}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={async () => {
              // Validate required fields
              if (!newContactData.name.trim()) {
                alert("Contact name is required")
                return
              }

              if (newContactData.paymentMethod === "bank") {
                if (!newContactData.bankName.trim()) {
                  alert("Bank name is required for bank transfers")
                  return
                }
                if (!newContactData.accountNumber.trim()) {
                  alert("Account number is required for bank transfers")
                  return
                }
                if (!newContactData.accountHolderName.trim()) {
                  alert("Account holder name is required for bank transfers")
                  return
                }
              }

              setShowNewContactConfirm(false)
              await processTransaction()
            }}
            className="flex-1"
          >
            {addToPermanentContacts
              ? "Create Contact & Save Transaction"
              : "Save Transaction"
            }
          </Button>
        </div>
      </div>
    </Modal>

    {/* Add/Edit Contact Modal */}
    <AddEditContactModal
      isOpen={showAddContactModal}
      onClose={handleCloseContactModal}
      onSave={handleContactCreated}
      contact={null}
      isEditing={false}
    />
    </>
  )
}
