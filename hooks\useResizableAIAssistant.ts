import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  AI_ASSISTANT_CONFIG, 
  clampWidth, 
  isMobile, 
  saveWidth, 
  loadWidth 
} from '../config/aiAssistantConfig'

interface UseResizableAIAssistantReturn {
  // State
  aiAssistantOpen: boolean
  sidebarWidth: number
  isResizing: boolean
  
  // Actions
  openAIAssistant: () => void
  closeAIAssistant: () => void
  setSidebarWidth: (width: number) => void
  
  // Resize handlers
  handleResizeStart: (e: React.MouseEvent) => void
  handleResizeEnd: () => void
  
  // Computed values
  sidebarStyle: React.CSSProperties
  mainContentStyle: React.CSSProperties
}

export function useResizableAIAssistant(): UseResizableAIAssistantReturn {
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false)
  const [sidebarWidth, setSidebarWidthState] = useState(AI_ASSISTANT_CONFIG.DEFAULT_WIDTH)
  const [isResizing, setIsResizing] = useState(false)
  
  const startXRef = useRef<number>(0)
  const startWidthRef = useRef<number>(0)
  const isMouseDownRef = useRef<boolean>(false)

  // Initialize width from localStorage
  useEffect(() => {
    const savedWidth = loadWidth()
    setSidebarWidthState(savedWidth)
  }, [])

  // Handle window resize to maintain constraints
  useEffect(() => {
    const handleWindowResize = () => {
      if (typeof window === 'undefined') return
      
      const clampedWidth = clampWidth(sidebarWidth, window.innerWidth)
      if (clampedWidth !== sidebarWidth) {
        setSidebarWidthState(clampedWidth)
        saveWidth(clampedWidth)
      }
    }

    window.addEventListener('resize', handleWindowResize)
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [sidebarWidth])

  // Mouse move handler
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isMouseDownRef.current || typeof window === 'undefined') return

    const deltaX = startXRef.current - e.clientX // Reverse direction for right sidebar
    const newWidth = startWidthRef.current + deltaX
    const clampedWidth = clampWidth(newWidth, window.innerWidth)
    
    setSidebarWidthState(clampedWidth)
  }, [])

  // Mouse up handler
  const handleMouseUp = useCallback(() => {
    if (!isMouseDownRef.current) return
    
    isMouseDownRef.current = false
    setIsResizing(false)
    saveWidth(sidebarWidth)
    
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }, [handleMouseMove, sidebarWidth])

  // Resize start handler
  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    if (isMobile()) return // Disable resize on mobile
    
    e.preventDefault()
    e.stopPropagation()
    
    isMouseDownRef.current = true
    startXRef.current = e.clientX
    startWidthRef.current = sidebarWidth
    setIsResizing(true)
    
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'col-resize'
    document.body.style.userSelect = 'none'
  }, [sidebarWidth, handleMouseMove, handleMouseUp])

  // Resize end handler
  const handleResizeEnd = useCallback(() => {
    if (isMouseDownRef.current) {
      handleMouseUp()
    }
  }, [handleMouseUp])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [handleMouseMove, handleMouseUp])

  // Actions
  const openAIAssistant = useCallback(() => {
    setAiAssistantOpen(true)
  }, [])

  const closeAIAssistant = useCallback(() => {
    setAiAssistantOpen(false)
  }, [])

  const setSidebarWidth = useCallback((width: number) => {
    if (typeof window === 'undefined') return
    
    const clampedWidth = clampWidth(width, window.innerWidth)
    setSidebarWidthState(clampedWidth)
    saveWidth(clampedWidth)
  }, [])

  // Computed styles
  const sidebarStyle: React.CSSProperties = {
    width: isMobile() ? '100%' : `${sidebarWidth}px`,
    transition: isResizing ? 'none' : `all ${AI_ASSISTANT_CONFIG.TRANSITION_DURATION}ms ${AI_ASSISTANT_CONFIG.TRANSITION_EASING}`
  }

  const mainContentStyle: React.CSSProperties = {
    marginRight: aiAssistantOpen && !isMobile() ? `${sidebarWidth}px` : '0',
    transition: isResizing ? 'none' : `all ${AI_ASSISTANT_CONFIG.TRANSITION_DURATION}ms ${AI_ASSISTANT_CONFIG.TRANSITION_EASING}`
  }

  return {
    // State
    aiAssistantOpen,
    sidebarWidth,
    isResizing,
    
    // Actions
    openAIAssistant,
    closeAIAssistant,
    setSidebarWidth,
    
    // Resize handlers
    handleResizeStart,
    handleResizeEnd,
    
    // Computed values
    sidebarStyle,
    mainContentStyle
  }
}
