/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for better performance
  experimental: {
    // Enable React Server Components optimizations
    serverComponentsExternalPackages: ['msw'],
    // Optimize CSS
    optimizeCss: true,
    // Enable SWC minification
    swcMinify: true,
  },

  // Compiler optimizations
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
    
    // Enable React optimizations
    reactRemoveProperties: process.env.NODE_ENV === 'production',
  },

  // Bundle analyzer (enable when needed)
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config, { isServer }) => {
      if (!isServer) {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: '../bundle-analyzer-report.html'
          })
        )
      }
      return config
    }
  }),

  // Webpack optimizations
  webpack: (config, { dev, isServer }) => {
    // Production optimizations
    if (!dev) {
      // Split chunks for better caching
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            // Vendor chunk for third-party libraries
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            // Shared components chunk
            shared: {
              test: /[\\/]components[\\/]shared[\\/]/,
              name: 'shared-components',
              chunks: 'all',
              priority: 8,
            },
            // UI components chunk
            ui: {
              test: /[\\/]components[\\/]ui[\\/]/,
              name: 'ui-components',
              chunks: 'all',
              priority: 7,
            },
            // Feature components chunk
            features: {
              test: /[\\/]components[\\/]features[\\/]/,
              name: 'feature-components',
              chunks: 'all',
              priority: 6,
            },
            // Common chunk for frequently used modules
            common: {
              minChunks: 2,
              chunks: 'all',
              name: 'common',
              priority: 5,
            },
          },
        },
      }

      // Tree shaking optimizations
      config.optimization.usedExports = true
      config.optimization.sideEffects = false
    }

    // Resolve optimizations
    config.resolve.alias = {
      ...config.resolve.alias,
      // Optimize imports with alias paths
      '@': path.resolve('.'),
      '@/components': path.resolve('./components'),
      '@/types': path.resolve('./types'),
      '@/services': path.resolve('./services'),
      '@/hooks': path.resolve('./hooks'),
      '@/utils': path.resolve('./utils'),
      '@/lib': path.resolve('./lib'),
      '@/styles': path.resolve('./styles'),
      '@/public': path.resolve('./public'),
      '@/app': path.resolve('./app'),
      '@/pages': path.resolve('./pages'),

      // Specific component aliases
      '@/ui': path.resolve('./components/ui'),
      '@/shared': path.resolve('./components/shared'),
      '@/features': path.resolve('./components/features'),
      '@/layout': path.resolve('./components/layout'),

      // API aliases
      '@/api': path.resolve('./services/api'),
      '@/mocks': path.resolve('./services/mocks'),
    }

    return config
  },

  // Image optimization
  images: {
    // Enable image optimization
    formats: ['image/webp', 'image/avif'],
    // Optimize images
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
    // Allowed domains for external images
    domains: [],
  },

  // Headers for better caching
  async headers() {
    return [
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=********, immutable',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, s-maxage=300',
          },
        ],
      },
    ]
  },

  // Redirects for better SEO
  async redirects() {
    return [
      // Add redirects as needed
    ]
  },

  // Rewrites for API routing
  async rewrites() {
    return process.env.NODE_ENV === 'development' ? [
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ] : []
  },

  // Output configuration
  output: 'standalone',

  // PoweredBy header removal
  poweredByHeader: false,

  // Compression
  compress: true,

  // Generate ETags
  generateEtags: true,

  // Page extensions
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // Trailing slash
  trailingSlash: false,

  // Environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // TypeScript configuration
  typescript: {
    // Enable type checking in production builds
    ignoreBuildErrors: false,
  },

  // ESLint configuration
  eslint: {
    // Enable ESLint during builds
    ignoreDuringBuilds: false,
  },
}

// Add path import
const path = require('path')

module.exports = nextConfig
