"use client"

import React from "react"
import type { Account, Category } from "@/lib/generated/prisma"
import type { TransactionKind } from "@/lib/generated/prisma"

interface FormData {
  type: TransactionKind
  accountId: string
  categoryId: string
  amount: string
  description: string
  date: string
  time: string
}

interface FormErrors {
  accountId?: string
  categoryId?: string
  amount?: string
  description?: string
  date?: string
  time?: string
}

interface BasicTransactionFieldsProps {
  formData: FormData
  errors: FormErrors
  accounts: Account[]
  categories: Category[]
  loading: boolean
  onInputChange: (field: keyof FormData, value: string | boolean) => void
}

export function BasicTransactionFields({
  formData,
  errors,
  accounts,
  categories,
  loading,
  onInputChange
}: BasicTransactionFieldsProps) {
  // Filter categories based on transaction type
  const availableCategories = categories.filter(category => {
    if (formData.type === "transfer") {
      return true // Transfer can use any category
    }
    return category.type === formData.type
  })

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2">
        Basic Information
      </h3>
      
      {/* Transaction Type & Account Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Transaction Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Type *
          </label>
          <select
            value={formData.type}
            onChange={(e) => onInputChange('type', e.target.value as "income" | "expense" | "transfer")}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors"
            disabled={loading}
          >
            <option value="expense">Expense</option>
            <option value="income">Income</option>
            <option value="transfer">Transfer</option>
          </select>
        </div>

        {/* Account */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Account *
          </label>
          <select
            value={formData.accountId}
            onChange={(e) => onInputChange('accountId', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
              errors.accountId 
                ? 'border-red-500 dark:border-red-400' 
                : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
          >
            <option value="">Select Account</option>
            {accounts.map((account) => (
              <option key={account.id} value={account.id.toString()}>
                {account.logo || '💳'} {account.name}
              </option>
            ))}
          </select>
          {errors.accountId && (
            <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.accountId}</p>
          )}
        </div>
      </div>

      {/* Category */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Category *
        </label>
        <select
          value={formData.categoryId}
          onChange={(e) => onInputChange('categoryId', e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
            errors.categoryId 
              ? 'border-red-500 dark:border-red-400' 
              : 'border-gray-300 dark:border-gray-600'
          }`}
          disabled={loading}
        >
          <option value="">Select Category</option>
          {availableCategories.map((category) => (
            <option key={category.id} value={category.id.toString()}>
              {category.icon} {category.name}
            </option>
          ))}
        </select>
        {errors.categoryId && (
          <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.categoryId}</p>
        )}
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Description *
        </label>
        <input
          type="text"
          value={formData.description}
          onChange={(e) => onInputChange('description', e.target.value)}
          placeholder="Enter description"
          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 transition-colors ${
            errors.description 
              ? 'border-red-500 dark:border-red-400' 
              : 'border-gray-300 dark:border-gray-600'
          }`}
          disabled={loading}
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-500 dark:text-red-400">{errors.description}</p>
        )}
      </div>
    </div>
  )
}
