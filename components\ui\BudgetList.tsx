"use client"

import React, { useState } from "react"
import { BudgetCard } from "./BudgetCard"
import { ChevronDown, ChevronUp, SortAsc, SortDesc, Filter, Target } from "lucide-react"

interface Budget {
  id: number
  name: string
  description?: string
  amount: number
  spent: number
  period: "monthly" | "weekly" | "yearly"
  categoryId?: number
  categoryName?: string
  categoryColor?: string
  startDate: string
  endDate: string
  isActive?: boolean
}

interface BudgetListProps {
  CategoryGoal: Budget[]
  onEdit?: (budget: Budget) => void
  onDelete?: (id: number) => void
  formatCurrency?: (amount: number) => string
  showFilters?: boolean
}

type SortField = "name" | "amount" | "spent" | "percentage" | "period"
type SortDirection = "asc" | "desc"

export function BudgetList({ 
  CategoryGoal, 
  onEdit, 
  onDelete, 
  formatCurrency,
  showFilters = true 
}: BudgetListProps) {
  const [sortField, setSortField] = useState<SortField>("name")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")
  const [showSortOptions, setShowSortOptions] = useState(false)
  const [filterPeriod, setFilterPeriod] = useState<"all" | "monthly" | "weekly" | "yearly">("all")
  const [filterStatus, setFilterStatus] = useState<"all" | "on-track" | "warning" | "over-budget">("all")

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
    setShowSortOptions(false)
  }

  const filteredCategoryGoal = CategoryGoal.filter(budget => {
    // Period filter
    if (filterPeriod !== "all" && budget.period !== filterPeriod) {
      return false
    }

    // Status filter
    if (filterStatus !== "all") {
      const spentPercentage = budget.amount > 0 ? (budget.spent / budget.amount) * 100 : 0
      if (filterStatus === "on-track" && spentPercentage >= 80) return false
      if (filterStatus === "warning" && (spentPercentage < 80 || spentPercentage >= 100)) return false
      if (filterStatus === "over-budget" && spentPercentage < 100) return false
    }

    return true
  })

  const sortedCategoryGoal = [...filteredCategoryGoal].sort((a, b) => {
    let aValue: any
    let bValue: any

    switch (sortField) {
      case "name":
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case "amount":
        aValue = a.amount
        bValue = b.amount
        break
      case "spent":
        aValue = a.spent
        bValue = b.spent
        break
      case "percentage":
        aValue = a.amount > 0 ? (a.spent / a.amount) * 100 : 0
        bValue = b.amount > 0 ? (b.spent / b.amount) * 100 : 0
        break
      case "period":
        aValue = a.period
        bValue = b.period
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
    return 0
  })

  const getSortLabel = (field: SortField) => {
    const labels = {
      name: "Name",
      amount: "Budget Amount",
      spent: "Amount Spent",
      percentage: "Progress",
      period: "Period"
    }
    return labels[field]
  }

  if (CategoryGoal.length === 0) {
    return (
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-3xl border border-gray-200/50 dark:border-gray-700/50 p-8 text-center">
        <div className="text-gray-400 dark:text-gray-500 mb-2">
          <Target size={48} className="mx-auto mb-4 opacity-50" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No CategoryGoal found
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Create your first budget to start tracking your spending goals.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Filter and Sort Controls */}
      {showFilters && (
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {filteredCategoryGoal.length} budget{filteredCategoryGoal.length !== 1 ? 's' : ''}
              </span>
              
              {/* Period Filter */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Period:</span>
                <select
                  value={filterPeriod}
                  onChange={(e) => setFilterPeriod(e.target.value as "all" | "monthly" | "weekly" | "yearly")}
                  className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All</option>
                  <option value="monthly">Monthly</option>
                  <option value="weekly">Weekly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>

              {/* Status Filter */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as "all" | "on-track" | "warning" | "over-budget")}
                  className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All</option>
                  <option value="on-track">On Track</option>
                  <option value="warning">Warning</option>
                  <option value="over-budget">Over Budget</option>
                </select>
              </div>
            </div>
            
            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortOptions(!showSortOptions)}
                className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                {sortDirection === "asc" ? <SortAsc size={16} /> : <SortDesc size={16} />}
                Sort by {getSortLabel(sortField)}
                {showSortOptions ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>

              {showSortOptions && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-10">
                  {(["name", "amount", "spent", "percentage", "period"] as SortField[]).map((field) => (
                    <button
                      key={field}
                      onClick={() => handleSort(field)}
                      className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                        sortField === field 
                          ? "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20" 
                          : "text-gray-700 dark:text-gray-300"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        {getSortLabel(field)}
                        {sortField === field && (
                          sortDirection === "asc" ? <SortAsc size={14} /> : <SortDesc size={14} />
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Budget Cards */}
      <div className="space-y-3">
        {sortedCategoryGoal.map((budget) => (
          <BudgetCard
            key={budget.id}
            budget={budget}
            onEdit={onEdit}
            onDelete={onDelete}
            formatCurrency={formatCurrency}
          />
        ))}
      </div>
    </div>
  )
}
