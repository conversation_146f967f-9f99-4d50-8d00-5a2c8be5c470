"use client"

import { AccountPaymentAppWithRelations, AccountWithRelations, DebitCardWithRelations } from "@/types/entities"
import { useState } from "react"
import { AccountSidebar } from "./AccountSidebar"

interface AccountSidebarWithChildrenProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: any) => void
  account?: AccountWithRelations | null
  isEditing: boolean
}

export function AccountSidebarWithChildren({
  isOpen,
  onClose,
  onSave,
  account,
  isEditing
}: AccountSidebarWithChildrenProps) {



  return (
    <>
      {/* Main Account Sidebar */}
      <AccountSidebar
        isOpen={isOpen}
        onClose={onClose}
        onSave={onSave}
        account={account}
        isEditing={isEditing}
    
      />

    </>
  )
}
