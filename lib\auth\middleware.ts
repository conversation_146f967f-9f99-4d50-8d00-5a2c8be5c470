import { NextRequest, NextResponse } from 'next/server'
import { verifyToken, extractBearerToken, JWTPayload } from './jwt'
import prisma from '../prisma'

// Extended request with user info
export interface AuthenticatedRequest extends NextRequest {
  user: JWTPayload
}

/**
 * Auth middleware for API routes
 */
export async function with<PERSON>uth(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Extract token from Authorization header
    const authHeader = request.headers.get('authorization')
    const token = extractBearerToken(authHeader)
    
    if (!token) {
      return NextResponse.json(
        { 
          success: false,
          error: {
            message: 'Authorization token required',
            code: 'UNAUTHORIZED'
          }
        },
        { status: 401 }
      )
    }
    
    // Verify token
    let payload: JWTPayload
    try {
      payload = verifyToken(token)
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Invalid token'
      return NextResponse.json(
        { 
          success: false,
          error: {
            message,
            code: 'UNAUTHORIZED'
          }
        },
        { status: 401 }
      )
    }
    
    // Check if user exists and is active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId as string },
      select: { id: true, isActive: true }
    })
    if (!user || !user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: {
            message: 'User not found or inactive',
            code: 'UNAUTHORIZED'
          }
        },
        { status: 401 }
      )
    }
    
    // Add user info to request
    const authenticatedRequest = request as AuthenticatedRequest
    authenticatedRequest.user = payload
    
    // Call the handler
    return await handler(authenticatedRequest)
    
  } catch (error) {
    console.error('Auth middleware error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: {
          message: 'Internal server error',
          code: 'INTERNAL_ERROR'
        }
      },
      { status: 500 }
    )
  }
}

/**
 * Role-based access control middleware
 */
export async function withRole(
  roles: ('admin' | 'user')[],
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  return withAuth(request, async (req) => {
    // Check if user has required role
    if (!roles.includes(req.user.role)) {
      return NextResponse.json(
        { 
          success: false,
          error: {
            message: 'Insufficient permissions',
            code: 'FORBIDDEN'
          }
        },
        { status: 403 }
      )
    }
    
    return await handler(req)
  })
}

/**
 * Admin-only middleware
 */
export async function withAdminRole(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  return withRole(['admin'], request, handler)
}

/**
 * Optional auth middleware (doesn't require authentication)
 */
export async function withOptionalAuth(
  request: NextRequest,
  handler: (req: NextRequest & { user?: JWTPayload }) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Try to extract and verify token
    const authHeader = request.headers.get('authorization')
    const token = extractBearerToken(authHeader)
    
    if (token) {
      try {
        const payload = verifyToken(token)
        const user = await prisma.user.findUnique({
          where: { id: payload.userId as string },
          select: { id: true, isActive: true }
        })
        
        if (user && user.isActive) {
          // Add user info to request if valid
          const requestWithUser = request as NextRequest & { user?: JWTPayload }
          requestWithUser.user = payload
          return await handler(requestWithUser)
        }
      } catch {
        // Invalid token, continue without user
      }
    }
    
    // Continue without authentication
    return await handler(request)
    
  } catch (error) {
    console.error('Optional auth middleware error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: {
          message: 'Internal server error',
          code: 'INTERNAL_ERROR'
        }
      },
      { status: 500 }
    )
  }
}
