/**
 * API Root Route
 * 
 * Health check and API information endpoint
 */

import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // API info data
  const apiInfo = {
    name: 'Money Management API',
    version: '2.0.0',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    architecture: 'GraphQL + REST Auth',
    description: 'GraphQL-first API with REST authentication endpoints',
    endpoints: {
      // GraphQL endpoint for all business logic
      graphql: '/api/graphql',

      // REST endpoints for authentication only
      auth: {
        login: '/api/auth/login',
        register: '/api/auth/register',
        refresh: '/api/auth/refresh',
        logout: '/api/auth/logout',
        me: '/api/auth/me',
        changePassword: '/api/auth/change-password'
      },

      // Health check
      health: '/api/health'
    },
    features: [
      'GraphQL API for all data operations',
      'REST authentication endpoints',
      'JWT token-based authentication',
      'Apollo Client caching',
      'Type-safe TypeScript integration',
      'Real-time subscriptions ready'
    ]
  }

  return NextResponse.json({
    success: true,
    data: apiInfo,
    timestamp: new Date().toISOString()
  })
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
