/**
 * Shared Validation Utilities
 * 
 * Common validation functions used across services and components
 */

import { ValidationError } from '@/lib/api/utils'

// ==================== BASIC VALIDATORS ====================

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate phone format (supports international formats)
 */
export function isValidPhone(phone: string): boolean {
  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '')
  // Check if it has 10-15 digits (international format)
  return digitsOnly.length >= 10 && digitsOnly.length <= 15
}

/**
 * Validate required field
 */
export function isRequired(value: any): boolean {
  if (value == null) return false
  if (typeof value === 'string') return value.trim().length > 0
  if (Array.isArray(value)) return value.length > 0
  return true
}

/**
 * Validate string length
 */
export function isValidLength(value: string, min: number, max?: number): boolean {
  const length = value.trim().length
  if (length < min) return false
  if (max !== undefined && length > max) return false
  return true
}

/**
 * Validate numeric range
 */
export function isInRange(value: number, min: number, max?: number): boolean {
  if (value < min) return false
  if (max !== undefined && value > max) return false
  return true
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * Validate date format and validity
 */
export function isValidDate(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj instanceof Date && !isNaN(dateObj.getTime())
}

/**
 * Validate currency amount (positive number with max 2 decimal places)
 */
export function isValidCurrency(amount: number): boolean {
  if (amount < 0) return false
  // Check if it has more than 2 decimal places
  const decimalPlaces = (amount.toString().split('.')[1] || '').length
  return decimalPlaces <= 2
}

// ==================== COMPLEX VALIDATORS ====================

/**
 * Validate password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean
  errors: string[]
  score: number
} {
  const errors: string[] = []
  let score = 0

  // Length check
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  } else {
    score += 1
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  } else {
    score += 1
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  } else {
    score += 1
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  } else {
    score += 1
  }

  // Special character check
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  } else {
    score += 1
  }

  return {
    isValid: errors.length === 0,
    errors,
    score
  }
}

/**
 * Validate Vietnamese phone number specifically
 */
export function isValidVietnamesePhone(phone: string): boolean {
  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '')
  
  // Vietnamese phone patterns
  const patterns = [
    /^(84|0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/, // Mobile
    /^(84|0)(2[0-9])\d{8}$/, // Landline
  ]
  
  return patterns.some(pattern => pattern.test(digitsOnly))
}

// ==================== VALIDATION HELPERS ====================

/**
 * Validate and throw error if invalid
 */
export function validateRequired(value: any, fieldName: string): void {
  if (!isRequired(value)) {
    throw new ValidationError(`${fieldName} is required`)
  }
}

/**
 * Validate email and throw error if invalid
 */
export function validateEmail(email: string, fieldName: string = 'Email'): void {
  if (!isValidEmail(email)) {
    throw new ValidationError(`${fieldName} format is invalid`)
  }
}

/**
 * Validate phone and throw error if invalid
 */
export function validatePhone(phone: string, fieldName: string = 'Phone'): void {
  if (!isValidPhone(phone)) {
    throw new ValidationError(`${fieldName} format is invalid`)
  }
}

/**
 * Validate string length and throw error if invalid
 */
export function validateLength(
  value: string, 
  min: number, 
  max: number | undefined, 
  fieldName: string
): void {
  if (!isValidLength(value, min, max)) {
    const maxText = max ? ` and maximum ${max}` : ''
    throw new ValidationError(`${fieldName} must be minimum ${min}${maxText} characters`)
  }
}

/**
 * Validate numeric range and throw error if invalid
 */
export function validateRange(
  value: number, 
  min: number, 
  max: number | undefined, 
  fieldName: string
): void {
  if (!isInRange(value, min, max)) {
    const maxText = max ? ` and maximum ${max}` : ''
    throw new ValidationError(`${fieldName} must be minimum ${min}${maxText}`)
  }
}

/**
 * Validate currency amount and throw error if invalid
 */
export function validateCurrency(amount: number, fieldName: string = 'Amount'): void {
  if (!isValidCurrency(amount)) {
    throw new ValidationError(`${fieldName} must be a positive number with maximum 2 decimal places`)
  }
}

// ==================== BATCH VALIDATION ====================

/**
 * Validate multiple fields at once
 */
export function validateFields(validations: Array<{
  value: any
  validator: (value: any) => boolean
  errorMessage: string
}>): void {
  const errors: string[] = []

  validations.forEach(({ value, validator, errorMessage }) => {
    if (!validator(value)) {
      errors.push(errorMessage)
    }
  })

  if (errors.length > 0) {
    throw new ValidationError(errors.join('; '))
  }
}

/**
 * Validate object against schema
 */
export function validateObject<T>(
  obj: any,
  schema: Record<keyof T, {
    required?: boolean
    validator?: (value: any) => boolean
    errorMessage?: string
  }>
): void {
  const errors: string[] = []

  Object.entries(schema).forEach(([key, rules]) => {
    const value = obj[key]
    
    // Check required
    if (rules.required && !isRequired(value)) {
      errors.push(`${key} is required`)
      return
    }

    // Skip validation if value is empty and not required
    if (!isRequired(value) && !rules.required) {
      return
    }

    // Check custom validator
    if (rules.validator && !rules.validator(value)) {
      errors.push(rules.errorMessage || `${key} is invalid`)
    }
  })

  if (errors.length > 0) {
    throw new ValidationError(errors.join('; '))
  }
}

// ==================== SANITIZATION ====================

/**
 * Sanitize string input
 */
export function sanitizeString(value: string): string {
  return value.trim().replace(/\s+/g, ' ')
}

/**
 * Sanitize email input
 */
export function sanitizeEmail(email: string): string {
  return email.trim().toLowerCase()
}

/**
 * Sanitize phone input
 */
export function sanitizePhone(phone: string): string {
  // Keep only digits, spaces, hyphens, parentheses, and plus sign
  return phone.replace(/[^\d\s\-\(\)\+]/g, '').trim()
}

/**
 * Sanitize numeric input
 */
export function sanitizeNumber(value: string | number): number {
  if (typeof value === 'number') return value
  const cleaned = value.replace(/[^\d.-]/g, '')
  const parsed = parseFloat(cleaned)
  return isNaN(parsed) ? 0 : parsed
}

// ==================== EXPORT VALIDATORS OBJECT ====================

export const validators = {
  email: isValidEmail,
  phone: isValidPhone,
  vietnamesePhone: isValidVietnamesePhone,
  required: isRequired,
  length: isValidLength,
  range: isInRange,
  url: isValidUrl,
  date: isValidDate,
  currency: isValidCurrency,
  passwordStrength: validatePasswordStrength
}

export const sanitizers = {
  string: sanitizeString,
  email: sanitizeEmail,
  phone: sanitizePhone,
  number: sanitizeNumber
}

export const validateAndThrow = {
  required: validateRequired,
  email: validateEmail,
  phone: validatePhone,
  length: validateLength,
  range: validateRange,
  currency: validateCurrency,
  fields: validateFields,
  object: validateObject
}
