"use client"

import React, { useState, useEffect } from "react"
import { But<PERSON> } from "./button"

interface SidebarFooterProps {
  children: React.ReactNode
  maxWidth?: number
  sidebarWidth?: number
  className?: string
}

interface SidebarFooterButtonsProps {
  onCancel: () => void
  onSave: () => void
  saveLabel?: string
  cancelLabel?: string
  loading?: boolean
  saveDisabled?: boolean
  className?: string
}

function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    // Set initial value
    checkIsMobile()

    // Add event listener
    window.addEventListener('resize', checkIsMobile)

    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  return isMobile
}

export function SidebarFooter({
  children,
  maxWidth = 400,
  sidebarWidth,
  className = ""
}: SidebarFooterProps) {
  const mobile = useIsMobile()

  // Footer should be constrained within sidebar width
  const effectiveWidth = mobile ? '100%' : (sidebarWidth || maxWidth)

  return (
    <div
      className={`
        w-full
        bg-white dark:bg-gray-900
        border-t border-gray-200 dark:border-gray-700
        shadow-lg
        ${className}
      `}
      style={{
        maxWidth: mobile ? '100%' : `${effectiveWidth}px`
      }}
    >
      {/* Content container with proper padding and constraints */}
      <div className="p-4 w-full">
        <div
          className="w-full"
          style={{
            maxWidth: mobile ? '100%' : `${Math.min(maxWidth, effectiveWidth as number - 32)}px`, // 32px for padding
            margin: mobile ? '0' : '0 auto'
          }}
        >
          {children}
        </div>
      </div>
    </div>
  )
}

export function SidebarFooterButtons({
  onCancel,
  onSave,
  saveLabel = "Lưu",
  cancelLabel = "Hủy",
  loading = false,
  saveDisabled = false,
  className = ""
}: SidebarFooterButtonsProps) {
  return (
    <div className={`flex gap-3 ${className}`}>
      {/* Cancel Button */}
      <Button
        type="button"
        onClick={onCancel}
        variant="outline"
        className="flex-1"
        disabled={loading}
      >
        {cancelLabel}
      </Button>



      {/* Save Button */}
      <Button
        type="submit"
        onClick={onSave}
        className="flex-1"
        disabled={loading || saveDisabled}
      >
        {loading ? "Đang lưu..." : saveLabel}
      </Button>
    </div>
  )
}

// Combined component for common use case
export function SidebarActionFooter({
  onCancel,
  onSave,
  saveLabel = "Lưu",
  cancelLabel = "Hủy",
  loading = false,
  saveDisabled = false,
  maxWidth = 400,
  sidebarWidth,
  className = ""
}: SidebarFooterButtonsProps & { maxWidth?: number; sidebarWidth?: number }) {
  return (
    <SidebarFooter
      maxWidth={maxWidth}
      sidebarWidth={sidebarWidth}
      className={className}
    >
      <SidebarFooterButtons
        onCancel={onCancel}
        onSave={onSave}
        saveLabel={saveLabel}
        cancelLabel={cancelLabel}
        loading={loading}
        saveDisabled={saveDisabled}
      />
    </SidebarFooter>
  )
}
