/**
 * Apollo GraphQL Provider
 * 
 * Provider component to wrap Next.js app with Apollo Client
 */

'use client'

import { ApolloProvider } from '@apollo/client'
import { apolloClient } from './client'

interface GraphQLProviderProps {
  children: React.ReactNode
}

export function GraphQLProvider({ children }: GraphQLProviderProps) {
  return (
    <ApolloProvider client={apolloClient}>
      {children}
    </ApolloProvider>
  )
}

export default GraphQLProvider
